#!/bin/bash

# ----------------------------------
# WARNING: ⚠️
# Do not change anything below this line unless you know what you're doing!
# ----------------------------------

# Certificate Keychain Installation Script
# This script manages certificate installation in a custom "CICDIOS" keychain
# It automatically detects all .p12 certificates in the "Certificates" folder


# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color


# Function to print colored messages
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}


# Source the certificate configuration file
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
CONFIG_FILE="$SCRIPT_DIR/certificate_password.sh"

# Load certificate passwords from configuration file
source "$CONFIG_FILE"


load_certificate_config() {
    print_message $YELLOW "Loading certificate configuration..."
    
    # Check if configuration file exists
    if [ ! -f "$CONFIG_FILE" ]; then
        print_message $RED "=== Configuration Error ==="
        print_message $RED "✗ Configuration file not found: $CONFIG_FILE"
        print_message $YELLOW "Please create a file named 'certificate_password.sh' in the same directory as this script."
        print_message $YELLOW "Location: $SCRIPT_DIR/"
        return 1
    fi
    
    # Load certificate passwords from configuration file
    source "$CONFIG_FILE"
    
    # Validate that CERT_PASSWORDS array is defined
    if [ -z "${CERT_PASSWORDS+x}" ] && [ -z "${UNIVERSAL_CERT_PASSWORD+x}" ]; then
        print_message $RED "=== Configuration Error ==="
        print_message $RED "✗ No certificate passwords found in configuration file"
        echo ""
        print_message $YELLOW "The configuration file exists but doesn't contain valid password definitions."
        print_message $YELLOW "Please ensure your $CONFIG_FILE contains either:"
        print_message $YELLOW "  - A CERT_PASSWORDS array with certificate passwords"
        echo ""
        print_message $BLUE "Example:"
        echo 'CERT_PASSWORDS=('
        echo '    'certificate1.p12:password1''
        echo '    'certificate2.p12:password2''
        echo ')'
        echo ""
        exit 1
    fi
    
    print_message $GREEN "✓ Certificate configuration loaded successfully"
    return 0
}

# Configuration
KEYCHAIN_NAME="CICDIOS"
KEYCHAIN_PASSWORD="indianic"  # Change this to your desired keychain password
CERTIFICATES_FOLDER="./Profile"
PROVISIONING_PROFILES_DIR="$HOME/Library/MobileDevice/Provisioning Profiles"

# Alternative: Use a single password for all certificates
# Uncomment the line below and comment out the array above if all certificates share the same password
# UNIVERSAL_CERT_PASSWORD="YourUniversalPassword"

# Function to check if keychain exists
keychain_exists() {
    security list-keychains | grep -q "$KEYCHAIN_NAME.keychain-db"
    return $?
}

# Function to create keychain
create_keychain() {
    print_message $YELLOW "Creating keychain: $KEYCHAIN_NAME"
    
    # Create the keychain with password
    security create-keychain -p "$KEYCHAIN_PASSWORD" "$KEYCHAIN_NAME.keychain-db"
    
    if [ $? -eq 0 ]; then
        print_message $GREEN "✓ Keychain created successfully"
        
        # Add keychain to search list
        security list-keychains -s "$KEYCHAIN_NAME.keychain-db" $(security list-keychains | grep -v "$KEYCHAIN_NAME" | sed 's/"//g')
        
        # Set keychain settings (no timeout, lock on sleep)
        security set-keychain-settings -t 3600 -l "$KEYCHAIN_NAME.keychain-db"
        
        # Unlock the keychain
        security unlock-keychain -p "$KEYCHAIN_PASSWORD" "$KEYCHAIN_NAME.keychain-db"
        
        return 0
    else
        print_message $RED "✗ Failed to create keychain"
        return 1
    fi
}

# Function to get certificate password
get_cert_password() {
    local cert_filename=$1
    
    # If universal password is set, use it
    if [ ! -z "${UNIVERSAL_CERT_PASSWORD+x}" ]; then
        echo "$UNIVERSAL_CERT_PASSWORD"
        return 0
    fi
    
    # Otherwise, look up in the passwords array
    for entry in "${CERT_PASSWORDS[@]}"; do
        local filename="${entry%%:*}"
        local password="${entry#*:}"
        
        if [ "$filename" = "$cert_filename" ]; then
            echo "$password"
            return 0
        fi
    done
    
    return 1
}

# Function to install certificate
install_certificate() {
    local cert_path=$1
    local cert_filename=$(basename "$cert_path")
    
    print_message $YELLOW "Installing certificate: $cert_filename"
    
    # Get password for this certificate
    local cert_password=$(get_cert_password "$cert_filename")
    
    if [ -z "$cert_password" ]; then
        print_message $RED "✗ No password defined for: $cert_filename"
        print_message $RED "  Please add password to CERT_PASSWORDS array or set UNIVERSAL_CERT_PASSWORD"
        return 1
    fi
    
    # Install P12 certificate with password
    security import "$cert_path" -k "$KEYCHAIN_NAME.keychain-db" -P "$cert_password" -T /usr/bin/codesign -T /usr/bin/security -T /usr/bin/productbuild
    
    if [ $? -eq 0 ]; then
        print_message $GREEN "✓ Certificate installed successfully: $cert_filename"
        return 0
    else
        print_message $RED "✗ Failed to install certificate: $cert_filename"
        return 1
    fi
}

# Function to unlock keychain
unlock_keychain() {
    print_message $YELLOW "Unlocking keychain: $KEYCHAIN_NAME"
    security unlock-keychain -p "$KEYCHAIN_PASSWORD" "$KEYCHAIN_NAME.keychain-db"
    
    if [ $? -eq 0 ]; then
        print_message $GREEN "✓ Keychain unlocked successfully"
        return 0
    else
        print_message $RED "✗ Failed to unlock keychain"
        return 1
    fi
}

# Function to find all .p12 certificates
find_certificates() {
    local cert_count=0
    
    if [ -d "$CERTIFICATES_FOLDER" ]; then
        cert_count=$(find "$CERTIFICATES_FOLDER" -name "*.p12" -type f 2>/dev/null | wc -l | tr -d ' ')
        print_message $BLUE "Found $cert_count .p12 certificate(s) in $CERTIFICATES_FOLDER"
    else
        print_message $RED "✗ Certificates folder not found: $CERTIFICATES_FOLDER"
        print_message $YELLOW "Please create the folder and add your .p12 certificates"
        return 1
    fi
    
    if [ "$cert_count" -eq 0 ]; then
        print_message $YELLOW "No .p12 certificates found in $CERTIFICATES_FOLDER"
        return 1
    fi
    
    return 0
}

# Function to find all .mobileprovision files
find_provisioning_profiles() {
    local profile_count=0
    
    if [ -d "$CERTIFICATES_FOLDER" ]; then
        profile_count=$(find "$CERTIFICATES_FOLDER" -name "*.mobileprovision" -type f 2>/dev/null | wc -l | tr -d ' ')
        print_message $BLUE "Found $profile_count .mobileprovision file(s) in $CERTIFICATES_FOLDER"
    else
        print_message $RED "✗ Certificates folder not found: $CERTIFICATES_FOLDER"
        return 1
    fi
    
    if [ "$profile_count" -eq 0 ]; then
        print_message $YELLOW "No .mobileprovision files found in $CERTIFICATES_FOLDER"
        return 1
    fi
    
    return 0
}

# Function to install provisioning profile
install_provisioning_profile() {
    local profile_path=$1
    local profile_filename=$(basename "$profile_path")
    
    print_message $YELLOW "Installing provisioning profile: $profile_filename"
    
    # Create provisioning profiles directory if it doesn't exist
    if [ ! -d "$PROVISIONING_PROFILES_DIR" ]; then
        print_message $YELLOW "Creating provisioning profiles directory..."
        mkdir -p "$PROVISIONING_PROFILES_DIR"
        if [ $? -eq 0 ]; then
            print_message $GREEN "✓ Provisioning profiles directory created"
        else
            print_message $RED "✗ Failed to create provisioning profiles directory"
            return 1
        fi
    fi
    
    # Copy provisioning profile
    cp -f "$profile_path" "$PROVISIONING_PROFILES_DIR/"
    
    if [ $? -eq 0 ]; then
        print_message $GREEN "✓ Provisioning profile installed successfully: $profile_filename"
        return 0
    else
        print_message $RED "✗ Failed to install provisioning profile: $profile_filename"
        return 1
    fi
}

# Function to install all provisioning profiles
install_all_provisioning_profiles() {
    print_message $GREEN "=== Installing Provisioning Profiles ==="
    echo ""
    
    # Check if there are any provisioning profiles to install
    if ! find_provisioning_profiles; then
        print_message $YELLOW "Skipping provisioning profile installation"
        return 0
    fi
    
    echo ""
    
    local success_count=0
    local fail_count=0
    
    # Process each .mobileprovision file
    while IFS= read -r profile_path; do
        echo ""
        profile_filename=$(basename "$profile_path")
        
        # Always install/overwrite provisioning profile
        install_provisioning_profile "$profile_path"
        if [ $? -eq 0 ]; then
            ((success_count++))
        else
            ((fail_count++))
        fi
    done < <(find "$CERTIFICATES_FOLDER" -name "*.mobileprovision" -type f 2>/dev/null | sort)
    
    # Summary
    echo ""
    print_message $GREEN "=== Provisioning Profile Installation Summary ==="
    print_message $GREEN "✓ Successful installations: $success_count"
    if [ $fail_count -gt 0 ]; then
        print_message $RED "✗ Failed installations: $fail_count"
    fi
    
    # List all provisioning profiles in the destination directory
    if [ -d "$PROVISIONING_PROFILES_DIR" ]; then
        echo ""
        print_message $YELLOW "=== Provisioning profiles in destination directory ==="
        local profile_count=$(find "$PROVISIONING_PROFILES_DIR" -name "*.mobileprovision" -type f 2>/dev/null | wc -l | tr -d ' ')
        print_message $BLUE "Total provisioning profiles: $profile_count"
        # find "$PROVISIONING_PROFILES_DIR" -name "*.mobileprovision" -type f 2>/dev/null | sort | while read -r profile; do
        #     print_message $GREEN "  • $(basename "$profile")"
        # done
    fi
    
    return $fail_count
}

# Main execution
main() {
    print_message $GREEN "=== Certificate Keychain Installation Script ==="
    echo ""
    
    # Check if running on macOS
    if [[ "$OSTYPE" != "darwin"* ]]; then
        print_message $RED "✗ This script is designed for macOS only"
        exit 1
    fi

    # Check for certificates folder
    if ! load_certificate_config; then
        exit 1
    fi

    # Check for certificates folder
    if ! find_certificates; then
        exit 1
    fi
    
    echo ""
    
    # Check if keychain exists
    if keychain_exists; then
        print_message $GREEN "✓ Keychain '$KEYCHAIN_NAME' already exists"
        unlock_keychain
    else
        print_message $YELLOW "Keychain '$KEYCHAIN_NAME' not found"
        create_keychain
        if [ $? -ne 0 ]; then
            print_message $RED "✗ Failed to create keychain. Exiting."
            exit 1
        fi
    fi
    
    echo ""
    
    # Install all .p12 certificates found in the folder
    local cert_success_count=0
    local cert_fail_count=0
    local cert_skip_count=0
    
    # Process each .p12 file
    while IFS= read -r cert_path; do
        echo ""
        cert_filename=$(basename "$cert_path")
        
        # Check if certificate already exists in keychain
        if security find-certificate -c "${cert_filename%.p12}" "$KEYCHAIN_NAME.keychain-db" &>/dev/null; then
            print_message $BLUE "⊝ Certificate already in keychain: $cert_filename (skipping)"
            ((cert_skip_count++))
        else
            install_certificate "$cert_path"
            if [ $? -eq 0 ]; then
                ((cert_success_count++))
            else
                ((cert_fail_count++))
            fi
        fi
    done < <(find "$CERTIFICATES_FOLDER" -name "*.p12" -type f 2>/dev/null | sort)
    
    # Certificate Summary
    echo ""
    print_message $GREEN "=== Certificate Installation Summary ==="
    print_message $GREEN "✓ Successful installations: $cert_success_count"
    if [ $cert_skip_count -gt 0 ]; then
        print_message $BLUE "⊝ Skipped (already installed): $cert_skip_count"
    fi
    if [ $cert_fail_count -gt 0 ]; then
        print_message $RED "✗ Failed installations: $cert_fail_count"
    fi
    
    # List all certificates in the keychain
    echo ""
    print_message $YELLOW "=== Certificates in $KEYCHAIN_NAME keychain ==="
    security find-certificate -a "$KEYCHAIN_NAME.keychain-db" 2>/dev/null | grep "labl" | cut -d '"' -f 4 | while read -r cert_name; do
        print_message $GREEN "  • $cert_name"
    done
    
    # Install provisioning profiles
    echo ""
    install_all_provisioning_profiles
    local profile_fail_count=$?
    
    # Provide example for missing passwords
    if [ $cert_fail_count -gt 0 ]; then
        echo ""
        print_message $YELLOW "To add passwords for failed certificates, update the CERT_PASSWORDS array:"
        print_message $YELLOW 'CERT_PASSWORDS=('
        print_message $YELLOW '    "certificate_name.p12:password"'
        print_message $YELLOW ')'
    fi
    
    # Store total fail count for exit code
    fail_count=$((cert_fail_count + profile_fail_count))
}

# Run main function
main

# Exit with appropriate code
if [ ${fail_count:-0} -gt 0 ]; then
    exit 1
else
    exit 0
fi