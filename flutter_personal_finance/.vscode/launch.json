{"version": "0.2.0", "configurations": [{"name": "DEVELOPMENT (debug)", "request": "launch", "type": "dart", "flutterMode": "debug", "program": "lib/main_dev.dart", "args": ["--flavor", "development"]}, {"name": "DEVELOPMENT (release)", "request": "launch", "type": "dart", "flutterMode": "release", "program": "lib/main_dev.dart", "args": ["--flavor", "development"]}, {"name": "STAGING (debug)", "request": "launch", "type": "dart", "flutterMode": "debug", "program": "lib/main_staging.dart", "args": ["--flavor", "staging"]}, {"name": "STAGING (release)", "request": "launch", "type": "dart", "flutterMode": "release", "program": "lib/main_staging.dart", "args": ["--flavor", "staging"]}, {"name": "PRODUCTION (debug)", "request": "launch", "type": "dart", "flutterMode": "debug", "program": "lib/main_production.dart", "args": ["--flavor", "production"]}, {"name": "PRODUCTION (release)", "request": "launch", "type": "dart", "flutterMode": "release", "program": "lib/main_production.dart", "args": ["--flavor", "production"]}, {"name": "ENTERPRISE (debug)", "request": "launch", "type": "dart", "flutterMode": "debug", "program": "lib/main_enterprise.dart", "args": ["--flavor", "enterprise"]}, {"name": "ENTERPRISE (release)", "request": "launch", "type": "dart", "flutterMode": "release", "program": "lib/main_enterprise.dart", "args": ["--flavor", "enterprise"]}]}