{"Home": "Home", "Google Sign in": "Google Sign in", "Sign In": "Sign In", "Begin by entering your email address.": "Begin by entering your email address.", "By clicking, I accept the ": "By clicking, I accept the ", "OTP Verification": "OTP Verification", "Reusable Button Code Disable": "Reusable Button Code Disable", "Resend": "Resend", "Continue with Google": "Continue with Google", "Continue with Twiltter": "Continue with Twitter", "Sign in with Email": "Sign in with <PERSON><PERSON>", "splash_preparing_journey": "Preparing your financial journey...", "splash_secure_access": "Secure access to your financial future...", "splash_secure_path": "Let's secure your path to financial success...", "splash_checking_connectivity": "Checking connectivity...", "splash_checking_updates": "Checking for updates...", "splash_no_internet_offline": "No internet, proceeding offline...", "splash_app_title": "Financial Freedom", "splash_subtitle": "Your journey to financial success starts here", "signup_welcome_title": "Welcome to Personal Finance", "signup_secure_biometric": "Secure your path to financial success with biometric authentication", "signup_secure_pin": "Secure your path to financial success with PIN authentication", "signup_setting_up": "Setting up security...", "signup_setup_complete": "Setup Complete!", "signup_protect_biometric": "Protect your wealth with secure biometric access", "signup_protect_pin": "Protect your wealth with secure PIN access", "signup_enable_biometric": "Enable Biometric", "signup_set_pin": "Set Up PIN", "signup_skip": "Skip for Now", "signup_biometric_unavailable": "Biometric authentication is not available on this device or no biometrics are enrolled.", "signup_biometric_success": "Biometric authentication has been successfully enabled!", "signup_biometric_failed": "Biometric authentication setup failed. Please try again.", "signup_setup_error": "An error occurred during setup. Let's try securing your account again.", "signup_pin_success": "PIN authentication has been successfully enabled!", "signup_pin_failed": "PIN authentication setup failed. Please try again.", "signup_pin_error": "An error occurred during PIN setup. Please try again.", "login_app_title": "Personal Finance", "login_secure_access": "Secure access with face authentication", "login_authenticating": "Authenticating...", "login_face_instruction": "Use Face ID to access your account", "login_authenticate_face": "Authenticate with Face ID", "login_setup_face": "Setup Face <PERSON>ntication", "login_auth_failed": "Face authentication failed. Please try again.", "login_not_setup": "Face authentication is not set up. Please enable it first.", "login_not_available": "Face authentication is not available or failed.", "login_setup_failed": "Face authentication setup failed. Please try again.", "login_not_supported": "Face authentication is not supported on this device.", "auth_initializing": "Initializing Security...", "auth_authenticate_continue": "Authenticate to Con<PERSON>ue", "auth_biometric_instruction": "Use {biometrics} to access your financial data", "auth_auth_required": "Authentication required to access your financial data", "auth_authenticate": "Authenticate", "auth_skip": "<PERSON><PERSON> Au<PERSON>nti<PERSON>", "auth_biometric_failed": "Biometric authentication failed.", "auth_biometric_unavailable": "Biometric authentication unavailable.", "auth_checking": "Checking authentication...", "auth_setup_security": "Setup Security", "auth_setup_content": "To secure your financial data, please set up authentication.", "auth_skip_btn": "<PERSON><PERSON>", "auth_setup_btn": "Setup", "auth_required_title": "Authentication Required", "auth_required_subtitle": "Please authenticate to continue", "security_settings_title": "Security Settings", "security_current_status": "Current Status", "security_auth_enabled": "Authentication is enabled using {method}", "security_auth_disabled": "Authentication is disabled", "security_app_security": "App Security", "security_enable_auth": "Enable Authentication", "security_secure_biometric": "Secure your app with {biometrics}", "security_secure_system": "Secure your app with system PIN/Password", "security_how_works": "How it works", "security_biometric_auth": "Biometric Authentication", "security_biometric_desc": "Your device supports {biometrics}. This will be used for app authentication.", "security_system_integration": "System Integration", "security_system_desc": "Uses your device's built-in security features. Will use biometric if available, otherwise your device PIN/password.", "security_privacy_first": "Privacy First", "security_privacy_desc": "All authentication is handled by your device. We don't store any biometric data.", "security_enabled_with": "Authentication enabled with {method}", "security_enabled_system": "Authentication enabled with system authentication", "security_disabled": "Authentication disabled", "security_setup_failed": "Authentication setup failed", "security_update_error": "Error updating authentication settings", "bank_add_title": "Add Bank Account", "bank_edit_title": "Edit Bank Account", "bank_add_info": "Add your bank account with initial balance to start tracking transactions and manage your finances.", "bank_edit_info": "Edit your bank account details and update the initial balance.", "bank_name_label": "Bank Name", "bank_name_hint": "Enter bank name (e.g., SBI, HDFC, ICICI)", "bank_account_number_label": "Account Number", "bank_account_number_hint": "Enter account number", "bank_initial_balance_label": "Initial Balance", "bank_initial_balance_hint": "Enter initial account balance", "bank_name_required": "Please enter bank name", "bank_name_min_length": "Bank name must be at least 2 characters", "bank_account_required": "Please enter account number", "bank_account_min_length": "Account number must be at least 8 digits", "bank_balance_required": "Please enter initial balance", "bank_balance_invalid": "Please enter a valid amount", "bank_balance_negative": "Balance cannot be negative", "bank_add_failed": "Failed to add bank account. Please try again.", "bank_update_failed": "Failed to update bank account. Please try again.", "bank_success_title": "Success", "bank_add_success": "Bank account added successfully!", "bank_update_success": "Bank account updated successfully!", "bank_ok": "OK", "bank_add_button": "Add Bank Account", "bank_update_button": "Update Bank Account", "budget_title": "Budget Planner", "budget_select_category": "Select Category", "budget_income": "Income", "budget_expense": "Expense", "budget_cancel": "Cancel", "budget_set_limit_title": "Set Monthly Limit", "budget_update_limit_title": "Update Monthly Limit", "budget_category_prefix": "Category:", "budget_monthly_limit_label": "Monthly Limit", "budget_set_limit": "<PERSON>", "budget_update": "Update", "budget_delete_title": "Delete Budget", "budget_delete_confirm": "Are you sure you want to delete the budget for {category}?", "budget_delete": "Delete", "budget_add_new": "Add New Budget", "budget_no_budgets_title": "No Budgets Yet", "budget_no_budgets_desc": "Start managing your finances by creating your first budget. Set spending limits for different categories and track your progress.", "budget_create_first": "Create First Budget", "budget_spent": "Spent", "budget_budget": "Budget", "budget_exceeded": "Exceeded", "budget_remaining": "₹{amount} remaining", "budget_type_income": "INCOME BUDGET", "budget_type_expense": "EXPENSE BUDGET", "budget_no_categories": "Please add income/expense categories in Profile first", "budget_already_exists": "Budget already exists for this category", "budget_created_success": "Budget created successfully", "budget_updated_success": "Budget updated successfully", "budget_deleted_success": "Budget deleted successfully", "budget_invalid_amount": "Please enter a valid amount", "budget_delete_error": "Error deleting budget", "investment_title": "Investment", "investment_view_examples": "View Examples", "investment_add_investment": "Add Investment", "investment_portfolio_overview": "Portfolio Overview", "investment_total_portfolio_value": "Total Portfolio Value", "investment_total_invested": "Total Invested", "investment_total_returns": "Total Returns", "investment_return_percent": "Return %", "investment_no_investments_title": "No Investments Yet", "investment_no_investments_desc": "Start building your investment portfolio by adding your first investment", "investment_add_first": "Add First Investment", "investment_current_value": "Current Value", "investment_invested": "Invested", "investment_profit_loss": "Profit & Loss", "investment_error_loading": "Error loading investments: {error}", "investment_add_title": "Add Investment", "investment_edit_title": "Edit Investment", "investment_save": "Save", "investment_update": "Update", "investment_basic_info": "Basic Information", "investment_name_label": "Investment Name", "investment_name_hint": "e.g., HDFC Mutual Fund", "investment_name_required": "Please enter investment name", "investment_category_label": "Category", "investment_category_required": "Please select a category", "investment_description_label": "Description", "investment_description_hint": "Optional description", "investment_initial_amount_label": "Initial Amount", "investment_initial_amount_hint": "Enter current invested amount (e.g., ₹10,000.50)", "investment_initial_amount_helper": "Enter total amount invested till today", "investment_initial_amount_required": "Please enter initial amount", "investment_amount_invalid": "Please enter a valid amount (e.g., 10000.50)", "investment_return_details": "Return Details", "investment_return_type_label": "Return Type", "investment_return_auto_calc": "Expected returns will be calculated automatically and shown in dashboard", "investment_return_rate_label": "Expected Return Rate (%)", "investment_return_rate_hint": "e.g., 12.5, 8.75, 15", "investment_return_rate_required": "Please enter expected return rate", "investment_return_rate_invalid": "Please enter a valid rate (e.g., 12.5)", "investment_return_rate_high": "Return rate seems too high. Please check.", "investment_return_frequency_label": "Return Frequency", "investment_dates": "Investment Dates", "investment_start_date": "Start Date", "investment_has_maturity": "Has Maturity Date", "investment_maturity_date": "Maturity Date", "investment_select_maturity": "Select maturity date", "investment_advanced_options": "Advanced Options", "investment_status_label": "Status", "investment_simple_interest": "Simple Interest", "investment_compound_interest": "Compound Interest", "investment_exact_amount": "Exact Amount", "investment_manual_entry": "Manual Entry", "investment_monthly": "Monthly", "investment_quarterly": "Quarterly", "investment_half_yearly": "Half Yearly", "investment_yearly": "Yearly", "investment_active": "Active", "investment_matured": "Matured", "investment_withdrawn": "Withdrawn", "investment_paused": "Paused", "investment_select_maturity_error": "Please select maturity date", "investment_updated_success": "Investment updated successfully", "investment_added_success": "Investment added successfully", "investment_save_error": "Error saving investment: {error}", "investment_detail_error_loading": "Error loading investment data: {error}", "investment_detail_edit": "Edit", "investment_detail_withdraw": "Withdraw", "investment_detail_delete": "Delete", "investment_detail_tab_overview": "Overview", "investment_detail_tab_entries": "Entries", "investment_detail_tab_withdrawals": "<PERSON><PERSON><PERSON><PERSON>", "investment_detail_current_value": "Current Value", "investment_detail_invested": "Invested", "investment_detail_expected": "Expected", "investment_detail_status": "Status", "investment_detail_investment_details": "Investment Details", "investment_detail_category": "Category", "investment_detail_description": "Description", "investment_detail_return_type": "Return Type", "investment_detail_expected_return": "Expected Return", "investment_detail_start_date": "Start Date", "investment_detail_maturity_date": "Maturity Date", "investment_detail_projected_maturity": "Projected Value at Maturity", "investment_detail_financial_summary": "Financial Summary", "investment_detail_initial_amount": "Initial Amount", "investment_detail_total_invested": "Total Invested", "investment_detail_total_entries": "Total Entries", "investment_detail_total_withdrawals": "Total Withdrawals", "investment_detail_withdrawal_summary": "<PERSON><PERSON><PERSON>", "investment_detail_total_withdrawn": "Total Withdrawn", "investment_detail_transactions": "Transactions", "investment_detail_average_amount": "Average Amount", "investment_detail_no_entries_title": "No Investment Entries", "investment_detail_no_entries_desc": "Investment entries will appear here when you make transactions in investment categories", "investment_detail_no_withdrawals_title": "No Withdrawals", "investment_detail_no_withdrawals_desc": "Withdrawal history will appear here when you withdraw from this investment", "investment_detail_delete_title": "Delete Investment", "investment_detail_delete_confirm": "Are you sure you want to delete \"{name}\"? This action cannot be undone.", "investment_detail_cancel": "Cancel", "investment_detail_deleted_success": "Investment deleted successfully", "investment_detail_delete_error": "Error deleting investment: {error}", "investment_withdrawal_title": "Withdraw Investment", "investment_withdrawal_current_value": "Current Value", "investment_withdrawal_total_invested": "Total Invested", "investment_withdrawal_profit_loss": "Profit/Loss", "investment_withdrawal_type": "Withdrawal Type", "investment_withdrawal_percentage_label": "Percentage to Withdraw", "investment_withdrawal_amount_label": "Amount to Withdraw", "investment_withdrawal_percentage_hint": "Percentage (%)", "investment_withdrawal_amount_hint": "Amount (₹)", "investment_withdrawal_percentage_example": "e.g., 25", "investment_withdrawal_amount_example": "e.g., 10000", "investment_withdrawal_enter_percentage": "Please enter percentage", "investment_withdrawal_enter_amount": "Please enter amount", "investment_withdrawal_valid_percentage": "Please enter a valid percentage", "investment_withdrawal_valid_amount": "Please enter a valid amount", "investment_withdrawal_amount_exceed_percentage": "Amount cannot exceed 100%", "investment_withdrawal_amount_exceed_amount": "Amount cannot exceed ₹{amount}", "investment_withdrawal_max_percentage": "Maximum: 100%", "investment_withdrawal_max_amount": "Maximum: ₹{amount}", "investment_withdrawal_reason_title": "Reason for Withdrawal (Optional)", "investment_withdrawal_reason_label": "Reason", "investment_withdrawal_reason_hint": "e.g., Emergency, Home purchase, etc.", "investment_withdrawal_calculate": "<PERSON><PERSON>", "investment_withdrawal_calculation": "Withdrawal Calculation", "investment_withdrawal_amount": "<PERSON><PERSON><PERSON> Amount", "investment_withdrawal_penalty": "Early Withdrawal Penalty", "investment_withdrawal_net_amount": "Net Amount You'll Receive", "investment_withdrawal_remaining_value": "Remaining Investment Value", "investment_withdrawal_close_investment": "This will close your investment completely", "investment_withdrawal_confirm_title": "Confirm <PERSON>", "investment_withdrawal_important_notes": "Important Notes", "investment_withdrawal_notes": "• Withdrawals cannot be reversed once processed\n• Early withdrawal penalties may apply\n• Consider tax implications on gains\n• Remaining amount will continue to grow", "investment_withdrawal_confirm_action": "Confirm <PERSON>", "investment_withdrawal_confirm_question": "Are you sure you want to withdraw ₹{amount}?", "investment_withdrawal_cannot_undone": "This action cannot be undone", "investment_withdrawal_confirm_button": "Confirm", "investment_withdrawal_success": "Withdrawal processed successfully", "investment_withdrawal_error_calculating": "Error calculating withdrawal: {error}", "investment_withdrawal_error_processing": "Error processing withdrawal: {error}", "investment_withdrawal_specific_amount": "Specific Amount", "investment_withdrawal_percentage": "Percentage", "investment_withdrawal_profits_only": "Profits Only", "investment_withdrawal_principal_only": "Principal Only", "investment_withdrawal_specific_desc": "Withdraw a specific rupee amount", "investment_withdrawal_percentage_desc": "Withdraw a percentage of total value", "investment_withdrawal_profits_desc": "Withdraw only the gains/profits", "investment_withdrawal_principal_desc": "Withdraw only the original investment", "investment_guide_title": "Investment Guide", "investment_guide_scenario1_title": "📊 Scenario 1: Track Existing Investment", "investment_guide_scenario1_desc": "I have ₹50,000 already invested in FD earning 7% yearly interest", "investment_guide_scenario1_step1": "✅ Go to Investment Dashboard → Add Investment", "investment_guide_scenario1_step2": "✅ Name: \"HDFC Fixed Deposit\"", "investment_guide_scenario1_step3": "✅ Category: \"Fixed Deposit\"", "investment_guide_scenario1_step4": "✅ Initial Amount: ₹50,000 (current invested amount)", "investment_guide_scenario1_step5": "✅ Return Type: Simple Interest", "investment_guide_scenario1_step6": "✅ Expected Return: 7% Yearly", "investment_guide_scenario1_step7": "✅ Set maturity date if applicable", "investment_guide_scenario1_result": "System calculates: Current value grows automatically based on time elapsed!", "investment_guide_scenario2_title": "💰 Scenario 2: Ongoing SIP Investment", "investment_guide_scenario2_desc": "I invested ₹1,38,000 in Mutual Fund SIP, continuing ₹5,000/month", "investment_guide_scenario2_step1": "✅ Mark \"SIP\" category as Investment in Categories screen", "investment_guide_scenario2_step2": "✅ Add Investment: Name \"Equity Mutual Fund\"", "investment_guide_scenario2_step3": "✅ Initial Amount: ₹1,38,000 (total invested till today)", "investment_guide_scenario2_step4": "✅ Return Type: Compound Interest", "investment_guide_scenario2_step5": "✅ Expected Return: 12% Yearly", "investment_guide_scenario2_step6": "✅ When you add ₹5,000 expense in \"SIP\" category →", "investment_guide_scenario2_step7": "✅ System automatically adds it to this investment!", "investment_guide_scenario2_result": "Auto-tracking: Every SIP transaction increases your investment value!", "investment_guide_scenario3_title": "🏠 Scenario 3: Property with Known Value", "investment_guide_scenario3_desc": "I bought property for ₹25,00,000, now worth ₹30,00,000", "investment_guide_scenario3_step1": "✅ Name: \"Residential Property Mumbai\"", "investment_guide_scenario3_step2": "✅ Category: \"Real Estate\"", "investment_guide_scenario3_step3": "✅ Initial Amount: ₹30,00,000 (current market value)", "investment_guide_scenario3_step4": "✅ Return Type: Manual Entry", "investment_guide_scenario3_step5": "✅ Update value manually when market changes", "investment_guide_scenario3_step6": "✅ Track appreciation over time", "investment_guide_scenario3_result": "Perfect for assets with irregular value changes!", "investment_guide_scenario4_title": "💸 Scenario 4: Investment with Withdrawals", "investment_guide_scenario4_desc": "I have ₹2,00,000 in investment, need to withdraw ₹50,000", "investment_guide_scenario4_step1": "✅ Go to Investment Detail → 3-dot menu → Withdraw", "investment_guide_scenario4_step2": "✅ Choose withdrawal type:", "investment_guide_scenario4_step3": "  • Specific Amount: ₹50,000", "investment_guide_scenario4_step4": "  • Percentage: 25% of total value", "investment_guide_scenario4_step5": "✅ System calculates penalty (if applicable)", "investment_guide_scenario4_step6": "✅ Shows net amount you'll receive after calculations", "investment_guide_scenario4_step7": "✅ Remaining investment value clearly displayed", "investment_guide_scenario4_result": "Smart withdrawal: System automatically handles profit vs principal allocation!", "investment_guide_complete_title": "Complete Investment Management", "investment_guide_complete_desc": "Track all your investments with automatic calculations, smart withdrawals, and seamless transaction linking. From SIPs to FDs to Real Estate!", "investment_guide_workflow_title": "Complete Workflow", "investment_guide_workflow_step1_num": "1", "investment_guide_workflow_step1_title": "Setup Categories", "investment_guide_workflow_step1_desc": "Mark categories as \"Investment\" in Income/Expense Categories", "investment_guide_workflow_step2_num": "2", "investment_guide_workflow_step2_title": "Add Investment", "investment_guide_workflow_step2_desc": "Create investment with proper return type and initial amount", "investment_guide_workflow_step3_num": "3", "investment_guide_workflow_step3_title": "Auto-Tracking", "investment_guide_workflow_step3_desc": "System links expense transactions to investments automatically", "investment_guide_workflow_step4_num": "4", "investment_guide_workflow_step4_title": "Monitor Growth", "investment_guide_workflow_step4_desc": "View real-time calculations and projections in dashboard", "investment_guide_workflow_step5_num": "5", "investment_guide_workflow_step5_title": "Smart Withdrawals", "investment_guide_workflow_step5_desc": "Withdraw specific amounts or percentages with automatic calculations", "investment_guide_withdrawal_options_title": "Available Withdrawal Options", "investment_guide_withdrawal_specific_title": "Specific Amount", "investment_guide_withdrawal_specific_desc": "Withdraw exact ₹ amount you need", "investment_guide_withdrawal_specific_example": "Need ₹50,000 for emergency expenses", "investment_guide_withdrawal_percentage_title": "Percentage", "investment_guide_withdrawal_percentage_desc": "Withdraw % of total investment value", "investment_guide_withdrawal_percentage_example": "Take out 25% of total investment portfolio", "investment_guide_pro_tips_title": "Pro Tips", "investment_guide_tip1_icon": "🎯", "investment_guide_tip1_text": "Use \"Initial Amount\" for total invested till today, not original amount", "investment_guide_tip2_icon": "🔗", "investment_guide_tip2_text": "Mark expense categories as \"Investment\" for auto-tracking", "investment_guide_tip3_icon": "⚠️", "investment_guide_tip3_text": "Set maturity dates to track investment duration and goals", "investment_guide_tip4_icon": "📈", "investment_guide_tip4_text": "Compound Interest for SIPs/Stocks, Simple Interest for FDs/Bonds", "investment_guide_tip5_icon": "💰", "investment_guide_tip5_text": "Withdrawals automatically handle profit vs principal allocation intelligently", "investment_guide_tip6_icon": "📊", "investment_guide_tip6_text": "View detailed analytics in the Withdrawals tab", "investment_guide_return_types_title": "Return Types Explained", "investment_guide_simple_interest_title": "Simple Interest", "investment_guide_simple_interest_desc": "Fixed returns (FD, Bonds, Government Securities)", "investment_guide_simple_interest_formula": "P + (P × R × T) - Fixed growth rate", "investment_guide_compound_interest_title": "Compound Interest", "investment_guide_compound_interest_desc": "Compounding growth (SIP, Mutual Funds, Stocks)", "investment_guide_compound_interest_formula": "P × (1 + R)^T - Growth on growth", "investment_guide_exact_amount_title": "Exact Amount", "investment_guide_exact_amount_desc": "Known maturity value (PPF, NSC, Insurance)", "investment_guide_exact_amount_formula": "Enter final amount - System calculates returns", "investment_guide_manual_entry_title": "Manual Entry", "investment_guide_manual_entry_desc": "Complex or irregular instruments (Real Estate, Gold)", "investment_guide_manual_entry_formula": "Update values manually as needed", "credit_card_title": "Credit Cards", "credit_card_add_card": "Add Card", "credit_card_loading": "Loading credit cards...", "credit_card_load_error": "Failed to load credit cards", "credit_card_error_title": "Error Loading Cards", "credit_card_retry": "Retry", "credit_card_no_cards_title": "No Credit Cards", "credit_card_no_cards_desc": "Add your credit cards to track your debt and manage payments effectively.", "credit_card_add_first": "Add Your First Credit Card", "credit_card_delete_title": "Delete Credit Card", "credit_card_delete_confirm": "Are you sure you want to delete \"{cardName}\"?\n\nThis will also delete all associated transactions. This action cannot be undone.", "credit_card_cancel": "Cancel", "credit_card_delete": "Delete", "credit_card_deleted_success": "Credit card \"{cardName}\" deleted successfully", "credit_card_delete_error": "Failed to delete credit card", "credit_card_updated_success": "Credit card updated successfully", "credit_card_closing": "Statement", "credit_card_due_date": "Due Date", "credit_card_outstanding_balance": "Outstanding Balance", "credit_card_due_soon": "DUE SOON", "credit_card_add_transaction": "Add Transaction", "credit_card_edit_card": "Edit Card", "credit_card_delete_card": "Delete Card", "credit_card_add_title": "Add Credit Card", "credit_card_new_card": "New Credit Card", "credit_card_add_desc": "Add your credit card for debt tracking", "credit_card_card_info": "Card Information", "credit_card_name_label": "Card Name", "credit_card_name_hint": "e.g., My Visa Card, Shopping Card", "credit_card_name_required": "Please enter card name", "credit_card_digits_label": "Last 4 Digits", "credit_card_digits_hint": "Enter last 4 digits", "credit_card_digits_required": "Please enter last 4 digits", "credit_card_digits_invalid": "Please enter exactly 4 digits", "credit_card_bank_label": "Bank Name", "credit_card_bank_hint": "e.g., HDFC Bank, SBI, ICICI", "credit_card_bank_required": "Please enter bank name", "credit_card_card_type": "Card Type", "credit_card_financial_info": "Financial Information", "credit_card_limit_label": "Credit Limit", "credit_card_limit_hint": "Enter credit limit amount", "credit_card_limit_required": "Please enter credit limit", "credit_card_amount_invalid": "Please enter a valid amount", "credit_card_balance_label": "Current Outstanding Balance", "credit_card_balance_hint": "Enter current debt amount (0 if new card)", "credit_card_balance_required": "Please enter outstanding balance (0 if none)", "credit_card_balance_exceed_error": "Outstanding balance cannot exceed credit limit", "credit_card_important_dates": "Important Dates", "credit_card_due_date_label": "Payment Due Date", "credit_card_closing_date_label": "Statement Closing Date", "credit_card_add_button": "Add Credit Card", "credit_card_add_error": "Failed to add credit card. Please try again.", "credit_card_success_title": "Success", "credit_card_added_success": "Credit card added successfully!", "credit_card_ok": "OK", "credit_card_important_info": "Important Information", "credit_card_info_notes": "• Only last 4 digits are stored for security\n• Outstanding balance tracks your current debt\n• Due dates help you avoid late payments\n• All data is stored locally on your device", "credit_card_edit_title": "Edit Credit Card", "credit_card_edit_name_hint": "e.g., My HDFC Card", "credit_card_edit_number_label": "Card Number (Last 4 digits)", "credit_card_edit_number_hint": "xxxx", "credit_card_edit_limit_hint": "Enter credit limit", "credit_card_edit_balance_label": "Outstanding Balance", "credit_card_edit_balance_hint": "Enter outstanding balance", "credit_card_edit_balance_negative_error": "Outstanding balance cannot be negative", "credit_card_edit_limit_positive_error": "Credit limit must be greater than 0", "credit_card_edit_due_date_label": "Payment Due Date", "credit_card_edit_closing_date_label": "Statement Closing Date", "credit_card_update_button": "Update Credit Card", "credit_card_update_error": "Failed to update credit card. Please try again.", "credit_card_updated_title": "Credit Card Updated!", "credit_card_updated_desc": "Your credit card details have been updated successfully.", "credit_card_continue": "Continue", "credit_card_transaction_add_title": "Add Credit Card Transaction", "credit_card_transaction_loading": "Loading transactions...", "credit_card_transaction_no_cards_title": "No Credit Cards Found", "credit_card_transaction_no_cards_desc": "Add a credit card first to create transactions.", "credit_card_transaction_load_error": "Failed to load transactions: {error}", "credit_card_transaction_select_card_error": "Please select a credit card", "credit_card_transaction_select_bank_error": "Please select a bank account", "credit_card_transaction_bank_balance_error": "Payment exceeds bank balance", "credit_card_transaction_add_error": "Failed to add transaction", "credit_card_transaction_purchase": "Purchase", "credit_card_transaction_payment": "Payment", "credit_card_transaction_add_purchase": "Add Purchase", "credit_card_transaction_add_payment": "Add Payment", "credit_card_transaction_select_card": "Select Credit Card", "credit_card_transaction_outstanding": "Outstanding: ₹{amount}", "credit_card_transaction_select_bank": "Select Bank Account", "credit_card_transaction_account_number": "A/C: {accountNumber}", "credit_card_transaction_select_category": "Select Category", "credit_card_transaction_overpay_title": "Confirm Overpayment", "credit_card_transaction_overpay_desc": "This payment will result in an overpayment of ₹{amount}. Continue?", "credit_card_transaction_success_title": "Success", "credit_card_transaction_success_desc": "{transactionType} added successfully!", "credit_card_transaction_card_label": "Credit Card", "credit_card_transaction_select_card_hint": "Select credit card", "credit_card_transaction_bank_label": "Bank Account", "credit_card_transaction_select_bank_hint": "Select bank account", "credit_card_transaction_amount_label": "Amount", "credit_card_transaction_amount_hint": "Enter amount", "credit_card_transaction_amount_required": "Please enter amount", "credit_card_transaction_amount_valid_error": "Please enter valid amount", "credit_card_transaction_description_label": "Description", "credit_card_transaction_description_hint": "Enter description", "credit_card_transaction_description_required": "Please enter description", "credit_card_transaction_category_label": "Category", "credit_card_transaction_category_hint": "Credit Card Payment", "credit_card_transaction_category_select": "Select category", "credit_card_transaction_merchant_label": "Merchant (Optional)", "credit_card_transaction_merchant_hint": "e.g., Amazon, Zomato", "credit_card_transaction_date_label": "Date", "credit_card_category_food": "Food & Dining", "credit_card_category_shopping": "Shopping", "credit_card_category_entertainment": "Entertainment", "credit_card_category_transportation": "Transportation", "credit_card_category_bills": "Bills & Utilities", "credit_card_category_healthcare": "Healthcare", "credit_card_category_travel": "Travel", "credit_card_category_groceries": "Groceries", "credit_card_category_fuel": "Fuel", "credit_card_category_online": "Online Shopping", "credit_card_category_subscriptions": "Subscriptions", "credit_card_category_education": "Education", "credit_card_category_other": "Other", "credit_card_transaction_list_title": "Credit Card Transactions", "credit_card_transaction_list_swipe_hint": "Swipe left or tap delete icon to remove credit card transactions", "credit_card_transaction_search_hint": "Search transactions, categories, merchants...", "credit_card_transaction_filter_purchases": "Purchases", "credit_card_transaction_filter_payments": "Payments", "credit_card_transaction_date_range": "Date Range", "credit_card_transaction_clear_all": "Clear All", "credit_card_transaction_overview": "Transaction Overview", "credit_card_transaction_outstanding_balance": "Outstanding: ₹{amount}", "credit_card_transaction_credit_balance": "Credit Balance: ₹{amount}", "credit_card_transaction_loading_list": "Loading transactions...", "credit_card_transaction_load_error_title": "Error Loading Transactions", "credit_card_transaction_no_matching": "No Matching Transactions", "credit_card_transaction_no_transactions": "No Transactions Yet", "credit_card_transaction_no_matching_desc": "Try adjusting your search or filters", "credit_card_transaction_no_transactions_desc": "Start by adding your first credit card transaction", "credit_card_transaction_filter_title": "Filter Transactions", "credit_card_transaction_type_label": "Transaction Type", "credit_card_transaction_all_types": "All Types", "credit_card_transaction_all_cards": "All Credit Cards", "credit_card_transaction_select_date_range": "Select Date Range", "credit_card_transaction_sort_label": "Sort By", "credit_card_transaction_sort_date_desc": "Date (Newest First)", "credit_card_transaction_sort_date_asc": "Date (Oldest First)", "credit_card_transaction_sort_amount_desc": "Amount (Highest First)", "credit_card_transaction_sort_amount_asc": "Amount (Lowest First)", "credit_card_transaction_apply_filters": "Apply Filters", "credit_card_transaction_delete_title": "Delete Transaction", "credit_card_transaction_delete_confirm": "Are you sure you want to delete this credit card transaction?\n\n{category} - ₹{amount}\n{date}\nCard: {cardName}", "credit_card_transaction_deleted_success": "Credit card transaction deleted successfully", "credit_card_transaction_delete_error": "Failed to delete credit card transaction", "credit_card_transaction_unknown_card": "Unknown Card", "dashboard_tab_home": "Home", "dashboard_tab_credit_cards": "Credit Cards", "dashboard_tab_investment": "Investment", "dashboard_tab_profile": "Profile", "dashboard_personal_finance": "Personal Finance", "dashboard_add": "Add", "dashboard_month_income": "Month Income", "dashboard_month_expenses": "Month Expenses", "dashboard_upcoming_expenses": "Upcoming Expenses", "dashboard_bank_account_updated_successfully": "Bank account updated successfully", "dashboard_add_expense_categories_first": "Please add expense categories in Profile first", "dashboard_add_bank_accounts_first": "Please add bank accounts first", "dashboard_add_upcoming_expense": "Add Upcoming Expense", "dashboard_category": "Category", "dashboard_bank_account": "Bank Account", "dashboard_amount": "Amount", "dashboard_enter_amount": "Enter amount", "dashboard_due_date": "Due Date", "dashboard_upcoming_expense_added_successfully": "Upcoming expense added successfully", "dashboard_add_first_bank_account_message": "Add your first bank account to start tracking your finances", "dashboard_account_number": "Account: {accountNumber}", "dashboard_no_credit_cards_added": "No Credit Cards Added", "dashboard_add_credit_cards_message": "Add your credit cards to track debt and manage payments", "dashboard_add_first_credit_card": "Add Your First Credit Card", "dashboard_get_started_credit_card_tracking": "Get started with credit card tracking", "dashboard_card": "Card", "dashboard_cards": "Cards", "dashboard_process": "Process", "dashboard_active": "Active", "dashboard_due": "Due", "dashboard_monthly": "Monthly", "dashboard_weekly": "Weekly", "dashboard_yearly": "Yearly", "dashboard_all": "All", "dashboard_top_expense_categories": "Top Expense Categories", "dashboard_top_income_categories": "Top Income Categories", "dashboard_net": "Net", "dashboard_processed_1_recurring_transaction": "Processed 1 recurring transaction: {description}", "dashboard_processed_recurring_transactions": "Processed {count} recurring transactions", "dashboard_failed_to_process_recurring_transactions": "Failed to process {count} recurring transactions", "dashboard_please_select_category": "Please select a category", "dashboard_please_select_bank_account": "Please select a bank account", "dashboard_please_enter_amount": "Please enter amount", "dashboard_please_enter_valid_amount": "Please enter a valid amount", "dashboard_week": "Week {number}", "dashboard_navigation_error": "Navigation error: {error}", "dashboard_average": "Average", "dashboard_highest": "Highest", "dashboard_lowest": "Lowest", "dashboard_no_spending_data_available": "No spending data available", "dashboard_add_expenses_to_see_trends": "Add some expenses to see your spending trends", "dashboard_recent_recurring": "Recent Recurring", "dashboard_view_all_recurring_transactions": "View all {count} recurring transactions", "dashboard_last_processed": "Last processed: {date}", "dashboard_due_soon_count": "{count} Due Soon", "dashboard_entries": "Entries", "dashboard_recurring_transactions_due": "{count} recurring transaction(s) due", "dashboard_total_balance": "Total Balance", "dashboard_hide_balance": "Hide Balance", "dashboard_show_balance": "Show Balance", "dashboard_income": "Income", "dashboard_expenses": "Expenses", "dashboard_upcoming": "Upcoming", "dashboard_accounts": "Accounts", "dashboard_add_bank": "Add Bank", "dashboard_no_bank_accounts": "No Bank Accounts", "dashboard_add_first_bank": "Add your first bank account to start tracking your finances", "dashboard_account_updated": "Bank account updated successfully", "dashboard_recent_transactions": "Recent Transactions", "dashboard_view_all": "View All", "dashboard_unknown_bank": "Unknown Bank", "dashboard_credit": "Credit", "dashboard_debit": "Debit", "dashboard_add_bank_first": "Please add a bank account first", "dashboard_category_label": "Category", "dashboard_select_category": "Please select a category", "dashboard_bank_account_label": "Bank Account", "dashboard_select_bank_account": "Please select a bank account", "dashboard_amount_label": "Amount", "dashboard_amount_required": "Please enter amount", "dashboard_amount_invalid": "Please enter a valid amount", "dashboard_due_date_label": "Due Date", "dashboard_cancel": "Cancel", "dashboard_upcoming_expense_added": "Upcoming expense added successfully", "dashboard_category_food": "Food & Dining", "dashboard_category_entertainment": "Entertainment", "dashboard_category_utilities": "Utilities", "dashboard_category_transportation": "Transportation", "dashboard_category_healthcare": "Healthcare", "dashboard_category_shopping": "Shopping", "dashboard_category_travel": "Travel", "dashboard_category_bills": "Bills", "dashboard_category_education": "Education", "dashboard_category_rent": "Rent/Mortgage", "dashboard_category_insurance": "Insurance", "dashboard_category_loans": "Loans", "dashboard_category_investment": "Investment", "dashboard_category_other": "Other", "dashboard_credit_cards": "Credit Cards", "dashboard_credit_card_count": "{count} {count, plural, one{Card} other{Cards}}", "dashboard_no_credit_cards": "No Credit Cards Added", "dashboard_add_credit_cards_desc": "Add your credit cards to track debt and manage payments", "dashboard_credit_card_subtitle": "Get started with credit card tracking", "dashboard_total_debt": "Total Debt", "dashboard_total_limit": "Total Limit", "dashboard_overdue_cards": "{count} Overdue", "dashboard_due_soon_cards": "{count} Due Soon", "dashboard_cards_label": "Cards", "dashboard_entries_label": "Entries", "dashboard_add_new": "Add New", "dashboard_cash_flow": "Cash Flow Summary", "dashboard_cash_flow_placeholder": "Cash Flow Chart Placeholder", "dashboard_spending_trends": "Spending Trends", "dashboard_spending_trends_placeholder": "Spending Trends Chart Placeholder", "dashboard_top_categories": "Top Categories", "dashboard_top_categories_placeholder": "Top Categories Chart Placeholder", "dashboard_recurring_transactions": "Recurring Transactions", "dashboard_recurring_placeholder": "Recurring Transactions Placeholder", "credit_card_transaction_type_purchase": "Purchase", "credit_card_transaction_type_payment": "Payment", "credit_card_transaction_credit_card_label": "Credit Card", "credit_card_transaction_select_credit_card": "Select credit card", "credit_card_transaction_bank_account_label": "Bank Account", "credit_card_transaction_select_bank_account": "Select bank account", "credit_card_transaction_amount_invalid": "Please enter valid amount", "credit_card_transaction_select_category_label": "Select category", "credit_card_transaction_error_title": "Error Loading Transactions", "credit_card_transaction_retry": "Retry", "credit_card_transaction_adjust_filters": "Try adjusting your search or filters", "credit_card_transaction_add_first": "Start by adding your first credit card transaction", "credit_card_transaction_credit_card_filter": "Credit Card", "credit_card_transaction_sort_by": "Sort By", "credit_card_transaction_swipe_hint": "Swipe left or tap delete icon to remove credit card transactions", "credit_card_type_visa": "VISA", "credit_card_type_mastercard": "MASTERCARD", "credit_card_type_amex": "AMERICAN EXPRESS", "credit_card_type_rupay": "RUPAY", "credit_card_type_discover": "DISCOVER", "credit_card_type_other": "OTHER", "export_all_accounts": "All Accounts", "export_all_time": "All Time", "export_personal_finance": "Personal Finance", "export_financial_system": "Financial Management System", "export_statement": "Statement", "export_transaction_statement": "Transaction Statement", "export_generated_on": "Generated on {date}", "export_statement_details": "Statement Details", "export_account": "Account", "export_statement_period": "Statement Period", "export_total_transactions": "Total Transactions", "export_current_balance": "Current Balance", "export_income_transactions": "Income Transactions", "export_expense_transactions": "Expense Transactions", "export_date": "Date", "export_category": "Category", "export_amount": "Amount", "export_total_income": "Total Income", "export_total_expenses": "Total Expenses", "export_net_amount": "Net Amount (Income - Expenses):", "export_account_balances": "Account Balances", "export_total_balance": "Total Balance", "export_investment_summary": "Investment Summary", "export_no_investment": "No investment data found.", "export_category_label": "Category: {category}", "export_total_investment_value": "Total Investment Value", "export_auto_generated": "Report generated automatically", "export_currency_note": "All amounts in Indian Rupees (Rs.)", "export_signature_note": "This is a system-generated document and does not require a signature.", "export_transactions": "Export Transactions", "export_filter_options": "Filter Options", "export_bank_account": "Bank Account", "export_all_bank_accounts": "All Bank Accounts", "export_date_range": "Date Range", "export_preview": "Preview", "export_transaction_count": "{count} transactions", "export_no_transactions": "No transactions found", "export_adjust_filters": "Try adjusting your filter criteria", "export_income": "Income", "export_expenses": "Expenses", "export_investment_count": "{count} investments", "export_no_investments": "No investments found", "export_and_more": "... and {count} more", "app_settings": "App Settings", "language_settings": "Language Settings", "theme_settings": "Theme Settings", "select_app_language": "Select App Language", "light_theme_color": "☀️ Light Theme Primary Color", "dark_theme_color": "🌙 Dark Theme Primary Color", "preview_color": "Preview Color", "language_changed": "Language changed successfully!", "theme_color_updated": "Theme color updated instantly!", "theme_settings_saved": "Theme settings saved! Restart the app to see theme changes.", "all_settings_saved": "All settings saved successfully!", "settings_note": "Note: All changes are applied instantly. No restart required!", "save": "Save", "language_customization": "Language & theme customization", "calculator_title": "Investment Calculator", "calculator_tooltip": "Investment Calculator", "calculator_tab_sip": "SIP", "calculator_tab_lumpsum": "Lumpsum", "calculator_tab_hybrid": "SIP + Lumpsum", "calculator_tab_lumpsum_sip": "Lumpsum + SIP", "calculator_sip_title": "SIP Details", "calculator_sip_monthly_amount": "Monthly SIP Amount", "calculator_sip_period": "Investment Period", "calculator_sip_return_rate": "Expected Annual Return", "calculator_sip_calculate": "Calculate SIP", "calculator_sip_results": "SIP Results", "calculator_lumpsum_title": "Lumpsum Investment Details", "calculator_lumpsum_initial_amount": "Initial Investment Amount", "calculator_lumpsum_duration": "Investment Duration", "calculator_lumpsum_calculate": "Calculate <PERSON>um", "calculator_lumpsum_results": "Lumpsum Results", "calculator_hybrid_title": "SIP + Lumpsum Details", "calculator_hybrid_sip_amount": "Monthly SIP Amount", "calculator_hybrid_sip_period": "SIP Period", "calculator_hybrid_total_period": "Total Investment Period", "calculator_hybrid_calculate": "Calculate SIP + Lumpsum", "calculator_hybrid_results": "Hybrid Investment Results", "calculator_hybrid_info": "This calculator shows returns when you invest via SIP for a certain period, then stop SIP but let the fund continue growing as lumpsum.", "calculator_total_invested": "Total Invested", "calculator_total_sip_invested": "Total SIP Invested", "calculator_initial_investment": "Initial Investment", "calculator_maturity_amount": "Maturity Amount", "calculator_final_maturity_amount": "Final Maturity Amount", "calculator_value_at_sip_end": "Value at SIP End", "calculator_total_returns": "Total Returns", "calculator_years": "Years", "calculator_percent": "%", "calculator_error_monthly_amount": "Please enter monthly amount", "calculator_error_initial_amount": "Please enter initial amount", "calculator_error_sip_amount": "Please enter SIP amount", "calculator_error_period": "Please enter investment period", "calculator_error_duration": "Please enter investment duration", "calculator_error_sip_period": "Please enter SIP period", "calculator_error_total_period": "Please enter total period", "calculator_error_return_rate": "Please enter expected return rate", "calculator_error_valid_amount": "Please enter a valid amount", "calculator_error_valid_period": "Please enter a valid period", "calculator_error_valid_duration": "Please enter a valid duration", "calculator_error_valid_rate": "Please enter a valid return rate", "calculator_error_total_greater_sip": "Total period must be greater than SIP period", "calculator_lumpsum_sip_title": "Lumpsum + SIP Details", "calculator_lumpsum_sip_initial_amount": "Initial Lumpsum Amount", "calculator_lumpsum_sip_monthly_amount": "Monthly SIP Amount", "calculator_lumpsum_sip_start_year": "SIP Start Year", "calculator_lumpsum_sip_calculate": "Calculate Lumpsum + SIP", "calculator_lumpsum_sip_results": "Lumpsum + SIP Results", "calculator_lumpsum_sip_info": "This calculator shows returns when you start with a lumpsum investment and then begin SIP after some years.", "calculator_lumpsum_value_at_sip_start": "Lumpsum Value at SIP Start", "calculator_error_lumpsum_amount": "Please enter lumpsum amount", "calculator_error_sip_start_year": "Please enter SIP start year", "calculator_error_valid_year": "Please enter a valid year", "calculator_error_total_greater_start": "Total period must be greater than SIP start year", "calculator_export_pdf": "Export PDF Report", "calculator_generating_pdf": "Generating PDF...", "calculator_pdf_saved": "PDF saved successfully!", "calculator_pdf_shared": "PDF shared successfully!", "calculator_pdf_error": "Failed to generate PDF", "calculator_investment_report": "Investment Report", "calculator_pdf_save": "Save PDF", "calculator_pdf_share": "Share PDF", "calculator_pdf_print": "Print PDF", "profile_settings": "Profile & Settings", "language": "Language", "select_language": "Select your preferred language", "cancel": "Cancel", "language_changed_successfully": "Language changed successfully!", "error_changing_language": "Error changing language. Please try again.", "quick_actions": "Quick Actions", "advanced_analytics_title": "Advanced Analytics", "monthly_spending_trends": "Monthly Spending Trends", "no_spending_data_available": "No spending data available", "category_insights_last_30_days": "Category Insights (Last 30 Days)", "no_category_data_available": "No category data available", "uncategorized": "Uncategorized", "profile_bank_account_updated_successfully": "Bank account updated successfully", "profile_delete_bank_account": "Delete Bank Account", "profile_delete_bank_confirm": "Are you sure you want to delete {bankName}?", "profile_cancel": "Cancel", "profile_delete": "Delete", "profile_bank_account_deleted": "Bank account deleted", "profile_no_bank_accounts_found": "No bank accounts found", "profile_delete_transactions": "Delete Transactions", "profile_select_bank_and_date_range": "Select bank account and date range to delete transactions:", "profile_bank_account": "Bank Account", "profile_date_range": "Date Range", "profile_select_date_range": "Select Date Range", "profile_next": "Next", "profile_fetching_transactions": "Fetching transactions...", "profile_no_transactions_found_in_range": "No transactions found in the selected date range", "profile_confirm_deletion": "Confirm Deletion", "profile_found_transactions": "Found {count} transaction(s) in the selected range:", "profile_bank_label": "Bank: {bankName}", "profile_date_range_label": "Date Range: {startDate} - {endDate}", "profile_delete_warning": "This action cannot be undone. Are you sure you want to delete these transactions?", "profile_delete_transactions_count": "Delete {count} Transaction(s)", "profile_error_fetching_transactions": "Error fetching transactions: {error}", "profile_deleting_transactions": "Deleting {count} transaction(s)...", "profile_successfully_deleted_transactions": "Successfully deleted {count} transaction(s)", "profile_deleted_with_failures": "Deleted {deletedCount} transaction(s), {failedCount} failed", "profile_error_deleting_transactions": "Error deleting transactions: {error}", "profile_failed_to_export_data": "Failed to export data: {error}", "profile_export_data": "Export Data", "profile_choose_export_method": "Choose how you want to export your financial data:", "profile_copy_to_clipboard": "Copy to Clipboard", "profile_copy_json_subtitle": "Copy JSON data to clipboard", "profile_failed_to_copy_data": "Failed to copy data: {error}", "profile_text_auto_selected": "Text is auto-selected. Press Ctrl+C (Cmd+C on Mac) to copy all data", "profile_all_text_selected": "All text selected! Press Ctrl+C (Cmd+C) to copy", "profile_error_label": "Error: {error}", "profile_select_all": "Select All", "profile_close": "Close", "profile_failed_to_import_data": "Failed to import data: {error}", "profile_import_data": "Import Data", "profile_paste_json_data": "Paste your exported JSON data below:", "profile_paste_json_hint": "Paste your JSON data here...", "profile_replace_all_data_warning": "This will replace all existing data!", "profile_import": "Import", "profile_please_paste_json": "Please paste the JSON data to import", "profile_importing_all_data": "Importing all data...", "profile_failed_to_import_data_process": "Failed to import data: {error}", "profile_change_language": "Change Language", "profile_help_features": "Help & Features", "profile_account_management": "Account Management", "profile_bank_cash_accounts": "Bank & Cash Accounts", "profile_add": "Add", "profile_no_bank_accounts_yet": "No bank accounts added yet", "profile_add_first_bank_account": "Add your first bank account to get started", "profile_add_bank": "Add Bank", "profile_new_account": "New Account", "profile_categories": "Categories", "profile_manage": "Manage", "profile_budget": "Budget", "profile_planner": "Planner", "profile_analytics": "Analytics", "profile_insights": "Insights", "profile_upcoming_expense_categories": "Upcoming Expense Categories", "profile_manage_categories_future_expenses": "Manage categories for future expenses", "profile_advanced_tools": "Advanced Tools", "profile_upcoming_expenses": "Upcoming Expenses", "profile_view_manage_future_expenses": "View and manage future expenses", "profile_recurring_transactions": "Recurring Transactions", "profile_automatic_transaction_management": "Automatic transaction management", "profile_data_settings": "Data & Settings", "profile_export_data_action": "Export Data", "profile_backup": "Backup", "profile_import_data_action": "Import Data", "profile_restore": "Rest<PERSON>", "profile_export_to_pdf": "Export to PDF", "profile_generate_reports": "Generate reports", "profile_notifications": "Notifications", "profile_alerts_reminders": "Alerts and reminders", "profile_security_settings": "Security Settings", "profile_app_protection": "App protection", "profile_delete_transactions_title": "Delete Transactions", "profile_remove_transactions_by_date": "Remove transactions by date range", "profile_delete_action": "Delete", "profile_personal_finance_title": "💰 Personal Finance", "profile_track_budget_save_grow": "Track • Budget • Save • Grow", "profile_developed_by": "Developed by ", "profile_edit": "Edit", "profile_delete_menu": "Delete"}