<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Base.lproj/TOCropViewControllerLocalizable.strings</key>
		<data>
		VxY/Lu0kUMa4Dw9FryxLSegDAKU=
		</data>
		<key>PrivacyInfo.xcprivacy</key>
		<data>
		ucg9pita0v8d353x3NuGfxweQYU=
		</data>
		<key>ar.lproj/TOCropViewControllerLocalizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			9KdbZrTesV5Q3goWjT47ZNi4Ah8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ca.lproj/TOCropViewControllerLocalizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			iS8YNUQF8hURkq4RIeYmTpfmltw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>cs.lproj/TOCropViewControllerLocalizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			tEF0ckSDjUIGE3y/Foty8T/omwo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>da-DK.lproj/TOCropViewControllerLocalizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			xYOBVXX2ZEVsfhA06CU5RMhj9cc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>de.lproj/TOCropViewControllerLocalizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			uIWAB6jgWd/rBUaaZTOOTvqjnvg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>en.lproj/TOCropViewControllerLocalizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			VxY/Lu0kUMa4Dw9FryxLSegDAKU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>es.lproj/TOCropViewControllerLocalizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			i6heSnr/3BK2BzBGbDoAnsdAkCM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>fa-IR.lproj/TOCropViewControllerLocalizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Aciw6FK1RrwpKtmXlxt/av73MMw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>fa.lproj/TOCropViewControllerLocalizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Aciw6FK1RrwpKtmXlxt/av73MMw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>fi.lproj/TOCropViewControllerLocalizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			hLO/2lkTZNPuVT+0tigvvZKnTYU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>fr.lproj/TOCropViewControllerLocalizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			x58vjyvUriL2N8lGdL4nn2fjT8A=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>hu.lproj/TOCropViewControllerLocalizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			BZbs7bmjLwJNgzeQzQVDFtr41GI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>id.lproj/TOCropViewControllerLocalizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			6uxsz9U6fGxSTcjLOc8ILDETdVM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>it.lproj/TOCropViewControllerLocalizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			IZkFMVHUu5vtuLmRk3EENQeD9EU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ja.lproj/TOCropViewControllerLocalizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Iwh4b74rKw69c3ajJ6GBbESqJcY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ko.lproj/TOCropViewControllerLocalizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			oEOISmZu9WGaR2DlX36uBwO4jA8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ms.lproj/TOCropViewControllerLocalizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			pKSMomS5I4qsvpOzphOCNEZdlls=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>nl.lproj/TOCropViewControllerLocalizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			WEQ/kTs4B6rCq6KCEOb2bdDNiSE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>pl.lproj/TOCropViewControllerLocalizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			/4nhHU0B8LbVl0spRGHdH+ft28Y=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>pt-BR.lproj/TOCropViewControllerLocalizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			PMFcvwmTAoQiqBPiOXTFlzBIkkA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>pt.lproj/TOCropViewControllerLocalizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			PMFcvwmTAoQiqBPiOXTFlzBIkkA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ro.lproj/TOCropViewControllerLocalizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			HwzpFshAR5HPP/7fNO6jOtUmoFU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ru.lproj/TOCropViewControllerLocalizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			hTF421mU8gwBrcc/+ttC5RHyqYM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>sk.lproj/TOCropViewControllerLocalizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Rk+VpaOVCyarxcVWE8LCO4WGBLs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>tr.lproj/TOCropViewControllerLocalizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			eTq0dNVVoOSEkqRyo6IQvBAYz1k=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>uk.lproj/TOCropViewControllerLocalizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			HAZ9/cHhAx2Mk+Z8kGrzGT8XTNc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>vi.lproj/TOCropViewControllerLocalizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			OSx0CuhFv7QU/x1KHNFaAmdz2n8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>zh-Hans.lproj/TOCropViewControllerLocalizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			jV7lFbAOlsqp061XodkTOQjI3f8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>zh-Hant.lproj/TOCropViewControllerLocalizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			kjhSWwxYpPFlPxiOkf6nDVYwp9U=
			</data>
			<key>optional</key>
			<true/>
		</dict>
	</dict>
	<key>files2</key>
	<dict>
		<key>Base.lproj/TOCropViewControllerLocalizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			VxY/Lu0kUMa4Dw9FryxLSegDAKU=
			</data>
			<key>hash2</key>
			<data>
			GW2Z+4M0keHcxPYX+oHfEHYqUBK1EC8Rm3mJLWrTWME=
			</data>
		</dict>
		<key>PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash</key>
			<data>
			ucg9pita0v8d353x3NuGfxweQYU=
			</data>
			<key>hash2</key>
			<data>
			Uh6274Qwdz5cAQ4YOP6d2PpdYre3bRzqjX2NqtyxROI=
			</data>
		</dict>
		<key>ar.lproj/TOCropViewControllerLocalizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			9KdbZrTesV5Q3goWjT47ZNi4Ah8=
			</data>
			<key>hash2</key>
			<data>
			RjhF1v7VkZ5pHNWwq4q2Ces11/nj9HFsjtbVNvmrIVE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ca.lproj/TOCropViewControllerLocalizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			iS8YNUQF8hURkq4RIeYmTpfmltw=
			</data>
			<key>hash2</key>
			<data>
			SDoOwkND5MnxphW/CGFKrwJN9Cyn0uikyvrfJYlurUo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>cs.lproj/TOCropViewControllerLocalizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			tEF0ckSDjUIGE3y/Foty8T/omwo=
			</data>
			<key>hash2</key>
			<data>
			ZeoLY9ER0bWz1xLQnZ786tOnjblZhepo9J9WQ8INJjs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>da-DK.lproj/TOCropViewControllerLocalizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			xYOBVXX2ZEVsfhA06CU5RMhj9cc=
			</data>
			<key>hash2</key>
			<data>
			eK+vebIPwDgHx6XlXWcOdabEnyBuBZNVBDX8XDBoTkY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>de.lproj/TOCropViewControllerLocalizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			uIWAB6jgWd/rBUaaZTOOTvqjnvg=
			</data>
			<key>hash2</key>
			<data>
			bI2DapbUPGYYvb180twTo+1N3KRD0haOQEiCCeOTmAs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>en.lproj/TOCropViewControllerLocalizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			VxY/Lu0kUMa4Dw9FryxLSegDAKU=
			</data>
			<key>hash2</key>
			<data>
			GW2Z+4M0keHcxPYX+oHfEHYqUBK1EC8Rm3mJLWrTWME=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>es.lproj/TOCropViewControllerLocalizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			i6heSnr/3BK2BzBGbDoAnsdAkCM=
			</data>
			<key>hash2</key>
			<data>
			WTSK+uSmaRM7UQRocaUzOvqF5V5/8YpyzYR36D0pym8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>fa-IR.lproj/TOCropViewControllerLocalizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Aciw6FK1RrwpKtmXlxt/av73MMw=
			</data>
			<key>hash2</key>
			<data>
			qlXElNSV96/Bhfm2VhhHvKhy9Vyhm5ui5ssqoLi7e58=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>fa.lproj/TOCropViewControllerLocalizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Aciw6FK1RrwpKtmXlxt/av73MMw=
			</data>
			<key>hash2</key>
			<data>
			qlXElNSV96/Bhfm2VhhHvKhy9Vyhm5ui5ssqoLi7e58=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>fi.lproj/TOCropViewControllerLocalizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			hLO/2lkTZNPuVT+0tigvvZKnTYU=
			</data>
			<key>hash2</key>
			<data>
			gwzYEHyto774wrEk5AeozvQ+/LSqh+7VlzmPfWEYXPM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>fr.lproj/TOCropViewControllerLocalizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			x58vjyvUriL2N8lGdL4nn2fjT8A=
			</data>
			<key>hash2</key>
			<data>
			4jSflqNQBX+HCtxmQTmc6ww4+516Z5X5NpS5e0acF/w=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>hu.lproj/TOCropViewControllerLocalizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			BZbs7bmjLwJNgzeQzQVDFtr41GI=
			</data>
			<key>hash2</key>
			<data>
			PINY8TGJjSuucf341WM1VYeDmxeW7mWhVLYxKgJqPQ0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>id.lproj/TOCropViewControllerLocalizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			6uxsz9U6fGxSTcjLOc8ILDETdVM=
			</data>
			<key>hash2</key>
			<data>
			/a7u1wffkoejir+Ph8ty7S3zSZ3Pah72F9x/O6DcDLs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>it.lproj/TOCropViewControllerLocalizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			IZkFMVHUu5vtuLmRk3EENQeD9EU=
			</data>
			<key>hash2</key>
			<data>
			hRsxJneovqXThfZCIF5dgEwF8lhQ1UlvwElPsLgG7sI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ja.lproj/TOCropViewControllerLocalizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Iwh4b74rKw69c3ajJ6GBbESqJcY=
			</data>
			<key>hash2</key>
			<data>
			ZVvNCp0WDyjoB3PsKvxDnYX/3oZwWAkKXWhkN8fOjK0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ko.lproj/TOCropViewControllerLocalizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			oEOISmZu9WGaR2DlX36uBwO4jA8=
			</data>
			<key>hash2</key>
			<data>
			YPl6zLnXtKno3TNY/BIj0W6I9mlqZ7YhSOa/6v4QHVg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ms.lproj/TOCropViewControllerLocalizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			pKSMomS5I4qsvpOzphOCNEZdlls=
			</data>
			<key>hash2</key>
			<data>
			wXzwrWRoQrN8I5PmLGsxXixSBxpedaYCiQXqY82uiQw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>nl.lproj/TOCropViewControllerLocalizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			WEQ/kTs4B6rCq6KCEOb2bdDNiSE=
			</data>
			<key>hash2</key>
			<data>
			J4uHCZGNypnCfNidppe9kHpgpIR5u3MVlDWME/Nd7hQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>pl.lproj/TOCropViewControllerLocalizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			/4nhHU0B8LbVl0spRGHdH+ft28Y=
			</data>
			<key>hash2</key>
			<data>
			YK8mEk8idwYRPjhO0j/IFOuhI7M6DIUsema/3e+kIUk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>pt-BR.lproj/TOCropViewControllerLocalizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			PMFcvwmTAoQiqBPiOXTFlzBIkkA=
			</data>
			<key>hash2</key>
			<data>
			JW83bBbOBexTFlLGZF7Kz/L4+2EFpKBmGkF8cNpOYyA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>pt.lproj/TOCropViewControllerLocalizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			PMFcvwmTAoQiqBPiOXTFlzBIkkA=
			</data>
			<key>hash2</key>
			<data>
			JW83bBbOBexTFlLGZF7Kz/L4+2EFpKBmGkF8cNpOYyA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ro.lproj/TOCropViewControllerLocalizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			HwzpFshAR5HPP/7fNO6jOtUmoFU=
			</data>
			<key>hash2</key>
			<data>
			D6fze7ikM8ZdRRPRcskPZRXuukhDAZPbO4LYylAdnq8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ru.lproj/TOCropViewControllerLocalizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			hTF421mU8gwBrcc/+ttC5RHyqYM=
			</data>
			<key>hash2</key>
			<data>
			kX2PTs0/gkxFuhKsZSdDQ8cQSdgmC7IO2RPEuR8qVLs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>sk.lproj/TOCropViewControllerLocalizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Rk+VpaOVCyarxcVWE8LCO4WGBLs=
			</data>
			<key>hash2</key>
			<data>
			1AVsDsZiHh+1A5AJYDS0Y7ejydIFr2Fk6fdW7JK7w5M=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>tr.lproj/TOCropViewControllerLocalizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			eTq0dNVVoOSEkqRyo6IQvBAYz1k=
			</data>
			<key>hash2</key>
			<data>
			ggavymadm0jrhdMFlgzBHlF6okjAULifx4rcj0zT+qo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>uk.lproj/TOCropViewControllerLocalizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			HAZ9/cHhAx2Mk+Z8kGrzGT8XTNc=
			</data>
			<key>hash2</key>
			<data>
			PD5wfZlyWq8AlmWzATu0eCO5dOez5Jw425yFl7N/vJI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>vi.lproj/TOCropViewControllerLocalizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			OSx0CuhFv7QU/x1KHNFaAmdz2n8=
			</data>
			<key>hash2</key>
			<data>
			HBDho/CHxq167q0X+NDTRzncPFjtA30P0JRm9urBXAA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>zh-Hans.lproj/TOCropViewControllerLocalizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			jV7lFbAOlsqp061XodkTOQjI3f8=
			</data>
			<key>hash2</key>
			<data>
			sOWecZADc259Yep3XGbJcTbgmn2JXvVPkh7S6uaXt1Q=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>zh-Hant.lproj/TOCropViewControllerLocalizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			kjhSWwxYpPFlPxiOkf6nDVYwp9U=
			</data>
			<key>hash2</key>
			<data>
			teEdEmvUad++KmbAGbmJ+lduOVy3BXAjeY1wkmP0LoE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
