//
//  GitHub
//  https://github.com/zhangao0086/DKImagePickerController
//
//
//  License
//  Copyright (c) 2014 ZhangAo
//  Released under an MIT license: http://opensource.org/licenses/MIT
//

"permission.camera.title" = "Vui lòng cho phép truy cập camera";

"permission.photo.title" = "Vui lòng cho phép truy cập ảnh";

"permission.allow" = "Cho phép truy cập";

"picker.alert.ok" = "Đồng ý";

"picker.select.title" = "Chọn(%@)";

"picker.select.done.title" = "Xong";

"picker.select.all.title" = "Chọn tất cả";

"picker.select.photosOrVideos.error.title" = "Chọn ảnh hoặc video";

"picker.select.photosOrVideos.error.message" = "Không thể lựa chọn đồng thời cả ảnh và video.";

"picker.select.maxLimitReached.error.title" = "Số lượng ảnh đã đạt giới hạn";

"picker.select.maxLimitReached.error.message" = "Bạn có thể chọn %@ mục";
