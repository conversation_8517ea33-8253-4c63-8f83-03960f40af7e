---
triple:          'arm64-apple-darwin'
binary-path:     '/Users/<USER>/Documents/manish/flutter_personal_finance_app/flutter_personal_finance/build/ios/Debug-Development-iphonesimulator/printing/printing.framework/printing'
relocations:
  - { offset: 0x67045, size: 0x8, addend: 0x0, symName: _printingVersionString, symObjAddr: 0x0, symBinAddr: 0x10600, symSize: 0x0 }
  - { offset: 0x6707A, size: 0x8, addend: 0x0, symName: _printingVersionNumber, symObjAddr: 0x28, symBinAddr: 0x10628, symSize: 0x0 }
  - { offset: 0x670B7, size: 0x8, addend: 0x0, symName: _net_nfet_printing_set_document, symObjAddr: 0x0, symBinAddr: 0x13F0, symSize: 0x18 }
  - { offset: 0x670C5, size: 0x8, addend: 0x0, symName: _net_nfet_printing_set_document, symObjAddr: 0x0, symBinAddr: 0x13F0, symSize: 0x18 }
  - { offset: 0x6710C, size: 0x8, addend: 0x0, symName: _net_nfet_printing_set_error, symObjAddr: 0x18, symBinAddr: 0x1408, symSize: 0x14 }
  - { offset: 0x67319, size: 0x8, addend: 0x0, symName: '_$s8printing16CustomPrintPaperCMa', symObjAddr: 0x108, symBinAddr: 0x1524, symSize: 0x20 }
  - { offset: 0x6732D, size: 0x8, addend: 0x0, symName: '_$s8printing14PrintingPluginC4jobsSDys6UInt32VAA8PrintJobCGvpfi', symObjAddr: 0x128, symBinAddr: 0x1544, symSize: 0xC }
  - { offset: 0x67345, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobC11pdfDocument33_8513FCA205AC62BEC931B97878D84065LLSo16CGPDFDocumentRefaSgvpfi', symObjAddr: 0x134, symBinAddr: 0x1550, symSize: 0x8 }
  - { offset: 0x6735D, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobC7jobName33_8513FCA205AC62BEC931B97878D84065LLSSSgvpfi', symObjAddr: 0x144, symBinAddr: 0x1558, symSize: 0xC }
  - { offset: 0x67375, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobC11orientation33_8513FCA205AC62BEC931B97878D84065LLSo22UIPrintInfoOrientationVSgvpfi', symObjAddr: 0x15C, symBinAddr: 0x1564, symSize: 0xC }
  - { offset: 0x6738D, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobC9semaphore33_8513FCA205AC62BEC931B97878D84065LLSo012OS_dispatch_D0Cvpfi', symObjAddr: 0x168, symBinAddr: 0x1570, symSize: 0x8 }
  - { offset: 0x673A5, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobC7dynamic33_8513FCA205AC62BEC931B97878D84065LLSbvpfi', symObjAddr: 0x170, symBinAddr: 0x1578, symSize: 0x8 }
  - { offset: 0x673BD, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobC11currentSize33_8513FCA205AC62BEC931B97878D84065LLSo6CGSizeVSgvpfi', symObjAddr: 0x178, symBinAddr: 0x1580, symSize: 0x10 }
  - { offset: 0x673D5, size: 0x8, addend: 0x0, symName: ___swift_memcpy16_8, symObjAddr: 0x1A4, symBinAddr: 0x15A4, symSize: 0xC }
  - { offset: 0x673E9, size: 0x8, addend: 0x0, symName: ___swift_noop_void_return, symObjAddr: 0x1B0, symBinAddr: 0x15B0, symSize: 0x4 }
  - { offset: 0x673FD, size: 0x8, addend: 0x0, symName: '_$sSo6CGSizeVwet', symObjAddr: 0x1B4, symBinAddr: 0x15B4, symSize: 0x20 }
  - { offset: 0x67411, size: 0x8, addend: 0x0, symName: '_$sSo6CGSizeVwst', symObjAddr: 0x1D4, symBinAddr: 0x15D4, symSize: 0x28 }
  - { offset: 0x67425, size: 0x8, addend: 0x0, symName: '_$sSo6CGRectVwCP', symObjAddr: 0x230, symBinAddr: 0x1624, symSize: 0x2C }
  - { offset: 0x67439, size: 0x8, addend: 0x0, symName: ___swift_memcpy32_8, symObjAddr: 0x25C, symBinAddr: 0x1650, symSize: 0xC }
  - { offset: 0x6744D, size: 0x8, addend: 0x0, symName: '_$sSo6CGRectVwet', symObjAddr: 0x268, symBinAddr: 0x165C, symSize: 0x20 }
  - { offset: 0x67461, size: 0x8, addend: 0x0, symName: '_$sSo6CGRectVwst', symObjAddr: 0x288, symBinAddr: 0x167C, symSize: 0x2C }
  - { offset: 0x674B3, size: 0x8, addend: 0x0, symName: '_$s8printing16CustomPrintPaperC9paperSizeSo6CGSizeVvgTo', symObjAddr: 0x0, symBinAddr: 0x141C, symSize: 0x14 }
  - { offset: 0x674CB, size: 0x8, addend: 0x0, symName: '_$s8printing16CustomPrintPaperC9paperSizeSo6CGSizeVvgTo', symObjAddr: 0x0, symBinAddr: 0x141C, symSize: 0x14 }
  - { offset: 0x674E6, size: 0x8, addend: 0x0, symName: '_$s8printing16CustomPrintPaperC9paperSizeSo6CGSizeVvg', symObjAddr: 0x14, symBinAddr: 0x1430, symSize: 0x14 }
  - { offset: 0x67517, size: 0x8, addend: 0x0, symName: '_$s8printing16CustomPrintPaperC13printableRectSo6CGRectVvgTo', symObjAddr: 0x28, symBinAddr: 0x1444, symSize: 0x1C }
  - { offset: 0x6752F, size: 0x8, addend: 0x0, symName: '_$s8printing16CustomPrintPaperC13printableRectSo6CGRectVvgTo', symObjAddr: 0x28, symBinAddr: 0x1444, symSize: 0x1C }
  - { offset: 0x6754A, size: 0x8, addend: 0x0, symName: '_$s8printing16CustomPrintPaperC13printableRectSo6CGRectVvg', symObjAddr: 0x44, symBinAddr: 0x1460, symSize: 0x1C }
  - { offset: 0x6756D, size: 0x8, addend: 0x0, symName: '_$s8printing16CustomPrintPaperCACycfC', symObjAddr: 0x60, symBinAddr: 0x147C, symSize: 0x20 }
  - { offset: 0x67587, size: 0x8, addend: 0x0, symName: '_$s8printing16CustomPrintPaperCACycfc', symObjAddr: 0x80, symBinAddr: 0x149C, symSize: 0x2C }
  - { offset: 0x675E0, size: 0x8, addend: 0x0, symName: '_$s8printing16CustomPrintPaperCACycfcTo', symObjAddr: 0xAC, symBinAddr: 0x14C8, symSize: 0x2C }
  - { offset: 0x6763F, size: 0x8, addend: 0x0, symName: '_$s8printing16CustomPrintPaperCfD', symObjAddr: 0xD8, symBinAddr: 0x14F4, symSize: 0x30 }
  - { offset: 0x677BC, size: 0x8, addend: 0x0, symName: '_$s8printing14PrintingPluginC8instance33_6FF184931A26925A7B008FED8A176594LLACSgvpZ', symObjAddr: 0x7320, symBinAddr: 0x19260, symSize: 0x0 }
  - { offset: 0x67C80, size: 0x8, addend: 0x0, symName: '_$syXlSgIeyBy_ypSgIegn_TR', symObjAddr: 0x2358, symBinAddr: 0x3A74, symSize: 0xD8 }
  - { offset: 0x67D0D, size: 0x8, addend: 0x0, symName: '_$s8printing14PrintingPluginCfETo', symObjAddr: 0x3050, symBinAddr: 0x474C, symSize: 0x38 }
  - { offset: 0x67D47, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4findys10_HashTableV6BucketV6bucket_Sb5foundtxSHRzlFs6UInt32V_Tg5', symObjAddr: 0x3088, symBinAddr: 0x4784, symSize: 0x34 }
  - { offset: 0x67D7F, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4findys10_HashTableV6BucketV6bucket_Sb5foundtxSHRzlFSS_Tg5', symObjAddr: 0x30BC, symBinAddr: 0x47B8, symSize: 0x64 }
  - { offset: 0x67DB7, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4find_9hashValues10_HashTableV6BucketV6bucket_Sb5foundtx_SitSHRzlFs6UInt32V_Tg5', symObjAddr: 0x3120, symBinAddr: 0x481C, symSize: 0x9C }
  - { offset: 0x67DEF, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4find_9hashValues10_HashTableV6BucketV6bucket_Sb5foundtx_SitSHRzlFSS_Tg5', symObjAddr: 0x31BC, symBinAddr: 0x48B8, symSize: 0xE0 }
  - { offset: 0x67EBF, size: 0x8, addend: 0x0, symName: '_$sSD8_VariantV8setValue_6forKeyyq_n_xtFSS_So9UIPrinterCTg5', symObjAddr: 0x329C, symBinAddr: 0x4998, symSize: 0xD8 }
  - { offset: 0x67FEA, size: 0x8, addend: 0x0, symName: '_$sSD8_VariantV8setValue_6forKeyyq_n_xtFs6UInt32V_8printing8PrintJobCTg5', symObjAddr: 0x3374, symBinAddr: 0x4A70, symSize: 0xC4 }
  - { offset: 0x680DE, size: 0x8, addend: 0x0, symName: '_$sSD8_VariantV11removeValue6forKeyq_Sgx_tFs6UInt32V_8printing8PrintJobCTg5', symObjAddr: 0x3438, symBinAddr: 0x4B34, symSize: 0x84 }
  - { offset: 0x6818C, size: 0x8, addend: 0x0, symName: '_$sSD8_VariantV11removeValue6forKeyq_Sgx_tFSS_So9UIPrinterCTg5', symObjAddr: 0x34BC, symBinAddr: 0x4BB8, symSize: 0xC4 }
  - { offset: 0x68222, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV12mutatingFind_8isUniques10_HashTableV6BucketV6bucket_Sb5foundtx_SbtFSS_So9UIPrinterCTg5', symObjAddr: 0x3580, symBinAddr: 0x4C7C, symSize: 0xD0 }
  - { offset: 0x6825B, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV12mutatingFind_8isUniques10_HashTableV6BucketV6bucket_Sb5foundtx_SbtFs6UInt32V_8printing8PrintJobCTg5', symObjAddr: 0x3650, symBinAddr: 0x4D4C, symSize: 0xC8 }
  - { offset: 0x6829F, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV4copyyyFs6UInt32V_8printing8PrintJobCTg5', symObjAddr: 0x3718, symBinAddr: 0x4E14, symSize: 0x1AC }
  - { offset: 0x68344, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV4copyyyFSS_So9UIPrinterCTg5', symObjAddr: 0x38C4, symBinAddr: 0x4FC0, symSize: 0x1C8 }
  - { offset: 0x6840A, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV20_copyOrMoveAndResize8capacity12moveElementsySi_SbtFs6UInt32V_8printing8PrintJobCTg5', symObjAddr: 0x3A8C, symBinAddr: 0x5188, symSize: 0x2CC }
  - { offset: 0x684F4, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV20_copyOrMoveAndResize8capacity12moveElementsySi_SbtFSS_So9UIPrinterCTg5', symObjAddr: 0x3D58, symBinAddr: 0x5454, symSize: 0x324 }
  - { offset: 0x685E9, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV7_delete2atys10_HashTableV6BucketV_tFs6UInt32V_8printing8PrintJobCTg5', symObjAddr: 0x407C, symBinAddr: 0x5778, symSize: 0x174 }
  - { offset: 0x68688, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV7_delete2atys10_HashTableV6BucketV_tFSS_So9UIPrinterCTg5', symObjAddr: 0x41F0, symBinAddr: 0x58EC, symSize: 0x1B0 }
  - { offset: 0x6873E, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV06InlineB0VyAESWcfCTf4nd_n', symObjAddr: 0x43A0, symBinAddr: 0x5A9C, symSize: 0xA4 }
  - { offset: 0x687B4, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV10LargeSliceVyAESWcfCTf4nd_n', symObjAddr: 0x4444, symBinAddr: 0x5B40, symSize: 0x78 }
  - { offset: 0x687E1, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV11InlineSliceVyAESWcfCTf4nd_n', symObjAddr: 0x44BC, symBinAddr: 0x5BB8, symSize: 0x80 }
  - { offset: 0x68857, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV15_RepresentationOyAESWcfCTf4nd_n', symObjAddr: 0x453C, symBinAddr: 0x5C38, symSize: 0x64 }
  - { offset: 0x68873, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV15_RepresentationOyAESWcfCTf4nd_n', symObjAddr: 0x453C, symBinAddr: 0x5C38, symSize: 0x64 }
  - { offset: 0x688CB, size: 0x8, addend: 0x0, symName: ___swift_instantiateConcreteTypeFromMangledName, symObjAddr: 0x48E4, symBinAddr: 0x5FE0, symSize: 0x44 }
  - { offset: 0x688DF, size: 0x8, addend: 0x0, symName: '_$sypWOb', symObjAddr: 0x4928, symBinAddr: 0x6024, symSize: 0x10 }
  - { offset: 0x688F3, size: 0x8, addend: 0x0, symName: '_$sypWOc', symObjAddr: 0x4938, symBinAddr: 0x6034, symSize: 0x3C }
  - { offset: 0x68907, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV15_RepresentationOWOe', symObjAddr: 0x4974, symBinAddr: 0x6070, symSize: 0x40 }
  - { offset: 0x68973, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV15_RepresentationOWOy', symObjAddr: 0x4B3C, symBinAddr: 0x6238, symSize: 0x40 }
  - { offset: 0x68987, size: 0x8, addend: 0x0, symName: '_$s8printing14PrintingPluginCMa', symObjAddr: 0x4B7C, symBinAddr: 0x6278, symSize: 0x20 }
  - { offset: 0x689A6, size: 0x8, addend: 0x0, symName: '_$syXlSgIeyBy_ypSgIegn_TRTA', symObjAddr: 0x7230, symBinAddr: 0x892C, symSize: 0x8 }
  - { offset: 0x689BA, size: 0x8, addend: 0x0, symName: ___swift_project_boxed_opaque_existential_0, symObjAddr: 0x7238, symBinAddr: 0x8934, symSize: 0x24 }
  - { offset: 0x689CE, size: 0x8, addend: 0x0, symName: ___swift_destroy_boxed_opaque_existential_0, symObjAddr: 0x725C, symBinAddr: 0x8958, symSize: 0x20 }
  - { offset: 0x68BAC, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCSS_ypTt0g5Tf4g_n', symObjAddr: 0x49B4, symBinAddr: 0x60B0, symSize: 0x10C }
  - { offset: 0x68D93, size: 0x8, addend: 0x0, symName: '_$s8printing14PrintingPluginC4jobsSDys6UInt32VAA8PrintJobCGvg', symObjAddr: 0x0, symBinAddr: 0x171C, symSize: 0x44 }
  - { offset: 0x68DB8, size: 0x8, addend: 0x0, symName: '_$s8printing14PrintingPluginC4jobsSDys6UInt32VAA8PrintJobCGvs', symObjAddr: 0x44, symBinAddr: 0x1760, symSize: 0x54 }
  - { offset: 0x68DED, size: 0x8, addend: 0x0, symName: '_$s8printing14PrintingPluginC4jobsSDys6UInt32VAA8PrintJobCGvM', symObjAddr: 0x98, symBinAddr: 0x17B4, symSize: 0x40 }
  - { offset: 0x68E0C, size: 0x8, addend: 0x0, symName: '_$s8printing14PrintingPluginC4jobsSDys6UInt32VAA8PrintJobCGvM.resume.0', symObjAddr: 0xD8, symBinAddr: 0x17F4, symSize: 0x4 }
  - { offset: 0x68E33, size: 0x8, addend: 0x0, symName: '_$s8printing14PrintingPluginC11setDocument3job3doc4sizeys6UInt32V_SPys5UInt8VGs6UInt64VtFZ', symObjAddr: 0xDC, symBinAddr: 0x17F8, symSize: 0x4 }
  - { offset: 0x68E54, size: 0x8, addend: 0x0, symName: '_$s8printing14PrintingPluginC11setDocument3job3doc4sizeys6UInt32V_SPys5UInt8VGs6UInt64VtFZTo', symObjAddr: 0xE0, symBinAddr: 0x17FC, symSize: 0x10 }
  - { offset: 0x68E7E, size: 0x8, addend: 0x0, symName: '_$s8printing14PrintingPluginC8setError3job7messageys6UInt32V_SPys4Int8VGtFZ', symObjAddr: 0xF0, symBinAddr: 0x180C, symSize: 0x4 }
  - { offset: 0x68E99, size: 0x8, addend: 0x0, symName: '_$s8printing14PrintingPluginC8setError3job7messageys6UInt32V_SPys4Int8VGtFZTo', symObjAddr: 0xF4, symBinAddr: 0x1810, symSize: 0xC }
  - { offset: 0x68F68, size: 0x8, addend: 0x0, symName: '_$s8printing14PrintingPluginC8register4withySo07FlutterC9Registrar_p_tFZ', symObjAddr: 0x100, symBinAddr: 0x181C, symSize: 0x4 }
  - { offset: 0x68F83, size: 0x8, addend: 0x0, symName: '_$s8printing14PrintingPluginC8register4withySo07FlutterC9Registrar_p_tFZTo', symObjAddr: 0x104, symBinAddr: 0x1820, symSize: 0x30 }
  - { offset: 0x690FF, size: 0x8, addend: 0x0, symName: '_$s8printing14PrintingPluginC6handle_6resultySo17FlutterMethodCallC_yypSgctF', symObjAddr: 0x134, symBinAddr: 0x1850, symSize: 0x21A8 }
  - { offset: 0x69EC2, size: 0x8, addend: 0x0, symName: '_$s8printing14PrintingPluginC6handle_6resultySo17FlutterMethodCallC_yypSgctFTo', symObjAddr: 0x22DC, symBinAddr: 0x39F8, symSize: 0x7C }
  - { offset: 0x69F4B, size: 0x8, addend: 0x0, symName: '_$s8printing14PrintingPluginC8onLayout8printJob5width6height10marginLeft0J3Top0J5Right0J6BottomyAA05PrintG0C_12CoreGraphics7CGFloatVA5PtF', symObjAddr: 0x2430, symBinAddr: 0x3B4C, symSize: 0x26C }
  - { offset: 0x6A0F5, size: 0x8, addend: 0x0, symName: '_$s8printing14PrintingPluginC11onCompleted8printJob9completed5erroryAA05PrintG0C_SbSo8NSStringCSgtF', symObjAddr: 0x269C, symBinAddr: 0x3DB8, symSize: 0x204 }
  - { offset: 0x6A27F, size: 0x8, addend: 0x0, symName: '_$s8printing14PrintingPluginC14onHtmlRendered8printJob7pdfDatayAA05PrintH0C_10Foundation0J0VtF', symObjAddr: 0x28A0, symBinAddr: 0x3FBC, symSize: 0x1E4 }
  - { offset: 0x6A360, size: 0x8, addend: 0x0, symName: '_$s8printing14PrintingPluginC11onHtmlError8printJob5erroryAA05PrintH0C_SStF', symObjAddr: 0x2A84, symBinAddr: 0x41A0, symSize: 0x174 }
  - { offset: 0x6A44C, size: 0x8, addend: 0x0, symName: '_$s8printing14PrintingPluginC16onPageRasterized8printJob9imageData5width6heightyAA05PrintH0C_10Foundation0J0VS2itF', symObjAddr: 0x2BF8, symBinAddr: 0x4314, symSize: 0x224 }
  - { offset: 0x6A55C, size: 0x8, addend: 0x0, symName: '_$s8printing14PrintingPluginC15onPageRasterEnd8printJob5erroryAA05PrintI0C_SSSgtF', symObjAddr: 0x2E1C, symBinAddr: 0x4538, symSize: 0x18C }
  - { offset: 0x6A648, size: 0x8, addend: 0x0, symName: '_$s8printing14PrintingPluginCACycfc', symObjAddr: 0x2FC8, symBinAddr: 0x46C4, symSize: 0x2C }
  - { offset: 0x6A6A1, size: 0x8, addend: 0x0, symName: '_$s8printing14PrintingPluginCACycfcTo', symObjAddr: 0x2FF4, symBinAddr: 0x46F0, symSize: 0x2C }
  - { offset: 0x6A700, size: 0x8, addend: 0x0, symName: '_$s8printing14PrintingPluginCfD', symObjAddr: 0x3020, symBinAddr: 0x471C, symSize: 0x30 }
  - { offset: 0x6A7A0, size: 0x8, addend: 0x0, symName: '_$s8printing14PrintingPluginC11setDocument3job3doc4sizeys6UInt32V_SPys5UInt8VGs6UInt64VtFZTf4nnnd_n', symObjAddr: 0x45A0, symBinAddr: 0x5C9C, symSize: 0xC4 }
  - { offset: 0x6A905, size: 0x8, addend: 0x0, symName: '_$s8printing14PrintingPluginC8setError3job7messageys6UInt32V_SPys4Int8VGtFZTf4nnd_n', symObjAddr: 0x4664, symBinAddr: 0x5D60, symSize: 0x140 }
  - { offset: 0x6AA25, size: 0x8, addend: 0x0, symName: '_$s8printing14PrintingPluginC8register4withySo07FlutterC9Registrar_p_tFZTf4nd_n', symObjAddr: 0x47A4, symBinAddr: 0x5EA0, symSize: 0x140 }
  - { offset: 0x6AAD1, size: 0x8, addend: 0x0, symName: '_$s8printing14PrintingPluginC6handle_6resultySo17FlutterMethodCallC_yypSgctF015$syXlSgIeyBy_ypK7Iegn_TRyXlSgIeyBy_Tf1ncn_nTf4nng_n', symObjAddr: 0x4B9C, symBinAddr: 0x6298, symSize: 0x2670 }
  - { offset: 0x6B97F, size: 0x8, addend: 0x0, symName: '_$s8printing16selectedPrintersSDySSSo9UIPrinterCGvp', symObjAddr: 0x20A08, symBinAddr: 0x19650, symSize: 0x0 }
  - { offset: 0x6B999, size: 0x8, addend: 0x0, symName: '_$s8printing13pickedPrinterSo9UIPrinterCSgvp', symObjAddr: 0x20A10, symBinAddr: 0x19658, symSize: 0x0 }
  - { offset: 0x6BE9A, size: 0x8, addend: 0x0, symName: '_$s8printing16selectedPrinters_WZ', symObjAddr: 0x54, symBinAddr: 0x8A10, symSize: 0x14 }
  - { offset: 0x6BEEA, size: 0x8, addend: 0x0, symName: '_$s8printing31dataProviderReleaseDataCallback4info0B04sizeySvSg_SVSitFTo', symObjAddr: 0x514, symBinAddr: 0x8ECC, symSize: 0x10 }
  - { offset: 0x6BF6B, size: 0x8, addend: 0x0, symName: '_$sSbIegy_SbIeyBy_TR', symObjAddr: 0xB4C, symBinAddr: 0x9504, symSize: 0x3C }
  - { offset: 0x6C054, size: 0x8, addend: 0x0, symName: '_$sSaySo19WKWebsiteDataRecordCGIeghg_So7NSArrayCIeyBhy_TR', symObjAddr: 0x2B64, symBinAddr: 0xB51C, symSize: 0x6C }
  - { offset: 0x6C1C1, size: 0x8, addend: 0x0, symName: '_$sIeg_IyB_TR', symObjAddr: 0x3BDC, symBinAddr: 0xC594, symSize: 0x20 }
  - { offset: 0x6C1EF, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobCfETo', symObjAddr: 0x3CE8, symBinAddr: 0xC680, symSize: 0x80 }
  - { offset: 0x6C229, size: 0x8, addend: 0x0, symName: '_$ss29getContiguousArrayStorageType3fors01_bcD0CyxGmxm_tlFSo19WKWebsiteDataRecordC_Ttg5', symObjAddr: 0x3D68, symBinAddr: 0xC700, symSize: 0x64 }
  - { offset: 0x6C268, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV15_RepresentationO22withUnsafeMutableBytesyxxSwKXEKlFyt_Tgq5015$s10Foundation4B42V9repeating5countACs5UInt8V_SitcfcySwXEfU_s0L0VTf1ncn_n', symObjAddr: 0x3F98, symBinAddr: 0xC930, symSize: 0x23C }
  - { offset: 0x6C33B, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV15_RepresentationO22withUnsafeMutableBytesyxxSwKXEKlFyt_Tg5062$s8printing8PrintJobC9rasterPdf4data5pages5scaley10Foundation4B51V_SaySiGSg12CoreGraphics7CGFloatVtFyyYbcfU_ySwXEfU_S3i0Q8Graphics0S0VAJSo6CGRectVA2JSo12CGPDFPageRefaTf1ncn_n', symObjAddr: 0x41D4, symBinAddr: 0xCB6C, symSize: 0x3D8 }
  - { offset: 0x6C41A, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV11InlineSliceV22withUnsafeMutableBytesyxxSwKXEKlFyt_Tg5062$s8printing8PrintJobC9rasterPdf4data5pages5scaley10Foundation4B51V_SaySiGSg12CoreGraphics7CGFloatVtFyyYbcfU_ySwXEfU_S3i0R8Graphics0T0VAJSo6CGRectVA2JSo12CGPDFPageRefaTf1ncn_n', symObjAddr: 0x45AC, symBinAddr: 0xCF44, symSize: 0x154 }
  - { offset: 0x6C482, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV11InlineSliceV22withUnsafeMutableBytesyxxSwKXEKlFyt_Tgq5015$s10Foundation4B42V9repeating5countACs5UInt8V_SitcfcySwXEfU_s0M0VTf1ncn_n', symObjAddr: 0x4700, symBinAddr: 0xD098, symSize: 0x80 }
  - { offset: 0x6C53E, size: 0x8, addend: 0x0, symName: '_$sSTsE13_copyContents12initializing8IteratorQz_SitSry7ElementQzG_tFSNySiG_Tg5', symObjAddr: 0x4780, symBinAddr: 0xD118, symSize: 0xA4 }
  - { offset: 0x6C630, size: 0x8, addend: 0x0, symName: '_$ss22_ContiguousArrayBufferV19_uninitializedCount15minimumCapacityAByxGSi_SitcfCSi_Tt1g5', symObjAddr: 0x4824, symBinAddr: 0xD1BC, symSize: 0x78 }
  - { offset: 0x6C76F, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobC9rasterPdf4data5pages5scaley10Foundation4DataV_SaySiGSg12CoreGraphics7CGFloatVtFyyYbcfU_TA', symObjAddr: 0x58DC, symBinAddr: 0xE274, symSize: 0x10 }
  - { offset: 0x6C783, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x58EC, symBinAddr: 0xE284, symSize: 0x10 }
  - { offset: 0x6C797, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x58FC, symBinAddr: 0xE294, symSize: 0x8 }
  - { offset: 0x6C7AB, size: 0x8, addend: 0x0, symName: '_$sSay8Dispatch0A13WorkItemFlagsVGSayxGSTsWl', symObjAddr: 0x5948, symBinAddr: 0xE29C, symSize: 0x48 }
  - { offset: 0x6C7BF, size: 0x8, addend: 0x0, symName: ___swift_instantiateConcreteTypeFromMangledNameAbstract, symObjAddr: 0x5990, symBinAddr: 0xE2E4, symSize: 0x48 }
  - { offset: 0x6C7D3, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobCMa', symObjAddr: 0x59D8, symBinAddr: 0xE32C, symSize: 0x20 }
  - { offset: 0x6C7E7, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobC11setDocumentyy10Foundation4DataVSgFyyScMYccfU_TA', symObjAddr: 0x59F8, symBinAddr: 0xE34C, symSize: 0x8 }
  - { offset: 0x6C7FB, size: 0x8, addend: 0x0, symName: '_$s10Foundation3URLVSgWOc', symObjAddr: 0x5A00, symBinAddr: 0xE354, symSize: 0x48 }
  - { offset: 0x6C80F, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobC11setDocumentyy10Foundation4DataVSgFyyScMYccfU_ySbcfU_TA', symObjAddr: 0x5A4C, symBinAddr: 0xE3A0, symSize: 0x34 }
  - { offset: 0x6C823, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobC11setDocumentyy10Foundation4DataVSgFyyScMYccfU_ySbcfU_ySo28UIPrintInteractionControllerC_Sbs5Error_pSgtcACcfu_yAJ_SbALtcfu0_TA', symObjAddr: 0x5AA4, symBinAddr: 0xE3F8, symSize: 0x28 }
  - { offset: 0x6C87A, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobC9rasterPdf4data5pages5scaley10Foundation4DataV_SaySiGSg12CoreGraphics7CGFloatVtFyyYbcfU_yyScMYcXEfU1_TA', symObjAddr: 0x5ACC, symBinAddr: 0xE420, symSize: 0x8 }
  - { offset: 0x6C899, size: 0x8, addend: 0x0, symName: '_$sIg_Ieg_TRTA', symObjAddr: 0x5AE4, symBinAddr: 0xE438, symSize: 0x20 }
  - { offset: 0x6C8C2, size: 0x8, addend: 0x0, symName: '_$sIeg_SgWOe', symObjAddr: 0x5B04, symBinAddr: 0xE458, symSize: 0x10 }
  - { offset: 0x6C8D6, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobC9rasterPdf4data5pages5scaley10Foundation4DataV_SaySiGSg12CoreGraphics7CGFloatVtFyyYbcfU_yyScMYcXEfU0_TA', symObjAddr: 0x5B38, symBinAddr: 0xE48C, symSize: 0xC }
  - { offset: 0x6C8EA, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobC8printPdf4name12withPageSize9andMargin0G7Printer11dynamically10outputType011forceCustomB5PaperySS_So6CGSizeVSo6CGRectVSSSgSbSo017UIPrintInfoOutputO0VSbtFySbcfU_TA', symObjAddr: 0x5BF8, symBinAddr: 0xE4CC, symSize: 0x34 }
  - { offset: 0x6C8FE, size: 0x8, addend: 0x0, symName: ___swift_allocate_boxed_opaque_existential_0, symObjAddr: 0x5C6C, symBinAddr: 0xE540, symSize: 0x3C }
  - { offset: 0x6C912, size: 0x8, addend: 0x0, symName: _keypath_get_selector_isLoading, symObjAddr: 0x5CA8, symBinAddr: 0xE57C, symSize: 0xC }
  - { offset: 0x6C926, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobC11convertHtml_12withPageSize9andMargin0I7BaseUrlySS_So6CGRectVAI10Foundation3URLVSgtFySo9WKWebViewC_AJ24NSKeyValueObservedChangeVySbGtYbcfU_TA', symObjAddr: 0x5CE8, symBinAddr: 0xE5BC, symSize: 0x1C }
  - { offset: 0x6C93A, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobC11convertHtml_12withPageSize9andMargin0I7BaseUrlySS_So6CGRectVAI10Foundation3URLVSgtFySo9WKWebViewC_AJ24NSKeyValueObservedChangeVySbGtYbcfU_yyScMYccfU_TA', symObjAddr: 0x5D38, symBinAddr: 0xE60C, symSize: 0x1C }
  - { offset: 0x6C94E, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobC11pickPrinter6result14withSourceRectyyypSgc_So6CGRectVtFZySo25UIPrinterPickerControllerC_Sbs5Error_pSgtcfU_TA', symObjAddr: 0x5D78, symBinAddr: 0xE64C, symSize: 0x8 }
  - { offset: 0x6CF77, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobCAA5indexAcA14PrintingPluginC_SitcfC', symObjAddr: 0x0, symBinAddr: 0x89BC, symSize: 0x50 }
  - { offset: 0x6D000, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobC0A4InfoSo12NSDictionaryCyFZ', symObjAddr: 0x50, symBinAddr: 0x8A0C, symSize: 0x4 }
  - { offset: 0x6D014, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobC5indexSivg', symObjAddr: 0x68, symBinAddr: 0x8A24, symSize: 0x40 }
  - { offset: 0x6D03F, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobC5indexSivs', symObjAddr: 0xA8, symBinAddr: 0x8A64, symSize: 0x4C }
  - { offset: 0x6D074, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobC5indexSivM', symObjAddr: 0xF4, symBinAddr: 0x8AB0, symSize: 0x40 }
  - { offset: 0x6D099, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobCAA5indexAcA14PrintingPluginC_Sitcfc', symObjAddr: 0x138, symBinAddr: 0x8AF0, symSize: 0x30 }
  - { offset: 0x6D0DD, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobC8drawPage2at2inySi_So6CGRectVtF', symObjAddr: 0x168, symBinAddr: 0x8B20, symSize: 0x4 }
  - { offset: 0x6D0F8, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobC8drawPage2at2inySi_So6CGRectVtFTo', symObjAddr: 0x16C, symBinAddr: 0x8B24, symSize: 0x30 }
  - { offset: 0x6D2D7, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobC11setDocumentyy10Foundation4DataVSgF', symObjAddr: 0x19C, symBinAddr: 0x8B54, symSize: 0x378 }
  - { offset: 0x6D447, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobC11setDocumentyy10Foundation4DataVSgFyyScMYccfU_', symObjAddr: 0x524, symBinAddr: 0x8EDC, symSize: 0x628 }
  - { offset: 0x6D71C, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobC13numberOfPagesSivgTo', symObjAddr: 0xB8C, symBinAddr: 0x9544, symSize: 0x34 }
  - { offset: 0x6D744, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobC13numberOfPagesSivg', symObjAddr: 0xBC0, symBinAddr: 0x9578, symSize: 0x180 }
  - { offset: 0x6D7C3, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobC26printInteractionController_11choosePaperSo07UIPrintH0CSo0ieF0C_SayAGGtF', symObjAddr: 0xD40, symBinAddr: 0x96F8, symSize: 0x8 }
  - { offset: 0x6D7DE, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobC26printInteractionController_11choosePaperSo07UIPrintH0CSo0ieF0C_SayAGGtFTo', symObjAddr: 0xD48, symBinAddr: 0x9700, symSize: 0x94 }
  - { offset: 0x6D8B7, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobC8printPdf4name12withPageSize9andMargin0G7Printer11dynamically10outputType011forceCustomB5PaperySS_So6CGSizeVSo6CGRectVSSSgSbSo017UIPrintInfoOutputO0VSbtF', symObjAddr: 0xDDC, symBinAddr: 0x9794, symSize: 0xD18 }
  - { offset: 0x6DE62, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobC11convertHtml_12withPageSize9andMargin0I7BaseUrlySS_So6CGRectVAI10Foundation3URLVSgtF', symObjAddr: 0x1D20, symBinAddr: 0xA6D8, symSize: 0x490 }
  - { offset: 0x6DF9A, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobC11convertHtml_12withPageSize9andMargin0I7BaseUrlySS_So6CGRectVAI10Foundation3URLVSgtFySo9WKWebViewC_AJ24NSKeyValueObservedChangeVySbGtYbcfU_', symObjAddr: 0x21B0, symBinAddr: 0xAB68, symSize: 0x2BC }
  - { offset: 0x6E048, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobC11convertHtml_12withPageSize9andMargin0I7BaseUrlySS_So6CGRectVAI10Foundation3URLVSgtFySo9WKWebViewC_AJ24NSKeyValueObservedChangeVySbGtYbcfU_yyScMYccfU_', symObjAddr: 0x246C, symBinAddr: 0xAE24, symSize: 0x428 }
  - { offset: 0x6E230, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobC11convertHtml_12withPageSize9andMargin0I7BaseUrlySS_So6CGRectVAI10Foundation3URLVSgtFySo9WKWebViewC_AJ24NSKeyValueObservedChangeVySbGtYbcfU_yyScMYccfU_ySaySo19WKWebsiteDataRecordCGYbScMYccfU_', symObjAddr: 0x2894, symBinAddr: 0xB24C, symSize: 0x2A0 }
  - { offset: 0x6E3E8, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobC11convertHtml_12withPageSize9andMargin0I7BaseUrlySS_So6CGRectVAI10Foundation3URLVSgtFySo9WKWebViewC_AJ24NSKeyValueObservedChangeVySbGtYbcfU_yyScMYccfU_ySaySo19WKWebsiteDataRecordCGYbScMYccfU_yyYbScMYccfU_', symObjAddr: 0x2B34, symBinAddr: 0xB4EC, symSize: 0x4 }
  - { offset: 0x6E47E, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobC11pickPrinter6result14withSourceRectyyypSgc_So6CGRectVtFZySo25UIPrinterPickerControllerC_Sbs5Error_pSgtcfU_', symObjAddr: 0x2BD0, symBinAddr: 0xB588, symSize: 0x3E4 }
  - { offset: 0x6E65D, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobC9rasterPdf4data5pages5scaley10Foundation4DataV_SaySiGSg12CoreGraphics7CGFloatVtF', symObjAddr: 0x3038, symBinAddr: 0xB9F0, symSize: 0x314 }
  - { offset: 0x6E754, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobC9rasterPdf4data5pages5scaley10Foundation4DataV_SaySiGSg12CoreGraphics7CGFloatVtFyyYbcfU_', symObjAddr: 0x334C, symBinAddr: 0xBD04, symSize: 0x6A8 }
  - { offset: 0x6EC56, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobC9rasterPdf4data5pages5scaley10Foundation4DataV_SaySiGSg12CoreGraphics7CGFloatVtFyyYbcfU_ySwXEfU_', symObjAddr: 0x39F4, symBinAddr: 0xC3AC, symSize: 0x164 }
  - { offset: 0x6ED7E, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobC9rasterPdf4data5pages5scaley10Foundation4DataV_SaySiGSg12CoreGraphics7CGFloatVtFyyYbcfU_yyScMYcXEfU0_', symObjAddr: 0x3B58, symBinAddr: 0xC510, symSize: 0x84 }
  - { offset: 0x6EDFD, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobC9rasterPdf4data5pages5scaley10Foundation4DataV_SaySiGSg12CoreGraphics7CGFloatVtFyyYbcfU_yyScMYcXEfU1_', symObjAddr: 0x3BFC, symBinAddr: 0xC5B4, symSize: 0x44 }
  - { offset: 0x6EEA7, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobCACycfc', symObjAddr: 0x3C60, symBinAddr: 0xC5F8, symSize: 0x2C }
  - { offset: 0x6EF00, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobCACycfcTo', symObjAddr: 0x3C8C, symBinAddr: 0xC624, symSize: 0x2C }
  - { offset: 0x6EF5F, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobCfD', symObjAddr: 0x3CB8, symBinAddr: 0xC650, symSize: 0x30 }
  - { offset: 0x6EF82, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobCAA5indexAcA14PrintingPluginC_SitcfcTf4gnn_n', symObjAddr: 0x489C, symBinAddr: 0xD234, symSize: 0x12C }
  - { offset: 0x6EFDB, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobC8sharePdf4data14withSourceRect7andName7subject4bodyy10Foundation4DataV_So6CGRectVS2SSgAOtFZTf4nnnnnd_n', symObjAddr: 0x49C8, symBinAddr: 0xD360, symSize: 0x550 }
  - { offset: 0x6F221, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobC11pickPrinter6result14withSourceRectyyypSgc_So6CGRectVtFZTf4nnd_n', symObjAddr: 0x4F18, symBinAddr: 0xD8B0, symSize: 0x2B8 }
  - { offset: 0x6F2CA, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobC0A4InfoSo12NSDictionaryCyFZTf4d_n', symObjAddr: 0x51D0, symBinAddr: 0xDB68, symSize: 0x170 }
  - { offset: 0x6F352, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobC8drawPage2at2inySi_So6CGRectVtFTf4ndn_n', symObjAddr: 0x5340, symBinAddr: 0xDCD8, symSize: 0xDC }
  - { offset: 0x6F3E8, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobC17completionHandler15printController9completed5errorySo018UIPrintInteractionG0C_Sbs5Error_pSgtFTf4dnnn_n', symObjAddr: 0x541C, symBinAddr: 0xDDB4, symSize: 0x1AC }
  - { offset: 0x6F590, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobC26printInteractionController_11choosePaperSo07UIPrintH0CSo0ieF0C_SayAGGtFTf4dnn_n', symObjAddr: 0x55C8, symBinAddr: 0xDF60, symSize: 0x2E0 }
...
