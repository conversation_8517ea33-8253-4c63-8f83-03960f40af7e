---
triple:          'x86_64-apple-darwin'
binary-path:     '/Users/<USER>/Documents/manish/flutter_personal_finance_app/flutter_personal_finance/build/ios/Debug-Development-iphonesimulator/printing/printing.framework/printing'
relocations:
  - { offset: 0x6868B, size: 0x8, addend: 0x0, symName: _printingVersionString, symObjAddr: 0x0, symBinAddr: 0x11B50, symSize: 0x0 }
  - { offset: 0x686C0, size: 0x8, addend: 0x0, symName: _printingVersionNumber, symObjAddr: 0x28, symBinAddr: 0x11B78, symSize: 0x0 }
  - { offset: 0x686FD, size: 0x8, addend: 0x0, symName: _net_nfet_printing_set_document, symObjAddr: 0x0, symBinAddr: 0x13C0, symSize: 0x21 }
  - { offset: 0x6870B, size: 0x8, addend: 0x0, symName: _net_nfet_printing_set_document, symObjAddr: 0x0, symBinAddr: 0x13C0, symSize: 0x21 }
  - { offset: 0x68777, size: 0x8, addend: 0x0, symName: _net_nfet_printing_set_error, symObjAddr: 0x21, symBinAddr: 0x13E1, symSize: 0x1E }
  - { offset: 0x689A4, size: 0x8, addend: 0x0, symName: '_$s8printing16CustomPrintPaperCMa', symObjAddr: 0x140, symBinAddr: 0x1540, symSize: 0x20 }
  - { offset: 0x689B8, size: 0x8, addend: 0x0, symName: '_$s8printing14PrintingPluginC4jobsSDys6UInt32VAA8PrintJobCGvpfi', symObjAddr: 0x160, symBinAddr: 0x1560, symSize: 0x10 }
  - { offset: 0x689D0, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobC11pdfDocument33_8513FCA205AC62BEC931B97878D84065LLSo16CGPDFDocumentRefaSgvpfi', symObjAddr: 0x170, symBinAddr: 0x1570, symSize: 0x10 }
  - { offset: 0x689E8, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobC7jobName33_8513FCA205AC62BEC931B97878D84065LLSSSgvpfi', symObjAddr: 0x190, symBinAddr: 0x1580, symSize: 0x10 }
  - { offset: 0x68A00, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobC11orientation33_8513FCA205AC62BEC931B97878D84065LLSo22UIPrintInfoOrientationVSgvpfi', symObjAddr: 0x1B0, symBinAddr: 0x1590, symSize: 0x10 }
  - { offset: 0x68A18, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobC9semaphore33_8513FCA205AC62BEC931B97878D84065LLSo012OS_dispatch_D0Cvpfi', symObjAddr: 0x1C0, symBinAddr: 0x15A0, symSize: 0x10 }
  - { offset: 0x68A30, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobC11currentSize33_8513FCA205AC62BEC931B97878D84065LLSo6CGSizeVSgvpfi', symObjAddr: 0x1E0, symBinAddr: 0x15B0, symSize: 0x10 }
  - { offset: 0x68A48, size: 0x8, addend: 0x0, symName: ___swift_memcpy16_8, symObjAddr: 0x220, symBinAddr: 0x15E0, symSize: 0x10 }
  - { offset: 0x68A5C, size: 0x8, addend: 0x0, symName: ___swift_noop_void_return, symObjAddr: 0x230, symBinAddr: 0x15F0, symSize: 0x10 }
  - { offset: 0x68A70, size: 0x8, addend: 0x0, symName: '_$sSo6CGSizeVwet', symObjAddr: 0x240, symBinAddr: 0x1600, symSize: 0x20 }
  - { offset: 0x68A84, size: 0x8, addend: 0x0, symName: '_$sSo6CGSizeVwst', symObjAddr: 0x260, symBinAddr: 0x1620, symSize: 0x30 }
  - { offset: 0x68A98, size: 0x8, addend: 0x0, symName: '_$sSo6CGRectVwCP', symObjAddr: 0x2E0, symBinAddr: 0x1690, symSize: 0x30 }
  - { offset: 0x68AAC, size: 0x8, addend: 0x0, symName: ___swift_memcpy32_8, symObjAddr: 0x310, symBinAddr: 0x16C0, symSize: 0x20 }
  - { offset: 0x68AC0, size: 0x8, addend: 0x0, symName: '_$sSo6CGRectVwet', symObjAddr: 0x330, symBinAddr: 0x16E0, symSize: 0x20 }
  - { offset: 0x68AD4, size: 0x8, addend: 0x0, symName: '_$sSo6CGRectVwst', symObjAddr: 0x350, symBinAddr: 0x1700, symSize: 0x30 }
  - { offset: 0x68B26, size: 0x8, addend: 0x0, symName: '_$s8printing16CustomPrintPaperC9paperSizeSo6CGSizeVvgTo', symObjAddr: 0x0, symBinAddr: 0x1400, symSize: 0x20 }
  - { offset: 0x68B59, size: 0x8, addend: 0x0, symName: '_$s8printing16CustomPrintPaperC9paperSizeSo6CGSizeVvg', symObjAddr: 0x20, symBinAddr: 0x1420, symSize: 0x20 }
  - { offset: 0x68B8A, size: 0x8, addend: 0x0, symName: '_$s8printing16CustomPrintPaperC13printableRectSo6CGRectVvgTo', symObjAddr: 0x40, symBinAddr: 0x1440, symSize: 0x20 }
  - { offset: 0x68BB5, size: 0x8, addend: 0x0, symName: '_$s8printing16CustomPrintPaperC13printableRectSo6CGRectVvg', symObjAddr: 0x60, symBinAddr: 0x1460, symSize: 0x30 }
  - { offset: 0x68BD8, size: 0x8, addend: 0x0, symName: '_$s8printing16CustomPrintPaperCACycfC', symObjAddr: 0x90, symBinAddr: 0x1490, symSize: 0x20 }
  - { offset: 0x68BF2, size: 0x8, addend: 0x0, symName: '_$s8printing16CustomPrintPaperCACycfc', symObjAddr: 0xB0, symBinAddr: 0x14B0, symSize: 0x30 }
  - { offset: 0x68C4B, size: 0x8, addend: 0x0, symName: '_$s8printing16CustomPrintPaperCACycfcTo', symObjAddr: 0xE0, symBinAddr: 0x14E0, symSize: 0x30 }
  - { offset: 0x68CAA, size: 0x8, addend: 0x0, symName: '_$s8printing16CustomPrintPaperCfD', symObjAddr: 0x110, symBinAddr: 0x1510, symSize: 0x30 }
  - { offset: 0x68E21, size: 0x8, addend: 0x0, symName: '_$s8printing14PrintingPluginC8instance33_6FF184931A26925A7B008FED8A176594LLACSgvpZ', symObjAddr: 0x7CD0, symBinAddr: 0x17260, symSize: 0x0 }
  - { offset: 0x692DA, size: 0x8, addend: 0x0, symName: '_$syXlSgIeyBy_ypSgIegn_TR', symObjAddr: 0x28B0, symBinAddr: 0x4070, symSize: 0xB0 }
  - { offset: 0x69388, size: 0x8, addend: 0x0, symName: '_$s8printing14PrintingPluginCfETo', symObjAddr: 0x3630, symBinAddr: 0x4DF0, symSize: 0x30 }
  - { offset: 0x693C2, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4findys10_HashTableV6BucketV6bucket_Sb5foundtxSHRzlFs6UInt32V_Tg5', symObjAddr: 0x3660, symBinAddr: 0x4E20, symSize: 0x30 }
  - { offset: 0x693FA, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4findys10_HashTableV6BucketV6bucket_Sb5foundtxSHRzlFSS_Tg5', symObjAddr: 0x3690, symBinAddr: 0x4E50, symSize: 0x60 }
  - { offset: 0x69432, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4find_9hashValues10_HashTableV6BucketV6bucket_Sb5foundtx_SitSHRzlFs6UInt32V_Tg5', symObjAddr: 0x36F0, symBinAddr: 0x4EB0, symSize: 0x90 }
  - { offset: 0x69462, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4find_9hashValues10_HashTableV6BucketV6bucket_Sb5foundtx_SitSHRzlFSS_Tg5', symObjAddr: 0x3780, symBinAddr: 0x4F40, symSize: 0xF0 }
  - { offset: 0x69532, size: 0x8, addend: 0x0, symName: '_$sSD8_VariantV8setValue_6forKeyyq_n_xtFSS_So9UIPrinterCTg5', symObjAddr: 0x3870, symBinAddr: 0x5030, symSize: 0xC0 }
  - { offset: 0x6965D, size: 0x8, addend: 0x0, symName: '_$sSD8_VariantV8setValue_6forKeyyq_n_xtFs6UInt32V_8printing8PrintJobCTg5', symObjAddr: 0x3930, symBinAddr: 0x50F0, symSize: 0xB0 }
  - { offset: 0x69751, size: 0x8, addend: 0x0, symName: '_$sSD8_VariantV11removeValue6forKeyq_Sgx_tFs6UInt32V_8printing8PrintJobCTg5', symObjAddr: 0x39E0, symBinAddr: 0x51A0, symSize: 0x80 }
  - { offset: 0x697FF, size: 0x8, addend: 0x0, symName: '_$sSD8_VariantV11removeValue6forKeyq_Sgx_tFSS_So9UIPrinterCTg5', symObjAddr: 0x3A60, symBinAddr: 0x5220, symSize: 0xB0 }
  - { offset: 0x698A1, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV12mutatingFind_8isUniques10_HashTableV6BucketV6bucket_Sb5foundtx_SbtFSS_So9UIPrinterCTg5', symObjAddr: 0x3B10, symBinAddr: 0x52D0, symSize: 0xC0 }
  - { offset: 0x698DA, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV12mutatingFind_8isUniques10_HashTableV6BucketV6bucket_Sb5foundtx_SbtFs6UInt32V_8printing8PrintJobCTg5', symObjAddr: 0x3BD0, symBinAddr: 0x5390, symSize: 0xB0 }
  - { offset: 0x6991E, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV4copyyyFs6UInt32V_8printing8PrintJobCTg5', symObjAddr: 0x3C80, symBinAddr: 0x5440, symSize: 0x1E0 }
  - { offset: 0x699D7, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV4copyyyFSS_So9UIPrinterCTg5', symObjAddr: 0x3E60, symBinAddr: 0x5620, symSize: 0x200 }
  - { offset: 0x69AB1, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV20_copyOrMoveAndResize8capacity12moveElementsySi_SbtFs6UInt32V_8printing8PrintJobCTg5', symObjAddr: 0x4060, symBinAddr: 0x5820, symSize: 0x340 }
  - { offset: 0x69BAF, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV20_copyOrMoveAndResize8capacity12moveElementsySi_SbtFSS_So9UIPrinterCTg5', symObjAddr: 0x43A0, symBinAddr: 0x5B60, symSize: 0x3A0 }
  - { offset: 0x69CB8, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV7_delete2atys10_HashTableV6BucketV_tFs6UInt32V_8printing8PrintJobCTg5', symObjAddr: 0x4740, symBinAddr: 0x5F00, symSize: 0x170 }
  - { offset: 0x69D57, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV7_delete2atys10_HashTableV6BucketV_tFSS_So9UIPrinterCTg5', symObjAddr: 0x48B0, symBinAddr: 0x6070, symSize: 0x1D0 }
  - { offset: 0x69E05, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV06InlineB0VyAESWcfCTf4nd_n', symObjAddr: 0x4A80, symBinAddr: 0x6240, symSize: 0xB0 }
  - { offset: 0x69E7B, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV10LargeSliceVyAESWcfCTf4nd_n', symObjAddr: 0x4B30, symBinAddr: 0x62F0, symSize: 0x80 }
  - { offset: 0x69EA8, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV11InlineSliceVyAESWcfCTf4nd_n', symObjAddr: 0x4BB0, symBinAddr: 0x6370, symSize: 0x70 }
  - { offset: 0x69F1E, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV15_RepresentationOyAESWcfCTf4nd_n', symObjAddr: 0x4C20, symBinAddr: 0x63E0, symSize: 0x70 }
  - { offset: 0x69F9D, size: 0x8, addend: 0x0, symName: ___swift_instantiateConcreteTypeFromMangledName, symObjAddr: 0x4FD0, symBinAddr: 0x6790, symSize: 0x40 }
  - { offset: 0x69FB1, size: 0x8, addend: 0x0, symName: '_$sypWOb', symObjAddr: 0x5010, symBinAddr: 0x67D0, symSize: 0x20 }
  - { offset: 0x69FC5, size: 0x8, addend: 0x0, symName: '_$sypWOc', symObjAddr: 0x5030, symBinAddr: 0x67F0, symSize: 0x30 }
  - { offset: 0x69FD9, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV15_RepresentationOWOe', symObjAddr: 0x5060, symBinAddr: 0x6820, symSize: 0x40 }
  - { offset: 0x6A045, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV15_RepresentationOWOy', symObjAddr: 0x51E0, symBinAddr: 0x69A0, symSize: 0x40 }
  - { offset: 0x6A059, size: 0x8, addend: 0x0, symName: '_$s8printing14PrintingPluginCMa', symObjAddr: 0x5220, symBinAddr: 0x69E0, symSize: 0x20 }
  - { offset: 0x6A078, size: 0x8, addend: 0x0, symName: '_$syXlSgIeyBy_ypSgIegn_TRTA', symObjAddr: 0x7C30, symBinAddr: 0x93F0, symSize: 0x10 }
  - { offset: 0x6A08C, size: 0x8, addend: 0x0, symName: ___swift_project_boxed_opaque_existential_0, symObjAddr: 0x7C40, symBinAddr: 0x9400, symSize: 0x30 }
  - { offset: 0x6A0A0, size: 0x8, addend: 0x0, symName: ___swift_destroy_boxed_opaque_existential_0, symObjAddr: 0x7C70, symBinAddr: 0x9430, symSize: 0x30 }
  - { offset: 0x6A27E, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCSS_ypTt0g5Tf4g_n', symObjAddr: 0x50A0, symBinAddr: 0x6860, symSize: 0xE0 }
  - { offset: 0x6A451, size: 0x8, addend: 0x0, symName: '_$s8printing14PrintingPluginC4jobsSDys6UInt32VAA8PrintJobCGvg', symObjAddr: 0x0, symBinAddr: 0x17C0, symSize: 0x40 }
  - { offset: 0x6A474, size: 0x8, addend: 0x0, symName: '_$s8printing14PrintingPluginC4jobsSDys6UInt32VAA8PrintJobCGvs', symObjAddr: 0x40, symBinAddr: 0x1800, symSize: 0x50 }
  - { offset: 0x6A4A7, size: 0x8, addend: 0x0, symName: '_$s8printing14PrintingPluginC4jobsSDys6UInt32VAA8PrintJobCGvM', symObjAddr: 0x90, symBinAddr: 0x1850, symSize: 0x40 }
  - { offset: 0x6A4C6, size: 0x8, addend: 0x0, symName: '_$s8printing14PrintingPluginC4jobsSDys6UInt32VAA8PrintJobCGvM.resume.0', symObjAddr: 0xD0, symBinAddr: 0x1890, symSize: 0x10 }
  - { offset: 0x6A4ED, size: 0x8, addend: 0x0, symName: '_$s8printing14PrintingPluginC11setDocument3job3doc4sizeys6UInt32V_SPys5UInt8VGs6UInt64VtFZ', symObjAddr: 0xE0, symBinAddr: 0x18A0, symSize: 0x10 }
  - { offset: 0x6A50E, size: 0x8, addend: 0x0, symName: '_$s8printing14PrintingPluginC11setDocument3job3doc4sizeys6UInt32V_SPys5UInt8VGs6UInt64VtFZTo', symObjAddr: 0xF0, symBinAddr: 0x18B0, symSize: 0x20 }
  - { offset: 0x6A538, size: 0x8, addend: 0x0, symName: '_$s8printing14PrintingPluginC8setError3job7messageys6UInt32V_SPys4Int8VGtFZ', symObjAddr: 0x110, symBinAddr: 0x18D0, symSize: 0x10 }
  - { offset: 0x6A553, size: 0x8, addend: 0x0, symName: '_$s8printing14PrintingPluginC8setError3job7messageys6UInt32V_SPys4Int8VGtFZTo', symObjAddr: 0x120, symBinAddr: 0x18E0, symSize: 0x10 }
  - { offset: 0x6A622, size: 0x8, addend: 0x0, symName: '_$s8printing14PrintingPluginC8register4withySo07FlutterC9Registrar_p_tFZ', symObjAddr: 0x130, symBinAddr: 0x18F0, symSize: 0x10 }
  - { offset: 0x6A63D, size: 0x8, addend: 0x0, symName: '_$s8printing14PrintingPluginC8register4withySo07FlutterC9Registrar_p_tFZTo', symObjAddr: 0x140, symBinAddr: 0x1900, symSize: 0x30 }
  - { offset: 0x6A7B9, size: 0x8, addend: 0x0, symName: '_$s8printing14PrintingPluginC6handle_6resultySo17FlutterMethodCallC_yypSgctF', symObjAddr: 0x170, symBinAddr: 0x1930, symSize: 0x26C0 }
  - { offset: 0x6B26C, size: 0x8, addend: 0x0, symName: '_$s8printing14PrintingPluginC6handle_6resultySo17FlutterMethodCallC_yypSgctFTo', symObjAddr: 0x2830, symBinAddr: 0x3FF0, symSize: 0x80 }
  - { offset: 0x6B2F5, size: 0x8, addend: 0x0, symName: '_$s8printing14PrintingPluginC8onLayout8printJob5width6height10marginLeft0J3Top0J5Right0J6BottomyAA05PrintG0C_12CoreGraphics7CGFloatVA5PtF', symObjAddr: 0x2960, symBinAddr: 0x4120, symSize: 0x2C0 }
  - { offset: 0x6B4B1, size: 0x8, addend: 0x0, symName: '_$s8printing14PrintingPluginC11onCompleted8printJob9completed5erroryAA05PrintG0C_SbSo8NSStringCSgtF', symObjAddr: 0x2C20, symBinAddr: 0x43E0, symSize: 0x230 }
  - { offset: 0x6B665, size: 0x8, addend: 0x0, symName: '_$s8printing14PrintingPluginC14onHtmlRendered8printJob7pdfDatayAA05PrintH0C_10Foundation0J0VtF', symObjAddr: 0x2E50, symBinAddr: 0x4610, symSize: 0x1D0 }
  - { offset: 0x6B76A, size: 0x8, addend: 0x0, symName: '_$s8printing14PrintingPluginC11onHtmlError8printJob5erroryAA05PrintH0C_SStF', symObjAddr: 0x3020, symBinAddr: 0x47E0, symSize: 0x170 }
  - { offset: 0x6B856, size: 0x8, addend: 0x0, symName: '_$s8printing14PrintingPluginC16onPageRasterized8printJob9imageData5width6heightyAA05PrintH0C_10Foundation0J0VS2itF', symObjAddr: 0x3190, symBinAddr: 0x4950, symSize: 0x260 }
  - { offset: 0x6B990, size: 0x8, addend: 0x0, symName: '_$s8printing14PrintingPluginC15onPageRasterEnd8printJob5erroryAA05PrintI0C_SSSgtF', symObjAddr: 0x33F0, symBinAddr: 0x4BB0, symSize: 0x190 }
  - { offset: 0x6BA7E, size: 0x8, addend: 0x0, symName: '_$s8printing14PrintingPluginCACycfC', symObjAddr: 0x3580, symBinAddr: 0x4D40, symSize: 0x20 }
  - { offset: 0x6BA92, size: 0x8, addend: 0x0, symName: '_$s8printing14PrintingPluginCACycfc', symObjAddr: 0x35A0, symBinAddr: 0x4D60, symSize: 0x30 }
  - { offset: 0x6BAEB, size: 0x8, addend: 0x0, symName: '_$s8printing14PrintingPluginCACycfcTo', symObjAddr: 0x35D0, symBinAddr: 0x4D90, symSize: 0x30 }
  - { offset: 0x6BB4A, size: 0x8, addend: 0x0, symName: '_$s8printing14PrintingPluginCfD', symObjAddr: 0x3600, symBinAddr: 0x4DC0, symSize: 0x30 }
  - { offset: 0x6BBEA, size: 0x8, addend: 0x0, symName: '_$s8printing14PrintingPluginC11setDocument3job3doc4sizeys6UInt32V_SPys5UInt8VGs6UInt64VtFZTf4nnnd_n', symObjAddr: 0x4C90, symBinAddr: 0x6450, symSize: 0xC0 }
  - { offset: 0x6BD5B, size: 0x8, addend: 0x0, symName: '_$s8printing14PrintingPluginC8setError3job7messageys6UInt32V_SPys4Int8VGtFZTf4nnd_n', symObjAddr: 0x4D50, symBinAddr: 0x6510, symSize: 0x130 }
  - { offset: 0x6BE7B, size: 0x8, addend: 0x0, symName: '_$s8printing14PrintingPluginC8register4withySo07FlutterC9Registrar_p_tFZTf4nd_n', symObjAddr: 0x4E80, symBinAddr: 0x6640, symSize: 0x150 }
  - { offset: 0x6BF31, size: 0x8, addend: 0x0, symName: '_$s8printing14PrintingPluginC6handle_6resultySo17FlutterMethodCallC_yypSgctF015$syXlSgIeyBy_ypK7Iegn_TRyXlSgIeyBy_Tf1ncn_nTf4nng_n', symObjAddr: 0x5240, symBinAddr: 0x6A00, symSize: 0x29D0 }
  - { offset: 0x6CA96, size: 0x8, addend: 0x0, symName: '_$s8printing16selectedPrintersSDySSSo9UIPrinterCGvp', symObjAddr: 0x21708, symBinAddr: 0x17650, symSize: 0x0 }
  - { offset: 0x6CAB0, size: 0x8, addend: 0x0, symName: '_$s8printing13pickedPrinterSo9UIPrinterCSgvp', symObjAddr: 0x21710, symBinAddr: 0x17658, symSize: 0x0 }
  - { offset: 0x6CFB1, size: 0x8, addend: 0x0, symName: '_$s8printing16selectedPrinters_WZ', symObjAddr: 0x50, symBinAddr: 0x94E0, symSize: 0x20 }
  - { offset: 0x6D001, size: 0x8, addend: 0x0, symName: '_$s8printing31dataProviderReleaseDataCallback4info0B04sizeySvSg_SVSitFTo', symObjAddr: 0x500, symBinAddr: 0x9980, symSize: 0x20 }
  - { offset: 0x6D082, size: 0x8, addend: 0x0, symName: '_$sSbIegy_SbIeyBy_TR', symObjAddr: 0xB90, symBinAddr: 0xA010, symSize: 0x40 }
  - { offset: 0x6D181, size: 0x8, addend: 0x0, symName: '_$sSaySo19WKWebsiteDataRecordCGIeghg_So7NSArrayCIeyBhy_TR', symObjAddr: 0x2EB0, symBinAddr: 0xC330, symSize: 0x60 }
  - { offset: 0x6D2E3, size: 0x8, addend: 0x0, symName: '_$sIeg_IyB_TR', symObjAddr: 0x42A0, symBinAddr: 0xD720, symSize: 0x20 }
  - { offset: 0x6D311, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobCfETo', symObjAddr: 0x43B0, symBinAddr: 0xD830, symSize: 0x80 }
  - { offset: 0x6D34B, size: 0x8, addend: 0x0, symName: '_$ss29getContiguousArrayStorageType3fors01_bcD0CyxGmxm_tlFSo19WKWebsiteDataRecordC_Ttg5', symObjAddr: 0x4430, symBinAddr: 0xD8B0, symSize: 0x60 }
  - { offset: 0x6D38A, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV15_RepresentationO22withUnsafeMutableBytesyxxSwKXEKlFyt_Tgq5015$s10Foundation4B42V9repeating5countACs5UInt8V_SitcfcySwXEfU_s0L0VTf1ncn_n', symObjAddr: 0x4680, symBinAddr: 0xDB00, symSize: 0x2D0 }
  - { offset: 0x6D45D, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV15_RepresentationO22withUnsafeMutableBytesyxxSwKXEKlFyt_Tg5062$s8printing8PrintJobC9rasterPdf4data5pages5scaley10Foundation4B51V_SaySiGSg12CoreGraphics7CGFloatVtFyyYbcfU_ySwXEfU_S3i0Q8Graphics0S0VAJSo6CGRectVA2JSo12CGPDFPageRefaTf1ncn_n', symObjAddr: 0x4950, symBinAddr: 0xDDD0, symSize: 0x590 }
  - { offset: 0x6D51C, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV11InlineSliceV22withUnsafeMutableBytesyxxSwKXEKlFyt_Tg5062$s8printing8PrintJobC9rasterPdf4data5pages5scaley10Foundation4B51V_SaySiGSg12CoreGraphics7CGFloatVtFyyYbcfU_ySwXEfU_S3i0R8Graphics0T0VAJSo6CGRectVA2JSo12CGPDFPageRefaTf1ncn_n', symObjAddr: 0x4EE0, symBinAddr: 0xE360, symSize: 0x140 }
  - { offset: 0x6D584, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV11InlineSliceV22withUnsafeMutableBytesyxxSwKXEKlFyt_Tgq5015$s10Foundation4B42V9repeating5countACs5UInt8V_SitcfcySwXEfU_s0M0VTf1ncn_n', symObjAddr: 0x5020, symBinAddr: 0xE4A0, symSize: 0x90 }
  - { offset: 0x6D64B, size: 0x8, addend: 0x0, symName: '_$sSTsE13_copyContents12initializing8IteratorQz_SitSry7ElementQzG_tFSNySiG_Tg5', symObjAddr: 0x50B0, symBinAddr: 0xE530, symSize: 0x90 }
  - { offset: 0x6D741, size: 0x8, addend: 0x0, symName: '_$ss22_ContiguousArrayBufferV19_uninitializedCount15minimumCapacityAByxGSi_SitcfCSi_Tt1g5', symObjAddr: 0x5140, symBinAddr: 0xE5C0, symSize: 0x80 }
  - { offset: 0x6D88B, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobC9rasterPdf4data5pages5scaley10Foundation4DataV_SaySiGSg12CoreGraphics7CGFloatVtFyyYbcfU_TA', symObjAddr: 0x6380, symBinAddr: 0xF800, symSize: 0x20 }
  - { offset: 0x6D89F, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x63A0, symBinAddr: 0xF820, symSize: 0x20 }
  - { offset: 0x6D8B3, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x63C0, symBinAddr: 0xF840, symSize: 0x10 }
  - { offset: 0x6D8C7, size: 0x8, addend: 0x0, symName: '_$sSay8Dispatch0A13WorkItemFlagsVGSayxGSTsWl', symObjAddr: 0x6410, symBinAddr: 0xF850, symSize: 0x40 }
  - { offset: 0x6D8DB, size: 0x8, addend: 0x0, symName: ___swift_instantiateConcreteTypeFromMangledNameAbstract, symObjAddr: 0x6450, symBinAddr: 0xF890, symSize: 0x40 }
  - { offset: 0x6D8EF, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobCMa', symObjAddr: 0x6490, symBinAddr: 0xF8D0, symSize: 0x20 }
  - { offset: 0x6D903, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobC11setDocumentyy10Foundation4DataVSgFyyScMYccfU_TA', symObjAddr: 0x64B0, symBinAddr: 0xF8F0, symSize: 0x10 }
  - { offset: 0x6D917, size: 0x8, addend: 0x0, symName: '_$s10Foundation3URLVSgWOc', symObjAddr: 0x64C0, symBinAddr: 0xF900, symSize: 0x40 }
  - { offset: 0x6D92B, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobC11setDocumentyy10Foundation4DataVSgFyyScMYccfU_ySbcfU_TA', symObjAddr: 0x6510, symBinAddr: 0xF950, symSize: 0x40 }
  - { offset: 0x6D93F, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobC11setDocumentyy10Foundation4DataVSgFyyScMYccfU_ySbcfU_ySo28UIPrintInteractionControllerC_Sbs5Error_pSgtcACcfu_yAJ_SbALtcfu0_TA', symObjAddr: 0x6580, symBinAddr: 0xF9C0, symSize: 0x20 }
  - { offset: 0x6D996, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobC9rasterPdf4data5pages5scaley10Foundation4DataV_SaySiGSg12CoreGraphics7CGFloatVtFyyYbcfU_yyScMYcXEfU1_TA', symObjAddr: 0x65A0, symBinAddr: 0xF9E0, symSize: 0x10 }
  - { offset: 0x6D9B5, size: 0x8, addend: 0x0, symName: '_$sIg_Ieg_TRTA', symObjAddr: 0x65D0, symBinAddr: 0xFA10, symSize: 0x20 }
  - { offset: 0x6D9DE, size: 0x8, addend: 0x0, symName: '_$sIeg_SgWOe', symObjAddr: 0x65F0, symBinAddr: 0xFA30, symSize: 0x20 }
  - { offset: 0x6D9F2, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobC9rasterPdf4data5pages5scaley10Foundation4DataV_SaySiGSg12CoreGraphics7CGFloatVtFyyYbcfU_yyScMYcXEfU0_TA', symObjAddr: 0x6640, symBinAddr: 0xFA80, symSize: 0x20 }
  - { offset: 0x6DA06, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobC8printPdf4name12withPageSize9andMargin0G7Printer11dynamically10outputType011forceCustomB5PaperySS_So6CGSizeVSo6CGRectVSSSgSbSo017UIPrintInfoOutputO0VSbtFySbcfU_TA', symObjAddr: 0x6720, symBinAddr: 0xFAE0, symSize: 0x40 }
  - { offset: 0x6DA1A, size: 0x8, addend: 0x0, symName: ___swift_allocate_boxed_opaque_existential_0, symObjAddr: 0x6790, symBinAddr: 0xFB50, symSize: 0x30 }
  - { offset: 0x6DA2E, size: 0x8, addend: 0x0, symName: _keypath_get_selector_isLoading, symObjAddr: 0x67C0, symBinAddr: 0xFB80, symSize: 0x10 }
  - { offset: 0x6DA42, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobC11convertHtml_12withPageSize9andMargin0I7BaseUrlySS_So6CGRectVAI10Foundation3URLVSgtFySo9WKWebViewC_AJ24NSKeyValueObservedChangeVySbGtYbcfU_TA', symObjAddr: 0x6810, symBinAddr: 0xFBD0, symSize: 0x50 }
  - { offset: 0x6DA56, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobC11convertHtml_12withPageSize9andMargin0I7BaseUrlySS_So6CGRectVAI10Foundation3URLVSgtFySo9WKWebViewC_AJ24NSKeyValueObservedChangeVySbGtYbcfU_yyScMYccfU_TA', symObjAddr: 0x68A0, symBinAddr: 0xFC60, symSize: 0x50 }
  - { offset: 0x6DA6A, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobC11pickPrinter6result14withSourceRectyyypSgc_So6CGRectVtFZySo25UIPrinterPickerControllerC_Sbs5Error_pSgtcfU_TA', symObjAddr: 0x6910, symBinAddr: 0xFCD0, symSize: 0x20 }
  - { offset: 0x6E0A8, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobCAA5indexAcA14PrintingPluginC_SitcfC', symObjAddr: 0x0, symBinAddr: 0x9490, symSize: 0x40 }
  - { offset: 0x6E131, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobC0A4InfoSo12NSDictionaryCyFZ', symObjAddr: 0x40, symBinAddr: 0x94D0, symSize: 0x10 }
  - { offset: 0x6E145, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobC5indexSivg', symObjAddr: 0x70, symBinAddr: 0x9500, symSize: 0x30 }
  - { offset: 0x6E16E, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobC5indexSivs', symObjAddr: 0xA0, symBinAddr: 0x9530, symSize: 0x40 }
  - { offset: 0x6E1A1, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobC5indexSivM', symObjAddr: 0xE0, symBinAddr: 0x9570, symSize: 0x40 }
  - { offset: 0x6E1C6, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobCAA5indexAcA14PrintingPluginC_Sitcfc', symObjAddr: 0x130, symBinAddr: 0x95B0, symSize: 0x30 }
  - { offset: 0x6E20A, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobC8drawPage2at2inySi_So6CGRectVtF', symObjAddr: 0x160, symBinAddr: 0x95E0, symSize: 0x10 }
  - { offset: 0x6E225, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobC8drawPage2at2inySi_So6CGRectVtFTo', symObjAddr: 0x170, symBinAddr: 0x95F0, symSize: 0x30 }
  - { offset: 0x6E404, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobC11setDocumentyy10Foundation4DataVSgF', symObjAddr: 0x1A0, symBinAddr: 0x9620, symSize: 0x360 }
  - { offset: 0x6E574, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobC11setDocumentyy10Foundation4DataVSgFyyScMYccfU_', symObjAddr: 0x520, symBinAddr: 0x99A0, symSize: 0x670 }
  - { offset: 0x6E851, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobC13numberOfPagesSivgTo', symObjAddr: 0xBE0, symBinAddr: 0xA060, symSize: 0x30 }
  - { offset: 0x6E879, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobC13numberOfPagesSivg', symObjAddr: 0xC10, symBinAddr: 0xA090, symSize: 0x1D0 }
  - { offset: 0x6E8F8, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobC26printInteractionController_11choosePaperSo07UIPrintH0CSo0ieF0C_SayAGGtF', symObjAddr: 0xDE0, symBinAddr: 0xA260, symSize: 0x10 }
  - { offset: 0x6E913, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobC26printInteractionController_11choosePaperSo07UIPrintH0CSo0ieF0C_SayAGGtFTo', symObjAddr: 0xDF0, symBinAddr: 0xA270, symSize: 0x90 }
  - { offset: 0x6E9EC, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobC8printPdf4name12withPageSize9andMargin0G7Printer11dynamically10outputType011forceCustomB5PaperySS_So6CGSizeVSo6CGRectVSSSgSbSo017UIPrintInfoOutputO0VSbtF', symObjAddr: 0xE80, symBinAddr: 0xA300, symSize: 0xE80 }
  - { offset: 0x6EF79, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobC11convertHtml_12withPageSize9andMargin0I7BaseUrlySS_So6CGRectVAI10Foundation3URLVSgtF', symObjAddr: 0x1F10, symBinAddr: 0xB390, symSize: 0x520 }
  - { offset: 0x6F0B1, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobC11convertHtml_12withPageSize9andMargin0I7BaseUrlySS_So6CGRectVAI10Foundation3URLVSgtFySo9WKWebViewC_AJ24NSKeyValueObservedChangeVySbGtYbcfU_', symObjAddr: 0x2430, symBinAddr: 0xB8B0, symSize: 0x330 }
  - { offset: 0x6F15F, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobC11convertHtml_12withPageSize9andMargin0I7BaseUrlySS_So6CGRectVAI10Foundation3URLVSgtFySo9WKWebViewC_AJ24NSKeyValueObservedChangeVySbGtYbcfU_yyScMYccfU_', symObjAddr: 0x2760, symBinAddr: 0xBBE0, symSize: 0x490 }
  - { offset: 0x6F35D, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobC11convertHtml_12withPageSize9andMargin0I7BaseUrlySS_So6CGRectVAI10Foundation3URLVSgtFySo9WKWebViewC_AJ24NSKeyValueObservedChangeVySbGtYbcfU_yyScMYccfU_ySaySo19WKWebsiteDataRecordCGYbScMYccfU_', symObjAddr: 0x2BF0, symBinAddr: 0xC070, symSize: 0x280 }
  - { offset: 0x6F535, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobC11convertHtml_12withPageSize9andMargin0I7BaseUrlySS_So6CGRectVAI10Foundation3URLVSgtFySo9WKWebViewC_AJ24NSKeyValueObservedChangeVySbGtYbcfU_yyScMYccfU_ySaySo19WKWebsiteDataRecordCGYbScMYccfU_yyYbScMYccfU_', symObjAddr: 0x2E70, symBinAddr: 0xC2F0, symSize: 0x10 }
  - { offset: 0x6F5CB, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobC11pickPrinter6result14withSourceRectyyypSgc_So6CGRectVtFZySo25UIPrinterPickerControllerC_Sbs5Error_pSgtcfU_', symObjAddr: 0x2F10, symBinAddr: 0xC390, symSize: 0x4C0 }
  - { offset: 0x6F7AA, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobC9rasterPdf4data5pages5scaley10Foundation4DataV_SaySiGSg12CoreGraphics7CGFloatVtF', symObjAddr: 0x3460, symBinAddr: 0xC8E0, symSize: 0x320 }
  - { offset: 0x6F8AB, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobC9rasterPdf4data5pages5scaley10Foundation4DataV_SaySiGSg12CoreGraphics7CGFloatVtFyyYbcfU_', symObjAddr: 0x3780, symBinAddr: 0xCC00, symSize: 0x8C0 }
  - { offset: 0x6FDAD, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobC9rasterPdf4data5pages5scaley10Foundation4DataV_SaySiGSg12CoreGraphics7CGFloatVtFyyYbcfU_ySwXEfU_', symObjAddr: 0x4040, symBinAddr: 0xD4C0, symSize: 0x1E0 }
  - { offset: 0x6FF01, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobC9rasterPdf4data5pages5scaley10Foundation4DataV_SaySiGSg12CoreGraphics7CGFloatVtFyyYbcfU_yyScMYcXEfU0_', symObjAddr: 0x4220, symBinAddr: 0xD6A0, symSize: 0x80 }
  - { offset: 0x6FF80, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobC9rasterPdf4data5pages5scaley10Foundation4DataV_SaySiGSg12CoreGraphics7CGFloatVtFyyYbcfU_yyScMYcXEfU1_', symObjAddr: 0x42C0, symBinAddr: 0xD740, symSize: 0x40 }
  - { offset: 0x70039, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobCACycfC', symObjAddr: 0x4300, symBinAddr: 0xD780, symSize: 0x20 }
  - { offset: 0x7004D, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobCACycfc', symObjAddr: 0x4320, symBinAddr: 0xD7A0, symSize: 0x30 }
  - { offset: 0x700A6, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobCACycfcTo', symObjAddr: 0x4350, symBinAddr: 0xD7D0, symSize: 0x30 }
  - { offset: 0x70105, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobCfD', symObjAddr: 0x4380, symBinAddr: 0xD800, symSize: 0x30 }
  - { offset: 0x70128, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobCAA5indexAcA14PrintingPluginC_SitcfcTf4gnn_n', symObjAddr: 0x51C0, symBinAddr: 0xE640, symSize: 0x130 }
  - { offset: 0x70181, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobC8sharePdf4data14withSourceRect7andName7subject4bodyy10Foundation4DataV_So6CGRectVS2SSgAOtFZTf4nnnnnd_n', symObjAddr: 0x52F0, symBinAddr: 0xE770, symSize: 0x5C0 }
  - { offset: 0x70402, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobC11pickPrinter6result14withSourceRectyyypSgc_So6CGRectVtFZTf4nnd_n', symObjAddr: 0x58B0, symBinAddr: 0xED30, symSize: 0x2E0 }
  - { offset: 0x704D6, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobC0A4InfoSo12NSDictionaryCyFZTf4d_n', symObjAddr: 0x5B90, symBinAddr: 0xF010, symSize: 0x1A0 }
  - { offset: 0x7055E, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobC8drawPage2at2inySi_So6CGRectVtFTf4ndn_n', symObjAddr: 0x5D30, symBinAddr: 0xF1B0, symSize: 0x110 }
  - { offset: 0x705F2, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobC17completionHandler15printController9completed5errorySo018UIPrintInteractionG0C_Sbs5Error_pSgtFTf4dnnn_n', symObjAddr: 0x5E40, symBinAddr: 0xF2C0, symSize: 0x1F0 }
  - { offset: 0x7079C, size: 0x8, addend: 0x0, symName: '_$s8printing8PrintJobC26printInteractionController_11choosePaperSo07UIPrintH0CSo0ieF0C_SayAGGtFTf4dnn_n', symObjAddr: 0x6030, symBinAddr: 0xF4B0, symSize: 0x310 }
...
