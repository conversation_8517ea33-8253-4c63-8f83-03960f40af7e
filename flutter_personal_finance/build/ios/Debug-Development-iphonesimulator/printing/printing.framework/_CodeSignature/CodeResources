<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Headers/printing-Swift.h</key>
		<data>
		MD9sla/zJCsSsd9/Qaul6x4Be6g=
		</data>
		<key>Headers/printing-umbrella.h</key>
		<data>
		vn2Rjb9gEvH7Gst7AhSgLU2lEXc=
		</data>
		<key>Info.plist</key>
		<data>
		ib1RT8lwL3jLXgdMvZDCNqnxJfo=
		</data>
		<key>Modules/module.modulemap</key>
		<data>
		2goXJC7AOS/cus+7hNT39aoX3xU=
		</data>
		<key>Modules/printing.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo</key>
		<data>
		VaxEGVuh2Ouif02oJbKvn7dOZ78=
		</data>
		<key>Modules/printing.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo</key>
		<data>
		O47jvVk2dkBTpKa9xVTdUcPved8=
		</data>
		<key>Modules/printing.swiftmodule/arm64-apple-ios-simulator.abi.json</key>
		<data>
		gcwBsH4BgyFY4sVtNt+/xOKS3vY=
		</data>
		<key>Modules/printing.swiftmodule/arm64-apple-ios-simulator.swiftdoc</key>
		<data>
		nhKnNU08pM0XQXpwGZHstRz8d/Q=
		</data>
		<key>Modules/printing.swiftmodule/arm64-apple-ios-simulator.swiftmodule</key>
		<data>
		DGLGqFLjX7Etoe3nbLO4/vX3Xmw=
		</data>
		<key>Modules/printing.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<data>
		gcwBsH4BgyFY4sVtNt+/xOKS3vY=
		</data>
		<key>Modules/printing.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<data>
		KKAqFwvil4+pqLhrvB9+b929G+w=
		</data>
		<key>Modules/printing.swiftmodule/x86_64-apple-ios-simulator.swiftmodule</key>
		<data>
		3rxjah40n29hQ25wW0y8PKPAXHI=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/printing-Swift.h</key>
		<dict>
			<key>hash2</key>
			<data>
			1GyXhLAimX/UCFAqB0+hYW1Rvu3v3gAJievLRJ+XySE=
			</data>
		</dict>
		<key>Headers/printing-umbrella.h</key>
		<dict>
			<key>hash2</key>
			<data>
			hi+bLtzcGjN0wwuPcl987vT/cG6HSgBm566qmlZXl6M=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash2</key>
			<data>
			2RqaHDwS0Bkx2ck8xt2YAeQPwze2WjrPIWEoEvLyUwU=
			</data>
		</dict>
		<key>Modules/printing.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo</key>
		<dict>
			<key>hash2</key>
			<data>
			gfkfhHGk/FGK8+Zv36kZvAI+eOX8tQGl8LdM6g0LT6Y=
			</data>
		</dict>
		<key>Modules/printing.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo</key>
		<dict>
			<key>hash2</key>
			<data>
			qLdo5fo4KFt95/uAMoYsDk8dNs7UhAx9+faWjqcmGQw=
			</data>
		</dict>
		<key>Modules/printing.swiftmodule/arm64-apple-ios-simulator.abi.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Qnesa0n4URGWAopawg9bGx36dUwkYV00BoCJ8LFzlyg=
			</data>
		</dict>
		<key>Modules/printing.swiftmodule/arm64-apple-ios-simulator.swiftdoc</key>
		<dict>
			<key>hash2</key>
			<data>
			xkp6tUaEbIrAtO02wfXGeM6oj4+QyyFrbATeDdc1x7k=
			</data>
		</dict>
		<key>Modules/printing.swiftmodule/arm64-apple-ios-simulator.swiftmodule</key>
		<dict>
			<key>hash2</key>
			<data>
			lMdzkKRorlreEa+pzAnkiK+hpT1Lk4UW31IElh1Sohs=
			</data>
		</dict>
		<key>Modules/printing.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Qnesa0n4URGWAopawg9bGx36dUwkYV00BoCJ8LFzlyg=
			</data>
		</dict>
		<key>Modules/printing.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<dict>
			<key>hash2</key>
			<data>
			XrFAyYNl3ZuKlhvmbSG8v2CBfzxD4dygNatqpo0lg8g=
			</data>
		</dict>
		<key>Modules/printing.swiftmodule/x86_64-apple-ios-simulator.swiftmodule</key>
		<dict>
			<key>hash2</key>
			<data>
			QQ34z3uS0WsXNk4ExiqKTx0SOJgix1qIDSmKrJNaj1s=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
