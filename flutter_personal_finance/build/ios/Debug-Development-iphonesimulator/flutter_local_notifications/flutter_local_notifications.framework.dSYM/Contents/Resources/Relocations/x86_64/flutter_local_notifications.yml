---
triple:          'x86_64-apple-darwin'
binary-path:     '/Users/<USER>/Documents/manish/flutter_personal_finance_app/flutter_personal_finance/build/ios/Debug-Development-iphonesimulator/flutter_local_notifications/flutter_local_notifications.framework/flutter_local_notifications'
relocations:
  - { offset: 0x5F958, size: 0x8, addend: 0x0, symName: _flutter_local_notificationsVersionString, symObjAddr: 0x0, symBinAddr: 0x7F20, symSize: 0x0 }
  - { offset: 0x5F98D, size: 0x8, addend: 0x0, symName: _flutter_local_notificationsVersionNumber, symObjAddr: 0x40, symBinAddr: 0x7F60, symSize: 0x0 }
  - { offset: 0x5F9CA, size: 0x8, addend: 0x0, symName: '-[ActionEventSink init]', symObjAddr: 0x0, symBinAddr: 0xCE8, symSize: 0x66 }
  - { offset: 0x5FA3F, size: 0x8, addend: 0x0, symName: '-[ActionEventSink init]', symObjAddr: 0x0, symBinAddr: 0xCE8, symSize: 0x66 }
  - { offset: 0x5FA8E, size: 0x8, addend: 0x0, symName: '-[ActionEventSink addItem:]', symObjAddr: 0x66, symBinAddr: 0xD4E, symSize: 0x2C }
  - { offset: 0x5FAE7, size: 0x8, addend: 0x0, symName: '-[ActionEventSink onListenWithArguments:eventSink:]', symObjAddr: 0x92, symBinAddr: 0xD7A, symSize: 0x156 }
  - { offset: 0x5FBFC, size: 0x8, addend: 0x0, symName: '-[ActionEventSink onCancelWithArguments:]', symObjAddr: 0x1E8, symBinAddr: 0xED0, symSize: 0x1D }
  - { offset: 0x5FC4B, size: 0x8, addend: 0x0, symName: '-[ActionEventSink .cxx_destruct]', symObjAddr: 0x205, symBinAddr: 0xEED, symSize: 0x28 }
  - { offset: 0x5FCE6, size: 0x8, addend: 0x0, symName: '+[Converters parseNotificationCategoryOptions:]', symObjAddr: 0x0, symBinAddr: 0xF15, symSize: 0x12F }
  - { offset: 0x5FD3A, size: 0x8, addend: 0x0, symName: '+[Converters parseNotificationCategoryOptions:]', symObjAddr: 0x0, symBinAddr: 0xF15, symSize: 0x12F }
  - { offset: 0x5FE13, size: 0x8, addend: 0x0, symName: '+[Converters parseNotificationActionOptions:]', symObjAddr: 0x12F, symBinAddr: 0x1044, symSize: 0x12F }
  - { offset: 0x5FF69, size: 0x8, addend: 0x0, symName: '+[FlutterEngineManager shouldAddAppDelegateToRegistrar:]', symObjAddr: 0x0, symBinAddr: 0x1173, symSize: 0x74 }
  - { offset: 0x5FF83, size: 0x8, addend: 0x0, symName: _backgroundEngine, symObjAddr: 0x34C8, symBinAddr: 0xE1E0, symSize: 0x0 }
  - { offset: 0x5FFB6, size: 0x8, addend: 0x0, symName: '+[FlutterEngineManager shouldAddAppDelegateToRegistrar:]', symObjAddr: 0x0, symBinAddr: 0x1173, symSize: 0x74 }
  - { offset: 0x60031, size: 0x8, addend: 0x0, symName: '-[FlutterEngineManager init]', symObjAddr: 0x74, symBinAddr: 0x11E7, symSize: 0x66 }
  - { offset: 0x60080, size: 0x8, addend: 0x0, symName: '-[FlutterEngineManager startEngineIfNeeded:registerPlugins:]', symObjAddr: 0xDA, symBinAddr: 0x124D, symSize: 0x1ED }
  - { offset: 0x602A4, size: 0x8, addend: 0x0, symName: '___60-[FlutterEngineManager startEngineIfNeeded:registerPlugins:]_block_invoke', symObjAddr: 0x2C7, symBinAddr: 0x143A, symSize: 0xB4 }
  - { offset: 0x60392, size: 0x8, addend: 0x0, symName: ___copy_helper_block_e8_32s40s48s, symObjAddr: 0x37B, symBinAddr: 0x14EE, symSize: 0x2C }
  - { offset: 0x603DF, size: 0x8, addend: 0x0, symName: ___destroy_helper_block_e8_32s40s48s, symObjAddr: 0x3A7, symBinAddr: 0x151A, symSize: 0x2C }
  - { offset: 0x60422, size: 0x8, addend: 0x0, symName: '-[FlutterEngineManager registerDispatcherHandle:callbackHandle:]', symObjAddr: 0x3D3, symBinAddr: 0x1546, symSize: 0x71 }
  - { offset: 0x604C5, size: 0x8, addend: 0x0, symName: '-[FlutterEngineManager getCallbackHandle]', symObjAddr: 0x444, symBinAddr: 0x15B7, symSize: 0x1D }
  - { offset: 0x60508, size: 0x8, addend: 0x0, symName: '-[FlutterEngineManager .cxx_destruct]', symObjAddr: 0x461, symBinAddr: 0x15D4, symSize: 0x10 }
  - { offset: 0x606C3, size: 0x8, addend: 0x0, symName: '+[FlutterLocalNotificationsPlugin registerWithRegistrar:]', symObjAddr: 0x0, symBinAddr: 0x15E4, symSize: 0xEE }
  - { offset: 0x606DD, size: 0x8, addend: 0x0, symName: _FOREGROUND_ACTION_IDENTIFIERS, symObjAddr: 0x76D0, symBinAddr: 0xB120, symSize: 0x0 }
  - { offset: 0x6072B, size: 0x8, addend: 0x0, symName: _INITIALIZE_METHOD, symObjAddr: 0x76D8, symBinAddr: 0xB128, symSize: 0x0 }
  - { offset: 0x60741, size: 0x8, addend: 0x0, symName: _GET_CALLBACK_METHOD, symObjAddr: 0x76E0, symBinAddr: 0xB130, symSize: 0x0 }
  - { offset: 0x60757, size: 0x8, addend: 0x0, symName: _SHOW_METHOD, symObjAddr: 0x76E8, symBinAddr: 0xB138, symSize: 0x0 }
  - { offset: 0x6076D, size: 0x8, addend: 0x0, symName: _ZONED_SCHEDULE_METHOD, symObjAddr: 0x76F0, symBinAddr: 0xB140, symSize: 0x0 }
  - { offset: 0x60783, size: 0x8, addend: 0x0, symName: _PERIODICALLY_SHOW_METHOD, symObjAddr: 0x76F8, symBinAddr: 0xB148, symSize: 0x0 }
  - { offset: 0x60799, size: 0x8, addend: 0x0, symName: _PERIODICALLY_SHOW_WITH_DURATION_METHOD, symObjAddr: 0x7700, symBinAddr: 0xB150, symSize: 0x0 }
  - { offset: 0x607AF, size: 0x8, addend: 0x0, symName: _CANCEL_METHOD, symObjAddr: 0x7708, symBinAddr: 0xB158, symSize: 0x0 }
  - { offset: 0x607C5, size: 0x8, addend: 0x0, symName: _CANCEL_ALL_METHOD, symObjAddr: 0x7710, symBinAddr: 0xB160, symSize: 0x0 }
  - { offset: 0x607DB, size: 0x8, addend: 0x0, symName: _PENDING_NOTIFICATIONS_REQUESTS_METHOD, symObjAddr: 0x7718, symBinAddr: 0xB168, symSize: 0x0 }
  - { offset: 0x607F1, size: 0x8, addend: 0x0, symName: _GET_ACTIVE_NOTIFICATIONS_METHOD, symObjAddr: 0x7720, symBinAddr: 0xB170, symSize: 0x0 }
  - { offset: 0x60807, size: 0x8, addend: 0x0, symName: _GET_NOTIFICATION_APP_LAUNCH_DETAILS_METHOD, symObjAddr: 0x7728, symBinAddr: 0xB178, symSize: 0x0 }
  - { offset: 0x6081D, size: 0x8, addend: 0x0, symName: _CHANNEL, symObjAddr: 0x7730, symBinAddr: 0xB180, symSize: 0x0 }
  - { offset: 0x60833, size: 0x8, addend: 0x0, symName: _CALLBACK_CHANNEL, symObjAddr: 0x7738, symBinAddr: 0xB188, symSize: 0x0 }
  - { offset: 0x60849, size: 0x8, addend: 0x0, symName: _ON_NOTIFICATION_METHOD, symObjAddr: 0x7740, symBinAddr: 0xB190, symSize: 0x0 }
  - { offset: 0x6085F, size: 0x8, addend: 0x0, symName: _DID_RECEIVE_LOCAL_NOTIFICATION, symObjAddr: 0x7748, symBinAddr: 0xB198, symSize: 0x0 }
  - { offset: 0x60875, size: 0x8, addend: 0x0, symName: _REQUEST_PERMISSIONS_METHOD, symObjAddr: 0x7750, symBinAddr: 0xB1A0, symSize: 0x0 }
  - { offset: 0x6088B, size: 0x8, addend: 0x0, symName: _CHECK_PERMISSIONS_METHOD, symObjAddr: 0x7758, symBinAddr: 0xB1A8, symSize: 0x0 }
  - { offset: 0x608A1, size: 0x8, addend: 0x0, symName: _DAY, symObjAddr: 0x7760, symBinAddr: 0xB1B0, symSize: 0x0 }
  - { offset: 0x608B7, size: 0x8, addend: 0x0, symName: _REQUEST_SOUND_PERMISSION, symObjAddr: 0x7768, symBinAddr: 0xB1B8, symSize: 0x0 }
  - { offset: 0x608CD, size: 0x8, addend: 0x0, symName: _REQUEST_ALERT_PERMISSION, symObjAddr: 0x7770, symBinAddr: 0xB1C0, symSize: 0x0 }
  - { offset: 0x608E3, size: 0x8, addend: 0x0, symName: _REQUEST_BADGE_PERMISSION, symObjAddr: 0x7778, symBinAddr: 0xB1C8, symSize: 0x0 }
  - { offset: 0x608F9, size: 0x8, addend: 0x0, symName: _REQUEST_PROVISIONAL_PERMISSION, symObjAddr: 0x7780, symBinAddr: 0xB1D0, symSize: 0x0 }
  - { offset: 0x6090F, size: 0x8, addend: 0x0, symName: _REQUEST_CRITICAL_PERMISSION, symObjAddr: 0x7788, symBinAddr: 0xB1D8, symSize: 0x0 }
  - { offset: 0x60925, size: 0x8, addend: 0x0, symName: _DEFAULT_PRESENT_ALERT, symObjAddr: 0x7790, symBinAddr: 0xB1E0, symSize: 0x0 }
  - { offset: 0x6093B, size: 0x8, addend: 0x0, symName: _DEFAULT_PRESENT_SOUND, symObjAddr: 0x7798, symBinAddr: 0xB1E8, symSize: 0x0 }
  - { offset: 0x60951, size: 0x8, addend: 0x0, symName: _DEFAULT_PRESENT_BADGE, symObjAddr: 0x77A0, symBinAddr: 0xB1F0, symSize: 0x0 }
  - { offset: 0x60967, size: 0x8, addend: 0x0, symName: _DEFAULT_PRESENT_BANNER, symObjAddr: 0x77A8, symBinAddr: 0xB1F8, symSize: 0x0 }
  - { offset: 0x6097D, size: 0x8, addend: 0x0, symName: _DEFAULT_PRESENT_LIST, symObjAddr: 0x77B0, symBinAddr: 0xB200, symSize: 0x0 }
  - { offset: 0x60993, size: 0x8, addend: 0x0, symName: _SOUND_PERMISSION, symObjAddr: 0x77B8, symBinAddr: 0xB208, symSize: 0x0 }
  - { offset: 0x609A9, size: 0x8, addend: 0x0, symName: _ALERT_PERMISSION, symObjAddr: 0x77C0, symBinAddr: 0xB210, symSize: 0x0 }
  - { offset: 0x609BF, size: 0x8, addend: 0x0, symName: _BADGE_PERMISSION, symObjAddr: 0x77C8, symBinAddr: 0xB218, symSize: 0x0 }
  - { offset: 0x609D5, size: 0x8, addend: 0x0, symName: _PROVISIONAL_PERMISSION, symObjAddr: 0x77D0, symBinAddr: 0xB220, symSize: 0x0 }
  - { offset: 0x609EB, size: 0x8, addend: 0x0, symName: _CRITICAL_PERMISSION, symObjAddr: 0x77D8, symBinAddr: 0xB228, symSize: 0x0 }
  - { offset: 0x60A01, size: 0x8, addend: 0x0, symName: _CALLBACK_DISPATCHER, symObjAddr: 0x77E0, symBinAddr: 0xB230, symSize: 0x0 }
  - { offset: 0x60A17, size: 0x8, addend: 0x0, symName: _ON_NOTIFICATION_CALLBACK_DISPATCHER, symObjAddr: 0x77E8, symBinAddr: 0xB238, symSize: 0x0 }
  - { offset: 0x60A2D, size: 0x8, addend: 0x0, symName: _PLATFORM_SPECIFICS, symObjAddr: 0x77F0, symBinAddr: 0xB240, symSize: 0x0 }
  - { offset: 0x60A43, size: 0x8, addend: 0x0, symName: _ID, symObjAddr: 0x77F8, symBinAddr: 0xB248, symSize: 0x0 }
  - { offset: 0x60A59, size: 0x8, addend: 0x0, symName: _TITLE, symObjAddr: 0x7800, symBinAddr: 0xB250, symSize: 0x0 }
  - { offset: 0x60A6F, size: 0x8, addend: 0x0, symName: _SUBTITLE, symObjAddr: 0x7808, symBinAddr: 0xB258, symSize: 0x0 }
  - { offset: 0x60A85, size: 0x8, addend: 0x0, symName: _BODY, symObjAddr: 0x7810, symBinAddr: 0xB260, symSize: 0x0 }
  - { offset: 0x60A9B, size: 0x8, addend: 0x0, symName: _SOUND, symObjAddr: 0x7818, symBinAddr: 0xB268, symSize: 0x0 }
  - { offset: 0x60AB1, size: 0x8, addend: 0x0, symName: _ATTACHMENTS, symObjAddr: 0x7820, symBinAddr: 0xB270, symSize: 0x0 }
  - { offset: 0x60AC7, size: 0x8, addend: 0x0, symName: _ATTACHMENT_IDENTIFIER, symObjAddr: 0x7828, symBinAddr: 0xB278, symSize: 0x0 }
  - { offset: 0x60ADD, size: 0x8, addend: 0x0, symName: _ATTACHMENT_FILE_PATH, symObjAddr: 0x7830, symBinAddr: 0xB280, symSize: 0x0 }
  - { offset: 0x60AF3, size: 0x8, addend: 0x0, symName: _ATTACHMENT_HIDE_THUMBNAIL, symObjAddr: 0x7838, symBinAddr: 0xB288, symSize: 0x0 }
  - { offset: 0x60B09, size: 0x8, addend: 0x0, symName: _ATTACHMENT_THUMBNAIL_CLIPPING_RECT, symObjAddr: 0x7840, symBinAddr: 0xB290, symSize: 0x0 }
  - { offset: 0x60B1F, size: 0x8, addend: 0x0, symName: _INTERRUPTION_LEVEL, symObjAddr: 0x7848, symBinAddr: 0xB298, symSize: 0x0 }
  - { offset: 0x60B35, size: 0x8, addend: 0x0, symName: _THREAD_IDENTIFIER, symObjAddr: 0x7850, symBinAddr: 0xB2A0, symSize: 0x0 }
  - { offset: 0x60B4B, size: 0x8, addend: 0x0, symName: _PRESENT_ALERT, symObjAddr: 0x7858, symBinAddr: 0xB2A8, symSize: 0x0 }
  - { offset: 0x60B61, size: 0x8, addend: 0x0, symName: _PRESENT_SOUND, symObjAddr: 0x7860, symBinAddr: 0xB2B0, symSize: 0x0 }
  - { offset: 0x60B77, size: 0x8, addend: 0x0, symName: _PRESENT_BADGE, symObjAddr: 0x7868, symBinAddr: 0xB2B8, symSize: 0x0 }
  - { offset: 0x60B8D, size: 0x8, addend: 0x0, symName: _PRESENT_BANNER, symObjAddr: 0x7870, symBinAddr: 0xB2C0, symSize: 0x0 }
  - { offset: 0x60BA3, size: 0x8, addend: 0x0, symName: _PRESENT_LIST, symObjAddr: 0x7878, symBinAddr: 0xB2C8, symSize: 0x0 }
  - { offset: 0x60BB9, size: 0x8, addend: 0x0, symName: _BADGE_NUMBER, symObjAddr: 0x7880, symBinAddr: 0xB2D0, symSize: 0x0 }
  - { offset: 0x60BCF, size: 0x8, addend: 0x0, symName: _MILLISECONDS_SINCE_EPOCH, symObjAddr: 0x7888, symBinAddr: 0xB2D8, symSize: 0x0 }
  - { offset: 0x60BE5, size: 0x8, addend: 0x0, symName: _REPEAT_INTERVAL, symObjAddr: 0x7890, symBinAddr: 0xB2E0, symSize: 0x0 }
  - { offset: 0x60BFB, size: 0x8, addend: 0x0, symName: _REPEAT_INTERVAL_MILLISECODNS, symObjAddr: 0x7898, symBinAddr: 0xB2E8, symSize: 0x0 }
  - { offset: 0x60C11, size: 0x8, addend: 0x0, symName: _SCHEDULED_DATE_TIME, symObjAddr: 0x78A0, symBinAddr: 0xB2F0, symSize: 0x0 }
  - { offset: 0x60C27, size: 0x8, addend: 0x0, symName: _TIME_ZONE_NAME, symObjAddr: 0x78A8, symBinAddr: 0xB2F8, symSize: 0x0 }
  - { offset: 0x60C3D, size: 0x8, addend: 0x0, symName: _MATCH_DATE_TIME_COMPONENTS, symObjAddr: 0x78B0, symBinAddr: 0xB300, symSize: 0x0 }
  - { offset: 0x60C53, size: 0x8, addend: 0x0, symName: _UILOCALNOTIFICATION_DATE_INTERPRETATION, symObjAddr: 0x78B8, symBinAddr: 0xB308, symSize: 0x0 }
  - { offset: 0x60C69, size: 0x8, addend: 0x0, symName: _NOTIFICATION_ID, symObjAddr: 0x78C0, symBinAddr: 0xB310, symSize: 0x0 }
  - { offset: 0x60C7F, size: 0x8, addend: 0x0, symName: _PAYLOAD, symObjAddr: 0x78C8, symBinAddr: 0xB318, symSize: 0x0 }
  - { offset: 0x60C95, size: 0x8, addend: 0x0, symName: _NOTIFICATION_LAUNCHED_APP, symObjAddr: 0x78D0, symBinAddr: 0xB320, symSize: 0x0 }
  - { offset: 0x60CAB, size: 0x8, addend: 0x0, symName: _ACTION_ID, symObjAddr: 0x78D8, symBinAddr: 0xB328, symSize: 0x0 }
  - { offset: 0x60CC1, size: 0x8, addend: 0x0, symName: _NOTIFICATION_RESPONSE_TYPE, symObjAddr: 0x78E0, symBinAddr: 0xB330, symSize: 0x0 }
  - { offset: 0x60CD7, size: 0x8, addend: 0x0, symName: _UNSUPPORTED_OS_VERSION_ERROR_CODE, symObjAddr: 0x78E8, symBinAddr: 0xB338, symSize: 0x0 }
  - { offset: 0x60CED, size: 0x8, addend: 0x0, symName: _GET_ACTIVE_NOTIFICATIONS_ERROR_MESSAGE, symObjAddr: 0x78F0, symBinAddr: 0xB340, symSize: 0x0 }
  - { offset: 0x60D03, size: 0x8, addend: 0x0, symName: _PRESENTATION_OPTIONS_USER_DEFAULTS, symObjAddr: 0x78F8, symBinAddr: 0xB348, symSize: 0x0 }
  - { offset: 0x60D19, size: 0x8, addend: 0x0, symName: _IS_NOTIFICATIONS_ENABLED, symObjAddr: 0x7900, symBinAddr: 0xB350, symSize: 0x0 }
  - { offset: 0x60D2F, size: 0x8, addend: 0x0, symName: _IS_SOUND_ENABLED, symObjAddr: 0x7908, symBinAddr: 0xB358, symSize: 0x0 }
  - { offset: 0x60D45, size: 0x8, addend: 0x0, symName: _IS_ALERT_ENABLED, symObjAddr: 0x7910, symBinAddr: 0xB360, symSize: 0x0 }
  - { offset: 0x60D5B, size: 0x8, addend: 0x0, symName: _IS_BADGE_ENABLED, symObjAddr: 0x7918, symBinAddr: 0xB368, symSize: 0x0 }
  - { offset: 0x60D71, size: 0x8, addend: 0x0, symName: _IS_PROVISIONAL_ENABLED, symObjAddr: 0x7920, symBinAddr: 0xB370, symSize: 0x0 }
  - { offset: 0x60D87, size: 0x8, addend: 0x0, symName: _IS_CRITICAL_ENABLED, symObjAddr: 0x7928, symBinAddr: 0xB378, symSize: 0x0 }
  - { offset: 0x60DA9, size: 0x8, addend: 0x0, symName: _registerPlugins, symObjAddr: 0x1BC50, symBinAddr: 0xE1E8, symSize: 0x0 }
  - { offset: 0x60DC4, size: 0x8, addend: 0x0, symName: _actionEventSink, symObjAddr: 0x1BC58, symBinAddr: 0xE1F0, symSize: 0x0 }
  - { offset: 0x60E9E, size: 0x8, addend: 0x0, symName: '+[FlutterLocalNotificationsPlugin registerWithRegistrar:]', symObjAddr: 0x0, symBinAddr: 0x15E4, symSize: 0xEE }
  - { offset: 0x60FDE, size: 0x8, addend: 0x0, symName: '+[FlutterLocalNotificationsPlugin setPluginRegistrantCallback:]', symObjAddr: 0xEE, symBinAddr: 0x16D2, symSize: 0xD }
  - { offset: 0x6101B, size: 0x8, addend: 0x0, symName: '-[FlutterLocalNotificationsPlugin initWithChannel:registrar:]', symObjAddr: 0xFB, symBinAddr: 0x16DF, symSize: 0xB6 }
  - { offset: 0x610CF, size: 0x8, addend: 0x0, symName: '-[FlutterLocalNotificationsPlugin handleMethodCall:result:]', symObjAddr: 0x1B1, symBinAddr: 0x1795, symSize: 0x5B6 }
  - { offset: 0x61526, size: 0x8, addend: 0x0, symName: '-[FlutterLocalNotificationsPlugin pendingUserNotificationRequests:]', symObjAddr: 0x767, symBinAddr: 0x1D4B, symSize: 0xB9 }
  - { offset: 0x61600, size: 0x8, addend: 0x0, symName: '___67-[FlutterLocalNotificationsPlugin pendingUserNotificationRequests:]_block_invoke', symObjAddr: 0x820, symBinAddr: 0x1E04, symSize: 0x567 }
  - { offset: 0x61A28, size: 0x8, addend: 0x0, symName: ___copy_helper_block_e8_32b, symObjAddr: 0xD87, symBinAddr: 0x236B, symSize: 0x17 }
  - { offset: 0x61A51, size: 0x8, addend: 0x0, symName: ___destroy_helper_block_e8_32s, symObjAddr: 0xD9E, symBinAddr: 0x2382, symSize: 0xF }
  - { offset: 0x61A7C, size: 0x8, addend: 0x0, symName: '-[FlutterLocalNotificationsPlugin activeUserNotificationRequests:]', symObjAddr: 0xDAD, symBinAddr: 0x2391, symSize: 0xB9 }
  - { offset: 0x61B56, size: 0x8, addend: 0x0, symName: '___66-[FlutterLocalNotificationsPlugin activeUserNotificationRequests:]_block_invoke', symObjAddr: 0xE66, symBinAddr: 0x244A, symSize: 0x647 }
  - { offset: 0x62062, size: 0x8, addend: 0x0, symName: '-[FlutterLocalNotificationsPlugin pendingLocalNotificationRequests:]', symObjAddr: 0x14AD, symBinAddr: 0x2A91, symSize: 0x403 }
  - { offset: 0x62476, size: 0x8, addend: 0x0, symName: '-[FlutterLocalNotificationsPlugin pendingNotificationRequests:]', symObjAddr: 0x18B0, symBinAddr: 0x2E94, symSize: 0x12 }
  - { offset: 0x624D4, size: 0x8, addend: 0x0, symName: '-[FlutterLocalNotificationsPlugin configureNotificationCategories:withCompletionHandler:]', symObjAddr: 0x18C2, symBinAddr: 0x2EA6, symSize: 0x7B7 }
  - { offset: 0x62AA5, size: 0x8, addend: 0x0, symName: '-[FlutterLocalNotificationsPlugin getActiveNotifications:]', symObjAddr: 0x2079, symBinAddr: 0x365D, symSize: 0x12 }
  - { offset: 0x62B03, size: 0x8, addend: 0x0, symName: '-[FlutterLocalNotificationsPlugin initialize:result:]', symObjAddr: 0x208B, symBinAddr: 0x366F, symSize: 0x6EE }
  - { offset: 0x63134, size: 0x8, addend: 0x0, symName: '___53-[FlutterLocalNotificationsPlugin initialize:result:]_block_invoke', symObjAddr: 0x2779, symBinAddr: 0x3D5D, symSize: 0x3A }
  - { offset: 0x631E8, size: 0x8, addend: 0x0, symName: ___copy_helper_block_e8_32s40b, symObjAddr: 0x27B3, symBinAddr: 0x3D97, symSize: 0x30 }
  - { offset: 0x6321D, size: 0x8, addend: 0x0, symName: ___destroy_helper_block_e8_32s40s, symObjAddr: 0x27E3, symBinAddr: 0x3DC7, symSize: 0x25 }
  - { offset: 0x63254, size: 0x8, addend: 0x0, symName: '-[FlutterLocalNotificationsPlugin requestPermissions:result:]', symObjAddr: 0x2808, symBinAddr: 0x3DEC, symSize: 0x274 }
  - { offset: 0x634F5, size: 0x8, addend: 0x0, symName: '-[FlutterLocalNotificationsPlugin requestPermissionsImpl:alertPermission:badgePermission:provisionalPermission:criticalPermission:result:]', symObjAddr: 0x2A7C, symBinAddr: 0x4060, symSize: 0x124 }
  - { offset: 0x6364D, size: 0x8, addend: 0x0, symName: '___138-[FlutterLocalNotificationsPlugin requestPermissionsImpl:alertPermission:badgePermission:provisionalPermission:criticalPermission:result:]_block_invoke', symObjAddr: 0x2BA0, symBinAddr: 0x4184, symSize: 0x42 }
  - { offset: 0x636D5, size: 0x8, addend: 0x0, symName: '-[FlutterLocalNotificationsPlugin checkPermissions:result:]', symObjAddr: 0x2BE2, symBinAddr: 0x41C6, symSize: 0xB9 }
  - { offset: 0x637CE, size: 0x8, addend: 0x0, symName: '___59-[FlutterLocalNotificationsPlugin checkPermissions:result:]_block_invoke', symObjAddr: 0x2C9B, symBinAddr: 0x427F, symSize: 0x2D8 }
  - { offset: 0x63A40, size: 0x8, addend: 0x0, symName: '-[FlutterLocalNotificationsPlugin buildStandardUILocalNotification:]', symObjAddr: 0x2F73, symBinAddr: 0x4557, symSize: 0x711 }
  - { offset: 0x6406C, size: 0x8, addend: 0x0, symName: '-[FlutterLocalNotificationsPlugin getIdentifier:]', symObjAddr: 0x3684, symBinAddr: 0x4C68, symSize: 0x5A }
  - { offset: 0x640DD, size: 0x8, addend: 0x0, symName: '-[FlutterLocalNotificationsPlugin show:result:]', symObjAddr: 0x36DE, symBinAddr: 0x4CC2, symSize: 0xC3 }
  - { offset: 0x6420B, size: 0x8, addend: 0x0, symName: '-[FlutterLocalNotificationsPlugin zonedSchedule:result:]', symObjAddr: 0x37A1, symBinAddr: 0x4D85, symSize: 0xE9 }
  - { offset: 0x64364, size: 0x8, addend: 0x0, symName: '-[FlutterLocalNotificationsPlugin periodicallyShow:result:]', symObjAddr: 0x388A, symBinAddr: 0x4E6E, symSize: 0xE9 }
  - { offset: 0x644BD, size: 0x8, addend: 0x0, symName: '-[FlutterLocalNotificationsPlugin cancel:result:]', symObjAddr: 0x3973, symBinAddr: 0x4F57, symSize: 0xFC }
  - { offset: 0x6464E, size: 0x8, addend: 0x0, symName: '-[FlutterLocalNotificationsPlugin cancelAll:]', symObjAddr: 0x3A6F, symBinAddr: 0x5053, symSize: 0x7F }
  - { offset: 0x64781, size: 0x8, addend: 0x0, symName: '-[FlutterLocalNotificationsPlugin buildStandardNotificationContent:result:]', symObjAddr: 0x3AEE, symBinAddr: 0x50D2, symSize: 0xFB7 }
  - { offset: 0x654EF, size: 0x8, addend: 0x0, symName: _getFlutterError, symObjAddr: 0x4AA5, symBinAddr: 0x6089, symSize: 0xED }
  - { offset: 0x655C3, size: 0x8, addend: 0x0, symName: '-[FlutterLocalNotificationsPlugin buildUserNotificationCalendarTrigger:]', symObjAddr: 0x4B92, symBinAddr: 0x6176, symSize: 0x251 }
  - { offset: 0x65877, size: 0x8, addend: 0x0, symName: '-[FlutterLocalNotificationsPlugin buildUserNotificationTimeIntervalTrigger:]', symObjAddr: 0x4DE3, symBinAddr: 0x63C7, symSize: 0x1A5 }
  - { offset: 0x659B8, size: 0x8, addend: 0x0, symName: '-[FlutterLocalNotificationsPlugin buildUserDict:title:presentAlert:presentSound:presentBadge:presentBanner:presentList:payload:]', symObjAddr: 0x4F88, symBinAddr: 0x656C, symSize: 0x20F }
  - { offset: 0x65C87, size: 0x8, addend: 0x0, symName: '-[FlutterLocalNotificationsPlugin addNotificationRequest:content:result:trigger:]', symObjAddr: 0x5197, symBinAddr: 0x677B, symSize: 0xF3 }
  - { offset: 0x65DC8, size: 0x8, addend: 0x0, symName: '___81-[FlutterLocalNotificationsPlugin addNotificationRequest:content:result:trigger:]_block_invoke', symObjAddr: 0x528A, symBinAddr: 0x686E, symSize: 0x48 }
  - { offset: 0x65E58, size: 0x8, addend: 0x0, symName: '-[FlutterLocalNotificationsPlugin isAFlutterLocalNotification:]', symObjAddr: 0x52D2, symBinAddr: 0x68B6, symSize: 0x146 }
  - { offset: 0x65F56, size: 0x8, addend: 0x0, symName: '-[FlutterLocalNotificationsPlugin handleSelectNotification:payload:]', symObjAddr: 0x5418, symBinAddr: 0x69FC, symSize: 0x10A }
  - { offset: 0x660AA, size: 0x8, addend: 0x0, symName: '-[FlutterLocalNotificationsPlugin containsKey:forDictionary:]', symObjAddr: 0x5522, symBinAddr: 0x6B06, symSize: 0xD3 }
  - { offset: 0x661A9, size: 0x8, addend: 0x0, symName: '-[FlutterLocalNotificationsPlugin userNotificationCenter:willPresentNotification:withCompletionHandler:]', symObjAddr: 0x55F5, symBinAddr: 0x6BD9, symSize: 0x3EC }
  - { offset: 0x6669B, size: 0x8, addend: 0x0, symName: '-[FlutterLocalNotificationsPlugin extractNotificationResponseDict:]', symObjAddr: 0x59E1, symBinAddr: 0x6FC5, symSize: 0x364 }
  - { offset: 0x669D5, size: 0x8, addend: 0x0, symName: '-[FlutterLocalNotificationsPlugin userNotificationCenter:didReceiveNotificationResponse:withCompletionHandler:]', symObjAddr: 0x5D45, symBinAddr: 0x7329, symSize: 0x46D }
  - { offset: 0x66DFF, size: 0x8, addend: 0x0, symName: '-[FlutterLocalNotificationsPlugin application:didFinishLaunchingWithOptions:]', symObjAddr: 0x61B2, symBinAddr: 0x7796, symSize: 0x1D8 }
  - { offset: 0x66F99, size: 0x8, addend: 0x0, symName: '-[FlutterLocalNotificationsPlugin application:didReceiveLocalNotification:]', symObjAddr: 0x638A, symBinAddr: 0x796E, symSize: 0x6 }
  - { offset: 0x66FF0, size: 0x8, addend: 0x0, symName: '-[FlutterLocalNotificationsPlugin .cxx_destruct]', symObjAddr: 0x6390, symBinAddr: 0x7974, symSize: 0x3E }
...
