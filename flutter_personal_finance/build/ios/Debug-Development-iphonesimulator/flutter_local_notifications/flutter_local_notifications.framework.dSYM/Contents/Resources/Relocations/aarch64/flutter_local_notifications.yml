---
triple:          'arm64-apple-darwin'
binary-path:     '/Users/<USER>/Documents/manish/flutter_personal_finance_app/flutter_personal_finance/build/ios/Debug-Development-iphonesimulator/flutter_local_notifications/flutter_local_notifications.framework/flutter_local_notifications'
relocations:
  - { offset: 0x5E34A, size: 0x8, addend: 0x0, symName: _flutter_local_notificationsVersionString, symObjAddr: 0x0, symBinAddr: 0x7CB8, symSize: 0x0 }
  - { offset: 0x5E37F, size: 0x8, addend: 0x0, symName: _flutter_local_notificationsVersionNumber, symObjAddr: 0x40, symBinAddr: 0x7CF8, symSize: 0x0 }
  - { offset: 0x5E3BC, size: 0x8, addend: 0x0, symName: '-[ActionEventSink init]', symObjAddr: 0x0, symBinAddr: 0xD18, symSize: 0x6C }
  - { offset: 0x5E431, size: 0x8, addend: 0x0, symName: '-[ActionEventSink init]', symObjAddr: 0x0, symBinAddr: 0xD18, symSize: 0x6C }
  - { offset: 0x5E468, size: 0x8, addend: 0x0, symName: '-[ActionEventSink addItem:]', symObjAddr: 0x6C, symBinAddr: 0xD84, symSize: 0x20 }
  - { offset: 0x5E4B5, size: 0x8, addend: 0x0, symName: '-[ActionEventSink onListenWithArguments:eventSink:]', symObjAddr: 0x8C, symBinAddr: 0xDA4, symSize: 0x138 }
  - { offset: 0x5E53B, size: 0x8, addend: 0x0, symName: '-[ActionEventSink onCancelWithArguments:]', symObjAddr: 0x1C4, symBinAddr: 0xEDC, symSize: 0x24 }
  - { offset: 0x5E57E, size: 0x8, addend: 0x0, symName: '-[ActionEventSink .cxx_destruct]', symObjAddr: 0x1E8, symBinAddr: 0xF00, symSize: 0x30 }
  - { offset: 0x5E619, size: 0x8, addend: 0x0, symName: '+[Converters parseNotificationCategoryOptions:]', symObjAddr: 0x0, symBinAddr: 0xF30, symSize: 0xFC }
  - { offset: 0x5E66D, size: 0x8, addend: 0x0, symName: '+[Converters parseNotificationCategoryOptions:]', symObjAddr: 0x0, symBinAddr: 0xF30, symSize: 0xFC }
  - { offset: 0x5E6DF, size: 0x8, addend: 0x0, symName: '+[Converters parseNotificationActionOptions:]', symObjAddr: 0xFC, symBinAddr: 0x102C, symSize: 0xFC }
  - { offset: 0x5E7CE, size: 0x8, addend: 0x0, symName: '+[FlutterEngineManager shouldAddAppDelegateToRegistrar:]', symObjAddr: 0x0, symBinAddr: 0x1128, symSize: 0x68 }
  - { offset: 0x5E7E8, size: 0x8, addend: 0x0, symName: _backgroundEngine, symObjAddr: 0x2FC8, symBinAddr: 0x11168, symSize: 0x0 }
  - { offset: 0x5E81B, size: 0x8, addend: 0x0, symName: '+[FlutterEngineManager shouldAddAppDelegateToRegistrar:]', symObjAddr: 0x0, symBinAddr: 0x1128, symSize: 0x68 }
  - { offset: 0x5E85E, size: 0x8, addend: 0x0, symName: '-[FlutterEngineManager init]', symObjAddr: 0x68, symBinAddr: 0x1190, symSize: 0x6C }
  - { offset: 0x5E895, size: 0x8, addend: 0x0, symName: '-[FlutterEngineManager startEngineIfNeeded:registerPlugins:]', symObjAddr: 0xD4, symBinAddr: 0x11FC, symSize: 0x1A8 }
  - { offset: 0x5E98D, size: 0x8, addend: 0x0, symName: '___60-[FlutterEngineManager startEngineIfNeeded:registerPlugins:]_block_invoke', symObjAddr: 0x27C, symBinAddr: 0x13A4, symSize: 0x94 }
  - { offset: 0x5EA23, size: 0x8, addend: 0x0, symName: ___copy_helper_block_e8_32s40s48s, symObjAddr: 0x310, symBinAddr: 0x1438, symSize: 0x30 }
  - { offset: 0x5EA4C, size: 0x8, addend: 0x0, symName: ___destroy_helper_block_e8_32s40s48s, symObjAddr: 0x340, symBinAddr: 0x1468, symSize: 0x30 }
  - { offset: 0x5EA6B, size: 0x8, addend: 0x0, symName: '-[FlutterEngineManager registerDispatcherHandle:callbackHandle:]', symObjAddr: 0x370, symBinAddr: 0x1498, symSize: 0x64 }
  - { offset: 0x5EABE, size: 0x8, addend: 0x0, symName: '-[FlutterEngineManager getCallbackHandle]', symObjAddr: 0x3D4, symBinAddr: 0x14FC, symSize: 0x10 }
  - { offset: 0x5EAF5, size: 0x8, addend: 0x0, symName: '-[FlutterEngineManager .cxx_destruct]', symObjAddr: 0x3E4, symBinAddr: 0x150C, symSize: 0xC }
  - { offset: 0x5ECB0, size: 0x8, addend: 0x0, symName: '+[FlutterLocalNotificationsPlugin registerWithRegistrar:]', symObjAddr: 0x0, symBinAddr: 0x1518, symSize: 0xCC }
  - { offset: 0x5ECCA, size: 0x8, addend: 0x0, symName: _FOREGROUND_ACTION_IDENTIFIERS, symObjAddr: 0x6308, symBinAddr: 0xC120, symSize: 0x0 }
  - { offset: 0x5ED18, size: 0x8, addend: 0x0, symName: _INITIALIZE_METHOD, symObjAddr: 0x6310, symBinAddr: 0xC128, symSize: 0x0 }
  - { offset: 0x5ED2E, size: 0x8, addend: 0x0, symName: _GET_CALLBACK_METHOD, symObjAddr: 0x6318, symBinAddr: 0xC130, symSize: 0x0 }
  - { offset: 0x5ED44, size: 0x8, addend: 0x0, symName: _SHOW_METHOD, symObjAddr: 0x6320, symBinAddr: 0xC138, symSize: 0x0 }
  - { offset: 0x5ED5A, size: 0x8, addend: 0x0, symName: _ZONED_SCHEDULE_METHOD, symObjAddr: 0x6328, symBinAddr: 0xC140, symSize: 0x0 }
  - { offset: 0x5ED70, size: 0x8, addend: 0x0, symName: _PERIODICALLY_SHOW_METHOD, symObjAddr: 0x6330, symBinAddr: 0xC148, symSize: 0x0 }
  - { offset: 0x5ED86, size: 0x8, addend: 0x0, symName: _PERIODICALLY_SHOW_WITH_DURATION_METHOD, symObjAddr: 0x6338, symBinAddr: 0xC150, symSize: 0x0 }
  - { offset: 0x5ED9C, size: 0x8, addend: 0x0, symName: _CANCEL_METHOD, symObjAddr: 0x6340, symBinAddr: 0xC158, symSize: 0x0 }
  - { offset: 0x5EDB2, size: 0x8, addend: 0x0, symName: _CANCEL_ALL_METHOD, symObjAddr: 0x6348, symBinAddr: 0xC160, symSize: 0x0 }
  - { offset: 0x5EDC8, size: 0x8, addend: 0x0, symName: _PENDING_NOTIFICATIONS_REQUESTS_METHOD, symObjAddr: 0x6350, symBinAddr: 0xC168, symSize: 0x0 }
  - { offset: 0x5EDDE, size: 0x8, addend: 0x0, symName: _GET_ACTIVE_NOTIFICATIONS_METHOD, symObjAddr: 0x6358, symBinAddr: 0xC170, symSize: 0x0 }
  - { offset: 0x5EDF4, size: 0x8, addend: 0x0, symName: _GET_NOTIFICATION_APP_LAUNCH_DETAILS_METHOD, symObjAddr: 0x6360, symBinAddr: 0xC178, symSize: 0x0 }
  - { offset: 0x5EE0A, size: 0x8, addend: 0x0, symName: _CHANNEL, symObjAddr: 0x6368, symBinAddr: 0xC180, symSize: 0x0 }
  - { offset: 0x5EE20, size: 0x8, addend: 0x0, symName: _CALLBACK_CHANNEL, symObjAddr: 0x6370, symBinAddr: 0xC188, symSize: 0x0 }
  - { offset: 0x5EE36, size: 0x8, addend: 0x0, symName: _ON_NOTIFICATION_METHOD, symObjAddr: 0x6378, symBinAddr: 0xC190, symSize: 0x0 }
  - { offset: 0x5EE4C, size: 0x8, addend: 0x0, symName: _DID_RECEIVE_LOCAL_NOTIFICATION, symObjAddr: 0x6380, symBinAddr: 0xC198, symSize: 0x0 }
  - { offset: 0x5EE62, size: 0x8, addend: 0x0, symName: _REQUEST_PERMISSIONS_METHOD, symObjAddr: 0x6388, symBinAddr: 0xC1A0, symSize: 0x0 }
  - { offset: 0x5EE78, size: 0x8, addend: 0x0, symName: _CHECK_PERMISSIONS_METHOD, symObjAddr: 0x6390, symBinAddr: 0xC1A8, symSize: 0x0 }
  - { offset: 0x5EE8E, size: 0x8, addend: 0x0, symName: _DAY, symObjAddr: 0x6398, symBinAddr: 0xC1B0, symSize: 0x0 }
  - { offset: 0x5EEA4, size: 0x8, addend: 0x0, symName: _REQUEST_SOUND_PERMISSION, symObjAddr: 0x63A0, symBinAddr: 0xC1B8, symSize: 0x0 }
  - { offset: 0x5EEBA, size: 0x8, addend: 0x0, symName: _REQUEST_ALERT_PERMISSION, symObjAddr: 0x63A8, symBinAddr: 0xC1C0, symSize: 0x0 }
  - { offset: 0x5EED0, size: 0x8, addend: 0x0, symName: _REQUEST_BADGE_PERMISSION, symObjAddr: 0x63B0, symBinAddr: 0xC1C8, symSize: 0x0 }
  - { offset: 0x5EEE6, size: 0x8, addend: 0x0, symName: _REQUEST_PROVISIONAL_PERMISSION, symObjAddr: 0x63B8, symBinAddr: 0xC1D0, symSize: 0x0 }
  - { offset: 0x5EEFC, size: 0x8, addend: 0x0, symName: _REQUEST_CRITICAL_PERMISSION, symObjAddr: 0x63C0, symBinAddr: 0xC1D8, symSize: 0x0 }
  - { offset: 0x5EF12, size: 0x8, addend: 0x0, symName: _DEFAULT_PRESENT_ALERT, symObjAddr: 0x63C8, symBinAddr: 0xC1E0, symSize: 0x0 }
  - { offset: 0x5EF28, size: 0x8, addend: 0x0, symName: _DEFAULT_PRESENT_SOUND, symObjAddr: 0x63D0, symBinAddr: 0xC1E8, symSize: 0x0 }
  - { offset: 0x5EF3E, size: 0x8, addend: 0x0, symName: _DEFAULT_PRESENT_BADGE, symObjAddr: 0x63D8, symBinAddr: 0xC1F0, symSize: 0x0 }
  - { offset: 0x5EF54, size: 0x8, addend: 0x0, symName: _DEFAULT_PRESENT_BANNER, symObjAddr: 0x63E0, symBinAddr: 0xC1F8, symSize: 0x0 }
  - { offset: 0x5EF6A, size: 0x8, addend: 0x0, symName: _DEFAULT_PRESENT_LIST, symObjAddr: 0x63E8, symBinAddr: 0xC200, symSize: 0x0 }
  - { offset: 0x5EF80, size: 0x8, addend: 0x0, symName: _SOUND_PERMISSION, symObjAddr: 0x63F0, symBinAddr: 0xC208, symSize: 0x0 }
  - { offset: 0x5EF96, size: 0x8, addend: 0x0, symName: _ALERT_PERMISSION, symObjAddr: 0x63F8, symBinAddr: 0xC210, symSize: 0x0 }
  - { offset: 0x5EFAC, size: 0x8, addend: 0x0, symName: _BADGE_PERMISSION, symObjAddr: 0x6400, symBinAddr: 0xC218, symSize: 0x0 }
  - { offset: 0x5EFC2, size: 0x8, addend: 0x0, symName: _PROVISIONAL_PERMISSION, symObjAddr: 0x6408, symBinAddr: 0xC220, symSize: 0x0 }
  - { offset: 0x5EFD8, size: 0x8, addend: 0x0, symName: _CRITICAL_PERMISSION, symObjAddr: 0x6410, symBinAddr: 0xC228, symSize: 0x0 }
  - { offset: 0x5EFEE, size: 0x8, addend: 0x0, symName: _CALLBACK_DISPATCHER, symObjAddr: 0x6418, symBinAddr: 0xC230, symSize: 0x0 }
  - { offset: 0x5F004, size: 0x8, addend: 0x0, symName: _ON_NOTIFICATION_CALLBACK_DISPATCHER, symObjAddr: 0x6420, symBinAddr: 0xC238, symSize: 0x0 }
  - { offset: 0x5F01A, size: 0x8, addend: 0x0, symName: _PLATFORM_SPECIFICS, symObjAddr: 0x6428, symBinAddr: 0xC240, symSize: 0x0 }
  - { offset: 0x5F030, size: 0x8, addend: 0x0, symName: _ID, symObjAddr: 0x6430, symBinAddr: 0xC248, symSize: 0x0 }
  - { offset: 0x5F046, size: 0x8, addend: 0x0, symName: _TITLE, symObjAddr: 0x6438, symBinAddr: 0xC250, symSize: 0x0 }
  - { offset: 0x5F05C, size: 0x8, addend: 0x0, symName: _SUBTITLE, symObjAddr: 0x6440, symBinAddr: 0xC258, symSize: 0x0 }
  - { offset: 0x5F072, size: 0x8, addend: 0x0, symName: _BODY, symObjAddr: 0x6448, symBinAddr: 0xC260, symSize: 0x0 }
  - { offset: 0x5F088, size: 0x8, addend: 0x0, symName: _SOUND, symObjAddr: 0x6450, symBinAddr: 0xC268, symSize: 0x0 }
  - { offset: 0x5F09E, size: 0x8, addend: 0x0, symName: _ATTACHMENTS, symObjAddr: 0x6458, symBinAddr: 0xC270, symSize: 0x0 }
  - { offset: 0x5F0B4, size: 0x8, addend: 0x0, symName: _ATTACHMENT_IDENTIFIER, symObjAddr: 0x6460, symBinAddr: 0xC278, symSize: 0x0 }
  - { offset: 0x5F0CA, size: 0x8, addend: 0x0, symName: _ATTACHMENT_FILE_PATH, symObjAddr: 0x6468, symBinAddr: 0xC280, symSize: 0x0 }
  - { offset: 0x5F0E0, size: 0x8, addend: 0x0, symName: _ATTACHMENT_HIDE_THUMBNAIL, symObjAddr: 0x6470, symBinAddr: 0xC288, symSize: 0x0 }
  - { offset: 0x5F0F6, size: 0x8, addend: 0x0, symName: _ATTACHMENT_THUMBNAIL_CLIPPING_RECT, symObjAddr: 0x6478, symBinAddr: 0xC290, symSize: 0x0 }
  - { offset: 0x5F10C, size: 0x8, addend: 0x0, symName: _INTERRUPTION_LEVEL, symObjAddr: 0x6480, symBinAddr: 0xC298, symSize: 0x0 }
  - { offset: 0x5F122, size: 0x8, addend: 0x0, symName: _THREAD_IDENTIFIER, symObjAddr: 0x6488, symBinAddr: 0xC2A0, symSize: 0x0 }
  - { offset: 0x5F138, size: 0x8, addend: 0x0, symName: _PRESENT_ALERT, symObjAddr: 0x6490, symBinAddr: 0xC2A8, symSize: 0x0 }
  - { offset: 0x5F14E, size: 0x8, addend: 0x0, symName: _PRESENT_SOUND, symObjAddr: 0x6498, symBinAddr: 0xC2B0, symSize: 0x0 }
  - { offset: 0x5F164, size: 0x8, addend: 0x0, symName: _PRESENT_BADGE, symObjAddr: 0x64A0, symBinAddr: 0xC2B8, symSize: 0x0 }
  - { offset: 0x5F17A, size: 0x8, addend: 0x0, symName: _PRESENT_BANNER, symObjAddr: 0x64A8, symBinAddr: 0xC2C0, symSize: 0x0 }
  - { offset: 0x5F190, size: 0x8, addend: 0x0, symName: _PRESENT_LIST, symObjAddr: 0x64B0, symBinAddr: 0xC2C8, symSize: 0x0 }
  - { offset: 0x5F1A6, size: 0x8, addend: 0x0, symName: _BADGE_NUMBER, symObjAddr: 0x64B8, symBinAddr: 0xC2D0, symSize: 0x0 }
  - { offset: 0x5F1BC, size: 0x8, addend: 0x0, symName: _MILLISECONDS_SINCE_EPOCH, symObjAddr: 0x64C0, symBinAddr: 0xC2D8, symSize: 0x0 }
  - { offset: 0x5F1D2, size: 0x8, addend: 0x0, symName: _REPEAT_INTERVAL, symObjAddr: 0x64C8, symBinAddr: 0xC2E0, symSize: 0x0 }
  - { offset: 0x5F1E8, size: 0x8, addend: 0x0, symName: _REPEAT_INTERVAL_MILLISECODNS, symObjAddr: 0x64D0, symBinAddr: 0xC2E8, symSize: 0x0 }
  - { offset: 0x5F1FE, size: 0x8, addend: 0x0, symName: _SCHEDULED_DATE_TIME, symObjAddr: 0x64D8, symBinAddr: 0xC2F0, symSize: 0x0 }
  - { offset: 0x5F214, size: 0x8, addend: 0x0, symName: _TIME_ZONE_NAME, symObjAddr: 0x64E0, symBinAddr: 0xC2F8, symSize: 0x0 }
  - { offset: 0x5F22A, size: 0x8, addend: 0x0, symName: _MATCH_DATE_TIME_COMPONENTS, symObjAddr: 0x64E8, symBinAddr: 0xC300, symSize: 0x0 }
  - { offset: 0x5F240, size: 0x8, addend: 0x0, symName: _UILOCALNOTIFICATION_DATE_INTERPRETATION, symObjAddr: 0x64F0, symBinAddr: 0xC308, symSize: 0x0 }
  - { offset: 0x5F256, size: 0x8, addend: 0x0, symName: _NOTIFICATION_ID, symObjAddr: 0x64F8, symBinAddr: 0xC310, symSize: 0x0 }
  - { offset: 0x5F26C, size: 0x8, addend: 0x0, symName: _PAYLOAD, symObjAddr: 0x6500, symBinAddr: 0xC318, symSize: 0x0 }
  - { offset: 0x5F282, size: 0x8, addend: 0x0, symName: _NOTIFICATION_LAUNCHED_APP, symObjAddr: 0x6508, symBinAddr: 0xC320, symSize: 0x0 }
  - { offset: 0x5F298, size: 0x8, addend: 0x0, symName: _ACTION_ID, symObjAddr: 0x6510, symBinAddr: 0xC328, symSize: 0x0 }
  - { offset: 0x5F2AE, size: 0x8, addend: 0x0, symName: _NOTIFICATION_RESPONSE_TYPE, symObjAddr: 0x6518, symBinAddr: 0xC330, symSize: 0x0 }
  - { offset: 0x5F2C4, size: 0x8, addend: 0x0, symName: _UNSUPPORTED_OS_VERSION_ERROR_CODE, symObjAddr: 0x6520, symBinAddr: 0xC338, symSize: 0x0 }
  - { offset: 0x5F2DA, size: 0x8, addend: 0x0, symName: _GET_ACTIVE_NOTIFICATIONS_ERROR_MESSAGE, symObjAddr: 0x6528, symBinAddr: 0xC340, symSize: 0x0 }
  - { offset: 0x5F2F0, size: 0x8, addend: 0x0, symName: _PRESENTATION_OPTIONS_USER_DEFAULTS, symObjAddr: 0x6530, symBinAddr: 0xC348, symSize: 0x0 }
  - { offset: 0x5F306, size: 0x8, addend: 0x0, symName: _IS_NOTIFICATIONS_ENABLED, symObjAddr: 0x6538, symBinAddr: 0xC350, symSize: 0x0 }
  - { offset: 0x5F31C, size: 0x8, addend: 0x0, symName: _IS_SOUND_ENABLED, symObjAddr: 0x6540, symBinAddr: 0xC358, symSize: 0x0 }
  - { offset: 0x5F332, size: 0x8, addend: 0x0, symName: _IS_ALERT_ENABLED, symObjAddr: 0x6548, symBinAddr: 0xC360, symSize: 0x0 }
  - { offset: 0x5F348, size: 0x8, addend: 0x0, symName: _IS_BADGE_ENABLED, symObjAddr: 0x6550, symBinAddr: 0xC368, symSize: 0x0 }
  - { offset: 0x5F35E, size: 0x8, addend: 0x0, symName: _IS_PROVISIONAL_ENABLED, symObjAddr: 0x6558, symBinAddr: 0xC370, symSize: 0x0 }
  - { offset: 0x5F374, size: 0x8, addend: 0x0, symName: _IS_CRITICAL_ENABLED, symObjAddr: 0x6560, symBinAddr: 0xC378, symSize: 0x0 }
  - { offset: 0x5F396, size: 0x8, addend: 0x0, symName: _registerPlugins, symObjAddr: 0x14B38, symBinAddr: 0x11170, symSize: 0x0 }
  - { offset: 0x5F3B1, size: 0x8, addend: 0x0, symName: _actionEventSink, symObjAddr: 0x14B40, symBinAddr: 0x11178, symSize: 0x0 }
  - { offset: 0x5F48B, size: 0x8, addend: 0x0, symName: '+[FlutterLocalNotificationsPlugin registerWithRegistrar:]', symObjAddr: 0x0, symBinAddr: 0x1518, symSize: 0xCC }
  - { offset: 0x5F4EA, size: 0x8, addend: 0x0, symName: '+[FlutterLocalNotificationsPlugin setPluginRegistrantCallback:]', symObjAddr: 0xCC, symBinAddr: 0x15E4, symSize: 0xC }
  - { offset: 0x5F527, size: 0x8, addend: 0x0, symName: '-[FlutterLocalNotificationsPlugin initWithChannel:registrar:]', symObjAddr: 0xD8, symBinAddr: 0x15F0, symSize: 0xC0 }
  - { offset: 0x5F57E, size: 0x8, addend: 0x0, symName: '-[FlutterLocalNotificationsPlugin handleMethodCall:result:]', symObjAddr: 0x198, symBinAddr: 0x16B0, symSize: 0x4E4 }
  - { offset: 0x5F63A, size: 0x8, addend: 0x0, symName: '-[FlutterLocalNotificationsPlugin pendingUserNotificationRequests:]', symObjAddr: 0x67C, symBinAddr: 0x1B94, symSize: 0xA4 }
  - { offset: 0x5F689, size: 0x8, addend: 0x0, symName: '___67-[FlutterLocalNotificationsPlugin pendingUserNotificationRequests:]_block_invoke', symObjAddr: 0x720, symBinAddr: 0x1C38, symSize: 0x3BC }
  - { offset: 0x5F73D, size: 0x8, addend: 0x0, symName: ___copy_helper_block_e8_32b, symObjAddr: 0xADC, symBinAddr: 0x1FF4, symSize: 0x10 }
  - { offset: 0x5F766, size: 0x8, addend: 0x0, symName: ___destroy_helper_block_e8_32s, symObjAddr: 0xAEC, symBinAddr: 0x2004, symSize: 0x8 }
  - { offset: 0x5F785, size: 0x8, addend: 0x0, symName: '-[FlutterLocalNotificationsPlugin activeUserNotificationRequests:]', symObjAddr: 0xAF4, symBinAddr: 0x200C, symSize: 0xA4 }
  - { offset: 0x5F7D4, size: 0x8, addend: 0x0, symName: '___66-[FlutterLocalNotificationsPlugin activeUserNotificationRequests:]_block_invoke', symObjAddr: 0xB98, symBinAddr: 0x20B0, symSize: 0x46C }
  - { offset: 0x5F888, size: 0x8, addend: 0x0, symName: '-[FlutterLocalNotificationsPlugin pendingLocalNotificationRequests:]', symObjAddr: 0x1004, symBinAddr: 0x251C, symSize: 0x328 }
  - { offset: 0x5F957, size: 0x8, addend: 0x0, symName: '-[FlutterLocalNotificationsPlugin pendingNotificationRequests:]', symObjAddr: 0x132C, symBinAddr: 0x2844, symSize: 0x4 }
  - { offset: 0x5F998, size: 0x8, addend: 0x0, symName: '-[FlutterLocalNotificationsPlugin configureNotificationCategories:withCompletionHandler:]', symObjAddr: 0x1330, symBinAddr: 0x2848, symSize: 0x558 }
  - { offset: 0x5FB5C, size: 0x8, addend: 0x0, symName: '-[FlutterLocalNotificationsPlugin getActiveNotifications:]', symObjAddr: 0x1888, symBinAddr: 0x2DA0, symSize: 0x4 }
  - { offset: 0x5FB9D, size: 0x8, addend: 0x0, symName: '-[FlutterLocalNotificationsPlugin initialize:result:]', symObjAddr: 0x188C, symBinAddr: 0x2DA4, symSize: 0x5AC }
  - { offset: 0x5FC59, size: 0x8, addend: 0x0, symName: '___53-[FlutterLocalNotificationsPlugin initialize:result:]_block_invoke', symObjAddr: 0x1E38, symBinAddr: 0x3350, symSize: 0x20 }
  - { offset: 0x5FD01, size: 0x8, addend: 0x0, symName: ___copy_helper_block_e8_32s40b, symObjAddr: 0x1E58, symBinAddr: 0x3370, symSize: 0x34 }
  - { offset: 0x5FD2A, size: 0x8, addend: 0x0, symName: ___destroy_helper_block_e8_32s40s, symObjAddr: 0x1E8C, symBinAddr: 0x33A4, symSize: 0x28 }
  - { offset: 0x5FD49, size: 0x8, addend: 0x0, symName: '-[FlutterLocalNotificationsPlugin requestPermissions:result:]', symObjAddr: 0x1EB4, symBinAddr: 0x33CC, symSize: 0x200 }
  - { offset: 0x5FDF4, size: 0x8, addend: 0x0, symName: '-[FlutterLocalNotificationsPlugin requestPermissionsImpl:alertPermission:badgePermission:provisionalPermission:criticalPermission:result:]', symObjAddr: 0x20B4, symBinAddr: 0x35CC, symSize: 0x130 }
  - { offset: 0x5FECF, size: 0x8, addend: 0x0, symName: '___138-[FlutterLocalNotificationsPlugin requestPermissionsImpl:alertPermission:badgePermission:provisionalPermission:criticalPermission:result:]_block_invoke', symObjAddr: 0x21E4, symBinAddr: 0x36FC, symSize: 0x4C }
  - { offset: 0x5FF46, size: 0x8, addend: 0x0, symName: '-[FlutterLocalNotificationsPlugin checkPermissions:result:]', symObjAddr: 0x2230, symBinAddr: 0x3748, symSize: 0xA4 }
  - { offset: 0x5FFB4, size: 0x8, addend: 0x0, symName: '___59-[FlutterLocalNotificationsPlugin checkPermissions:result:]_block_invoke', symObjAddr: 0x22D4, symBinAddr: 0x37EC, symSize: 0x244 }
  - { offset: 0x6009D, size: 0x8, addend: 0x0, symName: '-[FlutterLocalNotificationsPlugin buildStandardUILocalNotification:]', symObjAddr: 0x2518, symBinAddr: 0x3A30, symSize: 0x590 }
  - { offset: 0x6018E, size: 0x8, addend: 0x0, symName: '-[FlutterLocalNotificationsPlugin getIdentifier:]', symObjAddr: 0x2AA8, symBinAddr: 0x3FC0, symSize: 0x50 }
  - { offset: 0x601D3, size: 0x8, addend: 0x0, symName: '-[FlutterLocalNotificationsPlugin show:result:]', symObjAddr: 0x2AF8, symBinAddr: 0x4010, symSize: 0xB0 }
  - { offset: 0x60249, size: 0x8, addend: 0x0, symName: '-[FlutterLocalNotificationsPlugin zonedSchedule:result:]', symObjAddr: 0x2BA8, symBinAddr: 0x40C0, symSize: 0xD0 }
  - { offset: 0x602D0, size: 0x8, addend: 0x0, symName: '-[FlutterLocalNotificationsPlugin periodicallyShow:result:]', symObjAddr: 0x2C78, symBinAddr: 0x4190, symSize: 0xD0 }
  - { offset: 0x60357, size: 0x8, addend: 0x0, symName: '-[FlutterLocalNotificationsPlugin cancel:result:]', symObjAddr: 0x2D48, symBinAddr: 0x4260, symSize: 0xEC }
  - { offset: 0x603F4, size: 0x8, addend: 0x0, symName: '-[FlutterLocalNotificationsPlugin cancelAll:]', symObjAddr: 0x2E34, symBinAddr: 0x434C, symSize: 0x68 }
  - { offset: 0x6046F, size: 0x8, addend: 0x0, symName: '-[FlutterLocalNotificationsPlugin buildStandardNotificationContent:result:]', symObjAddr: 0x2E9C, symBinAddr: 0x43B4, symSize: 0xB90 }
  - { offset: 0x606AE, size: 0x8, addend: 0x0, symName: _getFlutterError, symObjAddr: 0x3A2C, symBinAddr: 0x4F44, symSize: 0xD8 }
  - { offset: 0x606D9, size: 0x8, addend: 0x0, symName: '-[FlutterLocalNotificationsPlugin buildUserNotificationCalendarTrigger:]', symObjAddr: 0x3B04, symBinAddr: 0x501C, symSize: 0x20C }
  - { offset: 0x607A2, size: 0x8, addend: 0x0, symName: '-[FlutterLocalNotificationsPlugin buildUserNotificationTimeIntervalTrigger:]', symObjAddr: 0x3D10, symBinAddr: 0x5228, symSize: 0x150 }
  - { offset: 0x6080B, size: 0x8, addend: 0x0, symName: '-[FlutterLocalNotificationsPlugin buildUserDict:title:presentAlert:presentSound:presentBadge:presentBanner:presentList:payload:]', symObjAddr: 0x3E60, symBinAddr: 0x5378, symSize: 0x1EC }
  - { offset: 0x608D8, size: 0x8, addend: 0x0, symName: '-[FlutterLocalNotificationsPlugin addNotificationRequest:content:result:trigger:]', symObjAddr: 0x404C, symBinAddr: 0x5564, symSize: 0xE8 }
  - { offset: 0x6096E, size: 0x8, addend: 0x0, symName: '___81-[FlutterLocalNotificationsPlugin addNotificationRequest:content:result:trigger:]_block_invoke', symObjAddr: 0x4134, symBinAddr: 0x564C, symSize: 0x5C }
  - { offset: 0x609F2, size: 0x8, addend: 0x0, symName: '-[FlutterLocalNotificationsPlugin isAFlutterLocalNotification:]', symObjAddr: 0x4190, symBinAddr: 0x56A8, symSize: 0x130 }
  - { offset: 0x60A37, size: 0x8, addend: 0x0, symName: '-[FlutterLocalNotificationsPlugin handleSelectNotification:payload:]', symObjAddr: 0x42C0, symBinAddr: 0x57D8, symSize: 0xF0 }
  - { offset: 0x60AAF, size: 0x8, addend: 0x0, symName: '-[FlutterLocalNotificationsPlugin containsKey:forDictionary:]', symObjAddr: 0x43B0, symBinAddr: 0x58C8, symSize: 0xC4 }
  - { offset: 0x60B05, size: 0x8, addend: 0x0, symName: '-[FlutterLocalNotificationsPlugin userNotificationCenter:willPresentNotification:withCompletionHandler:]', symObjAddr: 0x4474, symBinAddr: 0x598C, symSize: 0x34C }
  - { offset: 0x60C33, size: 0x8, addend: 0x0, symName: '-[FlutterLocalNotificationsPlugin extractNotificationResponseDict:]', symObjAddr: 0x47C0, symBinAddr: 0x5CD8, symSize: 0x2BC }
  - { offset: 0x60CBC, size: 0x8, addend: 0x0, symName: '-[FlutterLocalNotificationsPlugin userNotificationCenter:didReceiveNotificationResponse:withCompletionHandler:]', symObjAddr: 0x4A7C, symBinAddr: 0x5F94, symSize: 0x388 }
  - { offset: 0x60D92, size: 0x8, addend: 0x0, symName: '-[FlutterLocalNotificationsPlugin application:didFinishLaunchingWithOptions:]', symObjAddr: 0x4E04, symBinAddr: 0x631C, symSize: 0x190 }
  - { offset: 0x60E08, size: 0x8, addend: 0x0, symName: '-[FlutterLocalNotificationsPlugin application:didReceiveLocalNotification:]', symObjAddr: 0x4F94, symBinAddr: 0x64AC, symSize: 0x4 }
  - { offset: 0x60E5F, size: 0x8, addend: 0x0, symName: '-[FlutterLocalNotificationsPlugin .cxx_destruct]', symObjAddr: 0x4F98, symBinAddr: 0x64B0, symSize: 0x48 }
...
