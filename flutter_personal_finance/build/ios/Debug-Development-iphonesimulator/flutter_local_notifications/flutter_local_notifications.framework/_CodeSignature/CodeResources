<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Headers/ActionEventSink.h</key>
		<data>
		8dkPjBlPHAnG9/AzC6uiYtzo64U=
		</data>
		<key>Headers/Converters.h</key>
		<data>
		yeDhTMGc1kUYPOkt9lNnLrnb00g=
		</data>
		<key>Headers/FlutterEngineManager.h</key>
		<data>
		AM/NLip8hx5VGl63/ALqJEzb768=
		</data>
		<key>Headers/FlutterLocalNotificationsPlugin.h</key>
		<data>
		jU9TcXeoDIMhDy3wgvasz0ZEQZ0=
		</data>
		<key>Headers/flutter_local_notifications-umbrella.h</key>
		<data>
		VHn8xjhTAVRXpSCGbF/y8syW8uI=
		</data>
		<key>Info.plist</key>
		<data>
		RZxg52oFQcAEGixo9Y8Xby1w7cc=
		</data>
		<key>Modules/module.modulemap</key>
		<data>
		o3Hx2jctFO1lgiOtzSa+uVf9mgI=
		</data>
		<key>flutter_local_notifications_privacy.bundle/Info.plist</key>
		<data>
		VowqmsWnlDvIbFidAEplthpXV68=
		</data>
		<key>flutter_local_notifications_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		TACQAjNH8aZiPGPN1ibJPOzuF+k=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/ActionEventSink.h</key>
		<dict>
			<key>hash2</key>
			<data>
			WFGuaKGm4T3VykjjYUgiOa08YbbgtsjeXZ5wgB1JoRI=
			</data>
		</dict>
		<key>Headers/Converters.h</key>
		<dict>
			<key>hash2</key>
			<data>
			tIQTNmaKHyihn2r8k3ouqhcN5X7eyShmYLvSB2hUOdI=
			</data>
		</dict>
		<key>Headers/FlutterEngineManager.h</key>
		<dict>
			<key>hash2</key>
			<data>
			RAgq8XdbR2sH+59+8oLCFFZtav/QLgmEOrUpOjBdguw=
			</data>
		</dict>
		<key>Headers/FlutterLocalNotificationsPlugin.h</key>
		<dict>
			<key>hash2</key>
			<data>
			Zgwp3FRQTeX8F6nZ0suNYoEXd38+9v/6Ncjy/VsnYCM=
			</data>
		</dict>
		<key>Headers/flutter_local_notifications-umbrella.h</key>
		<dict>
			<key>hash2</key>
			<data>
			yLxU4vBHHRRoVKBEuBS76xWIokvHpiCtjjlQmHsremU=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash2</key>
			<data>
			p3eDTfQvYLoyf8fgGoVdr28t8tSQtNrkuptekNxAocE=
			</data>
		</dict>
		<key>flutter_local_notifications_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			nrPQLeqECwZ55eXzQtEfC4kvavVk2j86TCN4dWzQ7S4=
			</data>
		</dict>
		<key>flutter_local_notifications_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			j0y/Om8Yt96qvIZGvSVd9FPw0QDe8HTsBeREY0N76dU=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
