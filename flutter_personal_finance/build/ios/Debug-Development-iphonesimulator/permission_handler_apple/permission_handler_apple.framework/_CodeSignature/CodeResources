<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Headers/AppTrackingTransparencyPermissionStrategy.h</key>
		<data>
		hSkl0b3zmpyg1kIaXinvAUWwTdE=
		</data>
		<key>Headers/AssistantPermissionStrategy.h</key>
		<data>
		rfyiTSc3DWmT/IqegGQgKuWnDmk=
		</data>
		<key>Headers/AudioVideoPermissionStrategy.h</key>
		<data>
		k1XMk5D67/BlFMHryiDNCV87Nz4=
		</data>
		<key>Headers/BackgroundRefreshStrategy.h</key>
		<data>
		Zr7z2f6i1590PAAxns69n8gXkOM=
		</data>
		<key>Headers/BluetoothPermissionStrategy.h</key>
		<data>
		17/pBDn9OoNrkQGTFIML+sWrfDg=
		</data>
		<key>Headers/Codec.h</key>
		<data>
		TpOitwsmfbhnBzKjbvmBrG9OSwc=
		</data>
		<key>Headers/ContactPermissionStrategy.h</key>
		<data>
		BQkOYAHIcocgpEjvXKmMgIxf5qA=
		</data>
		<key>Headers/CriticalAlertsPermissionStrategy.h</key>
		<data>
		TDATrFm293Gs/WJ+i9elQ5Ty0Ms=
		</data>
		<key>Headers/EventPermissionStrategy.h</key>
		<data>
		8TQLrJJiXENXA0pzyX0MAehoib0=
		</data>
		<key>Headers/LocationPermissionStrategy.h</key>
		<data>
		4HZBgjY+jnz+Kef7iwJteHsOWA8=
		</data>
		<key>Headers/MediaLibraryPermissionStrategy.h</key>
		<data>
		nE9rvzK+2d+hcHKuCR+XtS8mDwM=
		</data>
		<key>Headers/NotificationPermissionStrategy.h</key>
		<data>
		W/t0YnZeptSrUKh9CfnGqgLXJxs=
		</data>
		<key>Headers/PermissionHandlerEnums.h</key>
		<data>
		Zk+h1C8RKll80Wp3k5TEdY45wjk=
		</data>
		<key>Headers/PermissionHandlerPlugin.h</key>
		<data>
		US8gfGrgvyHBMXjEc33uZU/u1/E=
		</data>
		<key>Headers/PermissionManager.h</key>
		<data>
		G4OvECvMo3LuS67YcSCDxvDGi4s=
		</data>
		<key>Headers/PermissionStrategy.h</key>
		<data>
		WIUX3JCm9FUEJMoS1xdJTAxa/ik=
		</data>
		<key>Headers/PhonePermissionStrategy.h</key>
		<data>
		LsoP+Y2q9MdLenVRQzHFKrXwRIc=
		</data>
		<key>Headers/PhotoPermissionStrategy.h</key>
		<data>
		QLgVRQwqcCVRmpfQThnv+4EIrEk=
		</data>
		<key>Headers/SensorPermissionStrategy.h</key>
		<data>
		xP0948JSWc+YtL8URX8qK54p2ic=
		</data>
		<key>Headers/SpeechPermissionStrategy.h</key>
		<data>
		IbFEyirnw1ktEDkKos/H7ZfFj4w=
		</data>
		<key>Headers/StoragePermissionStrategy.h</key>
		<data>
		puOgC58jkur+6VE/ynqT93gbVaE=
		</data>
		<key>Headers/UnknownPermissionStrategy.h</key>
		<data>
		t4BFc70Lz8ZDv/Q0Ql6IwCvrXuU=
		</data>
		<key>Headers/permission_handler_apple-umbrella.h</key>
		<data>
		ZBe03XoX9g8bhj5mSSZkPM+iHoo=
		</data>
		<key>Info.plist</key>
		<data>
		8THTeQ7JmH3WxwRccWmKDWCH+jQ=
		</data>
		<key>Modules/module.modulemap</key>
		<data>
		gm1KE03qnJ+bIZ5fWa5NiCyl/HM=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/AppTrackingTransparencyPermissionStrategy.h</key>
		<dict>
			<key>hash</key>
			<data>
			hSkl0b3zmpyg1kIaXinvAUWwTdE=
			</data>
			<key>hash2</key>
			<data>
			FDiqFCrpcVe8BwNe0IVGdgkfxR/Fr4jW0igw7Rzw4w0=
			</data>
		</dict>
		<key>Headers/AssistantPermissionStrategy.h</key>
		<dict>
			<key>hash</key>
			<data>
			rfyiTSc3DWmT/IqegGQgKuWnDmk=
			</data>
			<key>hash2</key>
			<data>
			YB605BVhTrNspfKznnWzz8+AY1xPwdziRLiqrds6sY0=
			</data>
		</dict>
		<key>Headers/AudioVideoPermissionStrategy.h</key>
		<dict>
			<key>hash</key>
			<data>
			k1XMk5D67/BlFMHryiDNCV87Nz4=
			</data>
			<key>hash2</key>
			<data>
			FEF02mBdpnc7Dr7Qs0FsK1DsPcBh/Dd2BbCAuTsZzjY=
			</data>
		</dict>
		<key>Headers/BackgroundRefreshStrategy.h</key>
		<dict>
			<key>hash</key>
			<data>
			Zr7z2f6i1590PAAxns69n8gXkOM=
			</data>
			<key>hash2</key>
			<data>
			E7ZqYfvF3pn8TJ9X/cK1zMaZiWAcwQdOYruCpnV2g9k=
			</data>
		</dict>
		<key>Headers/BluetoothPermissionStrategy.h</key>
		<dict>
			<key>hash</key>
			<data>
			17/pBDn9OoNrkQGTFIML+sWrfDg=
			</data>
			<key>hash2</key>
			<data>
			vZRPgDvRS2XdqV1Zbf4rg6pocg8trXvNM9aNFbgJQk4=
			</data>
		</dict>
		<key>Headers/Codec.h</key>
		<dict>
			<key>hash</key>
			<data>
			TpOitwsmfbhnBzKjbvmBrG9OSwc=
			</data>
			<key>hash2</key>
			<data>
			uU++6c8HCdYA/Uge3GPwQzy7/fOkqvVCHoV/bxZMaZQ=
			</data>
		</dict>
		<key>Headers/ContactPermissionStrategy.h</key>
		<dict>
			<key>hash</key>
			<data>
			BQkOYAHIcocgpEjvXKmMgIxf5qA=
			</data>
			<key>hash2</key>
			<data>
			q+zjx5YvN9zFvK+g0Lym6ir27CztjxMGWAetTJVr4v4=
			</data>
		</dict>
		<key>Headers/CriticalAlertsPermissionStrategy.h</key>
		<dict>
			<key>hash</key>
			<data>
			TDATrFm293Gs/WJ+i9elQ5Ty0Ms=
			</data>
			<key>hash2</key>
			<data>
			r3Ig/lRHFtHu30oEVwyVQjKHtAvQ7+042tUR46Epnco=
			</data>
		</dict>
		<key>Headers/EventPermissionStrategy.h</key>
		<dict>
			<key>hash</key>
			<data>
			8TQLrJJiXENXA0pzyX0MAehoib0=
			</data>
			<key>hash2</key>
			<data>
			RiIa+gGIv0FHQMMH403dbP13+CFGZakzWcX4vURlN14=
			</data>
		</dict>
		<key>Headers/LocationPermissionStrategy.h</key>
		<dict>
			<key>hash</key>
			<data>
			4HZBgjY+jnz+Kef7iwJteHsOWA8=
			</data>
			<key>hash2</key>
			<data>
			myNRYAK+ykYvBjMj53oJ1wIJRR4xOUP7TFQEwJZmHPc=
			</data>
		</dict>
		<key>Headers/MediaLibraryPermissionStrategy.h</key>
		<dict>
			<key>hash</key>
			<data>
			nE9rvzK+2d+hcHKuCR+XtS8mDwM=
			</data>
			<key>hash2</key>
			<data>
			WEBI3xU4uotIH5yLR8R+G/H5kno05nmKltW/p9CykxU=
			</data>
		</dict>
		<key>Headers/NotificationPermissionStrategy.h</key>
		<dict>
			<key>hash</key>
			<data>
			W/t0YnZeptSrUKh9CfnGqgLXJxs=
			</data>
			<key>hash2</key>
			<data>
			U/EqmXTD1SRvYAPQToIM9d8MhjWqTHhNtBcOoG5Luho=
			</data>
		</dict>
		<key>Headers/PermissionHandlerEnums.h</key>
		<dict>
			<key>hash</key>
			<data>
			Zk+h1C8RKll80Wp3k5TEdY45wjk=
			</data>
			<key>hash2</key>
			<data>
			Vqt68tV7QoCIjCQ1ZnsclVd0Ia2kuMlpN0onYS+9vWg=
			</data>
		</dict>
		<key>Headers/PermissionHandlerPlugin.h</key>
		<dict>
			<key>hash</key>
			<data>
			US8gfGrgvyHBMXjEc33uZU/u1/E=
			</data>
			<key>hash2</key>
			<data>
			qw8RV61OI4XxMpRQh1QTQQEiObRyJ3gNqeXAgxxMD6c=
			</data>
		</dict>
		<key>Headers/PermissionManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			G4OvECvMo3LuS67YcSCDxvDGi4s=
			</data>
			<key>hash2</key>
			<data>
			YJg3Mo/e8dtO2wQh1bVRI8kGaM3vhOVfY1KrAzJOmdA=
			</data>
		</dict>
		<key>Headers/PermissionStrategy.h</key>
		<dict>
			<key>hash</key>
			<data>
			WIUX3JCm9FUEJMoS1xdJTAxa/ik=
			</data>
			<key>hash2</key>
			<data>
			QZiNFqZAQ8mgm5sA8WnvJpAJ+xmTd6DhdG+T34t2oXg=
			</data>
		</dict>
		<key>Headers/PhonePermissionStrategy.h</key>
		<dict>
			<key>hash</key>
			<data>
			LsoP+Y2q9MdLenVRQzHFKrXwRIc=
			</data>
			<key>hash2</key>
			<data>
			Q2ytTOkdveMpynMYLHluRQwxxk/r6JeZPv4N37W9d9Q=
			</data>
		</dict>
		<key>Headers/PhotoPermissionStrategy.h</key>
		<dict>
			<key>hash</key>
			<data>
			QLgVRQwqcCVRmpfQThnv+4EIrEk=
			</data>
			<key>hash2</key>
			<data>
			jFUnkRzuhzc1haZtH4trCcPv7pnKgxDOqXC3KR4KrjY=
			</data>
		</dict>
		<key>Headers/SensorPermissionStrategy.h</key>
		<dict>
			<key>hash</key>
			<data>
			xP0948JSWc+YtL8URX8qK54p2ic=
			</data>
			<key>hash2</key>
			<data>
			555sFrHZE5aISnOxs0b/y/8EaAoqsKWKp/RtoTp/0eE=
			</data>
		</dict>
		<key>Headers/SpeechPermissionStrategy.h</key>
		<dict>
			<key>hash</key>
			<data>
			IbFEyirnw1ktEDkKos/H7ZfFj4w=
			</data>
			<key>hash2</key>
			<data>
			khedPP1JtYLi8TrS4+lKbO7zFtP1ftsYa/qNxqTHvH8=
			</data>
		</dict>
		<key>Headers/StoragePermissionStrategy.h</key>
		<dict>
			<key>hash</key>
			<data>
			puOgC58jkur+6VE/ynqT93gbVaE=
			</data>
			<key>hash2</key>
			<data>
			fzXdSapwI87Np9ohvHtabrUpIpovIL/wPHlazHwcOkU=
			</data>
		</dict>
		<key>Headers/UnknownPermissionStrategy.h</key>
		<dict>
			<key>hash</key>
			<data>
			t4BFc70Lz8ZDv/Q0Ql6IwCvrXuU=
			</data>
			<key>hash2</key>
			<data>
			s6QogQgVeKU/60rT8J+wtY57wuQA4oos8VGOe1XCvAM=
			</data>
		</dict>
		<key>Headers/permission_handler_apple-umbrella.h</key>
		<dict>
			<key>hash</key>
			<data>
			ZBe03XoX9g8bhj5mSSZkPM+iHoo=
			</data>
			<key>hash2</key>
			<data>
			A/h7t4BddfD+RIO3I6s6K0V/45lASGvPWeRVxYXGEF0=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			gm1KE03qnJ+bIZ5fWa5NiCyl/HM=
			</data>
			<key>hash2</key>
			<data>
			1keaghtAIv++ss5DHOr0GaYc37vcGEovRt35Be9B8Zc=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
