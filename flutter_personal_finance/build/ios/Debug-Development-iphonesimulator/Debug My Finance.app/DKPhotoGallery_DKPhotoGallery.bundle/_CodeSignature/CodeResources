<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Assets.car</key>
		<data>
		zdBJ3Heil/1rlk0JPdszvf2e2XY=
		</data>
		<key>Base.lproj/DKPhotoGallery.strings</key>
		<data>
		NA0j4SwR2AsLRfnHuNxL8Z1By44=
		</data>
		<key>en.lproj/DKPhotoGallery.strings</key>
		<dict>
			<key>hash</key>
			<data>
			lBygwcHp7X/0n1lfhAX4rhrabvk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>zh-Hans.lproj/DKPhotoGallery.strings</key>
		<dict>
			<key>hash</key>
			<data>
			TC+NpTOWlgfdj3gzJygtTnl0V0o=
			</data>
			<key>optional</key>
			<true/>
		</dict>
	</dict>
	<key>files2</key>
	<dict>
		<key>Assets.car</key>
		<dict>
			<key>hash</key>
			<data>
			zdBJ3Heil/1rlk0JPdszvf2e2XY=
			</data>
			<key>hash2</key>
			<data>
			mrqW9hbizCKm1MSz0hQetUNP0SY+xNTORr2gihw1i3I=
			</data>
		</dict>
		<key>Base.lproj/DKPhotoGallery.strings</key>
		<dict>
			<key>hash</key>
			<data>
			NA0j4SwR2AsLRfnHuNxL8Z1By44=
			</data>
			<key>hash2</key>
			<data>
			SDI8mZH3KxLV35hSqjP1DaoT/Ur7RH3bmV+MnjMnx54=
			</data>
		</dict>
		<key>en.lproj/DKPhotoGallery.strings</key>
		<dict>
			<key>hash</key>
			<data>
			lBygwcHp7X/0n1lfhAX4rhrabvk=
			</data>
			<key>hash2</key>
			<data>
			79OWR8uzUiipYvXTl3V4OfE8HGNgQXp4Is5CjRpErmE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>zh-Hans.lproj/DKPhotoGallery.strings</key>
		<dict>
			<key>hash</key>
			<data>
			TC+NpTOWlgfdj3gzJygtTnl0V0o=
			</data>
			<key>hash2</key>
			<data>
			KI058+XFexomjnRqlnWcg5B3sueg9C1fAlugBgmGNzs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
