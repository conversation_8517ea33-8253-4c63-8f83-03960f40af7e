# Following Environment Variables need to be configured for CICD 
# --------------------------------- #
#
# 1] EMAILS - Email of the developer who is responsible for the CICD pipeline.
#
# 2] MM_WEBHOOK_URL - URL of the Mattermost webhook. Build link will be shared here.
#
# 3] OVERAIR_API_KEY - Header token used to upload build on the server.
#
# 4] OVERAIR_API_URL - URL on which the build has to be uploaded.
#
# --------------------------------- #
# Stages added for develop , staging, enterprise, appStore_release to upload on testflight or live app
# --------------------------------- #
#
# --------------------------------- #
# Git Branches : develop, staging, enterprise, appStore_release, master(doc generate)
# --------------------------------- #
#

stages:
  - develop
  - staging
  - enterprise
  - appStore_release
  - docGenerate

# ******************************  #
variables:
  
  # ⚠️ DO NOT ADD SPACE IN THE APP NAME
  APP_NAME: "My_Finance" ####### ✍️ <-- CHANGE THIS

  APK_PATH_INITIAL: build/app/outputs/*
  ExportPlistFile: ExportOptions.plist
  ExportEnterprisePlistFile: ExportOptions_Enterprise.plist
  ExportITunesPlistFile: ExportOptions_itunes.plist

  ENTERPRISE_APK: "app-enterprise-release"
  DEV_APK: "app-development-release"
  STAGING_APK: "app-staging-release"
  PROD_APK: "app-production-release"

  FVM_VERSION: "3.29.0" ####### ✍️ <-- CHANGE THIS
# ******** SCRIPT STARTED ********* #
before_script:
  - rm -rf $PWD/$APK_PATH_INITIAL
  - rm -rf $PWD/Build/*
  - ./install_certificates.sh
  - fvm flutter clean

# ************ DEVELOP BUILD START ************ #
Develop Build:
  stage: develop
  environment:
    name: develop
  script:
    - echo $PWD
    - fvm install $FVM_VERSION 
    - fvm global $FVM_VERSION 
    - fvm flutter pub get
    - cd ios
    # - flutter precache --ios
    - pod install
    - cd ..

    # #ANDROID build commands ######## ✍️ <-- DO NOT CHANGE THIS
    - flutter build apk --flavor development --target lib/main_dev.dart
    - mv $PWD/build/app/outputs/flutter-apk/${DEV_APK}.apk $PWD/build/app/outputs/flutter-apk/${APP_NAME}.apk
    - |
      PLATFORM="Android" 
      BUILD_ENVIRONMENT="Development" # <<---- EDIT THIS - Environment of the build ----

      echo "Pipeline ID: $CI_PIPELINE_ID"
      CI_GIT_URL_INICIANIC="http://git.indianic.com/$CI_PROJECT_PATH/pipelines/$CI_PIPELINE_ID"
      echo "Pipeline URL: $CI_GIT_URL_INICIANIC"
      GIT_PIPELINE_DETAIL="Pipeline [#$CI_PIPELINE_ID]($CI_GIT_URL_INICIANIC) of branch [$CI_COMMIT_REF_NAME]($CI_PROJECT_URL/commits/$CI_COMMIT_REF_NAME) by *$GITLAB_USER_NAME*"
      BUILD_PATH=$PWD/build/app/outputs/flutter-apk/${APP_NAME}.apk 
      MM_MESSAGE="### $APP_NAME | $PLATFORM\n🚨 The required build file was not found.. Please check your build script configuration and look at the logs for further details."

      if [[ -f "$BUILD_PATH" ]]; then
        echo "Found IPA: $BUILD_PATH"
        APP_UPLOAD_RESPONSE=$(curl --silent --location "$OVERAIR_API_URL" \
          --header "Authorization: $OVERAIR_API_KEY" \
          --form "file=@\"$BUILD_PATH\"" \
          --form "emails=$EMAILS")


        echo "File Upload Response: $APP_UPLOAD_RESPONSE"
        if [[ -n "$APP_UPLOAD_RESPONSE" && "$APP_UPLOAD_RESPONSE" == http* ]]; then
          MM_MESSAGE="### $APP_NAME | $PLATFORM\n**✅ $BUILD_ENVIRONMENT build is available to download:**\n- $APP_UPLOAD_RESPONSE"
        else
          echo "Invalid or empty URL."
          MM_MESSAGE="### $APP_NAME | $PLATFORM\n⚠️ An invalid or empty URL was received from the OverAIR. Please consult the build logs for further information. For assistance with any issues, please contact @kushal OR @manish.mundra."
        fi
      else
        echo "IPA file not found."
        MM_MESSAGE="### $APP_NAME | $PLATFORM\n🚨 The required build file was not found.. Please check your build script configuration and look at the logs for further details. For assistance with any issues, please contact @kushal OR @manish.mundra."
      fi
      echo "Sending Mattermost notification: $MM_MESSAGE"
      curl -i -X POST -H 'Content-Type: application/json' \
        -d '{
        "icon_url": "https://i.ibb.co/DkHZH6H/CICD-iOS.png",
        "username": "GIT CI/CD",
        "text": "'"$MM_MESSAGE"'",
        "attachments": [{
          "color": "#36a64f",
          "text": "'"$GIT_PIPELINE_DETAIL"'"
        }]
      }' "$MM_WEBHOOK_URL"


    # iOS build commands ######## ✍️ <-- DO NOT CHANGE THIS
    - flutter build ipa --flavor development --target lib/main_dev.dart --export-options-plist=$(pwd)/$ExportPlistFile
    - releaseIPA=$(find $(pwd) -name "*.ipa")
    - echo $releaseIPA
    - |
      PLATFORM="iOS" #  <-- EDIT THIS
      echo "Pipeline ID: $CI_PIPELINE_ID"
      BUILD_ENVIRONMENT="Development" # <<---- EDIT THIS - Environment of the build ----
      CI_GIT_URL_INICIANIC="http://git.indianic.com/$CI_PROJECT_PATH/pipelines/$CI_PIPELINE_ID"
      echo "Pipeline URL: $CI_GIT_URL_INICIANIC"
      GIT_PIPELINE_DETAIL="Pipeline [#$CI_PIPELINE_ID]($CI_GIT_URL_INICIANIC) of branch [$CI_COMMIT_REF_NAME]($CI_PROJECT_URL/commits/$CI_COMMIT_REF_NAME) by *$GITLAB_USER_NAME*"
      BUILD_PATH=$releaseIPA # <-- EDIT THIS
      MM_MESSAGE="### $APP_NAME | $PLATFORM\n🚨 The required build file was not found.. Please check your build script configuration and look at the logs for further details."

      if [[ -f "$BUILD_PATH" ]]; then
        echo "Found IPA: $BUILD_PATH"
        APP_UPLOAD_RESPONSE=$(curl --silent --location "$OVERAIR_API_URL" \
          --header "Authorization: $OVERAIR_API_KEY" \
          --form "file=@\"$BUILD_PATH\"" \
          --form "emails=$EMAILS")


        echo "File Upload Response: $APP_UPLOAD_RESPONSE"
        if [[ -n "$APP_UPLOAD_RESPONSE" && "$APP_UPLOAD_RESPONSE" == http* ]]; then
          MM_MESSAGE="### $APP_NAME | $PLATFORM\n**✅ $BUILD_ENVIRONMENT build is available to download:**\n- $APP_UPLOAD_RESPONSE"
        else
          echo "Invalid or empty URL."
          MM_MESSAGE="### $APP_NAME | $PLATFORM\n⚠️ An invalid or empty URL was received from the OverAIR. Please consult the build logs for further information. For assistance with any issues, please contact @kushal OR @manish.mundra."
        fi
      else
        echo "IPA file not found."
        MM_MESSAGE="### $APP_NAME | $PLATFORM\n🚨 The required build file was not found.. Please check your build script configuration and look at the logs for further details. For assistance with any issues, please contact @kushal OR @manish.mundra."
      fi
      echo "Sending Mattermost notification: $MM_MESSAGE"
      curl -i -X POST -H 'Content-Type: application/json' \
        -d '{
        "icon_url": "https://i.ibb.co/DkHZH6H/CICD-iOS.png",
        "username": "GIT CI/CD",
        "text": "'"$MM_MESSAGE"'",
        "attachments": [{
          "color": "#36a64f",
          "text": "'"$GIT_PIPELINE_DETAIL"'"
        }]
      }' "$MM_WEBHOOK_URL"

  tags:
    - IOS
    - Xcode
    - Mac
  only:
    - develop # ✍️ NAME OF THE BRANCH ON WHICH YOU WANT TO RUN THIS.
  artifacts:
    paths:
      - $(find . -type f -name '*.ipa')
      - $PWD/build/app/outputs/flutter-apk/${APP_NAME}.apk
    expire_in: 1 day
# ************ END ************ #

# ************ staging BUILD START ************ #
Staging Build:
  stage: staging
  environment:
    name: staging
  script:
    - ./build.sh
    - echo $PWD
    - fvm install $FVM_VERSION
    - fvm global $FVM_VERSION 
    - fvm flutter pub get
    - cd ios
    - flutter precache --ios
    - pod install
    - cd ..

    # #ANDROID build commands ######## ✍️ <-- DO NOT CHANGE THIS
    - flutter build apk --flavor staging --target lib/main_staging.dart
    - mv $PWD/build/app/outputs/flutter-apk/${STAGING_APK}.apk $PWD/build/app/outputs/flutter-apk/${APP_NAME}.apk
    - |
      PLATFORM="Android" 
      echo "Pipeline ID: $CI_PIPELINE_ID"
      BUILD_ENVIRONMENT="Staging" # <<---- EDIT THIS - Environment of the build ----
      CI_GIT_URL_INICIANIC="http://git.indianic.com/$CI_PROJECT_PATH/pipelines/$CI_PIPELINE_ID"
      echo "Pipeline URL: $CI_GIT_URL_INICIANIC"
      GIT_PIPELINE_DETAIL="Pipeline [#$CI_PIPELINE_ID]($CI_GIT_URL_INICIANIC) of branch [$CI_COMMIT_REF_NAME]($CI_PROJECT_URL/commits/$CI_COMMIT_REF_NAME) by *$GITLAB_USER_NAME*"
      BUILD_PATH=$PWD/build/app/outputs/flutter-apk/${APP_NAME}.apk 
      MM_MESSAGE="### $APP_NAME | $PLATFORM\n🚨 The required build file was not found.. Please check your build script configuration and look at the logs for further details."

      if [[ -f "$BUILD_PATH" ]]; then
        echo "Found IPA: $BUILD_PATH"
        APP_UPLOAD_RESPONSE=$(curl --silent --location "$OVERAIR_API_URL" \
          --header "Authorization: $OVERAIR_API_KEY" \
          --form "file=@\"$BUILD_PATH\"" \
          --form "emails=$EMAILS")


        echo "File Upload Response: $APP_UPLOAD_RESPONSE"
        if [[ -n "$APP_UPLOAD_RESPONSE" && "$APP_UPLOAD_RESPONSE" == http* ]]; then
          MM_MESSAGE="### $APP_NAME | $PLATFORM\n**✅ $BUILD_ENVIRONMENT build is available to download:**\n- $APP_UPLOAD_RESPONSE"
        else
          echo "Invalid or empty URL."
          MM_MESSAGE="### $APP_NAME | $PLATFORM\n⚠️ An invalid or empty URL was received from the OverAIR. Please consult the build logs for further information. For assistance with any issues, please contact @kushal OR @manish.mundra."
        fi
      else
        echo "IPA file not found."
        MM_MESSAGE="### $APP_NAME | $PLATFORM\n🚨 The required build file was not found.. Please check your build script configuration and look at the logs for further details. For assistance with any issues, please contact @kushal OR @manish.mundra."
      fi
      echo "Sending Mattermost notification: $MM_MESSAGE"
      curl -i -X POST -H 'Content-Type: application/json' \
        -d '{
        "icon_url": "https://i.ibb.co/DkHZH6H/CICD-iOS.png",
        "username": "GIT CI/CD",
        "text": "'"$MM_MESSAGE"'",
        "attachments": [{
          "color": "#36a64f",
          "text": "'"$GIT_PIPELINE_DETAIL"'"
        }]
      }' "$MM_WEBHOOK_URL"


    # iOS build commands ######## ✍️ <-- DO NOT CHANGE THIS
    - flutter build ipa --flavor staging --target lib/main_staging.dart --export-options-plist=$(pwd)/$ExportPlistFile
    - releaseIPA=$(find $(pwd) -name "*.ipa")
    - echo $releaseIPA
    - |
      PLATFORM="iOS" #  <-- EDIT THIS
      BUILD_ENVIRONMENT="Staging" # <<---- EDIT THIS - Environment of the build ----
      echo "Pipeline ID: $CI_PIPELINE_ID"
      CI_GIT_URL_INICIANIC="http://git.indianic.com/$CI_PROJECT_PATH/pipelines/$CI_PIPELINE_ID"
      echo "Pipeline URL: $CI_GIT_URL_INICIANIC"
      GIT_PIPELINE_DETAIL="Pipeline [#$CI_PIPELINE_ID]($CI_GIT_URL_INICIANIC) of branch [$CI_COMMIT_REF_NAME]($CI_PROJECT_URL/commits/$CI_COMMIT_REF_NAME) by *$GITLAB_USER_NAME*"
      BUILD_PATH=$releaseIPA # <-- EDIT THIS
      MM_MESSAGE="### $APP_NAME | $PLATFORM\n🚨 The required build file was not found.. Please check your build script configuration and look at the logs for further details."

      if [[ -f "$BUILD_PATH" ]]; then
        echo "Found IPA: $BUILD_PATH"
        APP_UPLOAD_RESPONSE=$(curl --silent --location "$OVERAIR_API_URL" \
          --header "Authorization: $OVERAIR_API_KEY" \
          --form "file=@\"$BUILD_PATH\"" \
          --form "emails=$EMAILS")


        echo "File Upload Response: $APP_UPLOAD_RESPONSE"
        if [[ -n "$APP_UPLOAD_RESPONSE" && "$APP_UPLOAD_RESPONSE" == http* ]]; then
          MM_MESSAGE="### $APP_NAME | $PLATFORM\n**✅ $BUILD_ENVIRONMENT build is available to download:**\n- $APP_UPLOAD_RESPONSE"
        else
          echo "Invalid or empty URL."
          MM_MESSAGE="### $APP_NAME | $PLATFORM\n⚠️ An invalid or empty URL was received from the OverAIR. Please consult the build logs for further information. For assistance with any issues, please contact @kushal OR @manish.mundra."
        fi
      else
        echo "IPA file not found."
        MM_MESSAGE="### $APP_NAME | $PLATFORM\n🚨 The required build file was not found.. Please check your build script configuration and look at the logs for further details. For assistance with any issues, please contact @kushal OR @manish.mundra."
      fi
      echo "Sending Mattermost notification: $MM_MESSAGE"
      curl -i -X POST -H 'Content-Type: application/json' \
        -d '{
        "icon_url": "https://i.ibb.co/DkHZH6H/CICD-iOS.png",
        "username": "GIT CI/CD",
        "text": "'"$MM_MESSAGE"'",
        "attachments": [{
          "color": "#36a64f",
          "text": "'"$GIT_PIPELINE_DETAIL"'"
        }]
      }' "$MM_WEBHOOK_URL"

  tags:
    - IOS
    - Xcode
    - Mac
  only:
    - staging # ✍️ NAME OF THE BRANCH ON WHICH YOU WANT TO RUN THIS.
  artifacts:
    paths:
      - $(find . -type f -name '*.ipa')
      - $PWD/build/app/outputs/flutter-apk/${APP_NAME}.apk
    expire_in: 1 day
# ************ END ************ #


# ************ enterprise BUILD START ************ #
Enterprise Build:
  stage: enterprise
  environment:
    name: enterprise
  script:
    - ./build.sh
    - echo $PWD
    - fvm install $FVM_VERSION
    - fvm global $FVM_VERSION
    - fvm flutter pub get
    - cd ios
    - flutter precache --ios
    - pod install
    - cd ..

    # #ANDROID build commands ######## ✍️ <-- DO NOT CHANGE THIS
    - flutter build apk --flavor enterprise --target lib/main_dev.dart
    - mv $PWD/build/app/outputs/flutter-apk/${ENTERPRISE_APK}.apk $PWD/build/app/outputs/flutter-apk/${APP_NAME}.apk
    - |
      PLATFORM="Android" 
      echo "Pipeline ID: $CI_PIPELINE_ID"
      BUILD_ENVIRONMENT="Enterprise" # <<---- EDIT THIS - Environment of the build ----
      CI_GIT_URL_INICIANIC="http://git.indianic.com/$CI_PROJECT_PATH/pipelines/$CI_PIPELINE_ID"
      echo "Pipeline URL: $CI_GIT_URL_INICIANIC"
      GIT_PIPELINE_DETAIL="Pipeline [#$CI_PIPELINE_ID]($CI_GIT_URL_INICIANIC) of branch [$CI_COMMIT_REF_NAME]($CI_PROJECT_URL/commits/$CI_COMMIT_REF_NAME) by *$GITLAB_USER_NAME*"
      BUILD_PATH=$PWD/build/app/outputs/flutter-apk/${APP_NAME}.apk 
      MM_MESSAGE="### $APP_NAME | $PLATFORM\n🚨 The required build file was not found.. Please check your build script configuration and look at the logs for further details."

      if [[ -f "$BUILD_PATH" ]]; then
        echo "Found IPA: $BUILD_PATH"
        APP_UPLOAD_RESPONSE=$(curl --silent --location "$OVERAIR_API_URL" \
          --header "Authorization: $OVERAIR_API_KEY" \
          --form "file=@\"$BUILD_PATH\"" \
          --form "emails=$EMAILS")


        echo "File Upload Response: $APP_UPLOAD_RESPONSE"
        if [[ -n "$APP_UPLOAD_RESPONSE" && "$APP_UPLOAD_RESPONSE" == http* ]]; then
          MM_MESSAGE="### $APP_NAME | $PLATFORM\n**✅ $BUILD_ENVIRONMENT build is available to download:**\n- $APP_UPLOAD_RESPONSE"
        else
          echo "Invalid or empty URL."
          MM_MESSAGE="### $APP_NAME | $PLATFORM\n⚠️ An invalid or empty URL was received from the OverAIR. Please consult the build logs for further information. For assistance with any issues, please contact @kushal OR @manish.mundra."
        fi
      else
        echo "IPA file not found."
        MM_MESSAGE="### $APP_NAME | $PLATFORM\n🚨 The required build file was not found.. Please check your build script configuration and look at the logs for further details. For assistance with any issues, please contact @kushal OR @manish.mundra."
      fi
      echo "Sending Mattermost notification: $MM_MESSAGE"
      curl -i -X POST -H 'Content-Type: application/json' \
        -d '{
        "icon_url": "https://i.ibb.co/DkHZH6H/CICD-iOS.png",
        "username": "GIT CI/CD",
        "text": "'"$MM_MESSAGE"'",
        "attachments": [{
          "color": "#36a64f",
          "text": "'"$GIT_PIPELINE_DETAIL"'"
        }]
      }' "$MM_WEBHOOK_URL"


    # iOS build commands ######## ✍️ <-- DO NOT CHANGE THIS
    - flutter build ipa --flavor enterprise --target lib/main_dev.dart --export-options-plist=$(pwd)/$ExportEnterprisePlistFile
    - releaseIPA=$(find $(pwd) -name "*.ipa")
    - echo $releaseIPA
    - |
      PLATFORM="iOS" #  <-- EDIT THIS
      BUILD_ENVIRONMENT="Enterprise" # <<---- EDIT THIS - Environment of the build ----
      echo "Pipeline ID: $CI_PIPELINE_ID"
      CI_GIT_URL_INICIANIC="http://git.indianic.com/$CI_PROJECT_PATH/pipelines/$CI_PIPELINE_ID"
      echo "Pipeline URL: $CI_GIT_URL_INICIANIC"
      GIT_PIPELINE_DETAIL="Pipeline [#$CI_PIPELINE_ID]($CI_GIT_URL_INICIANIC) of branch [$CI_COMMIT_REF_NAME]($CI_PROJECT_URL/commits/$CI_COMMIT_REF_NAME) by *$GITLAB_USER_NAME*"
      BUILD_PATH=$releaseIPA # <-- EDIT THIS
      MM_MESSAGE="### $APP_NAME | $PLATFORM\n🚨 The required build file was not found.. Please check your build script configuration and look at the logs for further details."

      if [[ -f "$BUILD_PATH" ]]; then
        echo "Found IPA: $BUILD_PATH"
        APP_UPLOAD_RESPONSE=$(curl --silent --location "$OVERAIR_API_URL" \
          --header "Authorization: $OVERAIR_API_KEY" \
          --form "file=@\"$BUILD_PATH\"" \
          --form "emails=$EMAILS")


        echo "File Upload Response: $APP_UPLOAD_RESPONSE"
        if [[ -n "$APP_UPLOAD_RESPONSE" && "$APP_UPLOAD_RESPONSE" == http* ]]; then
          MM_MESSAGE="### $APP_NAME | $PLATFORM\n**✅ $BUILD_ENVIRONMENT build is available to download:**\n- $APP_UPLOAD_RESPONSE"
        else
          echo "Invalid or empty URL."
          MM_MESSAGE="### $APP_NAME | $PLATFORM\n⚠️ An invalid or empty URL was received from the OverAIR. Please consult the build logs for further information. For assistance with any issues, please contact @kushal OR @manish.mundra."
        fi
      else
        echo "IPA file not found."
        MM_MESSAGE="### $APP_NAME | $PLATFORM\n🚨 The required build file was not found.. Please check your build script configuration and look at the logs for further details. For assistance with any issues, please contact @kushal OR @manish.mundra."
      fi
      echo "Sending Mattermost notification: $MM_MESSAGE"
      curl -i -X POST -H 'Content-Type: application/json' \
        -d '{
        "icon_url": "https://i.ibb.co/DkHZH6H/CICD-iOS.png",
        "username": "GIT CI/CD",
        "text": "'"$MM_MESSAGE"'",
        "attachments": [{
          "color": "#36a64f",
          "text": "'"$GIT_PIPELINE_DETAIL"'"
        }]
      }' "$MM_WEBHOOK_URL"

  tags:
    - IOS
    - Xcode
    - Mac
  only:
    - enterprise # ✍️ NAME OF THE BRANCH ON WHICH YOU WANT TO RUN THIS.
  artifacts:
    paths:
      - $(find . -type f -name '*.ipa')
      - $PWD/build/app/outputs/flutter-apk/${APP_NAME}.apk
    expire_in: 1 day
# ************ END ************ #

# ************ START ************ #
AppStore Build:
  stage: appStore_release
  environment:
    name: testflight or iTunes release
  script:
    - ./build.sh
    - echo $PWD
    - fvm install $FVM_VERSION
    - fvm global $FVM_VERSION
    - fvm flutter pub get
    - cd ios
    - flutter precache --ios
    - pod install
    - cd ..

    # iOS build commands ######## ✍️ <-- DO NOT CHANGE THIS
    - flutter build ipa --flavor production --target lib/main_production.dart --export-options-plist=$(pwd)/$ExportITunesPlistFile
    - xcodebuild -exportArchive -archivePath $PWD/build/ios/archive/Runner.xcarchive -exportPath $PWD/build/ -exportOptionsPlist $PWD/ExportOptions_itunes.plist
    - releaseIPA=$(find $(pwd) -name "*.ipa")
    - echo $releaseIPA
    - ./upload_store_ipa/uploadApp.sh
    - curl -i -X POST --data-urlencode 'payload={"text":"#### '$SCHEME'\n**Testflight/Distribution Build**\nPlease check status from git CI/CD panel"}' $MM_WEBHOOK_URL
  tags:
    - IOS
    - Xcode
    - Mac
  only:
    - production-release # ✍️ NAME OF THE BRANCH ON WHICH YOU WANT TO RUN THIS.
  artifacts:
    paths:
      - $(find . -type f -name '*.ipa')
      - $PWD/build/app/outputs/flutter-apk/${APP_NAME}.apk
    expire_in: 1 day
# ************ END ************ #

# ************ START ************ #
AppStore Build:
  stage: docGenerate
  environment:
    name: Doc Generate
  script:
    - dart doc .

  tags:
    - IOS
    - Xcode
    - Mac
  only:
    - master # ✍️ NAME OF THE BRANCH ON WHICH YOU WANT TO RUN THIS.
  artifacts:
    paths:
      - $PWD/doc/api
    expire_in: 4 weeks
# ************ END ************ #
