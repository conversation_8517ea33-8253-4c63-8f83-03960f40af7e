# Personal Finance App - Flutter

## Getting Started

A comprehensive personal finance management app built with Flutter that helps users track their income, expenses, budgets, investments, and financial goals. All data is stored locally on the device with no server dependency, ensuring complete privacy and offline functionality.

## App Description

Personal Finance App is a feature-rich financial management application that empowers users to take complete control of their finances. With biometric authentication, comprehensive expense tracking, advanced investment portfolio management, intelligent analytics, PDF export capabilities, and insightful visualizations, users can manage their money effectively while maintaining complete data privacy through local-only storage.

## ✨ Key Features

### 🔐 **Security & Authentication**
- **Biometric Authentication**: Secure login using Face ID/Touch ID/Fingerprint
- **Privacy First**: All data stored locally on device - no server required
- **Security Settings**: Comprehensive biometric authentication management
- **Data Protection**: Secure local storage with encryption

### 🏦 **Account Management**
- **Multi-Bank Support**: Connect and manage multiple bank accounts
- **Real-time Balances**: Automatic balance updates with each transaction
- **Bank Account Editing**: Update account details and current balances
- **Cash Account Support**: Track cash transactions alongside bank accounts
- **Account Overview**: Complete account summary with balance tracking

### 💰 **Transaction Management**
- **Income & Expense Tracking**: Add and categorize all financial transactions
- **Smart Categories**: Comprehensive income and expense category management
- **Flexible Date Selection**: Add transactions for past and future dates with time selection
- **Manual Transaction Control**: Users control when to navigate back after adding transactions
- **Transaction History**: Complete transaction listing with advanced filtering capabilities
- **Bulk Operations**: Efficient transaction management with batch operations

### 📊 **Investment Portfolio Management**
- **Investment Tracking**: Comprehensive investment portfolio management
- **Multiple Investment Types**: Support for stocks, mutual funds, fixed deposits, and more
- **Return Calculations**: Simple interest, compound interest, and manual entry options
- **Investment Growth**: Automatic value calculations based on return rates and time
- **Investment Entries**: Add additional investments to existing portfolios
- **Withdrawal Management**: Partial and full investment withdrawals with penalty calculations
- **Investment Analytics**: Performance tracking with profit/loss calculations
- **Category Integration**: Link investments to expense categories for automatic tracking

### 📈 **Advanced Budget Planning**
- **Budget Creation**: Set and manage budgets for different categories
- **Budget Tracking**: Monitor spending against budget limits
- **Visual Indicators**: Progress bars and alerts for budget status
- **Period-based Budgets**: Monthly, quarterly, and yearly budget planning
- **Category-wise Analysis**: Detailed breakdown of budget vs actual spending

### 📅 **Upcoming Expenses**
- **Expense Planning**: Add and track future expenses with due dates
- **Custom Categories**: Manage upcoming expense categories
- **Smart Notifications**: Get notified about upcoming due dates
- **One-Tap Payment**: Mark upcoming expenses as paid and auto-add to transactions
- **Due Date Management**: Track and manage payment schedules
- **Recurring Expense Support**: Set up recurring upcoming expenses

### 📊 **Enhanced Dashboard Analytics**
- **Interactive Charts**: Dynamic spending trends and cash flow visualizations
- **Reordered Layout**: Optimized section order - Total Balance, Accounts, Recent Transactions, Cash Flow, Spending Trends, Top Categories, Recurring Transactions
- **Weekly Analytics**: Complete weekly breakdown showing all weeks of current month
- **Cash Flow Tracking**: Income vs expenses with period-based filtering (weekly/monthly/yearly)
- **Top Categories Analysis**: Identify highest spending and earning categories with percentages
- **Smart Period Selection**: Dynamic chart periods that update based on current month
- **Investment Overview**: Portfolio performance and value tracking on dashboard

### 🔄 **Advanced Recurring Transactions**
- **Extended Frequencies**: Daily, Weekly, Monthly, Quarterly, Half-Yearly, and Yearly options
- **Auto-Processing**: Automatic transaction creation on due dates with status tracking
- **Smart Management**: Full CRUD operations with due date monitoring
- **Processing History**: Track when recurring transactions were last processed
- **Manual Processing**: Force process due recurring transactions anytime
- **End Date Support**: Optional end dates for recurring transactions

### 📄 **Data Export & Reporting**
- **PDF Export**: Generate comprehensive financial reports with professional formatting
- **Transaction Reports**: Complete transaction history with categorization
- **Bank Balance Summary**: Include current account balances in reports
- **Investment Summary**: Detailed investment portfolio reports on separate pages
- **Multi-Page Reports**: Organized report structure with proper pagination
- **Export Customization**: Select date ranges and specific data for export

### 🎛️ **Profile & Data Management**
- **Comprehensive Profile Settings**: Access to all financial management tools
- **Data Visualization**: Advanced charts and analytics with professional color schemes
- **Income/Expense Categories**: Manage and customize transaction categories
- **Notification Settings**: Configure alerts and reminders
- **Data Import/Export**: Backup and restore functionality
- **Advanced Analytics Access**: Deep dive into financial patterns and trends

### 📱 **Enhanced User Experience**
- **Modern Dashboard**: Comprehensive financial overview with optimized layout
- **Professional Color Schemes**: Vibrant yet professional color palette for data management sections
- **Theme Consistency**: Dark and light themes with consistent color schemes across all screens
- **Privacy Controls**: Selective amount visibility - hide income while showing expenses
- **Responsive Design**: Optimized for all screen sizes with proper text overflow handling
- **Smooth Navigation**: AppRouter-based navigation with proper state management
- **Loading States**: Proper loading indicators and error handling

### 🎨 **Visual Design System**
- **Consistent Theming**: Unified color palette and typography using Poppins font
- **Material Design**: Modern UI components with proper elevation and shadows
- **Color-Coded Elements**: Intuitive color coding for different transaction types and categories
- **Chart Visualizations**: Professional charts using FL Chart library
- **Interactive Elements**: Proper touch targets and visual feedback
- **Professional Data Cards**: Enhanced visual presentation of financial data

### 🔧 **Technical Features**
- **Local Storage**: Complete offline functionality with SharedPreferences
- **Data Import/Export**: Comprehensive backup and restore capabilities
- **Multi-Flavor Support**: Development, Staging, Production, and Enterprise builds
- **Performance Optimized**: Efficient data loading and UI rendering
- **Error Handling**: Comprehensive error handling with user-friendly messages
- **Memory Management**: Optimized data loading and state management

## Installation Requirements

- iOS 15+
- Xcode 16+
- Android API Level 21+
- Android Studio
- Flutter 3.29.x
- Biometric hardware support (for Face/Touch ID)

## Contributors

- **Development Team**
  1. Manish Mundra - Tech Lead

- **UI/UX Design**
  1. Manish Mundra

## Project Details

| Title                    | Description                              |
| ------------------------ | ---------------------------------------- |
| App Type                 | Personal Finance Management              |
| Platform                 | Flutter (iOS & Android)                 |
| Architecture             | Clean Architecture with Local Storage   |
| Database                 | Local Storage (Shared Preferences)      |
| Authentication           | Biometric (Face ID/Touch ID/Fingerprint)|
| Data Privacy             | 100% Local - No Server Required         |
| Offline Support          | Full Offline Functionality              |
| Charts & Analytics       | FL Chart Library Integration             |
| PDF Generation           | Professional Financial Reports           |
| Investment Management    | Complete Portfolio Tracking             |

## 🏗️ Architecture & Technical Features

### **Local Storage**
- All data stored using Shared Preferences
- No server dependency - complete privacy
- Import/Export functionality for data backup
- Automatic data migration and updates
- Investment data management with complex calculations

### **Navigation**
- Go Router for type-safe navigation
- AppRouter pattern implementation (@AppRouter)
- Clean route management with proper parameter passing
- Consistent navigation throughout the entire app

### **State Management**
- StatefulWidget with optimized local state management
- Service layer for business logic separation
- Repository pattern for data access
- Efficient data loading and caching
- Investment service with advanced calculations

### **Design System**
- Custom theme with Poppins font family
- Consistent color palette (AppColorPalette)
- Responsive UI components with proper theming
- Dark/Light mode support with proper contrast
- Professional data management color schemes

### **Charts & Analytics**
- FL Chart integration for professional visualizations
- Dynamic data calculation and display
- Interactive charts with tooltips and legends
- Period-based filtering and analysis
- Investment performance tracking

### **Investment Management System**
- Complex return calculations (simple/compound interest)
- Automatic portfolio value updates
- Withdrawal management with penalty calculations
- Investment entry tracking and management
- Category-based investment organization

### **PDF Export System**
- Professional report generation
- Multi-page document support
- Investment summaries on separate pages
- Bank balance integration
- Customizable date ranges and data selection

### **Biometric Integration**
- Face ID/Touch ID/Fingerprint authentication
- Secure local authentication storage
- Privacy-focused security implementation

## Run and Build Commands with Flavors

### **iOS Flavors - Run Commands**
- Development: `flutter run --flavor development --target lib/main_dev.dart`
- Staging: `flutter run --flavor staging --target lib/main_staging.dart`
- Production: `flutter run --flavor production --target lib/main_production.dart`
- Enterprise: `flutter run --flavor enterprise --target lib/main_enterprise.dart`

### **iOS Flavors - Build Commands**
- Development: `flutter build ipa --flavor development --target lib/main_dev.dart`
- Staging: `flutter build ipa --flavor staging --target lib/main_staging.dart`
- Production: `flutter build ipa --flavor production --target lib/main_production.dart`
- Enterprise: `flutter build ipa --flavor enterprise --target lib/main_enterprise.dart`

### **Android Flavors - Run Commands**
- Development: `flutter run --flavor development --target lib/main_dev.dart`
- Staging: `flutter run --flavor staging --target lib/main_staging.dart`
- Production: `flutter run --flavor production --target lib/main_production.dart`
- Enterprise: `flutter run --flavor enterprise --target lib/main_enterprise.dart`

### **Android Flavors - Build Commands**
- Development: `flutter build apk --flavor development --target lib/main_dev.dart`
- Staging: `flutter build apk --flavor staging --target lib/main_staging.dart`
- Production: `flutter build apk --flavor production --target lib/main_production.dart`
- Enterprise: `flutter build apk --flavor enterprise --target lib/main_enterprise.dart`

## 🎯 Key User Flows

### **Dashboard Experience**
1. View comprehensive financial overview with balance visibility toggle
2. Monitor total balance, accounts, and recent transactions in optimized order
3. Analyze spending trends with interactive weekly/monthly charts
4. Track cash flow with dynamic period selection
5. Monitor top spending and earning categories
6. View investment portfolio performance and value
7. Access quick actions for adding transactions and managing accounts

### **Investment Management**
1. Create new investments with different return types and categories
2. Add additional investment entries to existing portfolios
3. Track automatic value calculations based on return rates
4. Monitor profit/loss and performance analytics
5. Process partial or full withdrawals with penalty calculations
6. Export investment data in comprehensive PDF reports
7. Integrate investments with expense categories for automatic transaction processing

### **Adding Transactions**
1. Tap "Add" button on dashboard
2. Select transaction type (Income/Expense) with visual toggle
3. Choose category and bank account from organized lists
4. Enter amount with validation and select date/time
5. Optionally set up as recurring transaction with flexible frequencies
6. Transaction saved with option to add more without auto-navigation
7. Automatic investment processing for investment-related categories

### **Data Export & Reporting**
1. Access transaction export from profile data management section
2. Select date ranges and preview data before export
3. Generate comprehensive PDF reports with professional formatting
4. Include bank balances, transaction summaries, and investment data
5. Investment summaries appear on separate pages for better organization
6. Export and share reports for financial planning and analysis

### **Managing Recurring Transactions**
1. Set up recurring transactions with frequencies from daily to half-yearly
2. System auto-processes transactions on due dates
3. Manual processing available with status tracking
4. Monitor due transactions with visual indicators
5. Full management with edit, delete, and end date options

### **Upcoming Expenses Management**
1. Add future expenses with due dates and categories
2. Get notifications for upcoming payments
3. One-tap conversion to actual transactions when paid
4. Track payment schedules with visual status indicators

### **Privacy & Amount Visibility**
1. Toggle amount visibility for income while keeping expenses visible
2. Biometric authentication for secure app access
3. Complete local data storage with no external communication

## 🔐 Privacy & Security

- **No Data Collection**: Zero data sent to external servers
- **Local Encryption**: Sensitive data protected locally with biometric access
- **Biometric Security**: Face/Touch ID/Fingerprint for app access
- **User Control**: Complete control over data visibility and amount display
- **Offline Only**: No internet connection required for any functionality
- **Investment Data Protection**: Secure storage of sensitive financial portfolio information

## 📱 Key Improvements & Features

### **Enhanced Dashboard**
- Optimized section order: Total Balance → Accounts → Recent Transactions → Cash Flow → Spending Trends → Top Categories → Recurring Transactions
- Interactive charts with proper data synchronization
- Dynamic weekly displays showing all weeks of current month
- Improved cash flow visualization with accurate date filtering
- Investment portfolio overview integration

### **Advanced Investment Management**
- Complete portfolio tracking with multiple investment types
- Automatic value calculations with compound and simple interest options
- Advanced withdrawal management with penalty calculations
- Investment entry management for ongoing contributions
- Category integration for automatic transaction processing
- Professional investment reporting in PDF exports

### **Professional Data Export**
- Comprehensive PDF reports with multi-page organization
- Investment summaries on dedicated pages
- Bank balance integration in reports
- Professional formatting with proper data visualization
- Customizable export options with date range selection

### **Better User Experience**
- Professional color schemes for data management sections
- Consistent theming across all screens with vibrant yet professional colors
- Proper loading states and error handling
- Manual navigation control in transaction adding
- Responsive design with text overflow handling
- Enhanced profile management with organized data access

### **Advanced Analytics**
- Top categories with percentage breakdowns
- Period-based filtering for all chart types
- Accurate weekly data calculation with proper boundaries
- Professional chart styling with tooltips and legends
- Investment performance analytics and tracking
- Comprehensive financial overview with multiple data points

---

**Made with ❤️ IndiaNIC**