---
alwaysApply: true
---
## Code Organization
- Follow feature-based folder structure under `lib/features/`
- Use services layer in `lib/services/` for business logic
- Keep shared widgets in `lib/shared/widgets/`
- Place utility functions in `lib/utils/helper/`
- Store models in `lib/features/models/`

## Best Practices
- Use `const` constructors where possible for performance
- Follow the established padding and margin patterns from the theme
- Use semantic naming for colors (success, warning, error) instead of generic color names
- Implement proper error handling and loading states

