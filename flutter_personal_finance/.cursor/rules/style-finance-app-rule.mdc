---
alwaysApply: true
---
## Styling & Theming
- Use `@AppColorPalette` class for all color definitions in the app use theme
- Use `@AppTextStyles` class for consistent text styling following Material Design guidelines
- Use `@AppTheme` for comprehensive theming (light/dark themes available)
- Use `@AppColorPalette` for extended color options including semantic colors (success, warning, error, info)


## Assets & Resources
- Create and use `@AppImages` class for centralized image asset management
- Store images in `assets/svg/` for vector graphics and `assets/png/` for raster images
- Use internationalization with JSON files in `langs/` directory (en.json,hi.json)
- Always add text in `langs/` folder file and use in .dart 

## UI Components
- Use Material 3 design system (useMaterial3: true)
- Follow the established color scheme for financial apps (income: green, expense: red)
- Use bank-specific colors from `AppColorPalette` for bank representations
- Implement consistent card themes, input decorations, and button styles from `@AppTheme`