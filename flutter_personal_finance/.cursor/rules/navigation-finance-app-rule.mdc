---
alwaysApply: true
---
## Navigation & Routing
- Use `@AppRouter` class for all navigation and routing throughout the app
- Use `GoRouterConstants` for route names to avoid hardcoded strings
- Navigate using `AppRouter.goNamed()` for route replacement and `AppRouter.pushNamed()` for pushing new routes
- Always use named routes instead of direct paths
- On navigation , isf no any screen routs then add 