import 'package:flutter/material.dart';

class ImgConstants {
  //Splash
  static const String splashPng = 'assets/png/splash.png';

  static const String barcode = 'assets/svg/barcode.svg';
  static const String filter = 'assets/svg/filter.svg';
  static const String search = 'assets/svg/search.svg';
  static const String googleLogo = 'assets/svg/googleLogo.svg';
  static const String twitterLogo = 'assets/svg/twitterlogo.svg';
  static const String facebookLogo = 'assets/svg/facebooklogo.svg';
  static const String appleLogo = 'assets/svg/applelogo.svg';
  static const String linkedinLogo = 'assets/svg/linkedin.svg';
}

enum FlavoursName { development, staging, production, enterprise }

enum AppDeviceType {
  webPortraitMobile,
  webTablet,
  webWideScreen,
  mobileIosAndroid,
  unknown,
}

GlobalKey<NavigatorState> kNavigatorKey = GlobalKey<NavigatorState>();
