/// API endpoints constants for the application
/// All API endpoints should be defined here for easy management
class ApiEndpoints {
  ApiEndpoints._(); // Private constructor to prevent instantiation

  // Authentication endpoints
  static const String login = 'email_read_catagory_count';

  // File endpoints
  static const String uploadFile = 'upload';
  static const String uploadProfileImage = 'user/profile/image';
  static const String uploadDocument = 'documents/upload';
  static const String downloadFile =
      'files/{id}/download'; // Use with .replaceAll('{id}', fileId)
}
