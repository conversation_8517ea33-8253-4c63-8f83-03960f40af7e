import 'dart:io';
import 'package:dio/dio.dart';
import 'package:http_parser/http_parser.dart';
import 'package:path/path.dart' as path;
import 'package:pretty_dio_logger/pretty_dio_logger.dart';
import 'package:my_finance/enviroment/config.dart';

/// ApiService class for handling API requests
/// This class provides methods for making HTTP requests using Dio
class ApiService {
  late Dio _dio;

  /// Singleton instance
  static final ApiService _instance = ApiService._internal();

  /// Factory constructor to return the same instance every time
  factory ApiService() {
    return _instance;
  }

  /// Private constructor for singleton pattern
  ApiService._internal() {
    _initDio();
  }

  /// Initialize Dio with base options
  void _initDio() {
    BaseOptions options = BaseOptions(
      baseUrl: ENV.development.baseurl,
      connectTimeout: const Duration(seconds: 30),
      receiveTimeout: const Duration(seconds: 30),
      sendTimeout: const Duration(seconds: 30),
      responseType: ResponseType.json,
      validateStatus: (status) {
        return status! < 500;
      },
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    );

    _dio = Dio(options);
    _dio.interceptors.add(PrettyDioLogger());

    // Add interceptors for logging, authentication, etc.
    _dio.interceptors.add(
      LogInterceptor(
        request: true,
        requestHeader: true,
        requestBody: true,
        responseHeader: true,
        responseBody: true,
        error: true,
      ),
    );
  }

  /// Add or update headers
  void addHeaders(Map<String, dynamic> headers) {
    _dio.options.headers.addAll(headers);
  }

  /// Store the auth token
  String? _authToken;
  bool _includeAuthToken = true;

  /// Set the authorization token to be used in API requests
  void setAuthToken(String token) {
    _authToken = token;
  }

  /// Enable or disable including the auth token in requests
  void setIncludeAuthToken(bool include) {
    _includeAuthToken = include;
  }

  /// Check if auth token is available and should be included
  bool get _shouldIncludeAuthToken => _authToken != null && _includeAuthToken;

  /// Prepare request options with merged headers and auth token if needed
  Options _prepareRequestOptions({
    Map<String, dynamic>? headers,
    Options? options,
  }) {
    Options requestOptions = options ?? Options();
    Map<String, dynamic> mergedHeaders = {};

    // Set condition if token is exist or not
    setAuthToken('SET TOKEN');

    // Add auth header if available and enabled
    if (_shouldIncludeAuthToken) {
      mergedHeaders['Authorization'] = 'Bearer $_authToken';
    }

    // Add custom headers if provided
    if (headers != null) {
      mergedHeaders.addAll(headers);
    }

    // Set merged headers to request options
    if (mergedHeaders.isNotEmpty) {
      requestOptions.headers = {...?requestOptions.headers, ...mergedHeaders};
    }

    return requestOptions;
  }

  /// Execute a request with error handling
  Future<ApiResponse> _executeRequest(
    Future<Response> Function() requestFn,
  ) async {
    try {
      final response = await requestFn();
      return _handleResponse(response);
    } on DioException catch (e) {
      return _handleDioError(e);
    } catch (e) {
      return ApiResponse(
        success: false,
        statusCode: 0,
        message: 'Unknown error occurred: ${e.toString()}',
        error: e,
      );
    }
  }

  /// GET request method
  ///
  /// [endpoint] - API endpoint
  /// [queryParameters] - Optional query parameters
  /// [headers] - Optional additional headers
  /// [options] - Optional Dio request options
  Future<ApiResponse> get({
    required String endpoint,
    Map<String, dynamic>? queryParameters,
    Map<String, dynamic>? headers,
    Options? options,
  }) async {
    final requestOptions = _prepareRequestOptions(
      headers: headers,
      options: options,
    );
    return _executeRequest(
      () => _dio.get(
        endpoint,
        queryParameters: queryParameters,
        options: requestOptions,
      ),
    );
  }

  /// POST request method
  ///
  /// [endpoint] - API endpoint
  /// [data] - Request body data
  /// [queryParameters] - Optional query parameters
  /// [headers] - Optional additional headers
  /// [options] - Optional Dio request options
  Future<ApiResponse> post({
    required String endpoint,
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Map<String, dynamic>? headers,
    Options? options,
  }) async {
    final requestOptions = _prepareRequestOptions(
      headers: headers,
      options: options,
    );
    return _executeRequest(
      () => _dio.post(
        endpoint,
        data: data,
        queryParameters: queryParameters,
        options: requestOptions,
      ),
    );
  }

  /// Handle successful response
  ApiResponse _handleResponse(Response response) {
    return ApiResponse(
      success: response.statusCode! >= 200 && response.statusCode! < 300,
      statusCode: response.statusCode ?? 0,
      data: response.data,
      message: response.statusMessage,
    );
  }

  /// Handle Dio errors
  ApiResponse _handleDioError(DioException error) {
    String errorMessage = 'Something went wrong';
    int statusCode = 0;

    switch (error.type) {
      case DioExceptionType.connectionTimeout:
        errorMessage = 'Connection timeout';
        break;
      case DioExceptionType.sendTimeout:
        errorMessage = 'Send timeout';
        break;
      case DioExceptionType.receiveTimeout:
        errorMessage = 'Receive timeout';
        break;
      case DioExceptionType.badResponse:
        statusCode = error.response?.statusCode ?? 0;
        errorMessage = error.response?.statusMessage ?? 'Bad response';
        break;
      case DioExceptionType.cancel:
        errorMessage = 'Request cancelled';
        break;
      case DioExceptionType.connectionError:
        errorMessage = 'Connection error';
        break;
      case DioExceptionType.unknown:
        if (error.error is SocketException) {
          errorMessage = 'No internet connection';
        } else {
          errorMessage = 'Unexpected error occurred';
        }
        break;
      default:
        errorMessage = 'Something went wrong';
        break;
    }

    return ApiResponse(
      success: false,
      statusCode: statusCode,
      message: errorMessage,
      error: error,
      data: error.response?.data,
    );
  }

  /// PUT request method
  ///
  /// [endpoint] - API endpoint
  /// [data] - Request body data
  /// [queryParameters] - Optional query parameters
  /// [headers] - Optional additional headers
  /// [options] - Optional Dio request options
  Future<ApiResponse> put({
    required String endpoint,
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Map<String, dynamic>? headers,
    Options? options,
  }) async {
    final requestOptions = _prepareRequestOptions(
      headers: headers,
      options: options,
    );
    return _executeRequest(
      () => _dio.put(
        endpoint,
        data: data,
        queryParameters: queryParameters,
        options: requestOptions,
      ),
    );
  }

  /// PATCH request method
  ///
  /// [endpoint] - API endpoint
  /// [data] - Request body data
  /// [queryParameters] - Optional query parameters
  /// [headers] - Optional additional headers
  /// [options] - Optional Dio request options
  Future<ApiResponse> patch({
    required String endpoint,
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Map<String, dynamic>? headers,
    Options? options,
  }) async {
    final requestOptions = _prepareRequestOptions(
      headers: headers,
      options: options,
    );
    return _executeRequest(
      () => _dio.patch(
        endpoint,
        data: data,
        queryParameters: queryParameters,
        options: requestOptions,
      ),
    );
  }

  /// DELETE request method
  ///
  /// [endpoint] - API endpoint
  /// [data] - Optional request body data
  /// [queryParameters] - Optional query parameters
  /// [headers] - Optional additional headers
  /// [options] - Optional Dio request options
  Future<ApiResponse> delete({
    required String endpoint,
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Map<String, dynamic>? headers,
    Options? options,
  }) async {
    final requestOptions = _prepareRequestOptions(
      headers: headers,
      options: options,
    );
    return _executeRequest(
      () => _dio.delete(
        endpoint,
        data: data,
        queryParameters: queryParameters,
        options: requestOptions,
      ),
    );
  }

  /// Upload file to server
  ///
  /// [endpoint] - API endpoint
  /// [file] - File to upload
  /// [fileKey] - Form field name for the file (default: 'file')
  /// [data] - Additional form data to send with the file
  /// [onSendProgress] - Callback for upload progress
  /// [queryParameters] - Optional query parameters
  /// [headers] - Optional additional headers
  /// [options] - Optional Dio request options
  Future<ApiResponse> uploadFile({
    required String endpoint,
    required File file,
    String fileKey = 'file',
    Map<String, dynamic>? data,
    Function(int sent, int total)? onSendProgress,
    Map<String, dynamic>? queryParameters,
    Map<String, dynamic>? headers,
    Options? options,
  }) async {
    try {
      // Create form data
      final formData = FormData();

      // Add file to form data
      final fileName = path.basename(file.path);
      final fileExtension = path.extension(file.path).replaceAll('.', '');

      formData.files.add(
        MapEntry(
          fileKey,
          await MultipartFile.fromFile(
            file.path,
            filename: fileName,
            contentType: MediaType('application', fileExtension),
          ),
        ),
      );

      // Add additional data if provided
      if (data != null) {
        data.forEach((key, value) {
          formData.fields.add(MapEntry(key, value.toString()));
        });
      }

      // Prepare request options
      final requestOptions = _prepareRequestOptions(
        headers: headers,
        options: options,
      );

      // Execute the request
      return _executeRequest(
        () => _dio.post(
          endpoint,
          data: formData,
          queryParameters: queryParameters,
          options: requestOptions,
          onSendProgress: onSendProgress,
        ),
      );
    } catch (e) {
      return ApiResponse(
        success: false,
        statusCode: 0,
        message: 'File upload failed: ${e.toString()}',
        error: e,
      );
    }
  }

  /// Download file from server
  ///
  /// [endpoint] - API endpoint
  /// [savePath] - Path where the file should be saved
  /// [onReceiveProgress] - Callback for download progress
  /// [queryParameters] - Optional query parameters
  /// [headers] - Optional additional headers
  /// [options] - Optional Dio request options
  Future<ApiResponse> downloadFile({
    required String endpoint,
    required String savePath,
    Function(int received, int total)? onReceiveProgress,
    Map<String, dynamic>? queryParameters,
    Map<String, dynamic>? headers,
    Options? options,
  }) async {
    try {
      // Prepare request options
      final requestOptions = _prepareRequestOptions(
        headers: headers,
        options: options ?? Options(responseType: ResponseType.bytes),
      );

      // If options doesn't have responseType set, set it to bytes
      if (requestOptions.responseType == null) {
        requestOptions.responseType = ResponseType.bytes;
      }

      // Execute the request
      final response = await _dio.get(
        endpoint,
        queryParameters: queryParameters,
        options: requestOptions,
        onReceiveProgress: onReceiveProgress,
      );

      // Save the file
      final file = File(savePath);
      await file.writeAsBytes(response.data);

      return ApiResponse(
        success: true,
        statusCode: response.statusCode ?? 200,
        message: 'File downloaded successfully',
        data: {'filePath': savePath},
      );
    } catch (e) {
      return ApiResponse(
        success: false,
        statusCode: 0,
        message: 'File download failed: ${e.toString()}',
        error: e,
      );
    }
  }
}

/// ApiResponse class to standardize API responses
class ApiResponse {
  final bool success;
  final int statusCode;
  final dynamic data;
  final String? message;
  final dynamic error;

  ApiResponse({
    required this.success,
    required this.statusCode,
    this.data,
    this.message,
    this.error,
  });

  /// Check if response has data
  bool get hasData => data != null;

  /// Check if response has error
  bool get hasError => error != null;
}
