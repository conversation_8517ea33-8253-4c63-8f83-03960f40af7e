import 'package:flutter/material.dart';
import 'package:dio/dio.dart';
import 'api_service.dart';
import 'dart:io';
import 'package:my_finance/core/constants/api_constants.dart';

/// Example class demonstrating how to use the ApiService
class ApiServiceExample {
  final ApiService _apiService = ApiService();

  /// Initialize the API service
  /// Note: Base URL and default headers are now set in the ApiService initialization
  ApiServiceExample() {
    // If needed, add additional headers
    _apiService.addHeaders({'Custom-Header': 'CustomValue'});
  }

  /// Example 1: Simple GET request
  Future<void> getUsers() async {
    final response = await _apiService.get(endpoint: '/users');

    if (response.success) {
      // Handle successful response
      print('Users: ${response.data}');
    } else {
      // Handle error
      print('Error: ${response.message}');
    }
  }

  /// Example 2: GET request with query parameters
  Future<void> searchUsers(String query, int page) async {
    final response = await _apiService.get(
      endpoint: '/users/search',
      queryParameters: {'q': query, 'page': page, 'limit': 20},
    );

    if (response.success) {
      // Handle successful response
      print('Search results: ${response.data}');
    } else {
      // Handle error
      print('Error: ${response.message}');
    }
  }

  /// Example 3: GET request with custom headers
  Future<void> getUserProfile(String userId) async {
    final response = await _apiService.get(
      endpoint: '/users/$userId',
      headers: {'X-Custom-Header': 'CustomValue'},
    );

    if (response.success) {
      // Handle successful response
      print('User profile: ${response.data}');
    } else {
      // Handle error
      print('Error: ${response.message}');
    }
  }

  /// Example 4: GET request with authentication
  Future<void> getAuthenticatedData(String token) async {
    final response = await _apiService.get(endpoint: '/user/dashboard');

    if (response.success) {
      // Handle successful response
      print('Dashboard data: ${response.data}');
    } else {
      // Handle error
      print('Error: ${response.message}');
    }
  }

  /// Example 5: Temporarily disable authentication for specific requests
  Future<void> getPublicDataWithoutAuth() async {
    // Disable auth token for this specific set of requests
    _apiService.setIncludeAuthToken(false);

    // This request will not include the auth token
    final response = await _apiService.get(endpoint: '/public/data');

    // Re-enable auth token for subsequent requests
    _apiService.setIncludeAuthToken(true);

    if (response.success) {
      print('Public data: ${response.data}');
    } else {
      print('Error: ${response.message}');
    }
  }

  /// Example 6: POST request with data
  Future<void> createUser(Map<String, dynamic> userData) async {
    final response = await _apiService.post(endpoint: '/users', data: userData);

    if (response.success) {
      // Handle successful response
      print('User created: ${response.data}');
    } else {
      // Handle error
      print('Error: ${response.message}');
    }
  }

  /// Example 7: POST request with form data
  Future<void> uploadUserProfile(String userId, String filePath) async {
    // Create form data
    final formData = FormData.fromMap({
      'user_id': userId,
      'profile_picture': await MultipartFile.fromFile(filePath),
    });

    final response = await _apiService.post(
      endpoint: '/users/profile',
      data: formData,
      headers: {'Content-Type': 'multipart/form-data'},
    );

    if (response.success) {
      // Handle successful response
      print('Profile uploaded: ${response.data}');
    } else {
      // Handle error
      print('Error: ${response.message}');
    }
  }

  /// Upload profile image
  Future<Map<String, dynamic>?> uploadProfileImage(File imageFile) async {
    try {
      final response = await _apiService.uploadFile(
        endpoint: ApiEndpoints.uploadProfileImage,
        file: imageFile,
        fileKey: 'profile_image',
        data: {'user_id': '123'}, // Add any additional data needed
        onSendProgress: (sent, total) {
          // Update progress if needed
          final progress = (sent / total) * 100;
          print('Upload progress: $progress%');
        },
      );

      if (response.success) {
        return response.data;
      } else {
        print('Error uploading profile image: ${response.message}');
        return null;
      }
    } catch (e) {
      print('Exception uploading profile image: $e');
      return null;
    }
  }

  /// Download a document
  Future<String?> downloadDocument(String documentId, String savePath) async {
    try {
      final endpoint = ApiEndpoints.downloadFile.replaceAll('{id}', documentId);

      final response = await _apiService.downloadFile(
        endpoint: endpoint,
        savePath: savePath,
        onReceiveProgress: (received, total) {
          // Update progress if needed
          final progress = (received / total) * 100;
          print('Download progress: $progress%');
        },
      );

      if (response.success) {
        return response.data['filePath'];
      } else {
        print('Error downloading document: ${response.message}');
        return null;
      }
    } catch (e) {
      print('Exception downloading document: $e');
      return null;
    }
  }

  /// Example 8: Using the API service in a Flutter widget
  Widget buildUserListWidget() {
    return FutureBuilder<ApiResponse>(
      future: _apiService.get(endpoint: '/users'),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const CircularProgressIndicator();
        } else if (snapshot.hasError) {
          return Text('Error: ${snapshot.error}');
        } else if (snapshot.hasData) {
          final response = snapshot.data!;

          if (response.success) {
            // Convert data to list of users
            final users = (response.data as List).map((user) => user).toList();

            return ListView.builder(
              itemCount: users.length,
              itemBuilder: (context, index) {
                return ListTile(
                  title: Text(users[index]['name']),
                  subtitle: Text(users[index]['email']),
                );
              },
            );
          } else {
            return Text('API Error: ${response.message}');
          }
        }

        return const Text('No data available');
      },
    );
  }
}
