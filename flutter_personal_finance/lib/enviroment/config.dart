/// This class provides configuration for the app.
///
/// The [flavor] property is used to determine the environment.
/// The [baseurl] property is used to determine the base URL for API requests.
class AppConfig {
  final String flavor;

  AppConfig({required this.flavor});
}

ENV env = ENV.development;

/// The environment enum.
///
/// The [development] environment is used for development.
/// The [staging] environment is used for QA.
/// The [production] environment is used for live.
enum ENV { development, staging, production }

/// Extensions for the [ENV] enum.
extension ConfigExt on ENV {
  /// The base URL for the environment.
  String get baseurl {
    switch (this) {
      case ENV.development:
        return 'https://xyrhmwjzmnqbgrlpvgzy.supabase.co/rest/v1/';
      case ENV.staging:
        return 'https://xyrhmwjzmnqbgrlpvgzy.supabase.co/rest/v1/';
      case ENV.production:
        return 'https://xyrhmwjzmnqbgrlpvgzy.supabase.co/rest/v1/';
    }
  }

  /// The Supabase API key for the environment.
  String get supabaseApiKey {
    switch (this) {
      case ENV.development:
        return 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inh5cmhtd2p6bW5xYmdybHB2Z3p5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI0MjE4NjcsImV4cCI6MjA2Nzk5Nzg2N30.hd1z8enRIOGnC0GFwd_KopRPC3av3oM0w2vFvke2jUI';
      case ENV.staging:
        return 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inh5cmhtd2p6bW5xYmdybHB2Z3p5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI0MjE4NjcsImV4cCI6MjA2Nzk5Nzg2N30.hd1z8enRIOGnC0GFwd_KopRPC3av3oM0w2vFvke2jUI';
      case ENV.production:
        return 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inh5cmhtd2p6bW5xYmdybHB2Z3p5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI0MjE4NjcsImV4cCI6MjA2Nzk5Nzg2N30.hd1z8enRIOGnC0GFwd_KopRPC3av3oM0w2vFvke2jUI';
    }
  }
}
