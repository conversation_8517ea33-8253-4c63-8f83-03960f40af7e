import 'package:flutter/material.dart';

/// Button types supported by CustomButton
enum ButtonType {
  /// Elevated button with background color and elevation
  elevated,

  /// Text button with no background
  text,

  /// Outlined button with border
  outlined,

  /// Icon button (circular)
  icon,
}

/// A custom button widget that supports multiple button types
/// with customizable appearance and behavior
class CustomButton extends StatelessWidget {
  /// The type of button to display
  final ButtonType type;

  /// The callback when button is pressed
  final VoidCallback? onPressed;

  /// Condition that determines if the button is enabled
  /// If true, the button will be enabled; if false, it will be disabled
  final bool? isEnabled;

  /// The child widget inside the button (usually Text or Icon)
  final Widget child;

  /// Background color of the button (for elevated type)
  final Color? backgroundColor;

  /// Text/content color of the button
  final Color? foregroundColor;

  /// Border color for outlined button
  final Color? borderColor;

  /// Whether to show a loading indicator instead of child
  final bool isLoading;

  /// Width of the button, null for auto-sizing
  final double? width;

  /// Height of the button, null for auto-sizing
  final double? height;

  /// Border radius of the button
  final BorderRadius? borderRadius;

  /// Padding inside the button
  final EdgeInsetsGeometry? padding;

  /// Icon to show before text (for non-icon buttons)
  final IconData? prefixIcon;

  /// Icon to show after text (for non-icon buttons)
  final IconData? suffixIcon;

  /// Size of the icon
  final double? iconSize;

  /// Tooltip text for the button
  final String? tooltip;

  /// Creates a custom button with specified type and properties
  const CustomButton({
    Key? key,
    required this.type,
    required this.onPressed,
    required this.child,
    this.backgroundColor,
    this.foregroundColor,
    this.borderColor,
    this.isLoading = false,
    this.width,
    this.height,
    this.borderRadius,
    this.padding,
    this.prefixIcon,
    this.suffixIcon,
    this.iconSize,
    this.tooltip,
    this.isEnabled = true,
  }) : super(key: key);

  /// Creates an elevated button
  factory CustomButton.elevated({
    required VoidCallback? onPressed,
    required Widget child,
    Color? backgroundColor,
    Color? foregroundColor,
    Color? borderColor,
    bool isLoading = false,
    double? width,
    double? height,
    BorderRadius? borderRadius,
    EdgeInsetsGeometry? padding,
    IconData? prefixIcon,
    IconData? suffixIcon,
    String? tooltip,
    bool? isEnabled,
  }) {
    return CustomButton(
      type: ButtonType.elevated,
      onPressed: onPressed,
      child: child,
      backgroundColor: backgroundColor,
      foregroundColor: foregroundColor,
      borderColor: borderColor,
      isLoading: isLoading,
      width: width,
      height: height,
      borderRadius: borderRadius,
      padding: padding,
      prefixIcon: prefixIcon,
      suffixIcon: suffixIcon,
      tooltip: tooltip,
      isEnabled: isEnabled,
    );
  }

  /// Creates a text button
  factory CustomButton.text({
    required VoidCallback? onPressed,
    required Widget child,
    Color? foregroundColor,
    Color? borderColor,
    bool isLoading = false,
    double? width,
    double? height,
    BorderRadius? borderRadius,
    EdgeInsetsGeometry? padding,
    IconData? prefixIcon,
    IconData? suffixIcon,
    String? tooltip,
    bool? isEnabled,
  }) {
    return CustomButton(
      type: ButtonType.text,
      onPressed: onPressed,
      child: child,
      foregroundColor: foregroundColor,
      borderColor: borderColor,
      isLoading: isLoading,
      width: width,
      height: height,
      borderRadius: borderRadius,
      padding: padding,
      prefixIcon: prefixIcon,
      suffixIcon: suffixIcon,
      tooltip: tooltip,
      isEnabled: isEnabled,
    );
  }

  /// Creates an outlined button
  factory CustomButton.outlined({
    required VoidCallback? onPressed,
    required Widget child,
    Color? foregroundColor,
    Color? borderColor,
    bool isLoading = false,
    double? width,
    double? height,
    BorderRadius? borderRadius,
    EdgeInsetsGeometry? padding,
    IconData? prefixIcon,
    IconData? suffixIcon,
    String? tooltip,
    bool? isEnabled,
  }) {
    return CustomButton(
      type: ButtonType.outlined,
      onPressed: onPressed,
      child: child,
      foregroundColor: foregroundColor,
      borderColor: borderColor,
      isLoading: isLoading,
      width: width,
      height: height,
      borderRadius: borderRadius,
      padding: padding,
      prefixIcon: prefixIcon,
      suffixIcon: suffixIcon,
      tooltip: tooltip,
      isEnabled: isEnabled,
    );
  }

  /// Creates an icon button
  factory CustomButton.icon({
    required VoidCallback? onPressed,
    required Widget child,
    Color? backgroundColor,
    Color? foregroundColor,
    Color? borderColor,
    bool isLoading = false,
    double? iconSize,
    BorderRadius? borderRadius,
    String? tooltip,
    bool? isEnabled,
  }) {
    return CustomButton(
      type: ButtonType.icon,
      onPressed: onPressed,
      child: child,
      backgroundColor: backgroundColor,
      foregroundColor: foregroundColor,
      borderColor: borderColor,
      isLoading: isLoading,
      iconSize: iconSize,
      borderRadius: borderRadius,
      tooltip: tooltip,
      isEnabled: isEnabled,
    );
  }

  @override
  Widget build(BuildContext context) {
    // If loading, show loading indicator instead of child
    final buttonChild =
        isLoading
            ? SizedBox(
              width: 24,
              height: 24,
              child: CircularProgressIndicator(
                strokeWidth: 2.5,
                valueColor: AlwaysStoppedAnimation<Color>(
                  foregroundColor ?? Theme.of(context).colorScheme.onPrimary,
                ),
              ),
            )
            : _buildChildWithIcons();

    // Apply tooltip if provided
    if (tooltip != null) {
      return Tooltip(
        message: tooltip!,
        child: _buildButtonByType(context, buttonChild),
      );
    }

    return _buildButtonByType(context, buttonChild);
  }

  Widget _buildChildWithIcons() {
    // For icon button, just return the child
    if (type == ButtonType.icon) {
      return child;
    }

    // For other button types, add prefix/suffix icons if needed
    if (prefixIcon == null && suffixIcon == null) {
      return child;
    }

    return Row(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        if (prefixIcon != null) ...[
          Icon(prefixIcon, size: iconSize ?? 18),
          const SizedBox(width: 8),
        ],
        child,
        if (suffixIcon != null) ...[
          const SizedBox(width: 8),
          Icon(suffixIcon, size: iconSize ?? 18),
        ],
      ],
    );
  }

  Widget _buildButtonByType(BuildContext context, Widget buttonChild) {
    final theme = Theme.of(context);
    final defaultBorderRadius = BorderRadius.circular(8);

    // Determine if button should be enabled based on isEnabled and onPressed
    final bool buttonEnabled = (isEnabled ?? true) && onPressed != null;
    final VoidCallback? effectiveOnPressed = buttonEnabled ? onPressed : null;

    switch (type) {
      case ButtonType.elevated:
        return SizedBox(
          width: width,
          height: height,
          child: ElevatedButton(
            onPressed: isLoading ? null : effectiveOnPressed,
            style: ElevatedButton.styleFrom(
              backgroundColor: backgroundColor,
              foregroundColor: foregroundColor,
              padding: padding,
              disabledBackgroundColor: backgroundColor?.withValues(alpha: 0.6),
              disabledForegroundColor: foregroundColor?.withValues(alpha: 0.6),
              shape: RoundedRectangleBorder(
                borderRadius: borderRadius ?? defaultBorderRadius,
                side:
                    borderColor != null
                        ? BorderSide(
                          color:
                              buttonEnabled
                                  ? borderColor!
                                  : borderColor!.withValues(alpha: 0.6),
                          width: 1.5,
                        )
                        : BorderSide.none,
              ),
            ),
            child: buttonChild,
          ),
        );

      case ButtonType.text:
        return SizedBox(
          width: width,
          height: height,
          child: TextButton(
            onPressed: isLoading ? null : effectiveOnPressed,
            style: TextButton.styleFrom(
              foregroundColor: foregroundColor,
              padding: padding,
              disabledForegroundColor: foregroundColor?.withValues(alpha: 0.6),
              shape: RoundedRectangleBorder(
                borderRadius: borderRadius ?? defaultBorderRadius,
                side:
                    borderColor != null
                        ? BorderSide(
                          color:
                              buttonEnabled
                                  ? borderColor!
                                  : borderColor!.withValues(alpha: 0.6),
                          width: 1.0,
                        )
                        : BorderSide.none,
              ),
            ),
            child: buttonChild,
          ),
        );

      case ButtonType.outlined:
        return SizedBox(
          width: width,
          height: height,
          child: OutlinedButton(
            onPressed: isLoading ? null : effectiveOnPressed,
            style: OutlinedButton.styleFrom(
              foregroundColor: foregroundColor,
              padding: padding,
              disabledForegroundColor: foregroundColor?.withValues(alpha: 0.6),
              side: BorderSide(
                color:
                    buttonEnabled
                        ? (borderColor ?? theme.colorScheme.primary)
                        : (borderColor ?? theme.colorScheme.primary).withValues(
                          alpha: 0.6,
                        ),
              ),
              shape: RoundedRectangleBorder(
                borderRadius: borderRadius ?? defaultBorderRadius,
              ),
            ),
            child: buttonChild,
          ),
        );

      case ButtonType.icon:
        return IconButton(
          onPressed: isLoading ? null : effectiveOnPressed,
          icon: buttonChild,
          color: foregroundColor,
          iconSize: iconSize,
          style: IconButton.styleFrom(
            backgroundColor: backgroundColor,
            disabledBackgroundColor: backgroundColor?.withValues(alpha: 0.6),
            disabledForegroundColor: foregroundColor?.withValues(alpha: 0.6),
            side:
                borderColor != null
                    ? BorderSide(
                      color:
                          buttonEnabled
                              ? borderColor!
                              : borderColor!.withValues(alpha: 0.6),
                      width: 1.0,
                    )
                    : null,
            shape: RoundedRectangleBorder(
              borderRadius: borderRadius ?? BorderRadius.circular(50),
            ),
          ),
        );
    }
  }
}
