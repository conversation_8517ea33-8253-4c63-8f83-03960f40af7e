import 'dart:ui';

import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../resources/app_theme.dart';

class CustomTextField extends StatelessWidget {
  final TextEditingController? controller;
  final String? labelText;
  final String? hintText;
  final bool obscureText;
  final TextInputType? keyboardType;
  final TextInputAction? textInputAction;
  final IconData? prefixIcon;
  final Widget? suffixIcon;
  final String? Function(String?)? validator;
  final void Function(String)? onChanged;
  final void Function()? onTap;
  final bool readOnly;
  final bool enabled;
  final int? maxLines;
  final int? minLines;
  final int? maxLength;
  final List<TextInputFormatter>? inputFormatters;
  final FocusNode? focusNode;
  final TextStyle? style;
  final bool autofocus;
  final Color? fillColor;
  final Color? borderColor;
  final Color? focusedBorderColor;
  final double borderRadius;
  final EdgeInsetsGeometry? contentPadding;

  const CustomTextField({
    Key? key,
    this.controller,
    this.labelText,
    this.hintText,
    this.obscureText = false,
    this.keyboardType,
    this.textInputAction,
    this.prefixIcon,
    this.suffixIcon,
    this.validator,
    this.onChanged,
    this.onTap,
    this.readOnly = false,
    this.enabled = true,
    this.maxLines = 1,
    this.minLines,
    this.maxLength,
    this.inputFormatters,
    this.focusNode,
    this.style,
    this.autofocus = false,
    this.fillColor,
    this.borderColor,
    this.focusedBorderColor,
    this.borderRadius = 12.0,
    this.contentPadding,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (labelText != null)
          Padding(
            padding: const EdgeInsets.only(bottom: 8.0),
            child: Text(
              labelText!,
              style: theme.textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: theme.hintColor,
              ),
            ),
          ),
        TextFormField(
          controller: controller,
          focusNode: focusNode,
          obscureText: obscureText,
          keyboardType: keyboardType,
          textInputAction: textInputAction,
          validator: validator,
          onChanged: onChanged,
          onTap: onTap,
          readOnly: readOnly,
          enabled: enabled,
          maxLines: maxLines,
          minLines: minLines,
          maxLength: maxLength,
          inputFormatters: inputFormatters,
          style: style ?? theme.textTheme.bodyMedium,
          autofocus: autofocus,
          decoration: InputDecoration(
            hintText: hintText,
            hintStyle: theme.textTheme.bodyMedium?.copyWith(
              color: theme.hintColor.withValues(alpha: 0.7),
            ),
            prefixIcon:
                prefixIcon != null
                    ? Icon(prefixIcon, color: theme.hintColor, size: 20)
                    : null,
            suffixIcon: suffixIcon,
            filled: true,
            fillColor: fillColor ?? theme.hintColor.withValues(alpha: 0.1),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(borderRadius),
              borderSide: BorderSide(
                color: borderColor ?? theme.hintColor.withValues(alpha: 0.3),
                width: 1.5,
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(borderRadius),
              borderSide: BorderSide(
                color: borderColor ?? theme.hintColor.withValues(alpha: 0.3),
                width: 1.5,
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(borderRadius),
              borderSide: BorderSide(
                color: focusedBorderColor ?? colorScheme.primary,
                width: 2.0,
              ),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(borderRadius),
              borderSide: BorderSide(color: colorScheme.error, width: 1.5),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(borderRadius),
              borderSide: BorderSide(color: colorScheme.error, width: 2.0),
            ),
            contentPadding:
                contentPadding ??
                const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
            counterText: maxLength != null ? null : '',
          ),
        ),
      ],
    );
  }
}

// Keep the original CommonTextField for backward compatibility
class CommonTextField extends StatelessWidget {
  // Core properties
  final Key? key;
  final Object groupId;
  final TextEditingController? controller;
  final FocusNode? focusNode;
  final UndoHistoryController? undoController;

  // Input configuration
  final InputDecoration? decoration;
  final TextInputType? keyboardType;
  final TextInputAction? textInputAction;
  final TextCapitalization textCapitalization;
  final bool readOnly;
  final bool autofocus;
  final bool obscureText;
  final String obscuringCharacter;

  // Text styling
  final TextStyle? style;
  final StrutStyle? strutStyle;
  final TextAlign textAlign;
  final TextAlignVertical? textAlignVertical;
  final TextDirection? textDirection;

  // Input behavior
  final bool autocorrect;
  final SmartDashesType? smartDashesType;
  final SmartQuotesType? smartQuotesType;
  final bool enableSuggestions;
  final List<TextInputFormatter>? inputFormatters;
  final bool? enabled;

  // Length constraints
  final int? maxLines;
  final int? minLines;
  final bool expands;
  final int? maxLength;
  final MaxLengthEnforcement? maxLengthEnforcement;

  // Callbacks
  final void Function(String)? onChanged;
  final void Function()? onEditingComplete;
  final void Function(String)? onSubmitted;
  final void Function()? onTap;
  final void Function(PointerDownEvent)? onTapOutside;
  final void Function(PointerUpEvent)? onTapUpOutside;

  // Cursor customization
  final bool? showCursor;
  final double cursorWidth;
  final double? cursorHeight;
  final Radius? cursorRadius;
  final bool? cursorOpacityAnimates;
  final Color? cursorColor;
  final Color? cursorErrorColor;

  // Selection customization
  final BoxHeightStyle selectionHeightStyle;
  final BoxWidthStyle selectionWidthStyle;
  final bool? enableInteractiveSelection;
  final TextSelectionControls? selectionControls;

  // Scrolling
  final EdgeInsets scrollPadding;
  final ScrollController? scrollController;
  final ScrollPhysics? scrollPhysics;
  final DragStartBehavior dragStartBehavior;

  // Additional features
  final Brightness? keyboardAppearance;
  final MouseCursor? mouseCursor;
  final Iterable<String>? autofillHints;
  final ContentInsertionConfiguration? contentInsertionConfiguration;
  final Clip clipBehavior;
  final String? restorationId;
  final bool scribbleEnabled;
  final bool enableIMEPersonalizedLearning;
  final bool canRequestFocus;
  final SpellCheckConfiguration? spellCheckConfiguration;
  final TextMagnifierConfiguration? magnifierConfiguration;

  // Advanced callbacks and builders
  final Widget? Function(
    BuildContext, {
    required int currentLength,
    required bool isFocused,
    required int? maxLength,
  })?
  buildCounter;
  final Widget Function(BuildContext, EditableTextState)? contextMenuBuilder;
  final void Function(String, Map<String, dynamic>)? onAppPrivateCommand;
  final WidgetStatesController? statesController;
  final ToolbarOptions? toolbarOptions;
  final bool? ignorePointers;
  final bool onTapAlwaysCalled;
  final bool stylusHandwritingEnabled;

  const CommonTextField({
    this.key,
    this.groupId = EditableText,
    this.controller,
    this.focusNode,
    this.undoController,
    this.decoration = const InputDecoration(),
    this.keyboardType,
    this.textInputAction,
    this.textCapitalization = TextCapitalization.none,
    this.style,
    this.strutStyle,
    this.textAlign = TextAlign.start,
    this.textAlignVertical,
    this.textDirection,
    this.readOnly = false,
    this.toolbarOptions,
    this.showCursor,
    this.autofocus = false,
    this.statesController,
    this.obscuringCharacter = '•',
    this.obscureText = false,
    this.autocorrect = true,
    this.smartDashesType,
    this.smartQuotesType,
    this.enableSuggestions = true,
    this.maxLines = 1,
    this.minLines,
    this.expands = false,
    this.maxLength,
    this.maxLengthEnforcement,
    this.onChanged,
    this.onEditingComplete,
    this.onSubmitted,
    this.onAppPrivateCommand,
    this.inputFormatters,
    this.enabled,
    this.ignorePointers,
    this.cursorWidth = 2.0,
    this.cursorHeight,
    this.cursorRadius,
    this.cursorOpacityAnimates,
    this.cursorColor,
    this.cursorErrorColor,
    this.selectionHeightStyle = BoxHeightStyle.tight,
    this.selectionWidthStyle = BoxWidthStyle.tight,
    this.keyboardAppearance,
    this.scrollPadding = const EdgeInsets.all(20.0),
    this.dragStartBehavior = DragStartBehavior.start,
    this.enableInteractiveSelection,
    this.selectionControls,
    this.onTap,
    this.onTapAlwaysCalled = false,
    this.onTapOutside,
    this.onTapUpOutside,
    this.mouseCursor,
    this.buildCounter,
    this.scrollController,
    this.scrollPhysics,
    this.autofillHints = const <String>[],
    this.contentInsertionConfiguration,
    this.clipBehavior = Clip.hardEdge,
    this.restorationId,
    this.scribbleEnabled = true,
    this.stylusHandwritingEnabled = true,
    this.enableIMEPersonalizedLearning = true,
    this.contextMenuBuilder,
    this.canRequestFocus = true,
    this.spellCheckConfiguration,
    this.magnifierConfiguration,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return TextField(
      key: key,
      groupId: groupId,
      controller: controller,
      focusNode: focusNode,
      undoController: undoController,
      decoration: decoration,
      keyboardType: keyboardType,
      textInputAction: textInputAction,
      textCapitalization: textCapitalization,
      style: style,
      strutStyle: strutStyle,
      textAlign: textAlign,
      textAlignVertical: textAlignVertical,
      textDirection: textDirection,
      readOnly: readOnly,
      toolbarOptions: toolbarOptions,
      showCursor: showCursor,
      autofocus: autofocus,
      statesController: statesController,
      obscuringCharacter: obscuringCharacter,
      obscureText: obscureText,
      autocorrect: autocorrect,
      smartDashesType: smartDashesType,
      smartQuotesType: smartQuotesType,
      enableSuggestions: enableSuggestions,
      maxLines: maxLines,
      minLines: minLines,
      expands: expands,
      maxLength: maxLength,
      maxLengthEnforcement: maxLengthEnforcement,
      onChanged: onChanged,
      onEditingComplete: onEditingComplete,
      onSubmitted: onSubmitted,
      onAppPrivateCommand: onAppPrivateCommand,
      inputFormatters: inputFormatters,
      enabled: enabled,
      ignorePointers: ignorePointers,
      cursorWidth: cursorWidth,
      cursorHeight: cursorHeight,
      cursorRadius: cursorRadius,
      cursorOpacityAnimates: cursorOpacityAnimates,
      cursorColor: cursorColor,
      cursorErrorColor: cursorErrorColor,
      selectionHeightStyle: selectionHeightStyle,
      selectionWidthStyle: selectionWidthStyle,
      keyboardAppearance: keyboardAppearance,
      scrollPadding: scrollPadding,
      dragStartBehavior: dragStartBehavior,
      enableInteractiveSelection: enableInteractiveSelection,
      selectionControls: selectionControls,
      onTap: onTap,
      onTapAlwaysCalled: onTapAlwaysCalled,
      onTapOutside: onTapOutside,
      onTapUpOutside: onTapUpOutside,
      mouseCursor: mouseCursor,
      buildCounter: buildCounter,
      scrollController: scrollController,
      scrollPhysics: scrollPhysics,
      autofillHints: autofillHints,
      contentInsertionConfiguration: contentInsertionConfiguration,
      clipBehavior: clipBehavior,
      restorationId: restorationId,
      scribbleEnabled: scribbleEnabled,
      stylusHandwritingEnabled: stylusHandwritingEnabled,
      enableIMEPersonalizedLearning: enableIMEPersonalizedLearning,
      contextMenuBuilder: contextMenuBuilder,
      canRequestFocus: canRequestFocus,
      spellCheckConfiguration: spellCheckConfiguration,
      magnifierConfiguration: magnifierConfiguration,
    );
  }

  // Factory constructors for common use cases

  /// Creates a password text field
  factory CommonTextField.password({
    Key? key,
    TextEditingController? controller,
    String? hintText,
    String? labelText,
    TextInputAction? textInputAction,
    void Function(String)? onChanged,
    void Function(String)? onSubmitted,
    String? Function(String?)? validator,
  }) {
    return CommonTextField(
      key: key,
      controller: controller,
      obscureText: true,
      textInputAction: textInputAction ?? TextInputAction.done,
      keyboardType: TextInputType.visiblePassword,
      decoration: InputDecoration(hintText: hintText, labelText: labelText),
      onChanged: onChanged,
      onSubmitted: onSubmitted,
    );
  }

  /// Creates an email text field
  factory CommonTextField.email({
    Key? key,
    TextEditingController? controller,
    String? hintText,
    String? labelText,
    TextInputAction? textInputAction,
    void Function(String)? onChanged,
    void Function(String)? onSubmitted,
  }) {
    return CommonTextField(
      key: key,
      controller: controller,
      keyboardType: TextInputType.emailAddress,
      textInputAction: textInputAction ?? TextInputAction.next,
      decoration: InputDecoration(
        hintText: hintText ?? 'Enter email',
        labelText: labelText ?? 'Email',
      ),
      onChanged: onChanged,
      onSubmitted: onSubmitted,
    );
  }

  /// Creates a phone number text field
  factory CommonTextField.phone({
    Key? key,
    TextEditingController? controller,
    String? hintText,
    String? labelText,
    TextInputAction? textInputAction,
    void Function(String)? onChanged,
    void Function(String)? onSubmitted,
  }) {
    return CommonTextField(
      key: key,
      controller: controller,
      keyboardType: TextInputType.phone,
      textInputAction: textInputAction ?? TextInputAction.done,
      decoration: InputDecoration(
        hintText: hintText ?? 'Enter phone number',
        labelText: labelText ?? 'Phone',
      ),
      onChanged: onChanged,
      onSubmitted: onSubmitted,
    );
  }

  /// Creates a multiline text field
  factory CommonTextField.multiline({
    Key? key,
    TextEditingController? controller,
    String? hintText,
    String? labelText,
    int? maxLines,
    int? minLines,
    void Function(String)? onChanged,
  }) {
    return CommonTextField(
      key: key,
      controller: controller,
      maxLines: maxLines,
      minLines: minLines ?? 3,
      keyboardType: TextInputType.multiline,
      textInputAction: TextInputAction.newline,
      decoration: InputDecoration(
        hintText: hintText,
        labelText: labelText,
        alignLabelWithHint: true,
      ),
      onChanged: onChanged,
    );
  }

  /// Creates a number text field
  factory CommonTextField.number({
    Key? key,
    TextEditingController? controller,
    String? hintText,
    String? labelText,
    TextInputAction? textInputAction,
    void Function(String)? onChanged,
    void Function(String)? onSubmitted,
    bool allowDecimals = true,
  }) {
    return CommonTextField(
      key: key,
      controller: controller,
      keyboardType:
          allowDecimals
              ? const TextInputType.numberWithOptions(decimal: true)
              : TextInputType.number,
      textInputAction: textInputAction ?? TextInputAction.done,
      inputFormatters:
          allowDecimals
              ? [FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*'))]
              : [FilteringTextInputFormatter.digitsOnly],
      decoration: InputDecoration(hintText: hintText, labelText: labelText),
      onChanged: onChanged,
      onSubmitted: onSubmitted,
    );
  }

  /// Creates a mobile number text field with formatting
  ///
  /// This factory is specifically designed for mobile number input with proper formatting
  /// and validation. It limits input to 10 digits and can optionally include country code.
  factory CommonTextField.mobileNumber({
    Key? key,
    TextEditingController? controller,
    String? hintText,
    String? labelText,
    TextInputAction? textInputAction,
    void Function(String)? onChanged,
    void Function(String)? onSubmitted,
    bool includeCountryCode = false,
    String defaultCountryCode = '+91',
    int maxLength = 10,
  }) {
    return CommonTextField(
      key: key,
      controller: controller,
      keyboardType: TextInputType.phone,
      textInputAction: textInputAction ?? TextInputAction.done,
      maxLength:
          includeCountryCode
              ? maxLength + defaultCountryCode.length
              : maxLength,
      inputFormatters: [
        FilteringTextInputFormatter.digitsOnly,
        LengthLimitingTextInputFormatter(maxLength),
      ],
      decoration: InputDecoration(
        hintText: hintText ?? 'Enter mobile number',
        labelText: labelText ?? 'Mobile Number',
        prefixText: includeCountryCode ? defaultCountryCode : null,
        counterText: '',
      ),
      onChanged: onChanged,
      onSubmitted: onSubmitted,
    );
  }
}

// Usages Example
///
/// ```dart
// Basic usage
// CommonTextField(
//   decoration: InputDecoration(labelText: 'Name'),
//   onChanged: (value) => print(value),
// )
/// ```dart
///
/// ```dart
// // Password field
// CommonTextField.password(
//   labelText: 'Password',
//   onSubmitted: (value) => login(value),
// )
/// ```dart
///
/// ```dart
// // Email field
// CommonTextField.email(
//   controller: emailController,
//   hintText: 'Enter your email',
// )
/// ```dart
///
/// ```dart
// // Multiline text
// CommonTextField.multiline(
//   labelText: 'Description',
//   minLines: 3,
//   maxLines: 6,
// )
/// ```dart
