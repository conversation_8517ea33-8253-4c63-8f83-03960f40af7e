import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'app_text_styles.dart';

// App Color Palette - Centralized color management
class AppColorPalette {
  // Primary Brand Colors
  static const Color primary = Color.fromARGB(255, 52, 120, 245);
  static const Color primaryLight = Color(0xFF42A5F5);
  static const Color primaryDark = Color(0xFF0D47A1);
  static const Color primaryContainer = Color(0xFFBBDEFB);
  static const Color onPrimaryContainer = Color(0xFF0D47A1);

  // Secondary Colors
  static const Color secondary = Color(0xFF03DAC6);
  static const Color secondaryLight = Color(0xFF4DD0E1);
  static const Color secondaryContainer = Color(0xFFB2DFDB);
  static const Color onSecondaryContainer = Color(0xFF004D40);

  // Tertiary Colors
  static const Color tertiary = Color(0xFF9C27B0);
  static const Color tertiaryLight = Color(0xFFBA68C8);
  static const Color tertiaryContainer = Color(0xFFE1BEE7);
  static const Color onTertiaryContainer = Color(0xFF4A148C);

  // Status Colors
  static const Color success = Color(0xFF28A745);
  static const Color successLight = Color(0xFF10B981);
  static const Color warning = Color(0xFFFFC107);
  static const Color warningLight = Color(0xFFF59E0B);
  static const Color error = Color(0xFFDF3545);
  static const Color errorLight = Color(0xFFEF4444);
  static const Color info = Color(0xFF007BFF);
  static const Color infoLight = Color(0xFF3B82F6);

  // Neutral Colors
  static const Color black = Color(0xFF000000);
  static const Color white = Color(0xFFFFFFFF);
  static const Color grey = Color(0xFF6C757D);
  static const Color greyLight = Color(0xFFBDBDBD);
  static const Color greyDark = Color(0xFF424242);
  static const Color transparent = Color(0x00000000);

  // Background Colors
  static const Color background = Color(0xFFFAFAFA);
  static const Color surface = Color(0xFFFFFFFF);
  static const Color surfaceDark = Color(0xFF0F0F0F);
  static const Color surfaceVariant = Color(0xFFF5F5F5);
  static const Color surfaceVariantDark = Color(0xFF1A1A1A);

  // Text Colors
  static const Color onSurface = Color(0xFF212121);
  static const Color onSurfaceDark = Color(0xFFE8E8E8);
  static const Color onSurfaceVariant = Color(0xFF424242);
  static const Color onSurfaceVariantDark = Color(0xFFB8B8B8);

  // Border & Outline Colors
  static const Color outline = Color(0xFFBDBDBD);
  static const Color outlineDark = Color(0xFF404040);
  static const Color outlineVariant = Color(0xFFE0E0E0);
  static const Color outlineVariantDark = Color(0xFF2A2A2A);

  // Shadow & Overlay Colors
  static const Color shadow = Color(0xFF000000);
  static const Color scrim = Color(0xFF000000);

  // Enhanced Dark Theme Colors
  static const Color darkBackground = Color(0xFF0A0A0A);
  static const Color darkSurface = Color(0xFF0F0F0F);
  static const Color darkSurfaceElevated = Color(0xFF181818);
  static const Color darkCard = Color(0xFF1C1C1C);
  static const Color darkBorder = Color(0xFF2D2D2D);
  static const Color darkTextPrimary = Color(0xFFF0F0F0);
  static const Color darkTextSecondary = Color(0xFFB8B8B8);
  static const Color darkTextTertiary = Color(0xFF808080);

  // Bank-specific Colors
  static const Color bankSBI = Color(0xFF1565C0);
  static const Color bankHDFC = Color(0xFFD32F2F);
  static const Color bankICICI = Color(0xFFE65100);
  static const Color bankAxis = Color(0xFF7B1FA2);
  static const Color bankKotak = Color(0xFFD32F2F);
  static const Color bankYes = Color(0xFF1976D2);
  static const Color bankPNB = Color(0xFF388E3C);
  static const Color bankCanara = Color(0xFFE65100);
  static const Color bankUnion = Color(0xFF795548);
  static const Color bankBOB = Color(0xFF1976D2);
  static const Color bankIDBI = Color(0xFF388E3C);
  static const Color bankFederal = Color(0xFF7B1FA2);
  static const Color bankIndusind = Color(0xFFE65100);

  // Semantic Colors
  static const Color income = Color(0xFF28A745);
  static const Color expense = Color(0xFFDF3545);
  static const Color overdue = Color(0xFFDF3545);
  static const Color dueSoon = Color(0xFFFF5722);
  static const Color normal = Color(0xFF007BFF);
  static const Color paid = Color(0xFF28A745);

  // Opacity Variants (Common usage patterns)
  static Color get primary10 => primary.withValues(alpha: 0.1);
  static Color get primary20 => primary.withValues(alpha: 0.2);
  static Color get primary30 => primary.withValues(alpha: 0.3);
  static Color get primary50 => primary.withValues(alpha: 0.5);
  static Color get primary80 => primary.withValues(alpha: 0.8);

  static Color get grey10 => grey.withValues(alpha: 0.1);
  static Color get grey20 => grey.withValues(alpha: 0.2);
  static Color get grey30 => grey.withValues(alpha: 0.3);
  static Color get grey50 => grey.withValues(alpha: 0.5);
  static Color get grey70 => grey.withValues(alpha: 0.7);
  static Color get grey80 => grey.withValues(alpha: 0.8);

  static Color get white10 => white.withValues(alpha: 0.1);
  static Color get white20 => white.withValues(alpha: 0.2);
  static Color get white50 => white.withValues(alpha: 0.5);
  static Color get white80 => white.withValues(alpha: 0.8);
  static Color get white90 => white.withValues(alpha: 0.9);
}

class AppTheme {
  // Color schemes
  static const ColorScheme lightColorScheme = ColorScheme(
    brightness: Brightness.light,
    primary: AppColorPalette.primary,
    onPrimary: AppColorPalette.white,
    primaryContainer: AppColorPalette.primaryContainer,
    onPrimaryContainer: AppColorPalette.onPrimaryContainer,
    secondary: AppColorPalette.secondary,
    onSecondary: AppColorPalette.black,
    secondaryContainer: AppColorPalette.secondaryContainer,
    onSecondaryContainer: AppColorPalette.onSecondaryContainer,
    tertiary: AppColorPalette.tertiary,
    onTertiary: AppColorPalette.white,
    tertiaryContainer: AppColorPalette.tertiaryContainer,
    onTertiaryContainer: AppColorPalette.onTertiaryContainer,
    error: AppColorPalette.error,
    onError: AppColorPalette.white,
    errorContainer: Color(0xFFFFCDD2),
    onErrorContainer: Color(0xFFB71C1C),
    // background: AppColorPalette.background,
    // onBackground: AppColorPalette.onSurface,
    surface: AppColorPalette.surface,
    onSurface: AppColorPalette.onSurface,
    // surfaceVariant: AppColorPalette.surfaceVariant,
    onSurfaceVariant: AppColorPalette.onSurfaceVariant,
    outline: AppColorPalette.outline,
    outlineVariant: AppColorPalette.outlineVariant,
    shadow: AppColorPalette.shadow,
    scrim: AppColorPalette.scrim,
    inverseSurface: Color(0xFF303030),
    onInverseSurface: AppColorPalette.white,
    inversePrimary: AppColorPalette.primaryLight,
    surfaceTint: AppColorPalette.primary,
  );

  static const ColorScheme darkColorScheme = ColorScheme(
    brightness: Brightness.dark,
    primary: AppColorPalette.primaryLight,
    onPrimary: AppColorPalette.primaryDark,
    primaryContainer: AppColorPalette.primaryDark,
    onPrimaryContainer: AppColorPalette.primaryContainer,
    secondary: AppColorPalette.secondaryLight,
    onSecondary: Color(0xFF006064),
    secondaryContainer: Color(0xFF00838F),
    onSecondaryContainer: AppColorPalette.secondaryContainer,
    tertiary: AppColorPalette.tertiaryLight,
    onTertiary: AppColorPalette.onTertiaryContainer,
    tertiaryContainer: Color(0xFF7B1FA2),
    onTertiaryContainer: AppColorPalette.tertiaryContainer,
    error: AppColorPalette.errorLight,
    onError: Color(0xFFB71C1C),
    errorContainer: Color(0xFFC62828),
    onErrorContainer: Color(0xFFFFCDD2),
    // background: Color(0xFF121212),
    // onBackground: AppColorPalette.onSurfaceDark,
    surface: AppColorPalette.surfaceDark,
    onSurface: AppColorPalette.onSurfaceDark,
    // surfaceVariant: AppColorPalette.surfaceVariantDark,
    onSurfaceVariant: AppColorPalette.onSurfaceVariantDark,
    outline: AppColorPalette.outlineDark,
    outlineVariant: AppColorPalette.outlineVariantDark,
    shadow: AppColorPalette.shadow,
    scrim: AppColorPalette.scrim,
    inverseSurface: AppColorPalette.onSurfaceDark,
    onInverseSurface: Color(0xFF303030),
    inversePrimary: AppColorPalette.primary,
    surfaceTint: AppColorPalette.primaryLight,
  );

  // Light Theme
  static ThemeData get lightTheme {
    return ThemeData(
      // Core properties
      useMaterial3: true,
      brightness: Brightness.light,
      colorScheme: lightColorScheme,
      visualDensity: VisualDensity.adaptivePlatformDensity,
      materialTapTargetSize: MaterialTapTargetSize.padded,
      platform: TargetPlatform.android,
      applyElevationOverlayColor: false,

      // Colors
      primaryColor: lightColorScheme.primary,
      primaryColorLight: AppColorPalette.primaryLight,
      primaryColorDark: AppColorPalette.primaryDark,
      canvasColor: AppColorPalette.background,
      cardColor: AppColorPalette.surface,
      scaffoldBackgroundColor: AppColorPalette.surface,
      // dialogBackgroundColor: AppColorPalette.surface,
      disabledColor: AppColorPalette.greyLight,
      dividerColor: AppColorPalette.outline,
      focusColor: AppColorPalette.primary10,
      highlightColor: AppColorPalette.primary10,
      hintColor: AppColorPalette.grey,
      hoverColor: AppColorPalette.primary10,
      indicatorColor: lightColorScheme.primary,
      secondaryHeaderColor: const Color(0xFFF3E5F5),
      shadowColor: AppColorPalette.shadow.withValues(alpha: 0.2),
      splashColor: AppColorPalette.primary10,
      unselectedWidgetColor: AppColorPalette.grey,

      // Typography
      fontFamily: 'Roboto',
      fontFamilyFallback: const ['Arial', 'Helvetica', 'sans-serif'],
      textTheme: TextTheme(
        displayLarge: AppTextStyles.displayLarge.copyWith(
          color: lightColorScheme.onPrimary,
        ),
        displayMedium: AppTextStyles.displayMedium,
        displaySmall: AppTextStyles.displaySmall.copyWith(
          color: lightColorScheme.onSecondary,
        ),
        headlineLarge: AppTextStyles.headlineLarge.copyWith(
          color: lightColorScheme.onSecondary,
        ),
        headlineMedium: AppTextStyles.headlineMedium.copyWith(
          color: lightColorScheme.onSecondary,
        ),
        headlineSmall: AppTextStyles.headlineSmall.copyWith(
          color: lightColorScheme.onSecondary,
        ),
        titleLarge: AppTextStyles.titleLarge.copyWith(
          color: lightColorScheme.onSecondary,
        ),
        titleMedium: AppTextStyles.titleMedium.copyWith(
          color: lightColorScheme.onSecondary,
        ),
        titleSmall: AppTextStyles.titleSmall.copyWith(
          color: lightColorScheme.onSecondary,
        ),
        bodyLarge: AppTextStyles.bodyLarge.copyWith(
          color: lightColorScheme.onSecondary,
        ),
        bodyMedium: AppTextStyles.bodyMedium.copyWith(
          color: lightColorScheme.onSecondary,
        ),
        bodySmall: AppTextStyles.bodySmall.copyWith(
          color: lightColorScheme.onSecondary,
        ),
        labelLarge: AppTextStyles.labelLarge.copyWith(
          color: lightColorScheme.onSecondary,
        ),
        labelMedium: AppTextStyles.labelMedium.copyWith(
          color: lightColorScheme.onSecondary,
        ),
        labelSmall: AppTextStyles.labelSmall.copyWith(
          color: lightColorScheme.onSecondary,
        ),
      ),

      // Icon themes
      iconTheme: IconThemeData(color: Colors.grey[700], size: 24),
      primaryIconTheme: const IconThemeData(color: Colors.white, size: 24),

      // Component themes
      appBarTheme: const AppBarTheme(
        elevation: 0,
        centerTitle: false,
        backgroundColor: AppColorPalette.primary,
        foregroundColor: AppColorPalette.white,
        iconTheme: IconThemeData(color: AppColorPalette.white),
        actionsIconTheme: IconThemeData(color: AppColorPalette.white),
        systemOverlayStyle: SystemUiOverlayStyle.light,
        titleTextStyle: TextStyle(
          fontSize: 22,
          fontWeight: FontWeight.w500,
          color: AppColorPalette.white,
        ),
      ),

      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          elevation: 2,
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        ),
      ),

      filledButtonTheme: FilledButtonThemeData(
        style: FilledButton.styleFrom(
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        ),
      ),

      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          side: BorderSide(color: lightColorScheme.outline),
        ),
      ),

      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        ),
      ),

      cardTheme: const CardTheme(
        elevation: 1,
        margin: EdgeInsets.all(4),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.all(Radius.circular(12)),
        ),
      ),

      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: AppColorPalette.grey10,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: AppColorPalette.outline),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: AppColorPalette.outline),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: lightColorScheme.primary, width: 2),
        ),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 12,
        ),
      ),

      floatingActionButtonTheme: const FloatingActionButtonThemeData(
        elevation: 6,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.all(Radius.circular(16)),
        ),
      ),

      bottomNavigationBarTheme: BottomNavigationBarThemeData(
        type: BottomNavigationBarType.fixed,
        backgroundColor: AppColorPalette.surface,
        selectedItemColor: lightColorScheme.primary,
        unselectedItemColor: AppColorPalette.grey,
        elevation: 8,
      ),

      navigationBarTheme: NavigationBarThemeData(
        backgroundColor: AppColorPalette.surface,
        indicatorColor: lightColorScheme.primaryContainer,
        elevation: 3,
      ),

      chipTheme: ChipThemeData(
        backgroundColor: AppColorPalette.surface,
        selectedColor: lightColorScheme.primaryContainer,
        secondarySelectedColor: lightColorScheme.secondaryContainer,
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      ),

      dividerTheme: DividerThemeData(
        color: AppColorPalette.outline,
        thickness: 1,
        space: 1,
      ),

      listTileTheme: const ListTileThemeData(
        contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.all(Radius.circular(8)),
        ),
      ),

      snackBarTheme: const SnackBarThemeData(
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.all(Radius.circular(8)),
        ),
      ),

      dialogTheme: const DialogTheme(
        elevation: 24,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.all(Radius.circular(16)),
        ),
      ),

      bottomSheetTheme: const BottomSheetThemeData(
        elevation: 8,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
        ),
      ),

      tabBarTheme: TabBarTheme(
        labelColor: lightColorScheme.primary,
        unselectedLabelColor: AppColorPalette.grey,
        indicator: UnderlineTabIndicator(
          borderSide: BorderSide(color: lightColorScheme.primary, width: 2),
        ),
      ),

      switchTheme: SwitchThemeData(
        thumbColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return lightColorScheme.primary;
          }
          return AppColorPalette.greyLight;
        }),
        trackColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return lightColorScheme.primary.withValues(alpha: 0.5);
          }
          return AppColorPalette.outline;
        }),
      ),

      checkboxTheme: CheckboxThemeData(
        fillColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return lightColorScheme.primary;
          }
          return Colors.transparent;
        }),
        checkColor: WidgetStateProperty.all(Colors.white),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(4)),
      ),

      radioTheme: RadioThemeData(
        fillColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return lightColorScheme.primary;
          }
          return Colors.grey[600];
        }),
      ),

      sliderTheme: SliderThemeData(
        activeTrackColor: lightColorScheme.primary,
        inactiveTrackColor: lightColorScheme.primary.withValues(alpha: 0.3),
        thumbColor: lightColorScheme.primary,
        overlayColor: lightColorScheme.primary.withValues(alpha: 0.12),
        valueIndicatorColor: lightColorScheme.primary,
      ),

      progressIndicatorTheme: ProgressIndicatorThemeData(
        color: lightColorScheme.primary,
        linearTrackColor: lightColorScheme.primary.withValues(alpha: 0.3),
        circularTrackColor: lightColorScheme.primary.withValues(alpha: 0.3),
      ),

      tooltipTheme: TooltipThemeData(
        decoration: BoxDecoration(
          color: Colors.grey[800],
          borderRadius: BorderRadius.circular(8),
        ),
        textStyle: const TextStyle(color: Colors.white),
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      ),

      pageTransitionsTheme: const PageTransitionsTheme(
        builders: {
          TargetPlatform.android: CupertinoPageTransitionsBuilder(),
          TargetPlatform.iOS: CupertinoPageTransitionsBuilder(),
        },
      ),

      splashFactory: InkRipple.splashFactory,
    );
  }

  // Dark Theme
  static ThemeData get darkTheme {
    return ThemeData(
      // Core properties
      useMaterial3: true,
      brightness: Brightness.dark,
      colorScheme: darkColorScheme,
      visualDensity: VisualDensity.adaptivePlatformDensity,
      materialTapTargetSize: MaterialTapTargetSize.padded,
      platform: TargetPlatform.android,
      applyElevationOverlayColor: true,

      // Colors
      primaryColor: darkColorScheme.primary,
      primaryColorLight: AppColorPalette.primaryLight,
      primaryColorDark: AppColorPalette.primaryDark,
      canvasColor: AppColorPalette.darkBackground,
      cardColor: AppColorPalette.darkCard,
      scaffoldBackgroundColor: AppColorPalette.darkBackground,
      disabledColor: AppColorPalette.darkTextTertiary,
      dividerColor: AppColorPalette.darkBorder,
      focusColor: AppColorPalette.primary.withValues(alpha: 0.12),
      highlightColor: AppColorPalette.primary.withValues(alpha: 0.08),
      hintColor: AppColorPalette.darkTextSecondary,
      hoverColor: AppColorPalette.primary.withValues(alpha: 0.04),
      indicatorColor: darkColorScheme.primary,
      secondaryHeaderColor: AppColorPalette.darkTextSecondary,
      shadowColor: AppColorPalette.shadow.withValues(alpha: 0.3),
      splashColor: AppColorPalette.primary.withValues(alpha: 0.12),
      unselectedWidgetColor: AppColorPalette.darkTextSecondary,

      // Typography
      fontFamily: 'Poppins',
      fontFamilyFallback: const ['Arial', 'Helvetica', 'sans-serif'],
      textTheme: TextTheme(
        displayLarge: AppTextStyles.displayLarge.copyWith(
          color: AppColorPalette.darkTextPrimary,
        ),
        displayMedium: AppTextStyles.displayMedium.copyWith(
          color: AppColorPalette.darkTextPrimary,
        ),
        displaySmall: AppTextStyles.displaySmall.copyWith(
          color: AppColorPalette.darkTextPrimary,
        ),
        headlineLarge: AppTextStyles.headlineLarge.copyWith(
          color: AppColorPalette.darkTextPrimary,
        ),
        headlineMedium: AppTextStyles.headlineMedium.copyWith(
          color: AppColorPalette.darkTextPrimary,
        ),
        headlineSmall: AppTextStyles.headlineSmall.copyWith(
          color: AppColorPalette.darkTextPrimary,
        ),
        titleLarge: AppTextStyles.titleLarge.copyWith(
          color: AppColorPalette.darkTextPrimary,
        ),
        titleMedium: AppTextStyles.titleMedium.copyWith(
          color: AppColorPalette.darkTextPrimary,
        ),
        titleSmall: AppTextStyles.titleSmall.copyWith(
          color: AppColorPalette.darkTextPrimary,
        ),
        bodyLarge: AppTextStyles.bodyLarge.copyWith(
          color: AppColorPalette.darkTextSecondary,
        ),
        bodyMedium: AppTextStyles.bodyMedium.copyWith(
          color: AppColorPalette.darkTextSecondary,
        ),
        bodySmall: AppTextStyles.bodySmall.copyWith(
          color: AppColorPalette.darkTextSecondary,
        ),
        labelLarge: AppTextStyles.labelLarge.copyWith(
          color: AppColorPalette.darkTextSecondary,
        ),
        labelMedium: AppTextStyles.labelMedium.copyWith(
          color: AppColorPalette.darkTextSecondary,
        ),
        labelSmall: AppTextStyles.labelSmall.copyWith(
          color: AppColorPalette.darkTextTertiary,
        ),
      ),

      // Icon themes
      iconTheme: const IconThemeData(
        color: AppColorPalette.darkTextSecondary,
        size: 24,
      ),
      primaryIconTheme: const IconThemeData(
        color: AppColorPalette.white,
        size: 24,
      ),

      // Component themes
      appBarTheme: const AppBarTheme(
        elevation: 0,
        centerTitle: false,
        backgroundColor: AppColorPalette.darkSurface,
        foregroundColor: AppColorPalette.white,
        iconTheme: IconThemeData(color: AppColorPalette.white),
        actionsIconTheme: IconThemeData(color: AppColorPalette.white),
        systemOverlayStyle: SystemUiOverlayStyle.light,
        titleTextStyle: TextStyle(
          fontSize: 22,
          fontWeight: FontWeight.w500,
          color: AppColorPalette.white,
        ),
      ),

      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          elevation: 2,
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          backgroundColor: darkColorScheme.primaryContainer,
          foregroundColor: darkColorScheme.onPrimaryContainer,
        ),
      ),

      filledButtonTheme: FilledButtonThemeData(
        style: FilledButton.styleFrom(
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        ),
      ),

      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          side: BorderSide(color: darkColorScheme.outline),
          foregroundColor: darkColorScheme.primary,
        ),
      ),

      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          foregroundColor: darkColorScheme.primary,
        ),
      ),

      cardTheme: const CardTheme(
        elevation: 2,
        margin: EdgeInsets.all(4),
        color: AppColorPalette.darkCard,
        shadowColor: AppColorPalette.shadow,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.all(Radius.circular(12)),
        ),
      ),

      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: AppColorPalette.darkSurfaceElevated,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: AppColorPalette.darkBorder),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: AppColorPalette.darkBorder),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: darkColorScheme.primary, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: AppColorPalette.error, width: 1),
        ),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 12,
        ),
        hintStyle: TextStyle(color: AppColorPalette.darkTextTertiary),
        labelStyle: TextStyle(color: AppColorPalette.darkTextSecondary),
      ),

      floatingActionButtonTheme: const FloatingActionButtonThemeData(
        elevation: 6,
        backgroundColor: AppColorPalette.primaryLight,
        foregroundColor: AppColorPalette.primaryDark,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.all(Radius.circular(16)),
        ),
      ),

      bottomNavigationBarTheme: BottomNavigationBarThemeData(
        type: BottomNavigationBarType.fixed,
        backgroundColor: AppColorPalette.darkSurface,
        selectedItemColor: darkColorScheme.primary,
        unselectedItemColor: AppColorPalette.darkTextTertiary,
        elevation: 8,
      ),

      navigationBarTheme: NavigationBarThemeData(
        backgroundColor: AppColorPalette.darkSurface,
        indicatorColor: darkColorScheme.primaryContainer,
        elevation: 3,
      ),

      chipTheme: ChipThemeData(
        backgroundColor: AppColorPalette.darkSurfaceElevated,
        selectedColor: darkColorScheme.primaryContainer,
        secondarySelectedColor: darkColorScheme.secondaryContainer,
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        labelStyle: TextStyle(color: AppColorPalette.darkTextPrimary),
        side: BorderSide(color: AppColorPalette.darkBorder),
      ),

      dividerTheme: DividerThemeData(
        color: AppColorPalette.darkBorder,
        thickness: 1,
        space: 1,
      ),

      listTileTheme: ListTileThemeData(
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.all(Radius.circular(8)),
        ),
        textColor: AppColorPalette.darkTextPrimary,
        iconColor: AppColorPalette.darkTextSecondary,
        tileColor: AppColorPalette.darkSurfaceElevated,
      ),

      snackBarTheme: SnackBarThemeData(
        behavior: SnackBarBehavior.floating,
        backgroundColor: AppColorPalette.darkCard,
        contentTextStyle: TextStyle(color: AppColorPalette.darkTextPrimary),
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.all(Radius.circular(8)),
        ),
      ),

      dialogTheme: DialogTheme(
        elevation: 24,
        backgroundColor: AppColorPalette.darkCard,
        shadowColor: AppColorPalette.shadow,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.all(Radius.circular(16)),
        ),
      ),

      bottomSheetTheme: BottomSheetThemeData(
        elevation: 8,
        backgroundColor: AppColorPalette.darkCard,
        shadowColor: AppColorPalette.shadow,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
        ),
      ),

      tabBarTheme: TabBarTheme(
        labelColor: darkColorScheme.primary,
        unselectedLabelColor: AppColorPalette.darkTextTertiary,
        indicator: UnderlineTabIndicator(
          borderSide: BorderSide(color: darkColorScheme.primary, width: 2),
        ),
        dividerColor: AppColorPalette.darkBorder,
      ),

      switchTheme: SwitchThemeData(
        thumbColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return darkColorScheme.primary;
          }
          return AppColorPalette.grey;
        }),
        trackColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return darkColorScheme.primary.withValues(alpha: 0.5);
          }
          return AppColorPalette.outlineDark;
        }),
      ),

      checkboxTheme: CheckboxThemeData(
        fillColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return darkColorScheme.primary;
          }
          return AppColorPalette.transparent;
        }),
        checkColor: WidgetStateProperty.all(AppColorPalette.white),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(4)),
      ),

      radioTheme: RadioThemeData(
        fillColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return darkColorScheme.primary;
          }
          return AppColorPalette.greyLight;
        }),
      ),

      sliderTheme: SliderThemeData(
        activeTrackColor: darkColorScheme.primary,
        inactiveTrackColor: darkColorScheme.primary.withValues(alpha: 0.3),
        thumbColor: darkColorScheme.primary,
        overlayColor: darkColorScheme.primary.withValues(alpha: 0.12),
        valueIndicatorColor: darkColorScheme.primary,
      ),

      progressIndicatorTheme: ProgressIndicatorThemeData(
        color: darkColorScheme.primary,
        linearTrackColor: darkColorScheme.primary.withValues(alpha: 0.3),
        circularTrackColor: darkColorScheme.primary.withValues(alpha: 0.3),
      ),

      tooltipTheme: TooltipThemeData(
        decoration: BoxDecoration(
          color: AppColorPalette.darkSurfaceElevated,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: AppColorPalette.darkBorder),
        ),
        textStyle: TextStyle(color: AppColorPalette.darkTextPrimary),
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      ),

      pageTransitionsTheme: const PageTransitionsTheme(
        builders: {
          TargetPlatform.android: CupertinoPageTransitionsBuilder(),
          TargetPlatform.iOS: CupertinoPageTransitionsBuilder(),
        },
      ),

      splashFactory: InkRipple.splashFactory,
    );
  }
}

// Usage example in main.dart:
/*
void main() {
  runApp(MyApp());
}

class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Flutter Theme Demo',
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: ThemeMode.system, // Follows system theme
      home: MyHomePage(),
    );
  }
}
*/
