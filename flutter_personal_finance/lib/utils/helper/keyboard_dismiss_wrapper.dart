import 'package:flutter/material.dart';

/// A reusable widget that dismisses the keyboard when tapping anywhere on the screen
class KeyboardDismissWrapper extends StatelessWidget {
  final Widget child;
  final bool enabled;
  final VoidCallback? onDismiss;

  const KeyboardDismissWrapper({
    Key? key,
    required this.child,
    this.enabled = true,
    this.onDismiss,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (!enabled) return child;

    return GestureDetector(
      onTap: () {
        // Hide keyboard when tapping anywhere
        FocusScope.of(context).unfocus();

        // Call optional callback
        if (onDismiss != null) {
          onDismiss!();
        }
      },
      // Ensure the gesture detector covers the entire area
      behavior: HitTestBehavior.opaque,
      child: child,
    );
  }
}

/// Alternative implementation using Listener for more control
class KeyboardDismissListener extends StatelessWidget {
  final Widget child;
  final bool enabled;
  final VoidCallback? onDismiss;

  const KeyboardDismissListener({
    Key? key,
    required this.child,
    this.enabled = true,
    this.onDismiss,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (!enabled) return child;

    return Listener(
      onPointerDown: (PointerDownEvent event) {
        // Hide keyboard on any pointer down event
        FocusScope.of(context).unfocus();

        // Call optional callback
        if (onDismiss != null) {
          onDismiss!();
        }
      },
      child: child,
    );
  }
}

/// Utility class with static methods for keyboard management
class KeyboardUtils {
  /// Hide the keyboard
  static void hideKeyboard(BuildContext context) {
    FocusScope.of(context).unfocus();
  }

  /// Alternative method to hide keyboard
  static void dismissKeyboard() {
    FocusManager.instance.primaryFocus?.unfocus();
  }

  /// Check if keyboard is currently visible
  static bool isKeyboardVisible(BuildContext context) {
    return MediaQuery.of(context).viewInsets.bottom > 0;
  }

  /// Get keyboard height
  static double getKeyboardHeight(BuildContext context) {
    return MediaQuery.of(context).viewInsets.bottom;
  }
}

/// Custom Scaffold that automatically handles keyboard dismissal
class KeyboardDismissScaffold extends StatelessWidget {
  final PreferredSizeWidget? appBar;
  final Widget? body;
  final Widget? floatingActionButton;
  final Widget? drawer;
  final Widget? endDrawer;
  final Color? backgroundColor;
  final bool resizeToAvoidBottomInset;
  final bool enableKeyboardDismiss;
  final VoidCallback? onKeyboardDismiss;

  const KeyboardDismissScaffold({
    Key? key,
    this.appBar,
    this.body,
    this.floatingActionButton,
    this.drawer,
    this.endDrawer,
    this.backgroundColor,
    this.resizeToAvoidBottomInset = true,
    this.enableKeyboardDismiss = true,
    this.onKeyboardDismiss,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    Widget scaffoldContent = Scaffold(
      appBar: appBar,
      body: body,
      floatingActionButton: floatingActionButton,
      drawer: drawer,
      endDrawer: endDrawer,
      backgroundColor: backgroundColor,
      resizeToAvoidBottomInset: resizeToAvoidBottomInset,
    );

    if (!enableKeyboardDismiss) {
      return scaffoldContent;
    }

    return KeyboardDismissWrapper(
      onDismiss: onKeyboardDismiss,
      child: scaffoldContent,
    );
  }
}

// Example usage demonstrations
class ExampleUsage extends StatefulWidget {
  @override
  _ExampleUsageState createState() => _ExampleUsageState();
}

class _ExampleUsageState extends State<ExampleUsage> {
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      home: DefaultTabController(
        length: 4,
        child: Scaffold(
          appBar: AppBar(
            title: Text('Keyboard Dismiss Examples'),
            bottom: TabBar(
              isScrollable: true,
              tabs: [
                Tab(text: 'Wrapper'),
                Tab(text: 'Listener'),
                Tab(text: 'Custom Scaffold'),
                Tab(text: 'Utils'),
              ],
            ),
          ),
          body: TabBarView(
            children: [
              _buildWrapperExample(),
              _buildListenerExample(),
              _buildCustomScaffoldExample(),
              _buildUtilsExample(),
            ],
          ),
        ),
      ),
    );
  }

  // Example 1: Using KeyboardDismissWrapper
  Widget _buildWrapperExample() {
    return KeyboardDismissWrapper(
      onDismiss: () {
        print('Keyboard dismissed via wrapper');
      },
      child: Padding(
        padding: EdgeInsets.all(16.0),
        child: Column(
          children: [
            Text(
              'Tap anywhere to dismiss keyboard',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 20),
            TextField(
              controller: _nameController,
              decoration: InputDecoration(
                labelText: 'Name',
                border: OutlineInputBorder(),
              ),
            ),
            SizedBox(height: 16),
            TextField(
              controller: _emailController,
              decoration: InputDecoration(
                labelText: 'Email',
                border: OutlineInputBorder(),
              ),
            ),
            SizedBox(height: 20),
            ElevatedButton(
              onPressed: () {
                ScaffoldMessenger.of(
                  context,
                ).showSnackBar(SnackBar(content: Text('Form submitted!')));
              },
              child: Text('Submit'),
            ),
          ],
        ),
      ),
    );
  }

  // Example 2: Using KeyboardDismissListener
  Widget _buildListenerExample() {
    return KeyboardDismissListener(
      onDismiss: () {
        print('Keyboard dismissed via listener');
      },
      child: Padding(
        padding: EdgeInsets.all(16.0),
        child: Column(
          children: [
            Text(
              'Using Listener approach',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 20),
            TextField(
              decoration: InputDecoration(
                labelText: 'Username',
                border: OutlineInputBorder(),
              ),
            ),
            SizedBox(height: 16),
            TextField(
              decoration: InputDecoration(
                labelText: 'Password',
                border: OutlineInputBorder(),
              ),
              obscureText: true,
            ),
          ],
        ),
      ),
    );
  }

  // Example 3: Using Custom Scaffold
  Widget _buildCustomScaffoldExample() {
    return KeyboardDismissScaffold(
      onKeyboardDismiss: () {
        print('Keyboard dismissed via custom scaffold');
      },
      body: Padding(
        padding: EdgeInsets.all(16.0),
        child: Column(
          children: [
            Text(
              'Custom Scaffold with built-in dismiss',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 20),
            TextField(
              decoration: InputDecoration(
                labelText: 'Search',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.search),
              ),
            ),
            SizedBox(height: 16),
            TextField(
              decoration: InputDecoration(
                labelText: 'Description',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
      ),
    );
  }

  // Example 4: Using Utility methods
  Widget _buildUtilsExample() {
    return Padding(
      padding: EdgeInsets.all(16.0),
      child: Column(
        children: [
          Text(
            'Using KeyboardUtils',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          SizedBox(height: 20),
          TextField(
            decoration: InputDecoration(
              labelText: 'Type something...',
              border: OutlineInputBorder(),
            ),
          ),
          SizedBox(height: 20),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              ElevatedButton(
                onPressed: () {
                  KeyboardUtils.hideKeyboard(context);
                },
                child: Text('Hide Keyboard'),
              ),
              ElevatedButton(
                onPressed: () {
                  KeyboardUtils.dismissKeyboard();
                },
                child: Text('Dismiss Alt'),
              ),
            ],
          ),
          SizedBox(height: 20),
          ElevatedButton(
            onPressed: () {
              bool isVisible = KeyboardUtils.isKeyboardVisible(context);
              double height = KeyboardUtils.getKeyboardHeight(context);

              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                    'Keyboard visible: $isVisible\nHeight: ${height.toStringAsFixed(1)}px',
                  ),
                ),
              );
            },
            child: Text('Check Keyboard Status'),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    super.dispose();
  }
}
