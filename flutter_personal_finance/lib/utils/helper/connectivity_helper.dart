import 'dart:io';
import 'package:flutter/foundation.dart';

/// Connectivity Helper class to check internet connectivity
class ConnectivityHelper {
  /// Check if device has internet connectivity using DNS lookup
  /// Returns true if internet is available, false otherwise
  static Future<bool> hasInternetConnection() async {
    try {
      debugPrint(
        'ConnectivityHelper: Performing internet connectivity test...',
      );

      // Try to connect to a reliable server (Google DNS)
      final result = await InternetAddress.lookup(
        'google.com',
      ).timeout(const Duration(seconds: 5));

      if (result.isNotEmpty && result[0].rawAddress.isNotEmpty) {
        debugPrint('ConnectivityHelper: Internet connectivity confirmed');
        return true;
      }

      debugPrint('ConnectivityHelper: Internet connectivity test failed');
      return false;
    } catch (e) {
      debugPrint('ConnectivityHelper: Internet connectivity test error: $e');
      return false;
    }
  }

  /// Alternative internet check using HTTP request (more reliable in some cases)
  static Future<bool> hasInternetConnectionHttp() async {
    try {
      debugPrint('ConnectivityHelper: Performing HTTP connectivity test...');

      final request = await HttpClient()
          .getUrl(Uri.parse('https://www.google.com'))
          .timeout(const Duration(seconds: 5));

      final response = await request.close().timeout(
        const Duration(seconds: 5),
      );

      if (response.statusCode == 200) {
        debugPrint('ConnectivityHelper: HTTP connectivity confirmed');
        return true;
      }

      debugPrint(
        'ConnectivityHelper: HTTP connectivity test failed with status: ${response.statusCode}',
      );
      return false;
    } catch (e) {
      debugPrint('ConnectivityHelper: HTTP connectivity test error: $e');
      return false;
    }
  }

  /// Quick connectivity check with both methods as fallback
  static Future<bool> hasInternetConnectionWithFallback() async {
    try {
      // First try DNS lookup (faster)
      bool hasConnection = await hasInternetConnection();

      // If DNS fails, try HTTP request as fallback
      if (!hasConnection) {
        debugPrint(
          'ConnectivityHelper: DNS lookup failed, trying HTTP request...',
        );
        hasConnection = await hasInternetConnectionHttp();
      }

      return hasConnection;
    } catch (e) {
      debugPrint(
        'ConnectivityHelper: Connectivity check with fallback failed: $e',
      );
      return false;
    }
  }
}
