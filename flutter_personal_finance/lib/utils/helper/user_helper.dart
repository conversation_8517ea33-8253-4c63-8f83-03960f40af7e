import '../../features/models/user_model.dart';
import '../../services/user_service.dart';

/// Utility class for easy access to current user data throughout the app
class UserHelper {
  /// Get current logged-in user
  static Future<UserModel?> getCurrentUser() async {
    return await UserService.getCurrentUser();
  }

  /// Get current user's email
  static Future<String?> getCurrentUserEmail() async {
    final user = await UserService.getCurrentUser();
    return user?.email;
  }

  /// Get current user's ID
  static Future<String?> getCurrentUserId() async {
    final user = await UserService.getCurrentUser();
    return user?.id;
  }

  /// Get any value from the API response data
  static Future<dynamic> getApiValue(String key) async {
    return await UserService.getCurrentUserApiValue(key);
  }

  /// Check if API data contains a specific key
  static Future<bool> hasApiKey(String key) async {
    return await UserService.currentUserHasApiKey(key);
  }

  /// Get current user's full API response data
  static Future<Map<String, dynamic>?> getApiData() async {
    final user = await UserService.getCurrentUser();
    return user?.apiData;
  }

  /// Check if user is logged in
  static Future<bool> isLoggedIn() async {
    return await UserService.isCurrentUserLoggedIn();
  }

  /// Update current user data
  static Future<void> updateCurrentUser(UserModel user) async {
    await UserService.saveCurrentUser(user);
  }

  /// Example usage methods for common scenarios:

  /// Get user's name from API data (if available)
  static Future<String?> getUserName() async {
    final apiData = await getApiData();
    return apiData?['name'] ?? apiData?['full_name'] ?? apiData?['first_name'];
  }

  /// Get user's phone from API data (if available)
  static Future<String?> getUserPhone() async {
    return await getApiValue('phone') ?? await getApiValue('mobile');
  }

  /// Get user's profile picture URL from API data (if available)
  static Future<String?> getProfilePictureUrl() async {
    return await getApiValue('profile_picture') ??
        await getApiValue('avatar') ??
        await getApiValue('image_url');
  }

  /// Get user's created date
  static Future<DateTime?> getUserCreatedDate() async {
    final user = await getCurrentUser();
    return user?.createdAt;
  }

  /// Print all available user data (for debugging)
  static Future<void> printUserData() async {
    final user = await getCurrentUser();
    if (user != null) {
      print('User ID: ${user.id}');
      print('Email: ${user.email}');
      print('Face Auth Enabled: ${user.isFaceAuthEnabled}');
      print('Created At: ${user.createdAt}');
      print('Updated At: ${user.updatedAt}');
      print('API Data: ${user.apiData}');
    } else {
      print('No current user found');
    }
  }
}
