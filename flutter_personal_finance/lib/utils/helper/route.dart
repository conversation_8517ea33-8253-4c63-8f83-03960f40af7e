import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../core/constants/app_constants.dart';
import '../../features/home/<USER>';
import '../../features/splash/splash.dart';
import '../../features/signup/signup_screen.dart';
import '../../features/signup/login_screen.dart';
import '../../features/dashboard/tabbed_dashboard_screen.dart';
import '../../features/profile/profile_screen.dart';
import '../../features/bank/add_bank_screen.dart';
import '../../features/bank/edit_bank_screen.dart';
import '../../features/transactions/add_transaction_screen.dart';
import '../../features/transactions/transaction_list_screen.dart';
import '../../features/recurring_transactions/recurring_transactions_list_screen.dart';
import '../../features/models/transaction_model.dart';
import '../../features/models/bank_account_model.dart';
import '../../features/models/credit_card_model.dart';
import '../../features/details/detailsScreen.dart';
import '../../features/profile/income_expense_categories_screen.dart';
import '../../features/upcoming_expenses/upcoming_expense_categories_screen.dart';
import '../../features/profile/transaction_export_screen.dart';
import '../../features/upcoming_expenses/upcoming_expenses_list_screen.dart';
import '../../features/budget/budget_planner_screen.dart';
import '../../features/auth/security_settings_screen.dart';
import '../../features/profile/notification_settings_screen.dart';
import '../../features/auth/authentication_screen.dart';
import '../../features/profile/advanced_analytics_screen.dart';
import '../../features/profile/data_visualization_screen.dart';
import '../../features/investment/investment_dashboard_screen.dart';
import '../../features/investment/add_investment_screen.dart';
import '../../features/investment/investment_calculator_screen.dart';
import '../../features/credit_card/credit_card_list_screen.dart';
import '../../features/credit_card/add_credit_card_screen.dart';
import '../../features/credit_card/edit_credit_card_screen.dart';
import '../../features/credit_card/add_credit_card_transaction_screen.dart';
import '../../features/credit_card/credit_card_transaction_list_screen.dart';

/// Router instance to be used in the app
final GoRouter router = AppRouter.router;

/// Constants for route names to avoid hardcoded strings
class GoRouterConstants {
  /// Private constructor to prevent instantiation
  GoRouterConstants._();

  static const String splash = 'splash';
  static const String signup = 'signup';
  static const String login = 'login';
  static const String home = 'home';
  static const String dashboard = 'dashboard';
  static const String profile = 'profile';
  static const String addBank = 'addBank';
  static const String editBank = 'editBank';
  static const String transactions = 'transactions';
  static const String transactionList = 'transactionList';
  static const String addTransaction = 'addTransaction';
  static const String recurringTransactions = 'recurringTransactions';
  static const String details = 'details';
  static const String incomeExpenseCategories = 'incomeExpenseCategories';
  static const String upcomingExpenseCategories = 'upcomingExpenseCategories';
  static const String transactionExport = 'transactionExport';
  static const String upcomingExpensesList = 'upcomingExpensesList';
  static const String budgetPlanner = 'budgetPlanner';
  static const String securitySettings = 'securitySettings';
  static const String notificationSettings = 'notificationSettings';
  static const String authentication = 'authentication';
  static const String advancedAnalytics = 'advancedAnalytics';
  static const String dataVisualization = 'dataVisualization';
  static const String investmentDashboard = 'investmentDashboard';
  static const String addInvestment = 'addInvestment';
  static const String investmentCalculator = 'investmentCalculator';
  static const String creditCardList = 'creditCardList';
  static const String addCreditCard = 'addCreditCard';
  static const String editCreditCard = 'editCreditCard';
  static const String addCreditCardTransaction = 'addCreditCardTransaction';
  static const String creditCardTransactionList = 'creditCardTransactionList';
}

/// AppRouter class responsible for managing the app's routing configuration
class AppRouter {
  /// Private constructor to prevent instantiation
  AppRouter._();

  /// The GoRouter instance for the application
  static final GoRouter router = GoRouter(
    initialLocation: '/',
    navigatorKey: kNavigatorKey,
    routes: _routes,
  );

  /// List of all routes in the application
  static List<RouteBase> get _routes => [
    GoRoute(
      path: '/',
      name: GoRouterConstants.splash,
      builder: (context, state) => const SplashVC(),
    ),
    GoRoute(
      path: '/signup',
      name: GoRouterConstants.signup,
      builder: (context, state) => const SignupScreen(),
    ),
    GoRoute(
      path: '/login',
      name: GoRouterConstants.login,
      builder: (context, state) => const LoginScreen(),
    ),
    GoRoute(
      path: '/dashboard',
      name: GoRouterConstants.dashboard,
      builder: (context, state) => const TabbedDashboardScreen(),
    ),
    GoRoute(
      path: '/profile',
      name: GoRouterConstants.profile,
      builder: (context, state) => const ProfileScreen(),
    ),
    GoRoute(
      path: '/add-bank',
      name: GoRouterConstants.addBank,
      builder: (context, state) => const AddBankScreen(),
    ),
    GoRoute(
      path: '/edit-bank/:bankId',
      name: GoRouterConstants.editBank,
      builder: (context, state) {
        final bankAccount = state.extra as BankAccountModel?;
        if (bankAccount == null) {
          // Fallback to AddBankScreen if no account data
          return const AddBankScreen();
        }
        return EditBankScreen(bankAccount: bankAccount);
      },
    ),
    GoRoute(
      path: '/add-transaction',
      name: GoRouterConstants.addTransaction,
      builder: (context, state) => const AddTransactionScreen(),
    ),
    GoRoute(
      path: '/transactions',
      name: GoRouterConstants.transactionList,
      builder: (context, state) => const TransactionListScreen(),
    ),
    GoRoute(
      path: '/recurring-transactions',
      name: GoRouterConstants.recurringTransactions,
      builder: (context, state) => const RecurringTransactionsListScreen(),
    ),
    GoRoute(
      path: '/add-income',
      name: 'addIncome',
      builder:
          (context, state) =>
              const AddTransactionScreen(initialType: TransactionType.credit),
    ),
    GoRoute(
      path: '/add-expense',
      name: 'addExpense',
      builder:
          (context, state) =>
              const AddTransactionScreen(initialType: TransactionType.debit),
    ),
    GoRoute(
      path: '/home',
      name: GoRouterConstants.home,
      builder: (context, state) => const HomeScreen(),
    ),
    GoRoute(
      path: '/details',
      name: GoRouterConstants.details,
      builder: (context, state) {
        return const DetailsScreen(id: 123, name: '');
      },
    ),
    GoRoute(
      path: '/income-expense-categories',
      name: GoRouterConstants.incomeExpenseCategories,
      builder: (context, state) => const IncomeExpenseCategoriesScreen(),
    ),
    GoRoute(
      path: '/upcoming-expense-categories',
      name: GoRouterConstants.upcomingExpenseCategories,
      builder: (context, state) => const UpcomingExpenseCategoriesScreen(),
    ),
    GoRoute(
      path: '/transaction-export',
      name: GoRouterConstants.transactionExport,
      builder: (context, state) => const TransactionExportScreen(),
    ),
    GoRoute(
      path: '/upcoming-expenses-list',
      name: GoRouterConstants.upcomingExpensesList,
      builder: (context, state) => const UpcomingExpensesListScreen(),
    ),
    GoRoute(
      path: '/budget-planner',
      name: GoRouterConstants.budgetPlanner,
      builder: (context, state) => const BudgetPlannerScreen(),
    ),
    GoRoute(
      path: '/security-settings',
      name: GoRouterConstants.securitySettings,
      builder: (context, state) => const SecuritySettingsScreen(),
    ),
    GoRoute(
      path: '/notification-settings',
      name: GoRouterConstants.notificationSettings,
      builder: (context, state) => const NotificationSettingsScreen(),
    ),
    GoRoute(
      path: '/authentication',
      name: GoRouterConstants.authentication,
      builder: (context, state) => const AuthenticationScreen(),
    ),
    GoRoute(
      path: '/advanced-analytics',
      name: GoRouterConstants.advancedAnalytics,
      builder: (context, state) => const AdvancedAnalyticsScreen(),
    ),
    GoRoute(
      path: '/data-visualization',
      name: GoRouterConstants.dataVisualization,
      builder: (context, state) => const DataVisualizationScreen(),
    ),
    GoRoute(
      path: '/investment-dashboard',
      name: GoRouterConstants.investmentDashboard,
      builder: (context, state) => const InvestmentDashboardScreen(),
    ),
    GoRoute(
      path: '/add-investment',
      name: GoRouterConstants.addInvestment,
      builder: (context, state) => const AddInvestmentScreen(),
    ),
    GoRoute(
      path: '/investment-calculator',
      name: GoRouterConstants.investmentCalculator,
      builder: (context, state) => const InvestmentCalculatorScreen(),
    ),
    GoRoute(
      path: '/credit-cards',
      name: GoRouterConstants.creditCardList,
      builder: (context, state) => const CreditCardListScreen(),
    ),
    GoRoute(
      path: '/add-credit-card',
      name: GoRouterConstants.addCreditCard,
      builder: (context, state) => const AddCreditCardScreen(),
    ),
    GoRoute(
      path: '/edit-credit-card/:creditCardId',
      name: GoRouterConstants.editCreditCard,
      builder: (context, state) {
        final creditCard = state.extra as CreditCardModel;
        return EditCreditCardScreen(creditCard: creditCard);
      },
    ),
    GoRoute(
      path: '/add-credit-card-transaction',
      name: GoRouterConstants.addCreditCardTransaction,
      builder: (context, state) => const AddCreditCardTransactionScreen(),
    ),
    GoRoute(
      path: '/credit-card-transactions',
      name: GoRouterConstants.creditCardTransactionList,
      builder: (context, state) {
        final creditCardId = state.uri.queryParameters['creditCardId'];
        return CreditCardTransactionListScreen(creditCardId: creditCardId);
      },
    ),
  ];

  /// Helper method to navigate to a named route, replacing the current route
  static void goNamed(
    BuildContext context,
    String routeName, {
    Map<String, String>? params,
  }) {
    context.goNamed(routeName, pathParameters: params ?? {});
  }

  /// Helper method to push a named route, adding a new route to the stack
  static void pushNamed(
    BuildContext context,
    String routeName, {
    Map<String, String>? params,
  }) {
    context.pushNamed(routeName, pathParameters: params ?? {});
  }
}
