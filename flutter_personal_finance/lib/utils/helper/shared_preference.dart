import 'package:shared_preferences/shared_preferences.dart';

/// Example of how to use SharedPreferencesHelper
///
/// ```dart
/// await SharedPreferencesHelper.init();
/// SharedPreferencesHelper.setUserName('<PERSON>');
/// String userName = SharedPreferencesHelper.getUserName();
/// ```

class SharedPreferencesHelper {
  static const String _keyUserName = 'user_name';
  static const String _keyIsLoggedIn = 'is_logged_in';
  static const String _keyUserEmail = 'user_email';
  static const String _keyUserPhone = 'user_phone';

  static late SharedPreferences _prefs;

  /// Initialize shared preferences (call this before using any method)
  static Future<void> init() async {
    _prefs = await SharedPreferences.getInstance();
  }

  /// Save string
  static void setUserName(String name) {
    _prefs.setString(_keyUserName, name);
  }

  /// Get string
  static String getUserName() {
    return _prefs.getString(_keyUserName) ?? '';
  }

  /// Save bool
  static void setIsLoggedIn(bool value) {
    _prefs.setBool(_keyIsLoggedIn, value);
  }

  /// Get bool
  static bool getIsLoggedIn() {
    return _prefs.getBool(_keyIsLoggedIn) ?? false;
  }

  /// Save string
  static void setUserEmail(String email) {
    _prefs.setString(_keyUserEmail, email);
  }

  /// Get string
  static String getUserEmail() {
    return _prefs.getString(_keyUserEmail) ?? '';
  }

  /// Save string
  static void setUserPhone(String phone) {
    _prefs.setString(_keyUserPhone, phone);
  }

  /// Get string
  static String getUserPhone() {
    return _prefs.getString(_keyUserPhone) ?? '';
  }

  /// Clear all data
  static void clearAll() {
    _prefs.clear();
  }
}
