import 'package:flutter/material.dart';
import '../../features/auth/authentication_screen.dart';

import '../../services/biometric_auth_service.dart';
import 'route.dart';

class AuthHelper {
  /// Show authentication screen and return true if authentication was successful
  static Future<bool> requireAuthentication(BuildContext context) async {
    // Check if authentication is required
    final authRequired = await BiometricAuthService.isAuthRequired();
    if (!authRequired) {
      return true; // No auth required, proceed
    }

    // Note: Using MaterialPageRoute here for authentication flow
    // which requires handling the bool result for auth validation
    final result = await Navigator.of(context).push<bool>(
      MaterialPageRoute(
        builder: (context) => const AuthenticationScreen(),
        fullscreenDialog: true,
      ),
    );

    return result ?? false;
  }

  /// Show security settings screen
  static Future<void> showSecuritySettings(BuildContext context) async {
    AppRouter.pushNamed(context, GoRouterConstants.securitySettings);
  }

  /// Check if any authentication method is set up
  static Future<bool> hasAuthenticationSetup() async {
    final biometricEnabled = await BiometricAuthService.isBiometricEnabled();
    return biometricEnabled;
  }

  /// Get current authentication status as a user-friendly string
  static Future<String> getAuthenticationStatus() async {
    return await BiometricAuthService.getAuthStatusDescription();
  }
}
