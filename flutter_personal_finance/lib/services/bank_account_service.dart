import '../core/network/api_service.dart';
import '../enviroment/config.dart';
import '../features/models/bank_account_model.dart';
import '../utils/helper/user_helper.dart';

/// Service class for managing bank account data via Supabase API
class BankAccountService {
  static final ApiService _apiService = ApiService();

  /// Get all bank accounts for the current user from API
  static Future<List<BankAccountModel>> getBankAccounts() async {
    try {
      // Get current user ID
      final currentUser = await UserHelper.getCurrentUser();
      if (currentUser == null) {
        throw Exception('User not logged in');
      }

      // Get user ID from API data
      final apiData = await UserHelper.getApiData();
      final userId = apiData?['id'];

      if (userId == null) {
        throw Exception('User ID not found');
      }

      // Make API request with proper headers and user_id filter
      final response = await _apiService.get(
        endpoint: 'bank_accounts',
        queryParameters: {'select': '*', 'user_id': 'eq.$userId'},
        headers: {
          'apikey': ENV.development.supabaseApiKey,
          'Authorization': 'Bearer ${ENV.development.supabaseApiKey}',
        },
      );

      if (!response.success) {
        throw Exception('Failed to fetch bank accounts: ${response.message}');
      }

      // Parse response data
      final List<dynamic> data = response.data ?? [];

      return data.map((json) {
        // Convert API response to BankAccountModel
        // Handle type conversion from API (int) to model (String)
        final currentAmount = _parseDouble(json['current_amount']);
        return BankAccountModel(
          id: json['id']?.toString() ?? '',
          bankName: json['bank_name']?.toString() ?? '',
          accountNumber: json['account_number']?.toString() ?? '',
          initialAmount: currentAmount, // Use current_amount as initial amount
          currentAmount: currentAmount,
          createdAt:
              json['created_at'] != null
                  ? DateTime.parse(json['created_at'])
                  : DateTime.now(),
          updatedAt:
              json['updated_at'] != null
                  ? DateTime.parse(json['updated_at'])
                  : DateTime.now(),
        );
      }).toList();
    } catch (e) {
      print('Error fetching bank accounts: $e');
      throw Exception('Failed to fetch bank accounts: $e');
    }
  }

  /// Add a new bank account via API
  static Future<void> addBankAccount(BankAccountModel account) async {
    try {
      // Get current user ID
      final apiData = await UserHelper.getApiData();
      final userId = apiData?['id'];

      if (userId == null) {
        throw Exception('User ID not found');
      }

      // Prepare data for API
      final bankData = {
        'bank_name': account.bankName,
        'account_number': account.accountNumber,
        'current_amount': account.currentAmount,
        'user_id': userId,
      };

      // Make API request
      final response = await _apiService.post(
        endpoint: 'bank_accounts',
        data: bankData,
        headers: {
          'apikey': ENV.development.supabaseApiKey,
          'Authorization': 'Bearer ${ENV.development.supabaseApiKey}',
          'Prefer': 'return=minimal',
        },
      );

      if (!response.success) {
        throw Exception('Failed to add bank account: ${response.message}');
      }
    } catch (e) {
      print('Error adding bank account: $e');
      throw Exception('Failed to add bank account: $e');
    }
  }

  /// Update a bank account via API
  static Future<void> updateBankAccount(BankAccountModel updatedAccount) async {
    try {
      // Prepare data for API
      final bankData = {
        'bank_name': updatedAccount.bankName,
        'account_number': updatedAccount.accountNumber,
        'current_amount': updatedAccount.currentAmount,
        'updated_at': DateTime.now().toIso8601String(),
      };

      // Make API request
      final response = await _apiService.patch(
        endpoint: 'bank_accounts',
        queryParameters: {'id': 'eq.${updatedAccount.id}'},
        data: bankData,
        headers: {
          'apikey': ENV.development.supabaseApiKey,
          'Authorization': 'Bearer ${ENV.development.supabaseApiKey}',
          'Prefer': 'return=minimal',
        },
      );

      if (!response.success) {
        throw Exception('Failed to update bank account: ${response.message}');
      }
    } catch (e) {
      print('Error updating bank account: $e');
      throw Exception('Failed to update bank account: $e');
    }
  }

  /// Delete a bank account via API
  static Future<void> deleteBankAccount(String accountId) async {
    try {
      // Make API request
      final response = await _apiService.delete(
        endpoint: 'bank_accounts',
        queryParameters: {'id': 'eq.$accountId'},
        headers: {
          'apikey': ENV.development.supabaseApiKey,
          'Authorization': 'Bearer ${ENV.development.supabaseApiKey}',
        },
      );

      if (!response.success) {
        throw Exception('Failed to delete bank account: ${response.message}');
      }
    } catch (e) {
      print('Error deleting bank account: $e');
      throw Exception('Failed to delete bank account: $e');
    }
  }

  /// Get a specific bank account by ID via API
  static Future<BankAccountModel?> getBankAccountById(String accountId) async {
    try {
      // Make API request
      final response = await _apiService.get(
        endpoint: 'bank_accounts',
        queryParameters: {'select': '*', 'id': 'eq.$accountId'},
        headers: {
          'apikey': ENV.development.supabaseApiKey,
          'Authorization': 'Bearer ${ENV.development.supabaseApiKey}',
        },
      );

      if (!response.success) {
        throw Exception('Failed to fetch bank account: ${response.message}');
      }

      final List<dynamic> data = response.data ?? [];
      if (data.isEmpty) return null;

      final json = data.first;
      final currentAmount = _parseDouble(json['current_amount']);
      return BankAccountModel(
        id: json['id']?.toString() ?? '',
        bankName: json['bank_name']?.toString() ?? '',
        accountNumber: json['account_number']?.toString() ?? '',
        initialAmount: currentAmount, // Use current_amount as initial amount
        currentAmount: currentAmount,
        createdAt:
            json['created_at'] != null
                ? DateTime.parse(json['created_at'])
                : DateTime.now(),
        updatedAt:
            json['updated_at'] != null
                ? DateTime.parse(json['updated_at'])
                : DateTime.now(),
      );
    } catch (e) {
      print('Error fetching bank account by ID: $e');
      return null;
    }
  }

  /// Update bank account balance via API
  static Future<void> updateBankAccountBalance(
    String accountId,
    double newBalance,
  ) async {
    try {
      // Prepare data for API
      final bankData = {
        'current_amount': newBalance,
        'updated_at': DateTime.now().toIso8601String(),
      };

      // Make API request
      final response = await _apiService.patch(
        endpoint: 'bank_accounts',
        queryParameters: {'id': 'eq.$accountId'},
        data: bankData,
        headers: {
          'apikey': ENV.development.supabaseApiKey,
          'Authorization': 'Bearer ${ENV.development.supabaseApiKey}',
          'Prefer': 'return=minimal',
        },
      );

      if (!response.success) {
        throw Exception(
          'Failed to update bank account balance: ${response.message}',
        );
      }
    } catch (e) {
      print('Error updating bank account balance: $e');
      throw Exception('Failed to update bank account balance: $e');
    }
  }

  /// Get total balance across all accounts for current user
  static Future<double> getTotalBalance() async {
    try {
      final accounts = await getBankAccounts();
      return accounts.fold<double>(
        0.0,
        (total, account) => total + account.currentAmount,
      );
    } catch (e) {
      print('Error calculating total balance: $e');
      return 0.0;
    }
  }

  /// Helper method to safely parse double values
  static double _parseDouble(dynamic value) {
    if (value == null) return 0.0;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) {
      return double.tryParse(value) ?? 0.0;
    }
    return 0.0;
  }
}
