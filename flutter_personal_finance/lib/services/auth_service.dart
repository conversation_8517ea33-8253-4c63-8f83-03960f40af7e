import 'package:local_auth/local_auth.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Service class for managing face authentication only
class AuthService {
  static final LocalAuthentication _localAuth = LocalAuthentication();
  static const String _faceAuthEnabledKey = 'face_auth_enabled';

  /// Check if biometric authentication is available
  static Future<bool> isBiometricAvailable() async {
    try {
      final bool isAvailable = await _localAuth.canCheckBiometrics;
      final bool isDeviceSupported = await _localAuth.isDeviceSupported();

      if (!isDeviceSupported) {
        print('Device does not support biometric authentication');
        return false;
      }

      if (!isAvailable) {
        print(
          'Biometric authentication not available (no enrolled biometrics)',
        );
        return false;
      }

      return isAvailable && isDeviceSupported;
    } catch (e) {
      print('Error checking biometric availability: $e');
      return false;
    }
  }

  /// Get available biometric types
  static Future<List<BiometricType>> getAvailableBiometrics() async {
    try {
      return await _localAuth.getAvailableBiometrics();
    } catch (e) {
      return [];
    }
  }

  /// Authenticate with biometrics
  static Future<bool> authenticateWithBiometrics() async {
    try {
      final bool isAvailable = await isBiometricAvailable();
      if (!isAvailable) {
        print('Biometric authentication not available');
        return false;
      }

      final bool didAuthenticate = await _localAuth.authenticate(
        localizedReason: 'Please authenticate to access your account',
        options: const AuthenticationOptions(
          biometricOnly: true,
          stickyAuth: true,
        ),
      );

      return didAuthenticate;
    } catch (e) {
      print('Biometric authentication error: $e');
      // Handle specific error types
      if (e.toString().contains('UserCancel')) {
        print('User cancelled biometric authentication');
      } else if (e.toString().contains('NotAvailable')) {
        print('Biometric authentication not available on this device');
      } else if (e.toString().contains('NotEnrolled')) {
        print('No biometric credentials enrolled on this device');
      }
      return false;
    }
  }

  /// Enable face authentication
  static Future<bool> enableFaceAuth() async {
    final bool canAuthenticate = await authenticateWithBiometrics();
    if (canAuthenticate) {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_faceAuthEnabledKey, true);
      return true;
    }
    return false;
  }

  /// Disable face authentication
  static Future<void> disableFaceAuth() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_faceAuthEnabledKey, false);
  }

  /// Check if user has face authentication enabled
  static Future<bool> isFaceAuthEnabled() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_faceAuthEnabledKey) ?? false;
  }

  /// Authenticate with face ID (main method for app access)
  static Future<bool> authenticateForAppAccess() async {
    final bool isFaceEnabled = await isFaceAuthEnabled();
    if (!isFaceEnabled) return false;

    return await authenticateWithBiometrics();
  }
}
