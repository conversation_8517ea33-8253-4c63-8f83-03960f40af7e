import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:timezone/timezone.dart' as tz;
import '../features/models/upcoming_expense_model.dart';
import '../features/models/budget_model.dart';
import '../features/models/recurring_transaction_model.dart';
import '../features/models/credit_card_model.dart';
import 'local_storage_service.dart';

class NotificationService {
  static final FlutterLocalNotificationsPlugin _notifications =
      FlutterLocalNotificationsPlugin();

  static const String _notificationSettingsKey = 'notification_settings';

  /// Initialize the notification service
  static Future<void> initialize() async {
    // Initialize timezone data
    // Note: You may need to call tz.initializeTimeZones() in main.dart

    const androidSettings = AndroidInitializationSettings(
      '@mipmap/ic_launcher',
    );
    const iosSettings = DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );

    const initSettings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );

    await _notifications.initialize(
      initSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );

    await _requestPermissions();
    print('NotificationService initialized successfully');
  }

  /// Handle notification tap
  static void _onNotificationTapped(NotificationResponse response) {
    print('Notification tapped: ${response.payload}');
    // TODO: Handle navigation to expense details if needed
  }

  /// Request notification permissions with enhanced debugging
  static Future<bool> _requestPermissions() async {
    print('🔔 Starting permission request...');

    try {
      // For iOS
      final iosImplementation =
          _notifications
              .resolvePlatformSpecificImplementation<
                IOSFlutterLocalNotificationsPlugin
              >();

      if (iosImplementation != null) {
        print('📱 iOS implementation found, requesting permissions...');

        // Check current permissions
        try {
          await iosImplementation.checkPermissions();
          print('📱 Checking current iOS permissions...');
        } catch (e) {
          print('📱 Could not check current permissions: $e');
        }

        // Request permissions
        final result = await iosImplementation.requestPermissions(
          alert: true,
          badge: true,
          sound: true,
          critical: false,
        );

        print('📱 iOS permission request result: $result');

        // Return the result from permission request
        return result ?? false;
      }

      // For Android
      final androidImplementation =
          _notifications
              .resolvePlatformSpecificImplementation<
                AndroidFlutterLocalNotificationsPlugin
              >();

      if (androidImplementation != null) {
        print('🤖 Android implementation found, requesting permissions...');
        final result =
            await androidImplementation.requestNotificationsPermission();
        print('🤖 Android permission result: $result');
        return result ?? false;
      }

      print('❌ No platform implementation found');
      return false;
    } catch (e) {
      print('❌ Error in permission request: $e');
      return false;
    }
  }

  /// Public method to manually trigger permission request
  static Future<bool> requestPermissionsManually() async {
    print('🔧 Manually requesting permissions...');
    final result = await _requestPermissions();

    if (result) {
      print(
        '✅ Permissions granted! App should appear in Settings → Notifications',
      );
    } else {
      print('❌ Permissions denied or failed');
    }

    return result;
  }

  /// Get notification settings
  static Future<NotificationSettings> getNotificationSettings() async {
    final prefs = await SharedPreferences.getInstance();
    final settingsJson = prefs.getString(_notificationSettingsKey);

    if (settingsJson == null) {
      // Return default settings
      return NotificationSettings();
    }

    return NotificationSettings.fromJson(json.decode(settingsJson));
  }

  /// Save notification settings
  static Future<void> saveNotificationSettings(
    NotificationSettings settings,
  ) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(
      _notificationSettingsKey,
      json.encode(settings.toJson()),
    );

    // Reschedule all notifications with new settings
    await rescheduleAllNotifications();
  }

  /// Schedule notification for an upcoming expense
  static Future<void> scheduleExpenseNotification(
    UpcomingExpenseModel expense,
  ) async {
    final settings = await getNotificationSettings();

    if (!settings.enabled || expense.isPaid) {
      return;
    }

    print(
      'Scheduling notification for ${expense.category} - ₹${expense.amount}',
    );

    final now = DateTime.now();
    final dueDate = expense.dueDate;

    for (int days in settings.daysBefore) {
      final notificationDate = DateTime(
        dueDate.year,
        dueDate.month,
        dueDate.day - days,
        settings.notificationTime.hour,
        settings.notificationTime.minute,
      );

      if (notificationDate.isAfter(now)) {
        await _scheduleNotification(
          id: '${expense.id}_$days'.hashCode,
          title: _getNotificationTitle(days),
          body: '${expense.category} - ₹${expense.amount.toStringAsFixed(0)}',
          scheduledDate: notificationDate,
          payload: expense.id,
          urgencyLevel: _getUrgencyLevel(days),
        );
        print(
          'Scheduled notification for ${_getNotificationTitle(days)} on $notificationDate',
        );
      }
    }
  }

  /// Cancel notifications for an expense
  static Future<void> cancelExpenseNotifications(String expenseId) async {
    final settings = await getNotificationSettings();

    for (int days in settings.daysBefore) {
      final notificationId = '${expenseId}_$days'.hashCode;
      await _notifications.cancel(notificationId);
    }
    print('Cancelled notifications for expense $expenseId');
  }

  /// Schedule a single notification
  static Future<void> _scheduleNotification({
    required int id,
    required String title,
    required String body,
    required DateTime scheduledDate,
    String? payload,
    String urgencyLevel = 'normal',
  }) async {
    try {
      final soundFile = _getSoundForUrgency(urgencyLevel);

      final androidDetails = AndroidNotificationDetails(
        'upcoming_expenses',
        'Upcoming Expenses',
        channelDescription: 'Notifications for upcoming expense payments',
        importance: Importance.high,
        priority: Priority.high,
        icon: '@mipmap/ic_launcher',
        sound: RawResourceAndroidNotificationSound(soundFile),
        enableLights: true,
        // ledColor: _getColorForUrgency(urgencyLevel),
        enableVibration: true,
        // vibrationPattern: _getVibrationForUrgency(urgencyLevel),
      );

      final iosDetails = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
        // sound: '$soundFile.caf',
      );

      final notificationDetails = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      await _notifications.zonedSchedule(
        id,
        title,
        body,
        tz.TZDateTime.from(scheduledDate, tz.local),
        notificationDetails,
        androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
        uiLocalNotificationDateInterpretation:
            UILocalNotificationDateInterpretation.absoluteTime,
        payload: payload,
      );

      print(
        'Scheduled notification: $title for ${scheduledDate.toString()} with $soundFile sound',
      );
    } catch (e) {
      print('Error scheduling notification: $e');
    }
  }

  /// Get notification title based on days before
  static String _getNotificationTitle(int days) {
    switch (days) {
      case 0:
        return 'Payment Due Today!';
      case 1:
        return 'Payment Due Tomorrow';
      default:
        return 'Payment Due in $days Days';
    }
  }

  /// Get urgency level based on days before due date
  static String _getUrgencyLevel(int days) {
    if (days <= 0) return 'urgent'; // Due today or overdue
    if (days <= 1) return 'high'; // Due tomorrow
    if (days <= 3) return 'medium'; // Due in 2-3 days
    return 'normal'; // More than 3 days
  }

  /// Get sound file based on urgency level
  static String _getSoundForUrgency(String urgencyLevel) {
    switch (urgencyLevel) {
      case 'urgent':
        return 'payment_due'; // Most attention-grabbing sound
      case 'high':
        return 'payment_due'; // Noticeable but not alarming
      case 'medium':
        return 'payment_due'; // Same as high priority
      case 'normal':
      default:
        return 'payment_due'; // Gentle reminder sound
    }
  }

  /// Get LED color based on urgency level (Android)
  // static int _getColorForUrgency(String urgencyLevel) {
  //   switch (urgencyLevel) {
  //     case 'urgent':
  //       return 0xFFFF0000; // Red
  //     case 'high':
  //       return 0xFFFF8800; // Orange
  //     case 'medium':
  //       return 0xFFFFDD00; // Yellow
  //     case 'normal':
  //     default:
  //       return 0xFF0088FF; // Blue
  //   }
  // }

  // /// Get vibration pattern based on urgency level (Android)
  // static List<int> _getVibrationForUrgency(String urgencyLevel) {
  //   switch (urgencyLevel) {
  //     case 'urgent':
  //       return [0, 1000, 500, 1000, 500, 1000]; // Long, urgent pattern
  //     case 'high':
  //       return [0, 800, 400, 800]; // Medium pattern
  //     case 'medium':
  //       return [0, 500, 300, 500]; // Shorter pattern
  //     case 'normal':
  //     default:
  //       return [0, 300, 200, 300]; // Gentle pattern
  //   }
  // }

  /// Reschedule all notifications for all expenses and credit cards
  static Future<void> rescheduleAllNotifications() async {
    print('Rescheduling all notifications');

    // Cancel all existing notifications
    await _notifications.cancelAll();

    // Get all upcoming expenses
    final expenses = await LocalStorageService.getUpcomingExpenses();

    // Schedule notifications for each unpaid expense
    for (final expense in expenses) {
      if (!expense.isPaid) {
        await scheduleExpenseNotification(expense);
      }
    }

    // Schedule notifications for all credit cards with outstanding balance
    await scheduleAllCreditCardNotifications();
  }

  /// Cancel all notifications
  static Future<void> cancelAllNotifications() async {
    await _notifications.cancelAll();
    print('Cancelled all notifications');
  }

  /// Test notification (for debugging)
  static Future<void> showTestNotification() async {
    final androidDetails = AndroidNotificationDetails(
      'test_channel',
      'Test Notifications',
      channelDescription: 'Test notifications',
      importance: Importance.high,
      priority: Priority.high,
      // sound: RawResourceAndroidNotificationSound('urgent_payment'),
      enableLights: true,
      // ledColor: 0xFFFF0000,
      enableVibration: true,
      // vibrationPattern: [0, 1000, 500, 1000],
    );

    const iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
      // sound: 'urgent_payment.caf',
    );

    final notificationDetails = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    await _notifications.show(
      999999,
      '💰 Test Notification',
      'This is a test notification from Personal Finance App',
      notificationDetails,
    );
  }

  /// Show immediate notification with custom title and body
  static Future<void> showImmediateNotification({
    required String title,
    required String body,
    String? payload,
  }) async {
    final androidDetails = AndroidNotificationDetails(
      'credit_card_updates',
      'Credit Card Updates',
      channelDescription: 'Credit card date and status updates',
      importance: Importance.defaultImportance,
      priority: Priority.defaultPriority,
      enableLights: true,
      enableVibration: true,
    );

    const iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    final notificationDetails = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    await _notifications.show(
      DateTime.now().millisecondsSinceEpoch.remainder(100000),
      title,
      body,
      notificationDetails,
      payload: payload,
    );
  }

  // ==================== BUDGET NOTIFICATIONS ====================

  /// Schedule budget warning notification
  static Future<void> scheduleBudgetWarningNotification(
    BudgetModel budget,
  ) async {
    final settings = await getNotificationSettings();

    if (!settings.enabled) return;

    final notificationId = 'budget_warning_${budget.id}'.hashCode;
    final title = 'Budget Warning';
    final body =
        '${budget.categoryName} budget is ${budget.progressPercentage.toStringAsFixed(1)}% used (₹${budget.currentSpent.toStringAsFixed(0)} of ₹${budget.monthlyLimit.toStringAsFixed(0)})';

    await _scheduleNotification(
      id: notificationId,
      title: title,
      body: body,
      scheduledDate: DateTime.now().add(
        const Duration(seconds: 5),
      ), // Show immediately
      payload: 'budget_warning_${budget.id}',
      urgencyLevel: 'medium',
    );

    print('Scheduled budget warning for ${budget.categoryName}');
  }

  /// Schedule budget critical notification
  static Future<void> scheduleBudgetCriticalNotification(
    BudgetModel budget,
  ) async {
    final settings = await getNotificationSettings();

    if (!settings.enabled) return;

    final notificationId = 'budget_critical_${budget.id}'.hashCode;
    final title = 'Budget Critical!';
    final body =
        '${budget.categoryName} budget is ${budget.progressPercentage.toStringAsFixed(1)}% used - almost at limit!';

    await _scheduleNotification(
      id: notificationId,
      title: title,
      body: body,
      scheduledDate: DateTime.now().add(
        const Duration(seconds: 5),
      ), // Show immediately
      payload: 'budget_critical_${budget.id}',
      urgencyLevel: 'high',
    );

    print('Scheduled budget critical alert for ${budget.categoryName}');
  }

  /// Schedule budget exceeded notification
  static Future<void> scheduleBudgetExceededNotification(
    BudgetModel budget,
  ) async {
    final settings = await getNotificationSettings();

    if (!settings.enabled) return;

    final notificationId = 'budget_exceeded_${budget.id}'.hashCode;
    final title = 'Budget Exceeded!';
    final body =
        '${budget.categoryName} budget exceeded by ₹${(budget.currentSpent - budget.monthlyLimit).toStringAsFixed(0)}';

    await _scheduleNotification(
      id: notificationId,
      title: title,
      body: body,
      scheduledDate: DateTime.now().add(
        const Duration(seconds: 5),
      ), // Show immediately
      payload: 'budget_exceeded_${budget.id}',
      urgencyLevel: 'urgent',
    );

    print('Scheduled budget exceeded alert for ${budget.categoryName}');
  }

  /// Check and trigger budget notifications based on current state
  static Future<void> checkAndTriggerBudgetNotifications() async {
    final settings = await getNotificationSettings();

    if (!settings.enabled) return;

    final budgets = await LocalStorageService.getCurrentMonthBudgets();

    for (final budget in budgets) {
      // Check for exceeded budget
      if (budget.isExceeded) {
        await scheduleBudgetExceededNotification(budget);
      }
      // Check for critical budget (90%+ used)
      else if (budget.progressPercentage >= 90) {
        await scheduleBudgetCriticalNotification(budget);
      }
      // Check for warning budget (75%+ used)
      else if (budget.progressPercentage >= 75) {
        await scheduleBudgetWarningNotification(budget);
      }
    }
  }

  /// Check and trigger budget notifications for a specific category after transaction
  static Future<void> checkBudgetNotificationsForCategory(
    String categoryName,
    String categoryType,
  ) async {
    final settings = await getNotificationSettings();

    if (!settings.enabled) return;

    final budgets = await LocalStorageService.getCurrentMonthBudgets();
    final categoryBudgets =
        budgets
            .where(
              (budget) =>
                  budget.categoryName == categoryName &&
                  budget.categoryType == categoryType,
            )
            .toList();

    for (final budget in categoryBudgets) {
      // Check for exceeded budget
      if (budget.isExceeded) {
        await scheduleBudgetExceededNotification(budget);
      }
      // Check for critical budget (90%+ used)
      else if (budget.progressPercentage >= 90) {
        await scheduleBudgetCriticalNotification(budget);
      }
      // Check for warning budget (75%+ used)
      else if (budget.progressPercentage >= 75) {
        await scheduleBudgetWarningNotification(budget);
      }
    }
  }

  // ==================== RECURRING TRANSACTION NOTIFICATIONS ====================

  /// Schedule notification for a recurring transaction
  static Future<void> scheduleRecurringTransactionNotification(
    RecurringTransactionModel recurringTransaction,
  ) async {
    final settings = await getNotificationSettings();

    if (!settings.enabled || !recurringTransaction.isActive) {
      return;
    }

    print(
      'Scheduling notification for recurring ${recurringTransaction.description} - ₹${recurringTransaction.amount}',
    );

    final now = DateTime.now();
    final dueDate = recurringTransaction.nextDueDate;

    for (int days in settings.daysBefore) {
      final notificationDate = DateTime(
        dueDate.year,
        dueDate.month,
        dueDate.day - days,
        settings.notificationTime.hour,
        settings.notificationTime.minute,
      );

      if (notificationDate.isAfter(now)) {
        await _scheduleNotification(
          id: 'recurring_${recurringTransaction.id}_$days'.hashCode,
          title: _getRecurringNotificationTitle(
            days,
            recurringTransaction.frequency,
          ),
          body:
              '${recurringTransaction.description} - ₹${recurringTransaction.amount.toStringAsFixed(0)}',
          scheduledDate: notificationDate,
          payload: 'recurring_${recurringTransaction.id}',
          urgencyLevel: _getUrgencyLevel(days),
        );
        print(
          'Scheduled recurring notification for ${_getRecurringNotificationTitle(days, recurringTransaction.frequency)} on $notificationDate',
        );
      }
    }
  }

  /// Cancel notifications for a recurring transaction
  static Future<void> cancelRecurringTransactionNotifications(
    String transactionId,
  ) async {
    final settings = await getNotificationSettings();

    for (int days in settings.daysBefore) {
      final notificationId = 'recurring_${transactionId}_$days'.hashCode;
      await _notifications.cancel(notificationId);
    }
    print('Cancelled notifications for recurring transaction $transactionId');
  }

  /// Get notification title for recurring transactions
  static String _getRecurringNotificationTitle(
    int days,
    RecurringFrequency frequency,
  ) {
    final frequencyText = frequency.name.toUpperCase();
    switch (days) {
      case 0:
        return 'Recurring $frequencyText Payment Due Today!';
      case 1:
        return 'Recurring $frequencyText Payment Due Tomorrow';
      default:
        return 'Recurring $frequencyText Payment Due in $days Days';
    }
  }

  // ==================== CREDIT CARD NOTIFICATIONS ====================

  /// Schedule notifications for credit card statement generation
  static Future<void> scheduleCreditCardStatementNotification(
    CreditCardModel creditCard,
  ) async {
    final settings = await getNotificationSettings();

    if (!settings.enabled) {
      return;
    }

    print('Scheduling statement notification for ${creditCard.cardName}');

    final now = DateTime.now();
    final closingDate = creditCard.closingDate;

    // Don't schedule notifications for past closing dates
    if (closingDate.isBefore(now.subtract(const Duration(days: 1)))) {
      print('Skipping past statement date for: ${creditCard.cardName}');
      return;
    }

    // Schedule notification for statement generation (on closing date)
    final statementNotificationDate = DateTime(
      closingDate.year,
      closingDate.month,
      closingDate.day,
      settings.notificationTime.hour,
      settings.notificationTime.minute,
    );

    if (statementNotificationDate.isAfter(now)) {
      await _scheduleNotification(
        id: 'statement_${creditCard.id}'.hashCode,
        title: 'Statement Generated!',
        body:
            '${creditCard.cardName} - New statement available. Outstanding: ₹${creditCard.outstandingBalance.toStringAsFixed(0)}',
        scheduledDate: statementNotificationDate,
        payload: 'statement_${creditCard.id}',
        urgencyLevel: 'normal',
      );
      print(
        'Scheduled statement notification for ${creditCard.cardName} on $statementNotificationDate',
      );
    }

    // Also schedule notifications before closing date (1-2 days before)
    for (int days in [1, 2]) {
      if (settings.daysBefore.contains(days)) {
        final reminderDate = DateTime(
          closingDate.year,
          closingDate.month,
          closingDate.day - days,
          settings.notificationTime.hour,
          settings.notificationTime.minute,
        );

        if (reminderDate.isAfter(now)) {
          await _scheduleNotification(
            id: 'statement_reminder_${creditCard.id}_$days'.hashCode,
            title: _getStatementReminderTitle(days),
            body:
                '${creditCard.cardName} - Complete pending transactions before statement closes',
            scheduledDate: reminderDate,
            payload: 'statement_reminder_${creditCard.id}',
            urgencyLevel: 'medium',
          );
          print(
            'Scheduled statement reminder for ${creditCard.cardName} $days days before closing',
          );
        }
      }
    }
  }

  /// Schedule notifications for credit card payment due date
  static Future<void> scheduleCreditCardPaymentNotification(
    CreditCardModel creditCard,
  ) async {
    final settings = await getNotificationSettings();

    if (!settings.enabled || creditCard.outstandingBalance <= 0) {
      return;
    }

    print(
      'Scheduling payment notification for ${creditCard.cardName} - ₹${creditCard.outstandingBalance}',
    );

    final now = DateTime.now();
    final dueDate = creditCard.dueDate;

    // Don't schedule notifications for overdue dates
    if (dueDate.isBefore(now.subtract(const Duration(days: 1)))) {
      print('Skipping overdue credit card: ${creditCard.cardName}');
      return;
    }

    for (int days in settings.daysBefore) {
      final notificationDate = DateTime(
        dueDate.year,
        dueDate.month,
        dueDate.day - days,
        settings.notificationTime.hour,
        settings.notificationTime.minute,
      );

      if (notificationDate.isAfter(now)) {
        await _scheduleNotification(
          id: 'payment_${creditCard.id}_$days'.hashCode,
          title: _getCreditCardPaymentNotificationTitle(days),
          body:
              '${creditCard.cardName} - ₹${creditCard.outstandingBalance.toStringAsFixed(0)} payment due',
          scheduledDate: notificationDate,
          payload: 'payment_${creditCard.id}',
          urgencyLevel: _getUrgencyLevel(days),
        );
        print(
          'Scheduled payment notification for ${creditCard.cardName} $days days before due date',
        );
      }
    }
  }

  /// Schedule both statement and payment notifications for a credit card
  static Future<void> scheduleCreditCardNotifications(
    CreditCardModel creditCard,
  ) async {
    // Schedule statement notifications
    await scheduleCreditCardStatementNotification(creditCard);

    // Schedule payment notifications (only if outstanding balance > 0)
    if (creditCard.outstandingBalance > 0) {
      await scheduleCreditCardPaymentNotification(creditCard);
    }
  }

  /// Cancel all notifications for a credit card (both statement and payment)
  static Future<void> cancelCreditCardNotifications(String creditCardId) async {
    final settings = await getNotificationSettings();

    // Cancel statement notifications
    await _notifications.cancel('statement_${creditCardId}'.hashCode);

    // Cancel statement reminder notifications
    for (int days in [1, 2]) {
      await _notifications.cancel(
        'statement_reminder_${creditCardId}_$days'.hashCode,
      );
    }

    // Cancel payment notifications
    for (int days in settings.daysBefore) {
      final notificationId = 'payment_${creditCardId}_$days'.hashCode;
      await _notifications.cancel(notificationId);
    }

    print('Cancelled all notifications for credit card $creditCardId');
  }

  /// Get notification title for statement reminders
  static String _getStatementReminderTitle(int days) {
    switch (days) {
      case 1:
        return 'Statement Closes Tomorrow!';
      case 2:
        return 'Statement Closes in 2 Days';
      default:
        return 'Statement Closing Soon';
    }
  }

  /// Get notification title for payment due dates
  static String _getCreditCardPaymentNotificationTitle(int days) {
    switch (days) {
      case 0:
        return 'Credit Card Payment Due Today!';
      case 1:
        return 'Credit Card Payment Due Tomorrow';
      default:
        return 'Credit Card Payment Due in $days Days';
    }
  }

  /// Schedule notifications for all credit cards (both statement and payment)
  static Future<void> scheduleAllCreditCardNotifications() async {
    final settings = await getNotificationSettings();

    if (!settings.enabled) return;

    try {
      final creditCards = await LocalStorageService.getCreditCards();

      for (final creditCard in creditCards) {
        await scheduleCreditCardNotifications(creditCard);
      }
      print('Scheduled notifications for ${creditCards.length} credit cards');
    } catch (e) {
      print('Error scheduling credit card notifications: $e');
    }
  }
}

/// Notification settings model
class NotificationSettings {
  final bool enabled;
  final List<int> daysBefore;
  final TimeOfDay notificationTime;

  NotificationSettings({
    this.enabled = true,
    this.daysBefore = const [1, 3], // Default: 1 and 3 days before
    this.notificationTime = const TimeOfDay(
      hour: 9,
      minute: 0,
    ), // Default: 9:00 AM
  });

  factory NotificationSettings.fromJson(Map<String, dynamic> json) {
    return NotificationSettings(
      enabled: json['enabled'] ?? true,
      daysBefore: List<int>.from(json['daysBefore'] ?? [1, 3]),
      notificationTime: TimeOfDay(
        hour: json['notificationHour'] ?? 9,
        minute: json['notificationMinute'] ?? 0,
      ),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'enabled': enabled,
      'daysBefore': daysBefore,
      'notificationHour': notificationTime.hour,
      'notificationMinute': notificationTime.minute,
    };
  }

  NotificationSettings copyWith({
    bool? enabled,
    List<int>? daysBefore,
    TimeOfDay? notificationTime,
  }) {
    return NotificationSettings(
      enabled: enabled ?? this.enabled,
      daysBefore: daysBefore ?? this.daysBefore,
      notificationTime: notificationTime ?? this.notificationTime,
    );
  }
}
