import 'dart:convert';
import 'dart:math' as math;
import 'package:shared_preferences/shared_preferences.dart';
import '../features/models/investment_model.dart';
import '../features/models/transaction_model.dart';
import 'local_storage_service.dart';

class InvestmentService {
  static const String _investmentsKey = 'investments';
  static const String _investmentEntriesKey = 'investment_entries';
  static const String _investmentWithdrawalsKey = 'investment_withdrawals';

  // Investment operations
  static Future<List<InvestmentModel>> getInvestments() async {
    final prefs = await SharedPreferences.getInstance();
    final investmentsJson = prefs.getString(_investmentsKey);

    if (investmentsJson == null) return [];

    final List<dynamic> investmentsList = json.decode(investmentsJson);
    return investmentsList
        .map((json) => InvestmentModel.fromJson(json))
        .toList();
  }

  static Future<void> saveInvestments(List<InvestmentModel> investments) async {
    final prefs = await SharedPreferences.getInstance();
    final investmentsJson = json.encode(
      investments.map((investment) => investment.toJson()).toList(),
    );
    await prefs.setString(_investmentsKey, investmentsJson);
  }

  static Future<void> addInvestment(InvestmentModel investment) async {
    final investments = await getInvestments();
    investments.add(investment);
    await saveInvestments(investments);
  }

  static Future<void> updateInvestment(
    InvestmentModel updatedInvestment,
  ) async {
    final investments = await getInvestments();
    final index = investments.indexWhere(
      (investment) => investment.id == updatedInvestment.id,
    );

    if (index != -1) {
      investments[index] = updatedInvestment;
      await saveInvestments(investments);
    }
  }

  static Future<void> deleteInvestment(String investmentId) async {
    final investments = await getInvestments();
    investments.removeWhere((investment) => investment.id == investmentId);
    await saveInvestments(investments);

    // Also delete related entries and withdrawals
    await deleteInvestmentEntriesByInvestmentId(investmentId);
    await deleteInvestmentWithdrawalsByInvestmentId(investmentId);
  }

  // Investment Entry operations
  static Future<List<InvestmentEntryModel>> getInvestmentEntries() async {
    final prefs = await SharedPreferences.getInstance();
    final entriesJson = prefs.getString(_investmentEntriesKey);

    if (entriesJson == null) return [];

    final List<dynamic> entriesList = json.decode(entriesJson);
    return entriesList
        .map((json) => InvestmentEntryModel.fromJson(json))
        .toList();
  }

  static Future<void> saveInvestmentEntries(
    List<InvestmentEntryModel> entries,
  ) async {
    final prefs = await SharedPreferences.getInstance();
    final entriesJson = json.encode(
      entries.map((entry) => entry.toJson()).toList(),
    );
    await prefs.setString(_investmentEntriesKey, entriesJson);
  }

  static Future<void> addInvestmentEntry(InvestmentEntryModel entry) async {
    final entries = await getInvestmentEntries();
    entries.add(entry);
    await saveInvestmentEntries(entries);

    // Update investment total invested amount
    await _updateInvestmentTotalInvested(entry.investmentId);
  }

  static Future<void> deleteInvestmentEntriesByInvestmentId(
    String investmentId,
  ) async {
    final entries = await getInvestmentEntries();
    entries.removeWhere((entry) => entry.investmentId == investmentId);
    await saveInvestmentEntries(entries);
  }

  static Future<List<InvestmentEntryModel>> getInvestmentEntriesByInvestmentId(
    String investmentId,
  ) async {
    final entries = await getInvestmentEntries();
    return entries
        .where((entry) => entry.investmentId == investmentId)
        .toList();
  }

  // Investment Withdrawal operations
  static Future<List<InvestmentWithdrawalModel>>
  getInvestmentWithdrawals() async {
    final prefs = await SharedPreferences.getInstance();
    final withdrawalsJson = prefs.getString(_investmentWithdrawalsKey);

    if (withdrawalsJson == null) return [];

    final List<dynamic> withdrawalsList = json.decode(withdrawalsJson);
    return withdrawalsList
        .map((json) => InvestmentWithdrawalModel.fromJson(json))
        .toList();
  }

  static Future<void> saveInvestmentWithdrawals(
    List<InvestmentWithdrawalModel> withdrawals,
  ) async {
    final prefs = await SharedPreferences.getInstance();
    final withdrawalsJson = json.encode(
      withdrawals.map((withdrawal) => withdrawal.toJson()).toList(),
    );
    await prefs.setString(_investmentWithdrawalsKey, withdrawalsJson);
  }

  static Future<void> addInvestmentWithdrawal(
    InvestmentWithdrawalModel withdrawal,
  ) async {
    final withdrawals = await getInvestmentWithdrawals();
    withdrawals.add(withdrawal);
    await saveInvestmentWithdrawals(withdrawals);

    // Update investment after withdrawal
    await _updateInvestmentAfterWithdrawal(
      withdrawal.investmentId,
      withdrawal.amount,
    );
  }

  static Future<void> deleteInvestmentWithdrawalsByInvestmentId(
    String investmentId,
  ) async {
    final withdrawals = await getInvestmentWithdrawals();
    withdrawals.removeWhere(
      (withdrawal) => withdrawal.investmentId == investmentId,
    );
    await saveInvestmentWithdrawals(withdrawals);
  }

  static Future<List<InvestmentWithdrawalModel>>
  getInvestmentWithdrawalsByInvestmentId(String investmentId) async {
    final withdrawals = await getInvestmentWithdrawals();
    return withdrawals
        .where((withdrawal) => withdrawal.investmentId == investmentId)
        .toList();
  }

  // Helper methods
  static Future<void> _updateInvestmentTotalInvested(
    String investmentId,
  ) async {
    final investments = await getInvestments();
    final entries = await getInvestmentEntriesByInvestmentId(investmentId);

    final investmentIndex = investments.indexWhere(
      (investment) => investment.id == investmentId,
    );

    if (investmentIndex != -1) {
      final investment = investments[investmentIndex];

      // Calculate total invested based on all entries (for auto-created investments, initial amount = first entry)
      final totalInvested = entries.fold<double>(
        0.0,
        (sum, entry) => sum + entry.amount,
      );

      // For auto-created investments, don't add initial amount as it's already counted in entries
      // For manually created investments, add initial amount to entries
      final isAutoCreated =
          investment.description == 'Auto-created from transaction';
      final finalTotalInvested =
          isAutoCreated
              ? totalInvested
              : investment.initialAmount + totalInvested;

      // For interest-based investments, calculate current value; for manual entry, use total invested
      final newCurrentValue =
          (investment.returnType == InvestmentReturnType.simpleInterest ||
                  investment.returnType ==
                      InvestmentReturnType.compoundInterest)
              ? investment
                  .copyWith(totalInvested: finalTotalInvested)
                  .calculateCurrentValue()
              : finalTotalInvested;

      final updatedInvestment = investment.copyWith(
        totalInvested: finalTotalInvested,
        currentValue: newCurrentValue,
        updatedAt: DateTime.now(),
      );

      await updateInvestment(updatedInvestment);
    }
  }

  static Future<void> _updateInvestmentAfterWithdrawal(
    String investmentId,
    double withdrawalAmount,
  ) async {
    final investments = await getInvestments();
    final investmentIndex = investments.indexWhere(
      (investment) => investment.id == investmentId,
    );

    if (investmentIndex != -1) {
      final investment = investments[investmentIndex];
      final newCurrentValue = investment.currentValue - withdrawalAmount;

      final updatedInvestment = investment.copyWith(
        currentValue: newCurrentValue,
        updatedAt: DateTime.now(),
      );

      await updateInvestment(updatedInvestment);
    }
  }

  // Auto-process transactions for investment categories
  static Future<void> processTransactionForInvestment(
    TransactionModel transaction,
  ) async {
    // Get categories to check if this transaction is investment-related
    final categories = await LocalStorageService.getIncomeExpenseCategories();

    final investmentCategory = categories.firstWhere(
      (cat) =>
          cat['name'] == transaction.category && cat['isInvestment'] == true,
      orElse: () => <String, dynamic>{},
    );

    if (investmentCategory.isNotEmpty &&
        transaction.type == TransactionType.debit) {
      // This is an investment transaction, find or create investment
      final investments = await getInvestments();

      // Try to find existing investment for this category
      InvestmentModel? existingInvestment;
      try {
        existingInvestment = investments.firstWhere(
          (inv) => inv.categoryIds.contains(investmentCategory['id']),
        );
      } catch (e) {
        existingInvestment = null;
      }

      if (existingInvestment == null) {
        // Create new investment
        final investment = InvestmentModel(
          name: transaction.category,
          category: transaction.category,
          description: 'Auto-created from transaction',
          initialAmount: transaction.amount,
          currentValue: transaction.amount,
          totalInvested: transaction.amount,
          returnType: InvestmentReturnType.manualEntry,
          expectedReturnRate: 0.0,
          returnFrequency: InvestmentFrequency.yearly,
          startDate: transaction.date,
          categoryIds: [investmentCategory['id']],
        );

        await addInvestment(investment);
        existingInvestment = investment;
      }

      // Add investment entry
      final entry = InvestmentEntryModel(
        investmentId: existingInvestment.id,
        amount: transaction.amount,
        date: transaction.date,
        description: transaction.description,
        transactionId: transaction.id,
      );

      await addInvestmentEntry(entry);
    }
  }

  // Calculate total portfolio value
  static Future<double> getTotalPortfolioValue() async {
    final investments = await getInvestments();
    return investments.fold<double>(
      0.0,
      (sum, investment) =>
          sum +
          ((investment.returnType == InvestmentReturnType.simpleInterest ||
                  investment.returnType ==
                      InvestmentReturnType.compoundInterest)
              ? investment.calculateCurrentValue()
              : investment.currentValue),
    );
  }

  // Calculate total invested amount
  static Future<double> getTotalInvestedAmount() async {
    final investments = await getInvestments();
    return investments.fold<double>(
      0.0,
      (sum, investment) => sum + investment.totalInvested,
    );
  }

  // Calculate total returns
  static Future<double> getTotalReturns() async {
    final investments = await getInvestments();
    return investments.fold<double>(0.0, (sum, investment) {
      // For interest-based investments, calculate dynamic current value; for manual entry, use stored value
      final currentValue =
          (investment.returnType == InvestmentReturnType.simpleInterest ||
                  investment.returnType ==
                      InvestmentReturnType.compoundInterest)
              ? investment.calculateCurrentValue()
              : investment.currentValue;
      final profitLoss = currentValue - investment.totalInvested;
      return sum + profitLoss;
    });
  }

  // Get investments by status
  static Future<List<InvestmentModel>> getInvestmentsByStatus(
    InvestmentStatus status,
  ) async {
    final investments = await getInvestments();
    return investments.where((inv) => inv.status == status).toList();
  }

  // Auto-update investment values
  static Future<void> updateInvestmentValues() async {
    final investments = await getInvestments();

    for (final investment in investments) {
      if (investment.isAutoGrowthEnabled ||
          investment.returnType == InvestmentReturnType.compoundInterest ||
          investment.returnType == InvestmentReturnType.simpleInterest) {
        final updatedValue = investment.calculateCurrentValue();

        if (updatedValue != investment.currentValue) {
          final updatedInvestment = investment.copyWith(
            currentValue: updatedValue,
            updatedAt: DateTime.now(),
          );

          await updateInvestment(updatedInvestment);
        }
      }
    }
  }

  /// Process partial withdrawal with detailed calculations
  static Future<InvestmentWithdrawalCalculation> calculateWithdrawal({
    required InvestmentModel investment,
    required double withdrawalAmount,
    required WithdrawalType type,
  }) async {
    final currentValue = investment.calculateCurrentValue();
    final totalProfits = currentValue - investment.totalInvested;

    double actualWithdrawalAmount;
    double penaltyAmount = 0.0;

    // Calculate actual withdrawal amount based on type
    switch (type) {
      case WithdrawalType.specificAmount:
        actualWithdrawalAmount = withdrawalAmount;
        break;
      case WithdrawalType.percentage:
        actualWithdrawalAmount = (currentValue * withdrawalAmount) / 100;
        break;
      case WithdrawalType.profitsOnly:
        actualWithdrawalAmount = totalProfits > 0 ? totalProfits : 0.0;
        break;
      case WithdrawalType.principalOnly:
        actualWithdrawalAmount = math.min(
          withdrawalAmount,
          investment.totalInvested,
        );
        break;
    }

    // Calculate penalty for early withdrawal
    if (investment.maturityDate != null &&
        DateTime.now().isBefore(investment.maturityDate!)) {
      penaltyAmount = _calculateEarlyWithdrawalPenalty(
        investment,
        actualWithdrawalAmount,
      );
    }

    final netAmount = actualWithdrawalAmount - penaltyAmount;
    final remainingValue = currentValue - actualWithdrawalAmount;

    return InvestmentWithdrawalCalculation(
      withdrawalAmount: actualWithdrawalAmount,
      penaltyAmount: penaltyAmount,
      netAmount: netAmount,
      remainingValue: remainingValue,
      isFullWithdrawal: remainingValue <= 0,
    );
  }

  /// Calculate early withdrawal penalty
  static double _calculateEarlyWithdrawalPenalty(
    InvestmentModel investment,
    double withdrawalAmount,
  ) {
    final daysToMaturity =
        investment.maturityDate!.difference(DateTime.now()).inDays;

    // Progressive penalty based on time remaining
    double penaltyRate = 0.0;

    if (daysToMaturity > 365) {
      penaltyRate = 0.02; // 2% penalty for withdrawal > 1 year early
    } else if (daysToMaturity > 180) {
      penaltyRate = 0.015; // 1.5% penalty for 6-12 months early
    } else if (daysToMaturity > 90) {
      penaltyRate = 0.01; // 1% penalty for 3-6 months early
    } else if (daysToMaturity > 30) {
      penaltyRate = 0.005; // 0.5% penalty for 1-3 months early
    }

    return withdrawalAmount * penaltyRate;
  }

  /// Process partial withdrawal and update investment
  static Future<void> processPartialWithdrawal({
    required InvestmentModel investment,
    required InvestmentWithdrawalModel withdrawal,
  }) async {
    // Add withdrawal record
    await addInvestmentWithdrawal(withdrawal);

    // Calculate new investment values
    final newCurrentValue = investment.currentValue - withdrawal.amount;
    final newStatus =
        newCurrentValue <= 0 ? InvestmentStatus.withdrawn : investment.status;

    // Update investment
    final updatedInvestment = investment.copyWith(
      currentValue: newCurrentValue,
      status: newStatus,
      updatedAt: DateTime.now(),
    );

    await updateInvestment(updatedInvestment);

    // If partially withdrawn, adjust future return calculations
    if (newCurrentValue > 0) {
      await _adjustReturnCalculations(updatedInvestment, withdrawal.amount);
    }
  }

  /// Adjust return calculations after partial withdrawal
  static Future<void> _adjustReturnCalculations(
    InvestmentModel investment,
    double withdrawnAmount,
  ) async {
    // For interest-based investments, handle withdrawal allocation properly
    if (investment.returnType == InvestmentReturnType.compoundInterest ||
        investment.returnType == InvestmentReturnType.simpleInterest) {
      final currentValue = investment.currentValue;
      final totalInvested = investment.totalInvested;
      final totalProfits = currentValue - totalInvested;

      // Determine how much comes from profits vs principal
      double principalWithdrawn = 0.0;

      if (totalProfits > 0 && withdrawnAmount <= totalProfits) {
        // Withdrawal comes entirely from profits - no principal reduction
        principalWithdrawn = 0.0;
      } else if (totalProfits > 0 && withdrawnAmount > totalProfits) {
        // Withdrawal comes from profits first, then principal
        principalWithdrawn = withdrawnAmount - totalProfits;
      } else {
        // No profits or losses - withdrawal comes from principal
        principalWithdrawn = withdrawnAmount;
      }

      // Only reduce totalInvested by the actual principal portion withdrawn
      final newTotalInvested = math.max(
        0.0,
        totalInvested - principalWithdrawn,
      );

      final updatedInvestment = investment.copyWith(
        totalInvested: newTotalInvested,
        updatedAt: DateTime.now(),
      );

      await updateInvestment(updatedInvestment);
    }
  }

  /// Get withdrawal history with analytics
  static Future<InvestmentWithdrawalAnalytics> getWithdrawalAnalytics(
    String investmentId,
  ) async {
    final withdrawals = await getInvestmentWithdrawalsByInvestmentId(
      investmentId,
    );

    final totalWithdrawn = withdrawals.fold<double>(
      0.0,
      (sum, withdrawal) => sum + withdrawal.amount,
    );

    final totalPenalties = withdrawals.fold<double>(
      0.0,
      (sum, withdrawal) => sum + withdrawal.penaltyAmount,
    );

    return InvestmentWithdrawalAnalytics(
      totalWithdrawals: withdrawals.length,
      totalWithdrawnAmount: totalWithdrawn,
      totalPenalties: totalPenalties,
      withdrawals: withdrawals,
    );
  }
}
