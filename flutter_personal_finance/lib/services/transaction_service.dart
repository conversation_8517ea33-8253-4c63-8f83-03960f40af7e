import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../features/models/transaction_model.dart';
import 'bank_account_service.dart';
import 'local_storage_service.dart';
import 'notification_service.dart';
import 'investment_service.dart';

/// Service class for managing transaction data
class TransactionService {
  static const String _transactionsKey = 'transactions';

  /// Save transactions to SharedPreferences
  static Future<void> saveTransactions(
    List<TransactionModel> transactions,
  ) async {
    final prefs = await SharedPreferences.getInstance();
    final transactionsJson =
        transactions.map((transaction) => transaction.toJson()).toList();
    await prefs.setString(_transactionsKey, jsonEncode(transactionsJson));
  }

  /// Get all transactions from SharedPreferences
  static Future<List<TransactionModel>> getTransactions() async {
    final prefs = await SharedPreferences.getInstance();
    final transactionsJson = prefs.getString(_transactionsKey);

    if (transactionsJson == null) return [];

    final transactionsList = jsonDecode(transactionsJson) as List<dynamic>;
    return transactionsList
        .map(
          (transaction) =>
              TransactionModel.fromJson(transaction as Map<String, dynamic>),
        )
        .toList();
  }

  /// Add a new transaction
  static Future<void> addTransaction(TransactionModel transaction) async {
    final transactions = await getTransactions();
    transactions.add(transaction);
    await saveTransactions(transactions);

    // Update bank account balance
    await _updateBankAccountBalance(transaction);

    // Auto-process for investment if applicable
    try {
      await InvestmentService.processTransactionForInvestment(transaction);
    } catch (e) {
      print('Error processing investment transaction: $e');
    }

    // Update budget spent amounts
    await LocalStorageService.updateBudgetSpentAmounts();

    // Check and trigger budget notifications for this specific category
    await NotificationService.checkBudgetNotificationsForCategory(
      transaction.category,
      transaction.type == TransactionType.credit ? 'income' : 'expense',
    );
  }

  /// Update a transaction
  static Future<void> updateTransaction(
    TransactionModel originalTransaction,
    TransactionModel updatedTransaction,
  ) async {
    final transactions = await getTransactions();
    final index = transactions.indexWhere(
      (transaction) => transaction.id == updatedTransaction.id,
    );

    if (index != -1) {
      transactions[index] = updatedTransaction.copyWith(
        updatedAt: DateTime.now(),
      );
      await saveTransactions(transactions);

      // Revert original transaction effect and apply new transaction effect
      await _revertTransactionEffect(originalTransaction);
      await _updateBankAccountBalance(updatedTransaction);

      // Update budget spent amounts
      await LocalStorageService.updateBudgetSpentAmounts();

      // Check and trigger budget notifications for this specific category
      await NotificationService.checkBudgetNotificationsForCategory(
        updatedTransaction.category,
        updatedTransaction.type == TransactionType.credit
            ? 'income'
            : 'expense',
      );
    }
  }

  /// Delete a transaction
  static Future<void> deleteTransaction(String transactionId) async {
    final transactions = await getTransactions();
    final transactionToDelete = transactions.firstWhere(
      (transaction) => transaction.id == transactionId,
    );

    transactions.removeWhere((transaction) => transaction.id == transactionId);
    await saveTransactions(transactions);

    // Revert transaction effect on bank account
    await _revertTransactionEffect(transactionToDelete);

    // Update budget spent amounts
    await LocalStorageService.updateBudgetSpentAmounts();

    // Check and trigger budget notifications for this specific category
    await NotificationService.checkBudgetNotificationsForCategory(
      transactionToDelete.category,
      transactionToDelete.type == TransactionType.credit ? 'income' : 'expense',
    );
  }

  /// Get transactions for a specific bank account
  static Future<List<TransactionModel>> getTransactionsByBankAccount(
    String bankAccountId,
  ) async {
    final transactions = await getTransactions();
    return transactions
        .where((transaction) => transaction.bankAccountId == bankAccountId)
        .toList();
  }

  /// Get transactions by type (income or expense)
  static Future<List<TransactionModel>> getTransactionsByType(
    TransactionType type,
  ) async {
    final transactions = await getTransactions();
    return transactions
        .where((transaction) => transaction.type == type)
        .toList();
  }

  /// Get transactions for a specific date range
  static Future<List<TransactionModel>> getTransactionsByDateRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    // Ensure end date includes the full day (23:59:59)
    final adjustedEndDate = DateTime(
      endDate.year,
      endDate.month,
      endDate.day,
      23,
      59,
      59,
    );

    final transactions = await getTransactions();
    return transactions
        .where(
          (transaction) =>
              (transaction.date.isAtSameMomentAs(startDate) ||
                  transaction.date.isAfter(startDate)) &&
              (transaction.date.isAtSameMomentAs(adjustedEndDate) ||
                  transaction.date.isBefore(adjustedEndDate)),
        )
        .toList();
  }

  /// Get total income
  static Future<double> getTotalIncome() async {
    final incomeTransactions = await getTransactionsByType(
      TransactionType.credit,
    );
    return incomeTransactions.fold<double>(
      0.0,
      (total, transaction) => total + transaction.amount,
    );
  }

  /// Get total expenses
  static Future<double> getTotalExpenses() async {
    final expenseTransactions = await getTransactionsByType(
      TransactionType.debit,
    );
    return expenseTransactions.fold<double>(
      0.0,
      (total, transaction) => total + transaction.amount,
    );
  }

  /// Get transactions sorted by date (newest first)
  static Future<List<TransactionModel>> getTransactionsSortedByDate() async {
    final transactions = await getTransactions();
    transactions.sort((a, b) => b.date.compareTo(a.date));
    return transactions;
  }

  /// Private method to update bank account balance when transaction is added
  static Future<void> _updateBankAccountBalance(
    TransactionModel transaction,
  ) async {
    final bankAccount = await BankAccountService.getBankAccountById(
      transaction.bankAccountId,
    );
    if (bankAccount != null) {
      final newBalance =
          transaction.type == TransactionType.credit
              ? bankAccount.currentAmount + transaction.amount
              : bankAccount.currentAmount - transaction.amount;

      await BankAccountService.updateBankAccountBalance(
        transaction.bankAccountId,
        newBalance,
      );
    }
  }

  /// Private method to revert transaction effect on bank account balance
  static Future<void> _revertTransactionEffect(
    TransactionModel transaction,
  ) async {
    final bankAccount = await BankAccountService.getBankAccountById(
      transaction.bankAccountId,
    );
    if (bankAccount != null) {
      final newBalance =
          transaction.type == TransactionType.credit
              ? bankAccount.currentAmount - transaction.amount
              : bankAccount.currentAmount + transaction.amount;

      await BankAccountService.updateBankAccountBalance(
        transaction.bankAccountId,
        newBalance,
      );
    }
  }
}
