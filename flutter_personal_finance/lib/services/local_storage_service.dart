import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../features/models/bank_account_model.dart';
import '../features/models/transaction_model.dart';
import '../features/models/user_model.dart';
import '../features/models/upcoming_expense_model.dart';
import '../features/models/budget_model.dart';
import '../features/models/recurring_transaction_model.dart';
import '../features/models/credit_card_model.dart';
import '../features/models/credit_card_transaction_model.dart';
import 'investment_service.dart';

class LocalStorageService {
  static const String _bankAccountsKey = 'bank_accounts';
  static const String _transactionsKey = 'transactions';
  static const String _userKey = 'current_user';
  static const String _userIdKey = 'user_id';
  static const String _upcomingExpensesKey = 'upcoming_expenses';
  static const String _upcomingExpenseCategoriesKey =
      'upcoming_expense_categories';
  static const String _incomeExpenseCategoriesKey = 'income_expense_categories';
  static const String _budgetsKey = 'budgets';
  static const String _recurringTransactionsKey = 'recurring_transactions';
  static const String _creditCardsKey = 'credit_cards';
  static const String _creditCardTransactionsKey = 'credit_card_transactions';

  // Bank Account operations
  static Future<List<BankAccountModel>> getBankAccounts() async {
    final prefs = await SharedPreferences.getInstance();
    final bankAccountsJson = prefs.getString(_bankAccountsKey);

    if (bankAccountsJson == null) return [];

    final List<dynamic> bankAccountsList = json.decode(bankAccountsJson);
    return bankAccountsList
        .map((json) => BankAccountModel.fromJson(json))
        .toList();
  }

  static Future<void> saveBankAccounts(
    List<BankAccountModel> bankAccounts,
  ) async {
    final prefs = await SharedPreferences.getInstance();
    final bankAccountsJson = json.encode(
      bankAccounts.map((account) => account.toJson()).toList(),
    );
    await prefs.setString(_bankAccountsKey, bankAccountsJson);
  }

  static Future<void> addBankAccount(BankAccountModel bankAccount) async {
    final bankAccounts = await getBankAccounts();
    bankAccounts.add(bankAccount);
    await saveBankAccounts(bankAccounts);
  }

  static Future<void> updateBankAccount(BankAccountModel updatedAccount) async {
    final bankAccounts = await getBankAccounts();
    final index = bankAccounts.indexWhere(
      (account) => account.id == updatedAccount.id,
    );

    if (index != -1) {
      bankAccounts[index] = updatedAccount;
      await saveBankAccounts(bankAccounts);
    }
  }

  static Future<void> deleteBankAccount(String accountId) async {
    final bankAccounts = await getBankAccounts();
    bankAccounts.removeWhere((account) => account.id == accountId);
    await saveBankAccounts(bankAccounts);

    // Also delete all transactions for this account
    final transactions = await getTransactions();
    transactions.removeWhere(
      (transaction) => transaction.bankAccountId == accountId,
    );
    await saveTransactions(transactions);
  }

  // Transaction operations
  static Future<List<TransactionModel>> getTransactions() async {
    final prefs = await SharedPreferences.getInstance();
    final transactionsJson = prefs.getString(_transactionsKey);

    if (transactionsJson == null) return [];

    final List<dynamic> transactionsList = json.decode(transactionsJson);
    return transactionsList
        .map((json) => TransactionModel.fromJson(json))
        .toList();
  }

  static Future<void> saveTransactions(
    List<TransactionModel> transactions,
  ) async {
    final prefs = await SharedPreferences.getInstance();
    final transactionsJson = json.encode(
      transactions.map((transaction) => transaction.toJson()).toList(),
    );
    await prefs.setString(_transactionsKey, transactionsJson);
  }

  static Future<void> addTransaction(TransactionModel transaction) async {
    final transactions = await getTransactions();
    transactions.add(transaction);
    // Sort by created date (newest first)
    transactions.sort((a, b) => b.createdAt.compareTo(a.createdAt));
    await saveTransactions(transactions);

    // Auto-process for investment if applicable
    try {
      await InvestmentService.processTransactionForInvestment(transaction);
    } catch (e) {
      print('Error processing investment transaction: $e');
    }
  }

  static Future<void> deleteTransaction(String transactionId) async {
    final transactions = await getTransactions();
    transactions.removeWhere((transaction) => transaction.id == transactionId);
    await saveTransactions(transactions);
  }

  static Future<void> deleteTransactionsInRange(
    String bankAccountId,
    DateTime startDate,
    DateTime endDate,
  ) async {
    // Ensure end date includes the full day (23:59:59)
    final adjustedEndDate = DateTime(
      endDate.year,
      endDate.month,
      endDate.day,
      23,
      59,
      59,
    );

    final transactions = await getTransactions();
    transactions.removeWhere(
      (transaction) =>
          transaction.bankAccountId == bankAccountId &&
          (transaction.createdAt.isAtSameMomentAs(startDate) ||
              transaction.createdAt.isAfter(startDate)) &&
          (transaction.createdAt.isAtSameMomentAs(adjustedEndDate) ||
              transaction.createdAt.isBefore(adjustedEndDate)),
    );
    await saveTransactions(transactions);
  }

  // User operations
  static Future<UserModel?> getCurrentUser() async {
    final prefs = await SharedPreferences.getInstance();
    final userJson = prefs.getString(_userKey);

    if (userJson == null) return null;

    return UserModel.fromJson(json.decode(userJson));
  }

  static Future<void> saveCurrentUser(UserModel user) async {
    final prefs = await SharedPreferences.getInstance();
    final userJson = json.encode(user.toJson());
    await prefs.setString(_userKey, userJson);
    await prefs.setString(_userIdKey, user.id);
  }

  static Future<String?> getCurrentUserId() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_userIdKey);
  }

  static Future<void> clearAllData() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_bankAccountsKey);
    await prefs.remove(_transactionsKey);
    await prefs.remove(_userKey);
    await prefs.remove(_userIdKey);
    await prefs.remove(_upcomingExpensesKey);
    await prefs.remove(_upcomingExpenseCategoriesKey);
  }

  // Export/Import operations
  static Future<Map<String, dynamic>> exportAllData() async {
    final bankAccounts = await getBankAccounts();
    final transactions = await getTransactions();
    final user = await getCurrentUser();
    final upcomingExpenses = await getUpcomingExpenses();
    final upcomingExpenseCategories = await getUpcomingExpenseCategories();

    // Add all the missing data types
    final creditCards = await getCreditCards();
    final creditCardTransactions = await getCreditCardTransactions();
    final budgets = await getBudgets();
    final recurringTransactions = await getRecurringTransactions();
    final incomeExpenseCategories = await getIncomeExpenseCategories();

    return {
      'export_date': DateTime.now().toIso8601String(),
      'app_version': '1.0.0',
      'user': user?.toJson(),
      'bank_accounts': bankAccounts.map((account) => account.toJson()).toList(),
      'transactions':
          transactions.map((transaction) => transaction.toJson()).toList(),
      'upcoming_expenses':
          upcomingExpenses.map((expense) => expense.toJson()).toList(),
      'upcoming_expense_categories':
          upcomingExpenseCategories
              .map((category) => category.toJson())
              .toList(),

      // Add the missing data exports
      'credit_cards': creditCards.map((card) => card.toJson()).toList(),
      'credit_card_transactions':
          creditCardTransactions
              .map((transaction) => transaction.toJson())
              .toList(),
      'budgets': budgets.map((budget) => budget.toJson()).toList(),
      'recurring_transactions':
          recurringTransactions
              .map((transaction) => transaction.toJson())
              .toList(),
      'income_expense_categories': incomeExpenseCategories,
    };
  }

  static Future<void> importAllData(Map<String, dynamic> data) async {
    try {
      // Clear existing data
      await clearAllData();

      // Import user data
      if (data['user'] != null) {
        final user = UserModel.fromJson(data['user']);
        await saveCurrentUser(user);
      }

      // Import bank accounts
      if (data['bank_accounts'] != null) {
        final bankAccountsList = data['bank_accounts'] as List;
        final bankAccounts =
            bankAccountsList
                .map((json) => BankAccountModel.fromJson(json))
                .toList();
        await saveBankAccounts(bankAccounts);
      }

      // Import transactions
      if (data['transactions'] != null) {
        final transactionsList = data['transactions'] as List;
        final transactions =
            transactionsList
                .map((json) => TransactionModel.fromJson(json))
                .toList();
        await saveTransactions(transactions);
      }

      // Import upcoming expenses
      if (data['upcoming_expenses'] != null) {
        final upcomingExpensesList = data['upcoming_expenses'] as List;
        final upcomingExpenses =
            upcomingExpensesList
                .map((json) => UpcomingExpenseModel.fromJson(json))
                .toList();
        await saveUpcomingExpenses(upcomingExpenses);
      }

      // Import upcoming expense categories
      if (data['upcoming_expense_categories'] != null) {
        final upcomingExpenseCategoriesList =
            data['upcoming_expense_categories'] as List;
        final upcomingExpenseCategories =
            upcomingExpenseCategoriesList
                .map((json) => UpcomingExpenseCategoryModel.fromJson(json))
                .toList();
        await saveUpcomingExpenseCategories(upcomingExpenseCategories);
      }

      // Import credit cards
      if (data['credit_cards'] != null) {
        final creditCardsList = data['credit_cards'] as List;
        final creditCards =
            creditCardsList
                .map((json) => CreditCardModel.fromJson(json))
                .toList();
        await saveCreditCards(creditCards);
      }

      // Import credit card transactions
      if (data['credit_card_transactions'] != null) {
        final creditCardTransactionsList =
            data['credit_card_transactions'] as List;
        final creditCardTransactions =
            creditCardTransactionsList
                .map((json) => CreditCardTransactionModel.fromJson(json))
                .toList();
        await saveCreditCardTransactions(creditCardTransactions);
      }

      // Import budgets
      if (data['budgets'] != null) {
        final budgetsList = data['budgets'] as List;
        final budgets =
            budgetsList.map((json) => BudgetModel.fromJson(json)).toList();
        await saveBudgets(budgets);
      }

      // Import recurring transactions
      if (data['recurring_transactions'] != null) {
        final recurringTransactionsList =
            data['recurring_transactions'] as List;
        final recurringTransactions =
            recurringTransactionsList
                .map((json) => RecurringTransactionModel.fromJson(json))
                .toList();
        await saveRecurringTransactions(recurringTransactions);
      }

      // Import income/expense categories
      if (data['income_expense_categories'] != null) {
        final categories = data['income_expense_categories'] as List;
        await saveIncomeExpenseCategories(
          categories.cast<Map<String, dynamic>>(),
        );
      }
    } catch (e) {
      throw Exception('Failed to import data: $e');
    }
  }

  // Calculate bank account balances based on transactions
  static Future<Map<String, double>> calculateBankAccountBalances() async {
    final transactions = await getTransactions();
    final Map<String, double> balances = {};

    for (final transaction in transactions) {
      balances[transaction.bankAccountId] ??= 0.0;

      if (transaction.type == TransactionType.credit) {
        balances[transaction.bankAccountId] =
            balances[transaction.bankAccountId]! + transaction.amount;
      } else if (transaction.type == TransactionType.debit) {
        balances[transaction.bankAccountId] =
            balances[transaction.bankAccountId]! - transaction.amount;
      }
    }

    return balances;
  }

  // Update bank account balances
  static Future<void> updateBankAccountBalances() async {
    final bankAccounts = await getBankAccounts();
    final balances = await calculateBankAccountBalances();

    final updatedAccounts =
        bankAccounts.map((account) {
          final transactionBalance = balances[account.id] ?? 0.0;
          final finalBalance = account.initialAmount + transactionBalance;

          return BankAccountModel(
            id: account.id,
            bankName: account.bankName,
            accountNumber: account.accountNumber,
            initialAmount: account.initialAmount,
            currentAmount: finalBalance,
            createdAt: account.createdAt,
            updatedAt: DateTime.now(),
          );
        }).toList();

    await saveBankAccounts(updatedAccounts);
  }

  // Upcoming Expense Categories operations
  static Future<List<UpcomingExpenseCategoryModel>>
  getUpcomingExpenseCategories() async {
    final prefs = await SharedPreferences.getInstance();
    final categoriesJson = prefs.getString(_upcomingExpenseCategoriesKey);

    if (categoriesJson == null) {
      // Return default categories if none exist
      return _getDefaultUpcomingExpenseCategories();
    }

    final List<dynamic> categoriesList = json.decode(categoriesJson);
    return categoriesList
        .map((json) => UpcomingExpenseCategoryModel.fromJson(json))
        .toList();
  }

  static List<UpcomingExpenseCategoryModel>
  _getDefaultUpcomingExpenseCategories() {
    return [
      UpcomingExpenseCategoryModel(name: 'Health Insurance (Mine)'),
      UpcomingExpenseCategoryModel(name: 'Health Insurance (Mom)'),
      UpcomingExpenseCategoryModel(name: 'Car Insurance'),
      UpcomingExpenseCategoryModel(name: 'Bike Insurance'),
      UpcomingExpenseCategoryModel(name: 'Life Insurance'),
      UpcomingExpenseCategoryModel(name: 'Property Tax'),
      UpcomingExpenseCategoryModel(name: 'License Renewal'),
    ];
  }

  static Future<void> saveUpcomingExpenseCategories(
    List<UpcomingExpenseCategoryModel> categories,
  ) async {
    final prefs = await SharedPreferences.getInstance();
    final categoriesJson = json.encode(
      categories.map((category) => category.toJson()).toList(),
    );
    await prefs.setString(_upcomingExpenseCategoriesKey, categoriesJson);
  }

  static Future<void> addUpcomingExpenseCategory(
    UpcomingExpenseCategoryModel category,
  ) async {
    final categories = await getUpcomingExpenseCategories();
    categories.add(category);
    await saveUpcomingExpenseCategories(categories);
  }

  static Future<void> updateUpcomingExpenseCategory(
    UpcomingExpenseCategoryModel updatedCategory,
  ) async {
    final categories = await getUpcomingExpenseCategories();
    final index = categories.indexWhere(
      (category) => category.id == updatedCategory.id,
    );

    if (index != -1) {
      categories[index] = updatedCategory;
      await saveUpcomingExpenseCategories(categories);
    }
  }

  static Future<void> deleteUpcomingExpenseCategory(String categoryId) async {
    final categories = await getUpcomingExpenseCategories();
    categories.removeWhere((category) => category.id == categoryId);
    await saveUpcomingExpenseCategories(categories);

    // Also delete all upcoming expenses with this category
    final expenses = await getUpcomingExpenses();
    final categoryName =
        categories
            .firstWhere(
              (cat) => cat.id == categoryId,
              orElse: () => UpcomingExpenseCategoryModel(name: ''),
            )
            .name;
    expenses.removeWhere((expense) => expense.category == categoryName);
    await saveUpcomingExpenses(expenses);
  }

  // Upcoming Expenses operations
  static Future<List<UpcomingExpenseModel>> getUpcomingExpenses() async {
    final prefs = await SharedPreferences.getInstance();
    final expensesJson = prefs.getString(_upcomingExpensesKey);

    if (expensesJson == null) return [];

    final List<dynamic> expensesList = json.decode(expensesJson);
    return expensesList
        .map((json) => UpcomingExpenseModel.fromJson(json))
        .toList();
  }

  static Future<void> saveUpcomingExpenses(
    List<UpcomingExpenseModel> expenses,
  ) async {
    final prefs = await SharedPreferences.getInstance();
    final expensesJson = json.encode(
      expenses.map((expense) => expense.toJson()).toList(),
    );
    await prefs.setString(_upcomingExpensesKey, expensesJson);
  }

  static Future<void> addUpcomingExpense(UpcomingExpenseModel expense) async {
    final expenses = await getUpcomingExpenses();
    expenses.add(expense);
    // Sort by due date (nearest first)
    expenses.sort((a, b) => a.dueDate.compareTo(b.dueDate));
    await saveUpcomingExpenses(expenses);
  }

  static Future<void> updateUpcomingExpense(
    UpcomingExpenseModel updatedExpense,
  ) async {
    final expenses = await getUpcomingExpenses();
    final index = expenses.indexWhere(
      (expense) => expense.id == updatedExpense.id,
    );

    if (index != -1) {
      expenses[index] = updatedExpense.copyWith(updatedAt: DateTime.now());
      expenses.sort((a, b) => a.dueDate.compareTo(b.dueDate));
      await saveUpcomingExpenses(expenses);
    }
  }

  static Future<void> deleteUpcomingExpense(String expenseId) async {
    final expenses = await getUpcomingExpenses();
    expenses.removeWhere((expense) => expense.id == expenseId);
    await saveUpcomingExpenses(expenses);
  }

  static Future<void> markUpcomingExpenseAsPaid(String expenseId) async {
    final expenses = await getUpcomingExpenses();
    final index = expenses.indexWhere((expense) => expense.id == expenseId);

    if (index != -1) {
      expenses[index] = expenses[index].copyWith(
        isPaid: true,
        updatedAt: DateTime.now(),
      );
      await saveUpcomingExpenses(expenses);
    }
  }

  // Get upcoming expenses due within next 30 days
  static Future<List<UpcomingExpenseModel>>
  getUpcomingExpensesDueIn30Days() async {
    final expenses = await getUpcomingExpenses();
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final next30DaysEnd = DateTime(
      now.year,
      now.month,
      now.day + 30,
      23,
      59,
      59,
    );

    return expenses
        .where(
          (expense) =>
              !expense.isPaid &&
              (expense.dueDate.isAtSameMomentAs(today) ||
                  expense.dueDate.isAfter(today)) &&
              (expense.dueDate.isAtSameMomentAs(next30DaysEnd) ||
                  expense.dueDate.isBefore(next30DaysEnd)),
        )
        .toList();
  }

  // Calculate total upcoming expenses amount
  static Future<double> getTotalUpcomingExpensesAmount() async {
    final expenses = await getUpcomingExpensesDueIn30Days();
    return expenses.fold<double>(0.0, (sum, expense) => sum + expense.amount);
  }

  // Income/Expense Categories operations
  static Future<List<Map<String, dynamic>>> getIncomeExpenseCategories() async {
    final prefs = await SharedPreferences.getInstance();
    final categoriesJson = prefs.getString(_incomeExpenseCategoriesKey);

    if (categoriesJson == null) return [];

    final List<dynamic> categoriesList = json.decode(categoriesJson);
    return categoriesList.cast<Map<String, dynamic>>();
  }

  static Future<void> saveIncomeExpenseCategories(
    List<Map<String, dynamic>> categories,
  ) async {
    final prefs = await SharedPreferences.getInstance();
    final categoriesJson = json.encode(categories);
    await prefs.setString(_incomeExpenseCategoriesKey, categoriesJson);
  }

  static Future<void> addIncomeExpenseCategory(
    Map<String, dynamic> category,
  ) async {
    final categories = await getIncomeExpenseCategories();
    categories.add(category);
    await saveIncomeExpenseCategories(categories);
  }

  static Future<void> updateIncomeExpenseCategory(
    Map<String, dynamic> updatedCategory,
  ) async {
    final categories = await getIncomeExpenseCategories();
    final index = categories.indexWhere(
      (category) => category['id'] == updatedCategory['id'],
    );

    if (index != -1) {
      categories[index] = updatedCategory;
      await saveIncomeExpenseCategories(categories);
    }
  }

  static Future<void> deleteIncomeExpenseCategory(String categoryId) async {
    final categories = await getIncomeExpenseCategories();
    categories.removeWhere((category) => category['id'] == categoryId);
    await saveIncomeExpenseCategories(categories);
  }

  // Budget operations
  static Future<List<BudgetModel>> getBudgets() async {
    final prefs = await SharedPreferences.getInstance();
    final budgetsJson = prefs.getString(_budgetsKey);

    if (budgetsJson == null) return [];

    final List<dynamic> budgetsList = json.decode(budgetsJson);
    return budgetsList.map((json) => BudgetModel.fromJson(json)).toList();
  }

  static Future<void> saveBudgets(List<BudgetModel> budgets) async {
    final prefs = await SharedPreferences.getInstance();
    final budgetsJson = json.encode(
      budgets.map((budget) => budget.toJson()).toList(),
    );
    await prefs.setString(_budgetsKey, budgetsJson);
  }

  static Future<void> addBudget(BudgetModel budget) async {
    final budgets = await getBudgets();
    budgets.add(budget);
    await saveBudgets(budgets);
  }

  static Future<void> updateBudget(BudgetModel updatedBudget) async {
    final budgets = await getBudgets();
    final index = budgets.indexWhere((budget) => budget.id == updatedBudget.id);

    if (index != -1) {
      budgets[index] = updatedBudget;
      await saveBudgets(budgets);
    }
  }

  static Future<void> deleteBudget(String budgetId) async {
    final budgets = await getBudgets();
    budgets.removeWhere((budget) => budget.id == budgetId);
    await saveBudgets(budgets);
  }

  // Get budgets for current month
  static Future<List<BudgetModel>> getCurrentMonthBudgets() async {
    final budgets = await getBudgets();
    final now = DateTime.now();
    final currentYear = now.year;
    final currentMonth = now.month;

    return budgets
        .where(
          (budget) =>
              budget.year == currentYear && budget.month == currentMonth,
        )
        .toList();
  }

  // Get budgets for specific month
  static Future<List<BudgetModel>> getBudgetsForMonth(
    int year,
    int month,
  ) async {
    final budgets = await getBudgets();
    return budgets
        .where((budget) => budget.year == year && budget.month == month)
        .toList();
  }

  // Update budget spent amount based on transactions
  static Future<void> updateBudgetSpentAmounts() async {
    final budgets = await getCurrentMonthBudgets();
    final transactions = await getTransactions();
    final now = DateTime.now();
    final currentYear = now.year;
    final currentMonth = now.month;

    // Filter transactions for current month
    final currentMonthTransactions =
        transactions.where((transaction) {
          final transactionDate = transaction.date;
          return transactionDate.year == currentYear &&
              transactionDate.month == currentMonth;
        }).toList();

    // Calculate spent amounts for each budget category
    final Map<String, double> categorySpent = {};
    for (final transaction in currentMonthTransactions) {
      if (transaction.type == TransactionType.debit) {
        categorySpent[transaction.category] =
            (categorySpent[transaction.category] ?? 0.0) + transaction.amount;
      }
    }

    // Update budget spent amounts
    for (final budget in budgets) {
      if (categorySpent.containsKey(budget.categoryName)) {
        final updatedBudget = budget.copyWith(
          currentSpent: categorySpent[budget.categoryName]!,
          updatedAt: DateTime.now(),
        );
        await updateBudget(updatedBudget);
      }
    }
  }

  // Reset budget spent amounts for new month
  static Future<void> resetBudgetSpentAmountsForNewMonth() async {
    final budgets = await getBudgets();
    final now = DateTime.now();
    final currentYear = now.year;
    final currentMonth = now.month;

    for (final budget in budgets) {
      // If this budget is for the current month and has spent amount, reset it
      if (budget.year == currentYear &&
          budget.month == currentMonth &&
          budget.currentSpent > 0) {
        final updatedBudget = budget.copyWith(
          currentSpent: 0.0,
          updatedAt: DateTime.now(),
        );
        await updateBudget(updatedBudget);
      }
    }
  }

  // ==================== RECURRING TRANSACTIONS ====================

  // Get all recurring transactions
  static Future<List<RecurringTransactionModel>>
  getRecurringTransactions() async {
    final prefs = await SharedPreferences.getInstance();
    final recurringTransactionsJson = prefs.getString(
      _recurringTransactionsKey,
    );

    if (recurringTransactionsJson == null) return [];

    final List<dynamic> recurringTransactionsList = json.decode(
      recurringTransactionsJson,
    );
    return recurringTransactionsList
        .map((json) => RecurringTransactionModel.fromJson(json))
        .toList();
  }

  // Save recurring transactions
  static Future<void> saveRecurringTransactions(
    List<RecurringTransactionModel> recurringTransactions,
  ) async {
    final prefs = await SharedPreferences.getInstance();
    final recurringTransactionsJson = json.encode(
      recurringTransactions.map((transaction) => transaction.toJson()).toList(),
    );
    await prefs.setString(_recurringTransactionsKey, recurringTransactionsJson);
  }

  // Add a new recurring transaction
  static Future<void> addRecurringTransaction(
    RecurringTransactionModel recurringTransaction,
  ) async {
    final recurringTransactions = await getRecurringTransactions();
    recurringTransactions.add(recurringTransaction);
    // Sort by next due date (nearest first)
    recurringTransactions.sort(
      (a, b) => a.nextDueDate.compareTo(b.nextDueDate),
    );
    await saveRecurringTransactions(recurringTransactions);
  }

  // Update a recurring transaction
  static Future<void> updateRecurringTransaction(
    RecurringTransactionModel updatedTransaction,
  ) async {
    final recurringTransactions = await getRecurringTransactions();
    final index = recurringTransactions.indexWhere(
      (transaction) => transaction.id == updatedTransaction.id,
    );

    if (index != -1) {
      recurringTransactions[index] = updatedTransaction.copyWith(
        updatedAt: DateTime.now(),
      );
      // Sort by next due date (nearest first)
      recurringTransactions.sort(
        (a, b) => a.nextDueDate.compareTo(b.nextDueDate),
      );
      await saveRecurringTransactions(recurringTransactions);
    }
  }

  // Delete a recurring transaction
  static Future<void> deleteRecurringTransaction(String transactionId) async {
    final recurringTransactions = await getRecurringTransactions();
    recurringTransactions.removeWhere(
      (transaction) => transaction.id == transactionId,
    );
    await saveRecurringTransactions(recurringTransactions);
  }

  // Get due recurring transactions (next due date is today or in the past)
  static Future<List<RecurringTransactionModel>>
  getDueRecurringTransactions() async {
    final recurringTransactions = await getRecurringTransactions();

    return recurringTransactions
        .where(
          (transaction) =>
              transaction.isActive &&
              transaction.isDue &&
              !transaction.hasEnded,
        )
        .toList();
  }

  // Get recurring transactions for a specific bank account
  static Future<List<RecurringTransactionModel>>
  getRecurringTransactionsByBankAccount(String bankAccountId) async {
    final recurringTransactions = await getRecurringTransactions();
    return recurringTransactions
        .where((transaction) => transaction.bankAccountId == bankAccountId)
        .toList();
  }

  // Get active recurring transactions
  static Future<List<RecurringTransactionModel>>
  getActiveRecurringTransactions() async {
    final recurringTransactions = await getRecurringTransactions();
    return recurringTransactions
        .where((transaction) => transaction.isActive && !transaction.hasEnded)
        .toList();
  }

  // Get recurring transactions due within next 30 days
  static Future<List<RecurringTransactionModel>>
  getRecurringTransactionsDueIn30Days() async {
    final recurringTransactions = await getRecurringTransactions();
    final now = DateTime.now();
    final next30Days = now.add(const Duration(days: 30));

    return recurringTransactions
        .where(
          (transaction) =>
              transaction.isActive &&
              !transaction.hasEnded &&
              transaction.nextDueDate.isAfter(
                now.subtract(const Duration(days: 1)),
              ) &&
              transaction.nextDueDate.isBefore(
                next30Days.add(const Duration(days: 1)),
              ),
        )
        .toList();
  }

  // Helper method to add comprehensive test data
  static Future<void> addTestData() async {
    print('🔧 Adding comprehensive test data...');

    // Create a test bank account if none exists
    final bankAccounts = await getBankAccounts();
    String testBankAccountId;

    if (bankAccounts.isEmpty) {
      final testBankAccount = BankAccountModel(
        bankName: 'HDFC Bank',
        accountNumber: '***********',
        initialAmount: 100000.0,
        currentAmount: 85000.0,
      );
      await addBankAccount(testBankAccount);
      testBankAccountId = testBankAccount.id;
      print('✅ Created test bank account: ${testBankAccount.bankName}');
    } else {
      testBankAccountId = bankAccounts.first.id;
      print('✅ Using existing bank account: ${bankAccounts.first.bankName}');
    }

    final now = DateTime.now();
    final testTransactions = <TransactionModel>[];

    // Income transactions (Credits) - Recent dates
    testTransactions.addAll([
      TransactionModel(
        bankAccountId: testBankAccountId,
        type: TransactionType.credit,
        amount: 75000.0,
        description: 'Monthly Salary',
        category: 'Salary',
        date: now.subtract(const Duration(days: 5)),
      ),
      TransactionModel(
        bankAccountId: testBankAccountId,
        type: TransactionType.credit,
        amount: 15000.0,
        description: 'Freelance Project Payment',
        category: 'Freelance',
        date: now.subtract(const Duration(days: 10)),
      ),
      TransactionModel(
        bankAccountId: testBankAccountId,
        type: TransactionType.credit,
        amount: 5000.0,
        description: 'Dividend from stocks',
        category: 'Dividend',
        date: now.subtract(const Duration(days: 15)),
      ),
      TransactionModel(
        bankAccountId: testBankAccountId,
        type: TransactionType.credit,
        amount: 2500.0,
        description: 'Interest from FD',
        category: 'Interest',
        date: now.subtract(const Duration(days: 20)),
      ),
    ]);

    // Expense transactions (Debits) - Real expenses
    testTransactions.addAll([
      TransactionModel(
        bankAccountId: testBankAccountId,
        type: TransactionType.debit,
        amount: 8000.0,
        description: 'Groceries and household items',
        category: 'Food',
        date: now.subtract(const Duration(days: 2)),
      ),
      TransactionModel(
        bankAccountId: testBankAccountId,
        type: TransactionType.debit,
        amount: 12000.0,
        description: 'Monthly rent payment',
        category: 'Rent',
        date: now.subtract(const Duration(days: 1)),
      ),
      TransactionModel(
        bankAccountId: testBankAccountId,
        type: TransactionType.debit,
        amount: 3500.0,
        description: 'Petrol and transportation',
        category: 'Petrol',
        date: now.subtract(const Duration(days: 3)),
      ),
      TransactionModel(
        bankAccountId: testBankAccountId,
        type: TransactionType.debit,
        amount: 2500.0,
        description: 'Restaurant and dining out',
        category: 'Food',
        date: now.subtract(const Duration(days: 7)),
      ),
      TransactionModel(
        bankAccountId: testBankAccountId,
        type: TransactionType.debit,
        amount: 4000.0,
        description: 'Electricity and utilities',
        category: 'Utilities',
        date: now.subtract(const Duration(days: 12)),
      ),
      TransactionModel(
        bankAccountId: testBankAccountId,
        type: TransactionType.debit,
        amount: 1500.0,
        description: 'Mobile and internet bills',
        category: 'Bills',
        date: now.subtract(const Duration(days: 8)),
      ),
      TransactionModel(
        bankAccountId: testBankAccountId,
        type: TransactionType.debit,
        amount: 6000.0,
        description: 'Flight tickets for vacation',
        category: 'Travel',
        date: now.subtract(const Duration(days: 18)),
      ),
    ]);

    // Investment transactions (Debits) - Should be treated as savings
    testTransactions.addAll([
      TransactionModel(
        bankAccountId: testBankAccountId,
        type: TransactionType.debit,
        amount: 10000.0,
        description: 'Monthly SIP in HDFC Top 100 Fund',
        category: 'SIP',
        date: now.subtract(const Duration(days: 1)),
      ),
      TransactionModel(
        bankAccountId: testBankAccountId,
        type: TransactionType.debit,
        amount: 25000.0,
        description: 'Fixed Deposit for 1 year',
        category: 'FD',
        date: now.subtract(const Duration(days: 6)),
      ),
      TransactionModel(
        bankAccountId: testBankAccountId,
        type: TransactionType.debit,
        amount: 15000.0,
        description: 'ELSS Tax Saving Mutual Fund',
        category: 'ELSS',
        date: now.subtract(const Duration(days: 14)),
      ),
      TransactionModel(
        bankAccountId: testBankAccountId,
        type: TransactionType.debit,
        amount: 5000.0,
        description: 'Monthly Recurring Deposit',
        category: 'RD',
        date: now.subtract(const Duration(days: 4)),
      ),
      TransactionModel(
        bankAccountId: testBankAccountId,
        type: TransactionType.debit,
        amount: 8000.0,
        description: 'Gold purchase for investment',
        category: 'Gold',
        date: now.subtract(const Duration(days: 9)),
      ),
      TransactionModel(
        bankAccountId: testBankAccountId,
        type: TransactionType.debit,
        amount: 12000.0,
        description: 'Portfolio diversification - Equity shares',
        category: 'Equity',
        date: now.subtract(const Duration(days: 16)),
      ),
      TransactionModel(
        bankAccountId: testBankAccountId,
        type: TransactionType.debit,
        amount: 3000.0,
        description: 'Silver jewelry investment',
        category: 'Silver',
        date: now.subtract(const Duration(days: 11)),
      ),
      TransactionModel(
        bankAccountId: testBankAccountId,
        type: TransactionType.debit,
        amount: 20000.0,
        description: 'Watch investment - Rolex',
        category: 'Watch',
        date: now.subtract(const Duration(days: 22)),
      ),
    ]);

    // Add all transactions to storage
    final existingTransactions = await getTransactions();
    final allTransactions = [...existingTransactions, ...testTransactions];
    await saveTransactions(allTransactions);

    print('✅ Added ${testTransactions.length} test transactions');
    print('📊 Test Data Summary:');
    print(
      '   💰 Total Income: ₹${testTransactions.where((t) => t.type == TransactionType.credit).fold(0.0, (sum, t) => sum + t.amount)}',
    );
    print(
      '   💸 Total Expenses: ₹${testTransactions.where((t) => t.type == TransactionType.debit && !_isInvestmentCategory(t.category)).fold(0.0, (sum, t) => sum + t.amount)}',
    );
    print(
      '   📈 Total Investments: ₹${testTransactions.where((t) => t.type == TransactionType.debit && _isInvestmentCategory(t.category)).fold(0.0, (sum, t) => sum + t.amount)}',
    );
    print(
      '🎯 Test data added successfully! Check Advanced Analytics for score.',
    );
  }

  // Helper method to check if a category is an investment
  static bool _isInvestmentCategory(String category) {
    const investmentKeywords = [
      'mutual fund',
      'sip',
      'rd',
      'fd',
      'fixed deposit',
      'recurring deposit',
      'investment',
      'equity',
      'debt',
      'bond',
      'stocks',
      'share',
      'portfolio',
      'elss',
      'ppf',
      'epf',
      'nps',
      'gold',
      'etf',
      'ulip',
      'insurance',
      'systematic',
      'lumpsum',
      'dividend',
      'capital',
      'fund',
      'mf',
      'silver',
      'platinum',
      'diamond',
      'rubies',
      'emeralds',
      'sapphires',
      'opals',
      'jewelry',
      'watch',
      'ring',
      'necklace',
      'earrings',
      'bracelet',
      'bangle',
    ];

    return investmentKeywords.any(
      (keyword) => category.toLowerCase().contains(keyword.toLowerCase()),
    );
  }

  // Credit Card operations
  static Future<List<CreditCardModel>> getCreditCards() async {
    final prefs = await SharedPreferences.getInstance();
    final creditCardsJson = prefs.getString(_creditCardsKey);

    if (creditCardsJson == null) return [];

    final List<dynamic> creditCardsList = json.decode(creditCardsJson);
    return creditCardsList
        .map((json) => CreditCardModel.fromJson(json))
        .toList();
  }

  static Future<void> saveCreditCards(List<CreditCardModel> creditCards) async {
    final prefs = await SharedPreferences.getInstance();
    final creditCardsJson = json.encode(
      creditCards.map((card) => card.toJson()).toList(),
    );
    await prefs.setString(_creditCardsKey, creditCardsJson);
  }

  static Future<void> addCreditCard(CreditCardModel creditCard) async {
    final creditCards = await getCreditCards();
    creditCards.add(creditCard);
    await saveCreditCards(creditCards);
  }

  // Credit Card Transaction operations
  static Future<List<CreditCardTransactionModel>>
  getCreditCardTransactions() async {
    final prefs = await SharedPreferences.getInstance();
    final transactionsJson = prefs.getString(_creditCardTransactionsKey);

    if (transactionsJson == null) return [];

    final List<dynamic> transactionsList = json.decode(transactionsJson);
    return transactionsList
        .map((json) => CreditCardTransactionModel.fromJson(json))
        .toList();
  }

  static Future<void> saveCreditCardTransactions(
    List<CreditCardTransactionModel> transactions,
  ) async {
    final prefs = await SharedPreferences.getInstance();
    final transactionsJson = json.encode(
      transactions.map((transaction) => transaction.toJson()).toList(),
    );
    await prefs.setString(_creditCardTransactionsKey, transactionsJson);
  }

  static Future<void> addCreditCardTransaction(
    CreditCardTransactionModel transaction,
  ) async {
    final transactions = await getCreditCardTransactions();
    transactions.add(transaction);
    await saveCreditCardTransactions(transactions);
  }
}
