import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../features/models/user_model.dart';

/// Service class for managing user data
class UserService {
  static const String _userKey = 'user_data';
  static const String _currentUserKey = 'current_user_data';
  static const String _isFirstTimeKey = 'is_first_time';

  /// Save user data to SharedPreferences
  static Future<void> saveUser(UserModel user) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_userKey, jsonEncode(user.toJson()));
  }

  /// Save current logged-in user data (from API response)
  static Future<void> saveCurrentUser(UserModel user) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_currentUserKey, jsonEncode(user.toJson()));
  }

  /// Get current logged-in user data
  static Future<UserModel?> getCurrentUser() async {
    final prefs = await SharedPreferences.getInstance();
    final userJson = prefs.getString(_currentUserKey);

    if (userJson == null) return null;

    final userMap = jsonDecode(userJson) as Map<String, dynamic>;
    return UserModel.fromJson(userMap);
  }

  /// Save user from API response
  static Future<void> saveUserFromApiResponse(
    Map<String, dynamic> apiResponse,
  ) async {
    final user = UserModel.fromApiResponse(apiResponse);
    await saveCurrentUser(user);
  }

  /// Get user data from SharedPreferences
  static Future<UserModel?> getUser() async {
    final prefs = await SharedPreferences.getInstance();
    final userJson = prefs.getString(_userKey);

    if (userJson == null) return null;

    final userMap = jsonDecode(userJson) as Map<String, dynamic>;
    return UserModel.fromJson(userMap);
  }

  /// Check if user exists
  static Future<bool> userExists() async {
    final user = await getUser();
    return user != null;
  }

  /// Check if current user is logged in
  static Future<bool> isCurrentUserLoggedIn() async {
    final user = await getCurrentUser();
    return user != null;
  }

  /// Delete user data
  static Future<void> deleteUser() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_userKey);
  }

  /// Delete current user data (logout)
  static Future<void> deleteCurrentUser() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_currentUserKey);
  }

  /// Clear all user data
  static Future<void> clearAllUserData() async {
    await deleteUser();
    await deleteCurrentUser();
  }

  /// Check if this is the first time user is opening the app
  static Future<bool> isFirstTime() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_isFirstTimeKey) ?? true;
  }

  /// Mark that user has completed first time setup
  static Future<void> setFirstTimeComplete() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_isFirstTimeKey, false);
  }

  /// Update user's face auth status
  static Future<void> updateFaceAuthStatus(bool isEnabled) async {
    final user = await getUser();
    if (user != null) {
      final updatedUser = user.copyWith(
        isFaceAuthEnabled: isEnabled,
        updatedAt: DateTime.now(),
      );
      await saveUser(updatedUser);
    }

    // Also update current user if logged in
    final currentUser = await getCurrentUser();
    if (currentUser != null) {
      final updatedCurrentUser = currentUser.copyWith(
        isFaceAuthEnabled: isEnabled,
        updatedAt: DateTime.now(),
      );
      await saveCurrentUser(updatedCurrentUser);
    }
  }

  /// Get a specific value from current user's API data
  static Future<dynamic> getCurrentUserApiValue(String key) async {
    final user = await getCurrentUser();
    return user?.getApiValue(key);
  }

  /// Check if current user's API data contains a specific key
  static Future<bool> currentUserHasApiKey(String key) async {
    final user = await getCurrentUser();
    return user?.hasApiKey(key) ?? false;
  }
}
