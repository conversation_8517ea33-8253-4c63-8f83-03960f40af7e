import '../features/models/credit_card_model.dart';
import '../features/models/credit_card_transaction_model.dart';
import 'local_storage_service.dart';
import 'notification_service.dart';

/// Service class for managing credit card data and transactions
class CreditCardService {
  /// Get all credit cards
  static Future<List<CreditCardModel>> getCreditCards() async {
    try {
      return await LocalStorageService.getCreditCards();
    } catch (e) {
      print('Error loading credit cards: $e');
      return [];
    }
  }

  /// Add a new credit card
  static Future<void> addCreditCard(CreditCardModel creditCard) async {
    try {
      await LocalStorageService.addCreditCard(creditCard);

      // Schedule notifications for the new credit card
      if (creditCard.outstandingBalance > 0) {
        await scheduleCreditCardNotifications(creditCard.id);
      }
    } catch (e) {
      print('Error adding credit card: $e');
      rethrow;
    }
  }

  /// Update an existing credit card
  static Future<void> updateCreditCard(
    CreditCardModel updatedCreditCard,
  ) async {
    try {
      final creditCards = await getCreditCards();
      final index = creditCards.indexWhere(
        (card) => card.id == updatedCreditCard.id,
      );

      if (index != -1) {
        creditCards[index] = updatedCreditCard;
        await LocalStorageService.saveCreditCards(creditCards);

        // Reschedule notifications for the updated credit card
        await rescheduleCreditCardNotifications(updatedCreditCard.id);
      } else {
        throw Exception('Credit card not found');
      }
    } catch (e) {
      print('Error updating credit card: $e');
      rethrow;
    }
  }

  /// Delete a credit card
  static Future<void> deleteCreditCard(String creditCardId) async {
    try {
      // Cancel notifications first
      await NotificationService.cancelCreditCardNotifications(creditCardId);

      final creditCards = await getCreditCards();
      creditCards.removeWhere((card) => card.id == creditCardId);
      await LocalStorageService.saveCreditCards(creditCards);

      // Also delete all transactions for this credit card
      await deleteCreditCardTransactions(creditCardId);
    } catch (e) {
      print('Error deleting credit card: $e');
      rethrow;
    }
  }

  /// Get credit card by ID
  static Future<CreditCardModel?> getCreditCardById(String id) async {
    try {
      final creditCards = await getCreditCards();
      return creditCards.where((card) => card.id == id).firstOrNull;
    } catch (e) {
      print('Error getting credit card by ID: $e');
      return null;
    }
  }

  /// Get all credit card transactions
  static Future<List<CreditCardTransactionModel>>
  getCreditCardTransactions() async {
    try {
      return await LocalStorageService.getCreditCardTransactions();
    } catch (e) {
      print('Error loading credit card transactions: $e');
      return [];
    }
  }

  /// Get transactions for a specific credit card
  static Future<List<CreditCardTransactionModel>>
  getCreditCardTransactionsByCardId(String creditCardId) async {
    try {
      final allTransactions = await getCreditCardTransactions();
      return allTransactions
          .where((transaction) => transaction.creditCardId == creditCardId)
          .toList();
    } catch (e) {
      print('Error loading credit card transactions for card: $e');
      return [];
    }
  }

  /// Add a new credit card transaction
  static Future<void> addCreditCardTransaction(
    CreditCardTransactionModel transaction,
  ) async {
    try {
      await LocalStorageService.addCreditCardTransaction(transaction);

      // Update credit card balance
      await _updateCreditCardBalance(transaction);

      // If this is a payment, also update bank account balance
      if (transaction.affectsBankAccount) {
        await _updateBankAccountForPayment(transaction);
      }
    } catch (e) {
      print('Error adding credit card transaction: $e');
      rethrow;
    }
  }

  /// Delete all transactions for a credit card
  static Future<void> deleteCreditCardTransactions(String creditCardId) async {
    try {
      final transactions = await getCreditCardTransactions();
      transactions.removeWhere(
        (transaction) => transaction.creditCardId == creditCardId,
      );
      await LocalStorageService.saveCreditCardTransactions(transactions);
    } catch (e) {
      print('Error deleting credit card transactions: $e');
      rethrow;
    }
  }

  /// Delete a single credit card transaction
  static Future<void> deleteCreditCardTransaction(String transactionId) async {
    try {
      final transactions = await getCreditCardTransactions();
      transactions.removeWhere(
        (transaction) => transaction.id == transactionId,
      );
      await LocalStorageService.saveCreditCardTransactions(transactions);
    } catch (e) {
      print('Error deleting credit card transaction: $e');
      rethrow;
    }
  }

  /// Get total outstanding balance across all credit cards
  static Future<double> getTotalOutstandingBalance() async {
    try {
      final creditCards = await getCreditCards();
      return creditCards.fold<double>(
        0.0,
        (sum, card) => sum + card.outstandingBalance,
      );
    } catch (e) {
      print('Error calculating total outstanding balance: $e');
      return 0.0;
    }
  }

  /// Get total credit limit across all credit cards
  static Future<double> getTotalCreditLimit() async {
    try {
      final creditCards = await getCreditCards();
      return creditCards.fold<double>(
        0.0,
        (sum, card) => sum + card.creditLimit,
      );
    } catch (e) {
      print('Error calculating total credit limit: $e');
      return 0.0;
    }
  }

  /// Get cards with upcoming due dates (within 7 days)
  static Future<List<CreditCardModel>> getCardsWithUpcomingDueDates() async {
    try {
      final creditCards = await getCreditCards();
      final now = DateTime.now();

      return creditCards.where((card) {
        final daysDiff = card.dueDate.difference(now).inDays;
        return daysDiff <= 7 && daysDiff >= 0 && card.outstandingBalance > 0;
      }).toList();
    } catch (e) {
      print('Error getting cards with upcoming due dates: $e');
      return [];
    }
  }

  /// Get overdue cards
  static Future<List<CreditCardModel>> getOverdueCards() async {
    try {
      final creditCards = await getCreditCards();
      return creditCards.where((card) => card.isOverdue).toList();
    } catch (e) {
      print('Error getting overdue cards: $e');
      return [];
    }
  }

  /// Calculate average utilization across all cards
  static Future<double> getAverageUtilization() async {
    try {
      final creditCards = await getCreditCards();
      if (creditCards.isEmpty) return 0.0;

      final totalUtilization = creditCards.fold(
        0.0,
        (sum, card) => sum + card.utilizationPercentage,
      );
      return totalUtilization / creditCards.length;
    } catch (e) {
      print('Error calculating average utilization: $e');
      return 0.0;
    }
  }

  /// Private method to update credit card balance after transaction
  static Future<void> _updateCreditCardBalance(
    CreditCardTransactionModel transaction,
  ) async {
    try {
      final creditCard = await getCreditCardById(transaction.creditCardId);
      if (creditCard == null) {
        throw Exception('Credit card not found');
      }

      final newOutstandingBalance =
          creditCard.outstandingBalance + transaction.creditCardImpactAmount;

      final updatedCreditCard = creditCard.copyWith(
        outstandingBalance: newOutstandingBalance.clamp(
          0.0,
          double.infinity,
        ), // Don't allow negative balance
        updatedAt: DateTime.now(),
      );

      await updateCreditCard(updatedCreditCard);
    } catch (e) {
      print('Error updating credit card balance: $e');
      rethrow;
    }
  }

  /// Private method to update bank account balance for credit card payments
  static Future<void> _updateBankAccountForPayment(
    CreditCardTransactionModel transaction,
  ) async {
    try {
      if (transaction.linkedBankAccountId == null) return;

      final bankAccounts = await LocalStorageService.getBankAccounts();
      final bankAccountIndex = bankAccounts.indexWhere(
        (account) => account.id == transaction.linkedBankAccountId,
      );

      if (bankAccountIndex == -1) {
        throw Exception('Bank account not found');
      }

      final bankAccount = bankAccounts[bankAccountIndex];
      final newBalance =
          bankAccount.currentAmount -
          transaction.amount; // Deduct payment amount

      final updatedBankAccount = bankAccount.copyWith(
        currentAmount: newBalance,
        updatedAt: DateTime.now(),
      );

      bankAccounts[bankAccountIndex] = updatedBankAccount;
      await LocalStorageService.saveBankAccounts(bankAccounts);
    } catch (e) {
      print('Error updating bank account for payment: $e');
      rethrow;
    }
  }

  /// Get recent credit card transactions (last 10)
  static Future<List<CreditCardTransactionModel>>
  getRecentCreditCardTransactions() async {
    try {
      final allTransactions = await getCreditCardTransactions();
      allTransactions.sort(
        (a, b) => b.date.compareTo(a.date),
      ); // Sort by date descending
      return allTransactions.take(10).toList();
    } catch (e) {
      print('Error getting recent credit card transactions: $e');
      return [];
    }
  }

  /// Get spending by category for credit cards
  static Future<Map<String, double>> getCreditCardSpendingByCategory() async {
    try {
      final transactions = await getCreditCardTransactions();
      final Map<String, double> categorySpending = {};

      for (final transaction in transactions) {
        if (transaction.type == CreditCardTransactionType.purchase) {
          categorySpending[transaction.category] =
              (categorySpending[transaction.category] ?? 0) +
              transaction.amount;
        }
      }

      return categorySpending;
    } catch (e) {
      print('Error getting credit card spending by category: $e');
      return {};
    }
  }

  /// Update credit card dates when they pass and reschedule notifications
  static Future<void> updateCreditCardDatesAndNotifications() async {
    try {
      final creditCards = await getCreditCards();
      final now = DateTime.now();
      bool hasUpdates = false;

      for (final creditCard in creditCards) {
        CreditCardModel? updatedCard;
        bool dueUpdated = false;
        bool closingUpdated = false;

        // Check if due date has passed
        if (creditCard.dueDate.isBefore(now)) {
          // Move to next month's due date
          final nextDueDate = _getNextMonthDate(creditCard.dueDate);
          dueUpdated = true;

          updatedCard = CreditCardModel(
            id: creditCard.id,
            cardName: creditCard.cardName,
            cardNumber: creditCard.cardNumber,
            cardType: creditCard.cardType,
            bankName: creditCard.bankName,
            creditLimit: creditCard.creditLimit,
            outstandingBalance: creditCard.outstandingBalance,
            dueDate: nextDueDate,
            closingDate: creditCard.closingDate,
            createdAt: creditCard.createdAt,
            updatedAt: DateTime.now(),
          );

          print(
            'Updated due date for ${creditCard.cardName}: ${creditCard.dueDate} → $nextDueDate',
          );
        }

        // Check if closing date has passed
        if (creditCard.closingDate.isBefore(now)) {
          // Move to next month's closing date
          final nextClosingDate = _getNextMonthDate(creditCard.closingDate);
          closingUpdated = true;

          updatedCard = CreditCardModel(
            id: updatedCard?.id ?? creditCard.id,
            cardName: updatedCard?.cardName ?? creditCard.cardName,
            cardNumber: updatedCard?.cardNumber ?? creditCard.cardNumber,
            cardType: updatedCard?.cardType ?? creditCard.cardType,
            bankName: updatedCard?.bankName ?? creditCard.bankName,
            creditLimit: updatedCard?.creditLimit ?? creditCard.creditLimit,
            outstandingBalance:
                updatedCard?.outstandingBalance ??
                creditCard.outstandingBalance,
            dueDate: updatedCard?.dueDate ?? creditCard.dueDate,
            closingDate: nextClosingDate,
            createdAt: updatedCard?.createdAt ?? creditCard.createdAt,
            updatedAt: DateTime.now(),
          );

          print(
            'Updated closing date for ${creditCard.cardName}: ${creditCard.closingDate} → $nextClosingDate',
          );
        }

        // Update the card if changes were made
        if (updatedCard != null) {
          await updateCreditCard(updatedCard);
          hasUpdates = true;

          // Send immediate notification about date updates
          if (dueUpdated && closingUpdated) {
            await _sendDateUpdateNotification(creditCard, 'both');
          } else if (dueUpdated) {
            await _sendDateUpdateNotification(creditCard, 'due');
          } else if (closingUpdated) {
            await _sendDateUpdateNotification(creditCard, 'statement');
          }
        }
      }

      // If any dates were updated, reschedule all notifications
      if (hasUpdates) {
        await NotificationService.scheduleAllCreditCardNotifications();
        print('Credit card dates updated and notifications rescheduled');
      }
    } catch (e) {
      print('Error updating credit card dates: $e');
    }
  }

  /// Helper method to get next month's date
  static DateTime _getNextMonthDate(DateTime currentDate) {
    int nextYear = currentDate.year;
    int nextMonth = currentDate.month + 1;

    if (nextMonth > 12) {
      nextMonth = 1;
      nextYear++;
    }

    // Handle month-end dates (e.g., Jan 31 → Feb 28/29)
    int day = currentDate.day;
    int daysInNextMonth = DateTime(nextYear, nextMonth + 1, 0).day;
    if (day > daysInNextMonth) {
      day = daysInNextMonth;
    }

    return DateTime(nextYear, nextMonth, day);
  }

  /// Send notification about date updates
  static Future<void> _sendDateUpdateNotification(
    CreditCardModel creditCard,
    String updateType,
  ) async {
    String title;
    String body;

    switch (updateType) {
      case 'due':
        title = 'Payment Date Updated';
        body = '${creditCard.cardName} - New payment due date scheduled';
        break;
      case 'statement':
        title = 'Statement Date Updated';
        body = '${creditCard.cardName} - New statement closing date scheduled';
        break;
      case 'both':
        title = 'Credit Card Dates Updated';
        body =
            '${creditCard.cardName} - Payment and statement dates have been updated';
        break;
      default:
        return;
    }

    try {
      await NotificationService.showImmediateNotification(
        title: title,
        body: body,
        payload: 'date_update_${creditCard.id}',
      );
    } catch (e) {
      print('Error sending date update notification: $e');
    }
  }

  /// Schedule notifications for a specific credit card
  static Future<void> scheduleCreditCardNotifications(
    String creditCardId,
  ) async {
    try {
      final creditCard = await getCreditCardById(creditCardId);
      if (creditCard != null) {
        await NotificationService.scheduleCreditCardNotifications(creditCard);
      }
    } catch (e) {
      print('Error scheduling notifications for credit card: $e');
    }
  }

  /// Cancel and reschedule notifications for a specific credit card
  static Future<void> rescheduleCreditCardNotifications(
    String creditCardId,
  ) async {
    try {
      // Cancel existing notifications
      await NotificationService.cancelCreditCardNotifications(creditCardId);

      // Schedule new notifications
      await scheduleCreditCardNotifications(creditCardId);
    } catch (e) {
      print('Error rescheduling notifications for credit card: $e');
    }
  }
}
