import 'package:flutter/services.dart';
import 'package:local_auth/local_auth.dart';
import 'package:shared_preferences/shared_preferences.dart';

class BiometricAuthService {
  static const String _biometricEnabledKey = 'biometric_enabled';
  static const String _authRequiredKey = 'auth_required';

  static final LocalAuthentication _localAuth = LocalAuthentication();

  /// Check if device supports biometric authentication
  static Future<bool> isBiometricSupported() async {
    try {
      final bool isAvailable = await _localAuth.canCheckBiometrics;
      final bool isDeviceSupported = await _localAuth.isDeviceSupported();
      return isAvailable && isDeviceSupported;
    } catch (e) {
      print('Error checking biometric support: $e');
      return false;
    }
  }

  /// Get available biometric types
  static Future<List<BiometricType>> getAvailableBiometrics() async {
    try {
      return await _localAuth.getAvailableBiometrics();
    } catch (e) {
      print('Error getting available biometrics: $e');
      return [];
    }
  }

  /// Check if user has enabled biometric authentication
  static Future<bool> isBiometricEnabled() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_biometricEnabledKey) ?? true;
  }

  /// Enable/disable biometric authentication
  static Future<void> setBiometricEnabled(bool enabled) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_biometricEnabledKey, enabled);
  }

  /// Check if authentication is required
  static Future<bool> isAuthRequired() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_authRequiredKey) ?? true;
  }

  /// Set authentication requirement
  static Future<void> setAuthRequired(bool required) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_authRequiredKey, required);
  }

  /// Authenticate with biometrics
  static Future<bool> authenticateWithBiometrics() async {
    try {
      final bool didAuthenticate = await _localAuth.authenticate(
        localizedReason:
            'Please authenticate to access your personal finance data',
        options: const AuthenticationOptions(
          biometricOnly: true,
          stickyAuth: true,
        ),
      );
      return didAuthenticate;
    } on PlatformException catch (e) {
      print('Biometric authentication error: $e');
      return false;
    }
  }

  /// Authenticate with system authentication (biometric + PIN/password fallback)
  static Future<bool> authenticateWithSystem() async {
    try {
      final bool didAuthenticate = await _localAuth.authenticate(
        localizedReason:
            'Please authenticate to access your personal finance data',
        options: const AuthenticationOptions(
          biometricOnly: false, // Allow PIN/password fallback
          stickyAuth: true,
        ),
      );
      return didAuthenticate;
    } on PlatformException catch (e) {
      print('System authentication error: $e');
      return false;
    }
  }

  /// Main authentication method - uses system authentication (biometric + PIN/password)
  static Future<AuthResult> authenticate() async {
    print('Starting authentication process...');

    // Check if authentication is required
    if (!await isAuthRequired()) {
      print('Authentication not required');
      return AuthResult.success();
    }

    print('Authentication is required');

    // Try system authentication (biometric if available, otherwise PIN/password)
    try {
      final authenticated = await authenticateWithSystem();
      if (authenticated) {
        print('System authentication successful');
        return AuthResult.success();
      } else {
        print('System authentication failed');
        return AuthResult.noAuthMethodSet();
      }
    } catch (e) {
      print('System authentication error: $e');
      return AuthResult.error('Authentication failed: $e');
    }
  }

  /// Get authentication status description
  static Future<String> getAuthStatusDescription() async {
    final biometricSupported = await isBiometricSupported();
    final biometricEnabled = await isBiometricEnabled();
    final authRequired = await isAuthRequired();

    if (!authRequired) {
      return 'Authentication disabled';
    }

    if (biometricSupported && biometricEnabled) {
      final biometrics = await getAvailableBiometrics();
      final biometricTypes = biometrics
          .map((type) => getBiometricTypeName(type))
          .join(', ');
      return 'Biometric authentication enabled ($biometricTypes)';
    }

    return 'System authentication enabled';
  }

  /// Get user-friendly biometric type name
  static String getBiometricTypeName(BiometricType type) {
    switch (type) {
      case BiometricType.face:
        return 'Face ID';
      case BiometricType.fingerprint:
        return 'Fingerprint';
      case BiometricType.iris:
        return 'Iris';
      case BiometricType.weak:
        return 'Screen Lock';
      case BiometricType.strong:
        return 'Strong Biometric';
    }
  }

  /// Initialize default authentication settings on first app launch
  static Future<void> initializeDefaults() async {
    final prefs = await SharedPreferences.getInstance();

    // Check if this is the first time initializing
    final isInitialized = prefs.getBool('auth_defaults_initialized') ?? false;

    if (!isInitialized) {
      // Check if biometric is supported and enable by default
      final biometricSupported = await isBiometricSupported();

      if (biometricSupported) {
        // Enable both auth required and biometric by default
        await prefs.setBool(_authRequiredKey, true);
        await prefs.setBool(_biometricEnabledKey, true);
      } else {
        // Only enable auth required if biometric not supported
        await prefs.setBool(_authRequiredKey, true);
        await prefs.setBool(_biometricEnabledKey, false);
      }

      // Mark as initialized
      await prefs.setBool('auth_defaults_initialized', true);
    }
  }

  /// Clear all authentication data
  static Future<void> clearAuthData() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_biometricEnabledKey);
    await prefs.remove(_authRequiredKey);
    await prefs.remove('auth_defaults_initialized');
  }
}

/// Result of authentication attempt
class AuthResult {
  final AuthStatus status;
  final String? message;

  AuthResult._(this.status, this.message);

  factory AuthResult.success() => AuthResult._(AuthStatus.success, null);
  factory AuthResult.biometricFailed() => AuthResult._(
    AuthStatus.biometricFailed,
    'Biometric authentication failed',
  );

  factory AuthResult.noAuthMethodSet() => AuthResult._(
    AuthStatus.noAuthMethodSet,
    'No authentication method configured',
  );
  factory AuthResult.error(String message) =>
      AuthResult._(AuthStatus.error, message);

  bool get isSuccess => status == AuthStatus.success;

  bool get biometricFailed => status == AuthStatus.biometricFailed;
  bool get noAuthMethod => status == AuthStatus.noAuthMethodSet;
}

enum AuthStatus { success, biometricFailed, noAuthMethodSet, error }
