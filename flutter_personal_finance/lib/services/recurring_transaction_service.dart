import 'package:shared_preferences/shared_preferences.dart';
import '../features/models/recurring_transaction_model.dart';
import '../features/models/transaction_model.dart';
import 'local_storage_service.dart';
import 'transaction_service.dart';
import 'notification_service.dart';

/// Service class for managing recurring transaction business logic
class RecurringTransactionService {
  static const String _lastProcessingTimeKey = 'last_recurring_processing_time';

  /// Get the last time recurring transactions were processed
  static Future<DateTime?> getLastProcessingTime() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final timeString = prefs.getString(_lastProcessingTimeKey);
      return timeString != null ? DateTime.parse(timeString) : null;
    } catch (e) {
      print('Error getting last processing time: $e');
      return null;
    }
  }

  /// Set the last processing time
  static Future<void> _setLastProcessingTime(DateTime time) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_lastProcessingTimeKey, time.toIso8601String());
    } catch (e) {
      print('Error setting last processing time: $e');
    }
  }

  /// Process all due recurring transactions
  static Future<Map<String, dynamic>> processDueRecurringTransactions() async {
    try {
      final dueTransactions =
          await LocalStorageService.getDueRecurringTransactions();

      int processedCount = 0;
      int failedCount = 0;
      List<String> processedDescriptions = [];

      for (final recurringTransaction in dueTransactions) {
        try {
          await _processRecurringTransaction(recurringTransaction);
          processedCount++;
          processedDescriptions.add(recurringTransaction.description);
        } catch (e) {
          failedCount++;
          print(
            'Failed to process recurring transaction ${recurringTransaction.id}: $e',
          );
        }
      }

      print(
        'Processed $processedCount due recurring transactions, $failedCount failed',
      );

      // Save the last processing time
      await _setLastProcessingTime(DateTime.now());

      return {
        'totalDue': dueTransactions.length,
        'processed': processedCount,
        'failed': failedCount,
        'processedDescriptions': processedDescriptions,
        'lastProcessingTime': DateTime.now(),
      };
    } catch (e) {
      print('Error processing due recurring transactions: $e');
      return {
        'totalDue': 0,
        'processed': 0,
        'failed': 0,
        'processedDescriptions': <String>[],
        'error': e.toString(),
      };
    }
  }

  /// Check if there are any due recurring transactions without processing them
  static Future<bool> hasDueRecurringTransactions() async {
    try {
      final dueTransactions =
          await LocalStorageService.getDueRecurringTransactions();
      return dueTransactions.isNotEmpty;
    } catch (e) {
      print('Error checking due recurring transactions: $e');
      return false;
    }
  }

  /// Process a single recurring transaction
  static Future<void> _processRecurringTransaction(
    RecurringTransactionModel recurringTransaction,
  ) async {
    try {
      // Create a new transaction from the recurring transaction
      final newTransaction = TransactionModel(
        bankAccountId: recurringTransaction.bankAccountId,
        type: TransactionType.debit, // Default to debit (expense)
        amount: recurringTransaction.amount,
        description: recurringTransaction.description,
        category: recurringTransaction.category,
        date: recurringTransaction.nextDueDate,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // Add the transaction using existing TransactionService
      await TransactionService.addTransaction(newTransaction);

      // Calculate next due date
      final nextDueDate = _calculateNextDueDate(
        recurringTransaction.frequency,
        recurringTransaction.nextDueDate,
      );

      // Update the recurring transaction with new due date and last processed date
      final updatedRecurringTransaction = recurringTransaction.copyWith(
        nextDueDate: nextDueDate,
        lastProcessedDate: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // Save the updated recurring transaction
      await LocalStorageService.updateRecurringTransaction(
        updatedRecurringTransaction,
      );

      // Schedule notification for the next occurrence
      await NotificationService.scheduleRecurringTransactionNotification(
        updatedRecurringTransaction,
      );

      print(
        'Processed recurring transaction: ${recurringTransaction.description}',
      );
    } catch (e) {
      print(
        'Error processing recurring transaction ${recurringTransaction.id}: $e',
      );
    }
  }

  /// Calculate next due date based on frequency
  static DateTime _calculateNextDueDate(
    RecurringFrequency frequency,
    DateTime currentDueDate,
  ) {
    switch (frequency) {
      case RecurringFrequency.daily:
        return currentDueDate.add(const Duration(days: 1));
      case RecurringFrequency.weekly:
        return currentDueDate.add(const Duration(days: 7));
      case RecurringFrequency.monthly:
        return DateTime(
          currentDueDate.year,
          currentDueDate.month + 1,
          currentDueDate.day,
        );
      case RecurringFrequency.quarterly:
        return DateTime(
          currentDueDate.year,
          currentDueDate.month + 3,
          currentDueDate.day,
        );
      case RecurringFrequency.halfYearly:
        return DateTime(
          currentDueDate.year,
          currentDueDate.month + 6,
          currentDueDate.day,
        );
      case RecurringFrequency.yearly:
        return DateTime(
          currentDueDate.year + 1,
          currentDueDate.month,
          currentDueDate.day,
        );
    }
  }

  /// Calculate next due date for a new recurring transaction
  static DateTime calculateNextDueDateForNew(
    RecurringFrequency frequency,
    DateTime startDate,
  ) {
    switch (frequency) {
      case RecurringFrequency.daily:
        return startDate.add(const Duration(days: 1));
      case RecurringFrequency.weekly:
        return startDate.add(const Duration(days: 7));
      case RecurringFrequency.monthly:
        return DateTime(startDate.year, startDate.month + 1, startDate.day);
      case RecurringFrequency.quarterly:
        return DateTime(startDate.year, startDate.month + 3, startDate.day);
      case RecurringFrequency.halfYearly:
        return DateTime(startDate.year, startDate.month + 6, startDate.day);
      case RecurringFrequency.yearly:
        return DateTime(startDate.year + 1, startDate.month, startDate.day);
    }
  }

  /// Create a recurring transaction from transaction data
  static Future<void> createRecurringTransaction({
    required String bankAccountId,
    required String description,
    required String category,
    required double amount,
    required RecurringFrequency frequency,
    required DateTime startDate,
    DateTime? endDate,
  }) async {
    try {
      final nextDueDate = calculateNextDueDateForNew(frequency, startDate);

      final recurringTransaction = RecurringTransactionModel(
        bankAccountId: bankAccountId,
        description: description,
        category: category,
        amount: amount,
        frequency: frequency,
        startDate: startDate,
        nextDueDate: nextDueDate,
        endDate: endDate,
        isActive: true,
      );

      // Save the recurring transaction
      await LocalStorageService.addRecurringTransaction(recurringTransaction);

      // Schedule notification for the first occurrence
      await NotificationService.scheduleRecurringTransactionNotification(
        recurringTransaction,
      );

      print('Created recurring transaction: $description');
    } catch (e) {
      print('Error creating recurring transaction: $e');
      rethrow;
    }
  }

  /// Toggle active status of a recurring transaction
  static Future<void> toggleRecurringTransactionStatus(
    String transactionId,
  ) async {
    try {
      final recurringTransactions =
          await LocalStorageService.getRecurringTransactions();
      final transaction = recurringTransactions.firstWhere(
        (t) => t.id == transactionId,
      );

      final updatedTransaction = transaction.copyWith(
        isActive: !transaction.isActive,
        updatedAt: DateTime.now(),
      );

      await LocalStorageService.updateRecurringTransaction(updatedTransaction);

      if (updatedTransaction.isActive) {
        // Schedule notification if reactivating
        await NotificationService.scheduleRecurringTransactionNotification(
          updatedTransaction,
        );
      } else {
        // Cancel notifications if deactivating
        await NotificationService.cancelRecurringTransactionNotifications(
          transactionId,
        );
      }

      print('Toggled recurring transaction status: ${transaction.description}');
    } catch (e) {
      print('Error toggling recurring transaction status: $e');
      rethrow;
    }
  }

  /// Delete a recurring transaction and cancel its notifications
  static Future<void> deleteRecurringTransaction(String transactionId) async {
    try {
      // Cancel notifications first
      await NotificationService.cancelRecurringTransactionNotifications(
        transactionId,
      );

      // Delete the recurring transaction
      await LocalStorageService.deleteRecurringTransaction(transactionId);

      print('Deleted recurring transaction: $transactionId');
    } catch (e) {
      print('Error deleting recurring transaction: $e');
      rethrow;
    }
  }

  /// Get recurring transactions summary for dashboard
  static Future<Map<String, dynamic>> getRecurringTransactionsSummary() async {
    try {
      final allRecurringTransactions =
          await LocalStorageService.getRecurringTransactions();
      final activeTransactions =
          await LocalStorageService.getActiveRecurringTransactions();
      final dueTransactions =
          await LocalStorageService.getDueRecurringTransactions();
      final dueIn30Days =
          await LocalStorageService.getRecurringTransactionsDueIn30Days();

      double totalMonthlyAmount = 0.0;
      double totalWeeklyAmount = 0.0;
      double totalDailyAmount = 0.0;
      double totalQuarterlyAmount = 0.0;
      double totalHalfYearlyAmount = 0.0;
      double totalYearlyAmount = 0.0;

      for (final transaction in activeTransactions) {
        switch (transaction.frequency) {
          case RecurringFrequency.daily:
            totalDailyAmount += transaction.amount * 30; // Approximate monthly
            break;
          case RecurringFrequency.weekly:
            totalWeeklyAmount += transaction.amount * 4; // Approximate monthly
            break;
          case RecurringFrequency.monthly:
            totalMonthlyAmount += transaction.amount;
            break;
          case RecurringFrequency.quarterly:
            totalQuarterlyAmount += transaction.amount / 3; // Monthly average
            break;
          case RecurringFrequency.halfYearly:
            totalHalfYearlyAmount += transaction.amount / 6; // Monthly average
            break;
          case RecurringFrequency.yearly:
            totalYearlyAmount += transaction.amount / 12; // Monthly average
            break;
        }
      }

      final totalMonthlyRecurring =
          totalDailyAmount +
          totalWeeklyAmount +
          totalMonthlyAmount +
          totalQuarterlyAmount +
          totalHalfYearlyAmount +
          totalYearlyAmount;

      return {
        'totalRecurringTransactions': allRecurringTransactions.length,
        'activeRecurringTransactions': activeTransactions.length,
        'dueRecurringTransactions': dueTransactions.length,
        'dueIn30Days': dueIn30Days.length,
        'totalMonthlyAmount': totalMonthlyRecurring,
        'dailyAmount': totalDailyAmount,
        'weeklyAmount': totalWeeklyAmount,
        'monthlyAmount': totalMonthlyAmount,
        'quarterlyAmount': totalQuarterlyAmount,
        'halfYearlyAmount': totalHalfYearlyAmount,
        'yearlyAmount': totalYearlyAmount,
      };
    } catch (e) {
      print('Error getting recurring transactions summary: $e');
      return {
        'totalRecurringTransactions': 0,
        'activeRecurringTransactions': 0,
        'dueRecurringTransactions': 0,
        'dueIn30Days': 0,
        'totalMonthlyAmount': 0.0,
        'dailyAmount': 0.0,
        'weeklyAmount': 0.0,
        'monthlyAmount': 0.0,
        'quarterlyAmount': 0.0,
        'halfYearlyAmount': 0.0,
        'yearlyAmount': 0.0,
      };
    }
  }
}
