import 'package:uuid/uuid.dart';

/// Model class for money transfers between bank accounts
class TransferModel {
  final String id;
  final String fromBankAccountId;
  final String toBankAccountId;
  final double amount;
  final String description;
  final DateTime date;
  final DateTime createdAt;
  final DateTime updatedAt;

  /// Constructor
  TransferModel({
    String? id,
    required this.fromBankAccountId,
    required this.toBankAccountId,
    required this.amount,
    required this.description,
    DateTime? date,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : id = id ?? const Uuid().v4(),
       date = date ?? DateTime.now(),
       createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  /// Convert model to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'fromBankAccountId': fromBankAccountId,
      'toBankAccountId': toBankAccountId,
      'amount': amount,
      'description': description,
      'date': date.toIso8601String(),
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  /// Create model from JSON
  factory TransferModel.fromJson(Map<String, dynamic> json) {
    return TransferModel(
      id: json['id'],
      fromBankAccountId: json['fromBankAccountId'],
      toBankAccountId: json['toBankAccountId'],
      amount: json['amount'].toDouble(),
      description: json['description'],
      date: DateTime.parse(json['date']),
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
    );
  }

  /// Create a copy of this model with updated fields
  TransferModel copyWith({
    String? id,
    String? fromBankAccountId,
    String? toBankAccountId,
    double? amount,
    String? description,
    DateTime? date,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return TransferModel(
      id: id ?? this.id,
      fromBankAccountId: fromBankAccountId ?? this.fromBankAccountId,
      toBankAccountId: toBankAccountId ?? this.toBankAccountId,
      amount: amount ?? this.amount,
      description: description ?? this.description,
      date: date ?? this.date,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
