import 'package:uuid/uuid.dart';

/// Enum for recurring transaction frequency
enum RecurringFrequency {
  daily,
  weekly,
  monthly,
  quarterly,
  halfYearly,
  yearly,
}

/// Model class for recurring financial transactions
class RecurringTransactionModel {
  final String id;
  final String bankAccountId;
  final String description;
  final String category;
  final double amount;
  final RecurringFrequency frequency;
  final DateTime startDate;
  final DateTime nextDueDate;
  final DateTime? lastProcessedDate;
  final DateTime? endDate;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  /// Constructor
  RecurringTransactionModel({
    String? id,
    required this.bankAccountId,
    required this.description,
    required this.category,
    required this.amount,
    required this.frequency,
    required this.startDate,
    required this.nextDueDate,
    this.lastProcessedDate,
    this.endDate,
    this.isActive = true,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : id = id ?? const Uuid().v4(),
       createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  /// Check if the recurring transaction is due
  bool get isDue {
    final now = DateTime.now();
    return isActive &&
        nextDueDate.isBefore(now.add(const Duration(days: 1))) &&
        (endDate == null || nextDueDate.isBefore(endDate!));
  }

  /// Check if the recurring transaction has ended
  bool get hasEnded {
    return endDate != null && DateTime.now().isAfter(endDate!);
  }

  /// Get frequency display name
  String get frequencyDisplayName {
    switch (frequency) {
      case RecurringFrequency.daily:
        return 'Daily';
      case RecurringFrequency.weekly:
        return 'Weekly';
      case RecurringFrequency.monthly:
        return 'Monthly';
      case RecurringFrequency.quarterly:
        return 'Quarterly';
      case RecurringFrequency.halfYearly:
        return 'Half Yearly';
      case RecurringFrequency.yearly:
        return 'Yearly';
    }
  }

  /// Convert model to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'bankAccountId': bankAccountId,
      'description': description,
      'category': category,
      'amount': amount,
      'frequency': frequency.name,
      'startDate': startDate.toIso8601String(),
      'nextDueDate': nextDueDate.toIso8601String(),
      'lastProcessedDate': lastProcessedDate?.toIso8601String(),
      'endDate': endDate?.toIso8601String(),
      'isActive': isActive,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  /// Create model from JSON
  factory RecurringTransactionModel.fromJson(Map<String, dynamic> json) {
    return RecurringTransactionModel(
      id: json['id'],
      bankAccountId: json['bankAccountId'],
      description: json['description'],
      category: json['category'],
      amount: json['amount'].toDouble(),
      frequency: RecurringFrequency.values.firstWhere(
        (e) => e.name == json['frequency'],
      ),
      startDate: DateTime.parse(json['startDate']),
      nextDueDate: DateTime.parse(json['nextDueDate']),
      lastProcessedDate:
          json['lastProcessedDate'] != null
              ? DateTime.parse(json['lastProcessedDate'])
              : null,
      endDate: json['endDate'] != null ? DateTime.parse(json['endDate']) : null,
      isActive: json['isActive'] ?? true,
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
    );
  }

  /// Create a copy of this model with updated fields
  RecurringTransactionModel copyWith({
    String? id,
    String? bankAccountId,
    String? description,
    String? category,
    double? amount,
    RecurringFrequency? frequency,
    DateTime? startDate,
    DateTime? nextDueDate,
    DateTime? lastProcessedDate,
    DateTime? endDate,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return RecurringTransactionModel(
      id: id ?? this.id,
      bankAccountId: bankAccountId ?? this.bankAccountId,
      description: description ?? this.description,
      category: category ?? this.category,
      amount: amount ?? this.amount,
      frequency: frequency ?? this.frequency,
      startDate: startDate ?? this.startDate,
      nextDueDate: nextDueDate ?? this.nextDueDate,
      lastProcessedDate: lastProcessedDate ?? this.lastProcessedDate,
      endDate: endDate ?? this.endDate,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'RecurringTransactionModel(id: $id, description: $description, category: $category, amount: $amount, frequency: $frequency, nextDueDate: $nextDueDate, isActive: $isActive)';
  }
}
