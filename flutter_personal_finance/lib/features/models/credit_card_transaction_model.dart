import 'package:uuid/uuid.dart';

/// Enum for credit card transaction types
enum CreditCardTransactionType {
  purchase, // Money spent using credit card
  payment, // Payment made to credit card
  refund, // Refund received on credit card
  reversal, // Transaction reversal
}

/// Model class for credit card transactions
class CreditCardTransactionModel {
  final String id;
  final String creditCardId;
  final String?
  linkedBankAccountId; // For payments - which bank account was used
  final CreditCardTransactionType type;
  final double amount;
  final String description;
  final String category;
  final String? merchantName;
  final DateTime date;
  final DateTime createdAt;
  final DateTime updatedAt;

  /// Constructor
  CreditCardTransactionModel({
    String? id,
    required this.creditCardId,
    this.linkedBankAccountId,
    required this.type,
    required this.amount,
    required this.description,
    required this.category,
    this.merchantName,
    DateTime? date,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : id = id ?? const Uuid().v4(),
       date = date ?? DateTime.now(),
       createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  /// Get transaction type display name
  String get typeDisplayName {
    switch (type) {
      case CreditCardTransactionType.purchase:
        return 'Purchase';
      case CreditCardTransactionType.payment:
        return 'Payment';
      case CreditCardTransactionType.refund:
        return 'Refund';
      case CreditCardTransactionType.reversal:
        return 'Reversal';
    }
  }

  /// Check if this transaction affects credit card balance positively
  bool get isPositiveForCreditCard {
    return type == CreditCardTransactionType.payment ||
        type == CreditCardTransactionType.refund ||
        type == CreditCardTransactionType.reversal;
  }

  /// Check if this transaction affects credit card balance negatively
  bool get isNegativeForCreditCard {
    return type == CreditCardTransactionType.purchase;
  }

  /// Get the impact amount on credit card balance (negative = increases debt, positive = reduces debt)
  double get creditCardImpactAmount {
    if (isPositiveForCreditCard) {
      return -amount; // Reduces outstanding balance
    } else {
      return amount; // Increases outstanding balance
    }
  }

  /// Check if this transaction affects bank account (only payments do)
  bool get affectsBankAccount {
    return type == CreditCardTransactionType.payment &&
        linkedBankAccountId != null;
  }

  /// Convert model to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'creditCardId': creditCardId,
      'linkedBankAccountId': linkedBankAccountId,
      'type': type.name,
      'amount': amount,
      'description': description,
      'category': category,
      'merchantName': merchantName,
      'date': date.toIso8601String(),
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  /// Create model from JSON
  factory CreditCardTransactionModel.fromJson(Map<String, dynamic> json) {
    return CreditCardTransactionModel(
      id: json['id'],
      creditCardId: json['creditCardId'],
      linkedBankAccountId: json['linkedBankAccountId'],
      type: CreditCardTransactionType.values.firstWhere(
        (e) => e.name == json['type'],
      ),
      amount: json['amount'].toDouble(),
      description: json['description'],
      category: json['category'],
      merchantName: json['merchantName'],
      date: DateTime.parse(json['date']),
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
    );
  }

  /// Create a copy of this model with updated fields
  CreditCardTransactionModel copyWith({
    String? id,
    String? creditCardId,
    String? linkedBankAccountId,
    CreditCardTransactionType? type,
    double? amount,
    String? description,
    String? category,
    String? merchantName,
    DateTime? date,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return CreditCardTransactionModel(
      id: id ?? this.id,
      creditCardId: creditCardId ?? this.creditCardId,
      linkedBankAccountId: linkedBankAccountId ?? this.linkedBankAccountId,
      type: type ?? this.type,
      amount: amount ?? this.amount,
      description: description ?? this.description,
      category: category ?? this.category,
      merchantName: merchantName ?? this.merchantName,
      date: date ?? this.date,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'CreditCardTransactionModel(id: $id, type: $type, amount: $amount, '
        'description: $description, creditCardId: $creditCardId)';
  }
}
