import 'package:uuid/uuid.dart';

class UpcomingExpenseModel {
  final String id;
  final String category;
  final double amount;
  final DateTime dueDate;
  final bool isPaid;
  final String? bankAccountId; // Optional bank account for expense tracking
  final DateTime createdAt;
  final DateTime updatedAt;

  UpcomingExpenseModel({
    String? id,
    required this.category,
    required this.amount,
    required this.dueDate,
    this.isPaid = false,
    this.bankAccountId,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : id = id ?? const Uuid().v4(),
       createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  UpcomingExpenseModel copyWith({
    String? id,
    String? category,
    double? amount,
    DateTime? dueDate,
    bool? isPaid,
    String? bankAccountId,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return UpcomingExpenseModel(
      id: id ?? this.id,
      category: category ?? this.category,
      amount: amount ?? this.amount,
      dueDate: dueDate ?? this.dueDate,
      isPaid: isPaid ?? this.isPaid,
      bankAccountId: bankAccountId ?? this.bankAccountId,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'category': category,
      'amount': amount,
      'due_date': dueDate.toIso8601String(),
      'is_paid': isPaid,
      'bank_account_id': bankAccountId,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  factory UpcomingExpenseModel.fromJson(Map<String, dynamic> json) {
    return UpcomingExpenseModel(
      id: json['id'] as String,
      category: json['category'] as String,
      amount: (json['amount'] as num).toDouble(),
      dueDate: DateTime.parse(json['due_date'] as String),
      isPaid: json['is_paid'] as bool? ?? false,
      bankAccountId: json['bank_account_id'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  @override
  String toString() {
    return 'UpcomingExpenseModel(id: $id, category: $category, amount: $amount, dueDate: $dueDate, isPaid: $isPaid, bankAccountId: $bankAccountId)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UpcomingExpenseModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

class UpcomingExpenseCategoryModel {
  final String id;
  final String name;
  final String description;
  final DateTime createdAt;

  UpcomingExpenseCategoryModel({
    String? id,
    required this.name,
    this.description = '',
    DateTime? createdAt,
  }) : id = id ?? const Uuid().v4(),
       createdAt = createdAt ?? DateTime.now();

  UpcomingExpenseCategoryModel copyWith({
    String? id,
    String? name,
    String? description,
    DateTime? createdAt,
  }) {
    return UpcomingExpenseCategoryModel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'created_at': createdAt.toIso8601String(),
    };
  }

  factory UpcomingExpenseCategoryModel.fromJson(Map<String, dynamic> json) {
    return UpcomingExpenseCategoryModel(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String? ?? '',
      createdAt: DateTime.parse(json['created_at'] as String),
    );
  }

  @override
  String toString() {
    return 'UpcomingExpenseCategoryModel(id: $id, name: $name, description: $description)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UpcomingExpenseCategoryModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
