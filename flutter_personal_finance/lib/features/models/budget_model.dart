import 'package:uuid/uuid.dart';

/// Model class for budget planning
class BudgetModel {
  final String id;
  final String categoryName;
  final String categoryType; // 'income' or 'expense'
  final double monthlyLimit;
  final double currentSpent;
  final int year;
  final int month;
  final DateTime createdAt;
  final DateTime updatedAt;

  /// Constructor
  BudgetModel({
    String? id,
    required this.categoryName,
    required this.categoryType,
    required this.monthlyLimit,
    this.currentSpent = 0.0,
    required this.year,
    required this.month,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : id = id ?? const Uuid().v4(),
       createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  /// Get progress percentage
  double get progressPercentage {
    if (monthlyLimit <= 0) return 0.0;
    return (currentSpent / monthlyLimit) * 100;
  }

  /// Get remaining amount
  double get remainingAmount {
    return monthlyLimit - currentSpent;
  }

  /// Check if budget is exceeded
  bool get isExceeded {
    return currentSpent > monthlyLimit;
  }

  /// Get progress status
  String get progressStatus {
    if (isExceeded) return 'Exceeded';
    if (progressPercentage >= 90) return 'Warning';
    if (progressPercentage >= 75) return 'Caution';
    return 'Good';
  }

  /// Get progress color
  String get progressColor {
    if (isExceeded) return 'red';
    if (progressPercentage >= 90) return 'orange';
    if (progressPercentage >= 75) return 'yellow';
    return 'green';
  }

  /// Convert model to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'categoryName': categoryName,
      'categoryType': categoryType,
      'monthlyLimit': monthlyLimit,
      'currentSpent': currentSpent,
      'year': year,
      'month': month,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  /// Create model from JSON
  factory BudgetModel.fromJson(Map<String, dynamic> json) {
    return BudgetModel(
      id: json['id'],
      categoryName: json['categoryName'],
      categoryType: json['categoryType'],
      monthlyLimit: json['monthlyLimit'].toDouble(),
      currentSpent: json['currentSpent'].toDouble(),
      year: json['year'],
      month: json['month'],
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
    );
  }

  /// Create a copy of this model with updated fields
  BudgetModel copyWith({
    String? id,
    String? categoryName,
    String? categoryType,
    double? monthlyLimit,
    double? currentSpent,
    int? year,
    int? month,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return BudgetModel(
      id: id ?? this.id,
      categoryName: categoryName ?? this.categoryName,
      categoryType: categoryType ?? this.categoryType,
      monthlyLimit: monthlyLimit ?? this.monthlyLimit,
      currentSpent: currentSpent ?? this.currentSpent,
      year: year ?? this.year,
      month: month ?? this.month,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
