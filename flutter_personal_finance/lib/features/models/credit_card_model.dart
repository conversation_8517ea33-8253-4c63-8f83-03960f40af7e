import 'package:uuid/uuid.dart';

/// Enum for credit card types
enum CreditCardType { visa, mastercard, amex, rupay, discover, other }

/// Model class for credit cards
class CreditCardModel {
  final String id;
  final String cardName;
  final String cardNumber; // Last 4 digits only for security
  final CreditCardType cardType;
  final String bankName;
  final double creditLimit;
  final double outstandingBalance;
  final DateTime dueDate;
  final DateTime closingDate;
  final DateTime createdAt;
  final DateTime updatedAt;

  /// Constructor
  CreditCardModel({
    String? id,
    required this.cardName,
    required this.cardNumber,
    required this.cardType,
    required this.bankName,
    required this.creditLimit,
    required this.outstandingBalance,
    required this.dueDate,
    required this.closingDate,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : id = id ?? const Uuid().v4(),
       createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  /// Calculate available credit (credit limit - outstanding balance)
  double get availableCredit => creditLimit - outstandingBalance;

  /// Calculate utilization percentage
  double get utilizationPercentage =>
      creditLimit > 0 ? (outstandingBalance / creditLimit) * 100 : 0;

  /// Check if payment is due soon (within 3 days)
  bool get isPaymentDueSoon {
    final now = DateTime.now();
    final dueDiff = dueDate.difference(now).inDays;
    return dueDiff <= 3 && dueDiff >= 0;
  }

  /// Check if payment is overdue
  bool get isOverdue {
    final now = DateTime.now();
    return now.isAfter(dueDate) && outstandingBalance > 0;
  }

  /// Get card type display name
  String get cardTypeDisplayName {
    switch (cardType) {
      case CreditCardType.visa:
        return 'Visa';
      case CreditCardType.mastercard:
        return 'Mastercard';
      case CreditCardType.amex:
        return 'American Express';
      case CreditCardType.rupay:
        return 'RuPay';
      case CreditCardType.discover:
        return 'Discover';
      case CreditCardType.other:
        return 'Other';
    }
  }

  /// Get masked card number for display
  String get maskedCardNumber => '**** **** **** $cardNumber';

  /// Convert model to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'cardName': cardName,
      'cardNumber': cardNumber,
      'cardType': cardType.name,
      'bankName': bankName,
      'creditLimit': creditLimit,
      'outstandingBalance': outstandingBalance,
      'dueDate': dueDate.toIso8601String(),
      'closingDate': closingDate.toIso8601String(),
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  /// Create model from JSON
  factory CreditCardModel.fromJson(Map<String, dynamic> json) {
    return CreditCardModel(
      id: json['id'],
      cardName: json['cardName'],
      cardNumber: json['cardNumber'],
      cardType: CreditCardType.values.firstWhere(
        (e) => e.name == json['cardType'],
      ),
      bankName: json['bankName'],
      creditLimit: json['creditLimit'].toDouble(),
      outstandingBalance: json['outstandingBalance'].toDouble(),
      dueDate: DateTime.parse(json['dueDate']),
      closingDate: DateTime.parse(json['closingDate']),
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
    );
  }

  /// Create a copy of this model with updated fields
  CreditCardModel copyWith({
    String? id,
    String? cardName,
    String? cardNumber,
    CreditCardType? cardType,
    String? bankName,
    double? creditLimit,
    double? outstandingBalance,
    DateTime? dueDate,
    DateTime? closingDate,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return CreditCardModel(
      id: id ?? this.id,
      cardName: cardName ?? this.cardName,
      cardNumber: cardNumber ?? this.cardNumber,
      cardType: cardType ?? this.cardType,
      bankName: bankName ?? this.bankName,
      creditLimit: creditLimit ?? this.creditLimit,
      outstandingBalance: outstandingBalance ?? this.outstandingBalance,
      dueDate: dueDate ?? this.dueDate,
      closingDate: closingDate ?? this.closingDate,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'CreditCardModel(id: $id, cardName: $cardName, bankName: $bankName, '
        'outstandingBalance: $outstandingBalance, creditLimit: $creditLimit)';
  }
}
