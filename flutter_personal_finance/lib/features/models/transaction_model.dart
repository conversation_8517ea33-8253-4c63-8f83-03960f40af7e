import 'package:uuid/uuid.dart';

/// Enum for transaction types
enum TransactionType { credit, debit, transfer }

/// Model class for financial transactions
class TransactionModel {
  final String id;
  final String bankAccountId;
  final TransactionType type;
  final double amount;
  final String description;
  final String category;
  final DateTime date;
  final DateTime createdAt;
  final DateTime updatedAt;

  /// Constructor
  TransactionModel({
    String? id,
    required this.bankAccountId,
    required this.type,
    required this.amount,
    required this.description,
    required this.category,
    DateTime? date,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : id = id ?? const Uuid().v4(),
       date = date ?? DateTime.now(),
       createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  /// Convert model to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'bankAccountId': bankAccountId,
      'type': type.name,
      'amount': amount,
      'description': description,
      'category': category,
      'date': date.toIso8601String(),
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  /// Create model from JSON
  factory TransactionModel.fromJson(Map<String, dynamic> json) {
    return TransactionModel(
      id: json['id'],
      bankAccountId: json['bankAccountId'],
      type: TransactionType.values.firstWhere((e) => e.name == json['type']),
      amount: json['amount'].toDouble(),
      description: json['description'],
      category: json['category'],
      date: DateTime.parse(json['date']),
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
    );
  }

  /// Create a copy of this model with updated fields
  TransactionModel copyWith({
    String? id,
    String? bankAccountId,
    TransactionType? type,
    double? amount,
    String? description,
    String? category,
    DateTime? date,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return TransactionModel(
      id: id ?? this.id,
      bankAccountId: bankAccountId ?? this.bankAccountId,
      type: type ?? this.type,
      amount: amount ?? this.amount,
      description: description ?? this.description,
      category: category ?? this.category,
      date: date ?? this.date,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
