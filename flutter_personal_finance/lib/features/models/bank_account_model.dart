import 'package:uuid/uuid.dart';

/// Model class for bank account
class BankAccountModel {
  final String id;
  final String bankName;
  final String accountNumber;
  final double initialAmount;
  final double currentAmount;
  final DateTime createdAt;
  final DateTime updatedAt;

  /// Constructor
  BankAccountModel({
    String? id,
    required this.bankName,
    required this.accountNumber,
    required this.initialAmount,
    required this.currentAmount,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : id = id ?? const Uuid().v4(),
       createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  /// Convert model to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'bankName': bankName,
      'accountNumber': accountNumber,
      'initialAmount': initialAmount,
      'currentAmount': currentAmount,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  /// Create model from JSON
  factory BankAccountModel.fromJson(Map<String, dynamic> json) {
    return BankAccountModel(
      id: json['id'],
      bankName: json['bankName'],
      accountNumber: json['accountNumber'],
      initialAmount: json['initialAmount'].toDouble(),
      currentAmount: json['currentAmount'].toDouble(),
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
    );
  }

  /// Create a copy of this model with updated fields
  BankAccountModel copyWith({
    String? id,
    String? bankName,
    String? accountNumber,
    double? initialAmount,
    double? currentAmount,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return BankAccountModel(
      id: id ?? this.id,
      bankName: bankName ?? this.bankName,
      accountNumber: accountNumber ?? this.accountNumber,
      initialAmount: initialAmount ?? this.initialAmount,
      currentAmount: currentAmount ?? this.currentAmount,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
