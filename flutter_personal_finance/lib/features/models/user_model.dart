import 'package:uuid/uuid.dart';

/// Model class for user information
class UserModel {
  final String id;
  final String email;
  final String? password;
  final bool isFaceAuthEnabled;
  final DateTime createdAt;
  final DateTime updatedAt;
  final Map<String, dynamic>? apiData; // Store full API response

  /// Constructor
  UserModel({
    String? id,
    required this.email,
    this.password,
    this.isFaceAuthEnabled = false,
    DateTime? createdAt,
    DateTime? updatedAt,
    this.apiData,
  }) : id = id ?? const Uuid().v4(),
       createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  /// Create UserModel from API response
  factory UserModel.fromApiResponse(Map<String, dynamic> apiResponse) {
    return UserModel(
      id: apiResponse['id']?.toString(),
      email: apiResponse['email'] ?? '',
      password: apiResponse['password'],
      isFaceAuthEnabled: false, // Default value, can be updated later
      createdAt:
          apiResponse['created_at'] != null
              ? DateTime.tryParse(apiResponse['created_at']) ?? DateTime.now()
              : DateTime.now(),
      updatedAt:
          apiResponse['updated_at'] != null
              ? DateTime.tryParse(apiResponse['updated_at']) ?? DateTime.now()
              : DateTime.now(),
      apiData: apiResponse, // Store the complete API response
    );
  }

  /// Convert model to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'password': password,
      'isFaceAuthEnabled': isFaceAuthEnabled,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'apiData': apiData,
    };
  }

  /// Create model from JSON
  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['id'],
      email: json['email'],
      password: json['password'],
      isFaceAuthEnabled: json['isFaceAuthEnabled'] ?? false,
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
      apiData: json['apiData'],
    );
  }

  /// Create a copy of this model with updated fields
  UserModel copyWith({
    String? id,
    String? email,
    String? password,
    bool? isFaceAuthEnabled,
    DateTime? createdAt,
    DateTime? updatedAt,
    Map<String, dynamic>? apiData,
  }) {
    return UserModel(
      id: id ?? this.id,
      email: email ?? this.email,
      password: password ?? this.password,
      isFaceAuthEnabled: isFaceAuthEnabled ?? this.isFaceAuthEnabled,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      apiData: apiData ?? this.apiData,
    );
  }

  /// Get value from API data by key
  dynamic getApiValue(String key) {
    return apiData?[key];
  }

  /// Check if API data contains a specific key
  bool hasApiKey(String key) {
    return apiData?.containsKey(key) ?? false;
  }
}
