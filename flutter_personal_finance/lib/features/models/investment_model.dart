import 'dart:math';
import 'package:uuid/uuid.dart';

/// Enum for investment return types
enum InvestmentReturnType {
  simpleInterest,
  compoundInterest,
  exactAmount,
  manualEntry,
}

/// Enum for investment frequency
enum InvestmentFrequency { monthly, quarterly, halfYearly, yearly }

/// Enum for investment status
enum InvestmentStatus { active, matured, withdrawn, paused }

/// Enum for withdrawal types
enum WithdrawalType { specificAmount, percentage, profitsOnly, principalOnly }

/// Model class for individual investment entries
class InvestmentEntryModel {
  final String id;
  final String investmentId;
  final double amount;
  final DateTime date;
  final String description;
  final String transactionId; // Link to the original transaction
  final DateTime createdAt;

  InvestmentEntryModel({
    String? id,
    required this.investmentId,
    required this.amount,
    required this.date,
    required this.description,
    required this.transactionId,
    DateTime? createdAt,
  }) : id = id ?? const Uuid().v4(),
       createdAt = createdAt ?? DateTime.now();

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'investmentId': investmentId,
      'amount': amount,
      'date': date.toIso8601String(),
      'description': description,
      'transactionId': transactionId,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  factory InvestmentEntryModel.fromJson(Map<String, dynamic> json) {
    return InvestmentEntryModel(
      id: json['id'],
      investmentId: json['investmentId'],
      amount: json['amount'].toDouble(),
      date: DateTime.parse(json['date']),
      description: json['description'],
      transactionId: json['transactionId'],
      createdAt: DateTime.parse(json['createdAt']),
    );
  }

  InvestmentEntryModel copyWith({
    String? id,
    String? investmentId,
    double? amount,
    DateTime? date,
    String? description,
    String? transactionId,
    DateTime? createdAt,
  }) {
    return InvestmentEntryModel(
      id: id ?? this.id,
      investmentId: investmentId ?? this.investmentId,
      amount: amount ?? this.amount,
      date: date ?? this.date,
      description: description ?? this.description,
      transactionId: transactionId ?? this.transactionId,
      createdAt: createdAt ?? this.createdAt,
    );
  }
}

/// Model class for investment portfolios
class InvestmentModel {
  final String id;
  final String name;
  final String category;
  final String description;
  final double initialAmount;
  final double currentValue;
  final double totalInvested;
  final double totalReturns;
  final InvestmentReturnType returnType;
  final double expectedReturnRate; // Percentage per annum
  final InvestmentFrequency returnFrequency;
  final DateTime startDate;
  final DateTime? maturityDate;
  final InvestmentStatus status;
  final bool isAutoGrowthEnabled;
  final double
  autoGrowthRate; // For additional yearly growth beyond normal returns
  final List<String> categoryIds; // Categories linked to this investment
  final DateTime createdAt;
  final DateTime updatedAt;

  InvestmentModel({
    String? id,
    required this.name,
    required this.category,
    required this.description,
    required this.initialAmount,
    double? currentValue,
    double? totalInvested,
    double? totalReturns,
    required this.returnType,
    required this.expectedReturnRate,
    required this.returnFrequency,
    required this.startDate,
    this.maturityDate,
    InvestmentStatus? status,
    bool? isAutoGrowthEnabled,
    double? autoGrowthRate,
    List<String>? categoryIds,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : id = id ?? const Uuid().v4(),
       currentValue = currentValue ?? initialAmount,
       totalInvested = totalInvested ?? initialAmount,
       totalReturns = totalReturns ?? 0.0,
       status = status ?? InvestmentStatus.active,
       isAutoGrowthEnabled = isAutoGrowthEnabled ?? false,
       autoGrowthRate = autoGrowthRate ?? 0.0,
       categoryIds = categoryIds ?? [],
       createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  /// Calculate current value based on return type and time
  double calculateCurrentValue() {
    final now = DateTime.now();
    final daysPassed = now.difference(startDate).inDays;
    final yearsPassed = daysPassed / 365.0;

    switch (returnType) {
      case InvestmentReturnType.simpleInterest:
        return totalInvested * (1 + (expectedReturnRate / 100) * yearsPassed);

      case InvestmentReturnType.compoundInterest:
        final compoundingsPerYear = _getCompoundingsPerYear();
        return totalInvested *
            pow(
              1 + (expectedReturnRate / 100) / compoundingsPerYear,
              compoundingsPerYear * yearsPassed,
            );

      case InvestmentReturnType.exactAmount:
        if (maturityDate != null) {
          final totalDays = maturityDate!.difference(startDate).inDays;
          final progressRatio = daysPassed / totalDays;
          return totalInvested + (totalReturns * progressRatio);
        }
        return currentValue;

      case InvestmentReturnType.manualEntry:
        return currentValue;
    }
  }

  /// Get compoundings per year based on frequency
  int _getCompoundingsPerYear() {
    switch (returnFrequency) {
      case InvestmentFrequency.monthly:
        return 12;
      case InvestmentFrequency.quarterly:
        return 4;
      case InvestmentFrequency.halfYearly:
        return 2;
      case InvestmentFrequency.yearly:
        return 1;
    }
  }

  /// Calculate profit/loss
  double get profitLoss => currentValue - totalInvested;

  /// Calculate profit/loss percentage
  double get profitLossPercentage =>
      totalInvested > 0 ? (profitLoss / totalInvested) * 100 : 0.0;

  /// Check if investment has matured
  bool get isMatured =>
      maturityDate != null && DateTime.now().isAfter(maturityDate!);

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'category': category,
      'description': description,
      'initialAmount': initialAmount,
      'currentValue': currentValue,
      'totalInvested': totalInvested,
      'totalReturns': totalReturns,
      'returnType': returnType.name,
      'expectedReturnRate': expectedReturnRate,
      'returnFrequency': returnFrequency.name,
      'startDate': startDate.toIso8601String(),
      'maturityDate': maturityDate?.toIso8601String(),
      'status': status.name,
      'isAutoGrowthEnabled': isAutoGrowthEnabled,
      'autoGrowthRate': autoGrowthRate,
      'categoryIds': categoryIds,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  factory InvestmentModel.fromJson(Map<String, dynamic> json) {
    return InvestmentModel(
      id: json['id'],
      name: json['name'],
      category: json['category'],
      description: json['description'],
      initialAmount: json['initialAmount'].toDouble(),
      currentValue: json['currentValue']?.toDouble(),
      totalInvested: json['totalInvested']?.toDouble(),
      totalReturns: json['totalReturns']?.toDouble(),
      returnType: InvestmentReturnType.values.firstWhere(
        (e) => e.name == json['returnType'],
      ),
      expectedReturnRate: json['expectedReturnRate'].toDouble(),
      returnFrequency: InvestmentFrequency.values.firstWhere(
        (e) => e.name == json['returnFrequency'],
      ),
      startDate: DateTime.parse(json['startDate']),
      maturityDate:
          json['maturityDate'] != null
              ? DateTime.parse(json['maturityDate'])
              : null,
      status: InvestmentStatus.values.firstWhere(
        (e) => e.name == json['status'],
      ),
      isAutoGrowthEnabled: json['isAutoGrowthEnabled'] ?? false,
      autoGrowthRate: json['autoGrowthRate']?.toDouble() ?? 0.0,
      categoryIds: List<String>.from(json['categoryIds'] ?? []),
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
    );
  }

  InvestmentModel copyWith({
    String? id,
    String? name,
    String? category,
    String? description,
    double? initialAmount,
    double? currentValue,
    double? totalInvested,
    double? totalReturns,
    InvestmentReturnType? returnType,
    double? expectedReturnRate,
    InvestmentFrequency? returnFrequency,
    DateTime? startDate,
    DateTime? maturityDate,
    InvestmentStatus? status,
    bool? isAutoGrowthEnabled,
    double? autoGrowthRate,
    List<String>? categoryIds,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return InvestmentModel(
      id: id ?? this.id,
      name: name ?? this.name,
      category: category ?? this.category,
      description: description ?? this.description,
      initialAmount: initialAmount ?? this.initialAmount,
      currentValue: currentValue ?? this.currentValue,
      totalInvested: totalInvested ?? this.totalInvested,
      totalReturns: totalReturns ?? this.totalReturns,
      returnType: returnType ?? this.returnType,
      expectedReturnRate: expectedReturnRate ?? this.expectedReturnRate,
      returnFrequency: returnFrequency ?? this.returnFrequency,
      startDate: startDate ?? this.startDate,
      maturityDate: maturityDate ?? this.maturityDate,
      status: status ?? this.status,
      isAutoGrowthEnabled: isAutoGrowthEnabled ?? this.isAutoGrowthEnabled,
      autoGrowthRate: autoGrowthRate ?? this.autoGrowthRate,
      categoryIds: categoryIds ?? this.categoryIds,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
    );
  }
}

/// Model for investment withdrawal transactions
class InvestmentWithdrawalModel {
  final String id;
  final String investmentId;
  final double amount;
  final DateTime date;
  final String reason;
  final double penaltyAmount;
  final DateTime createdAt;

  InvestmentWithdrawalModel({
    String? id,
    required this.investmentId,
    required this.amount,
    required this.date,
    required this.reason,
    double? penaltyAmount,
    DateTime? createdAt,
  }) : id = id ?? const Uuid().v4(),
       penaltyAmount = penaltyAmount ?? 0.0,
       createdAt = createdAt ?? DateTime.now();

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'investmentId': investmentId,
      'amount': amount,
      'date': date.toIso8601String(),
      'reason': reason,
      'penaltyAmount': penaltyAmount,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  factory InvestmentWithdrawalModel.fromJson(Map<String, dynamic> json) {
    return InvestmentWithdrawalModel(
      id: json['id'],
      investmentId: json['investmentId'],
      amount: json['amount'].toDouble(),
      date: DateTime.parse(json['date']),
      reason: json['reason'],
      penaltyAmount: json['penaltyAmount']?.toDouble() ?? 0.0,
      createdAt: DateTime.parse(json['createdAt']),
    );
  }

  InvestmentWithdrawalModel copyWith({
    String? id,
    String? investmentId,
    double? amount,
    DateTime? date,
    String? reason,
    double? penaltyAmount,
    DateTime? createdAt,
  }) {
    return InvestmentWithdrawalModel(
      id: id ?? this.id,
      investmentId: investmentId ?? this.investmentId,
      amount: amount ?? this.amount,
      date: date ?? this.date,
      reason: reason ?? this.reason,
      penaltyAmount: penaltyAmount ?? this.penaltyAmount,
      createdAt: createdAt ?? this.createdAt,
    );
  }
}

/// Data class for withdrawal calculations
class InvestmentWithdrawalCalculation {
  final double withdrawalAmount;
  final double penaltyAmount;
  final double netAmount;
  final double remainingValue;
  final bool isFullWithdrawal;

  InvestmentWithdrawalCalculation({
    required this.withdrawalAmount,
    required this.penaltyAmount,
    required this.netAmount,
    required this.remainingValue,
    required this.isFullWithdrawal,
  });
}

/// Data class for withdrawal analytics
class InvestmentWithdrawalAnalytics {
  final int totalWithdrawals;
  final double totalWithdrawnAmount;
  final double totalPenalties;
  final List<InvestmentWithdrawalModel> withdrawals;

  InvestmentWithdrawalAnalytics({
    required this.totalWithdrawals,
    required this.totalWithdrawnAmount,
    required this.totalPenalties,
    required this.withdrawals,
  });
}
