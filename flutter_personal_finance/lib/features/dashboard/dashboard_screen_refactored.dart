import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:go_router/go_router.dart';

import '../../services/local_storage_service.dart';
import '../../services/credit_card_service.dart';
import '../../utils/helper/route.dart';

import '../../features/models/bank_account_model.dart';
import '../../features/models/transaction_model.dart';
import '../../features/models/recurring_transaction_model.dart';
import '../../features/models/credit_card_model.dart';

// Import the extracted widget sections
import 'widgets/balance_section.dart';
import 'widgets/bank_accounts_section.dart';
import 'widgets/recent_transactions_section.dart';
import 'widgets/credit_cards_section.dart';
import 'widgets/placeholder_sections.dart';

class DashboardScreenRefactored extends StatefulWidget {
  const DashboardScreenRefactored({super.key});

  @override
  State<DashboardScreenRefactored> createState() =>
      _DashboardScreenRefactoredState();
}

class _DashboardScreenRefactoredState extends State<DashboardScreenRefactored> {
  List<BankAccountModel> _bankAccounts = [];
  List<TransactionModel> _recentTransactions = [];
  List<TransactionModel> _allTransactions = [];
  double _totalBalance = 0.0;
  double _totalIncome = 0.0;
  double _totalExpenses = 0.0;
  double _totalUpcomingExpenses = 0.0;

  // Spending trends data
  List<Map<String, dynamic>> _weeklyTrends = [];
  List<Map<String, dynamic>> _monthlyTrends = [];
  String _selectedTrendPeriod = 'monthly'; // 'weekly' or 'monthly'

  // Cash flow summary data
  List<Map<String, dynamic>> _cashFlowData = [];
  Map<String, double> _categoryIncome = {};
  Map<String, double> _categoryExpenses = {};
  String _selectedCashFlowPeriod = 'monthly'; // 'weekly', 'monthly', 'yearly'

  // Top categories data
  List<Map<String, dynamic>> _topExpenseCategories = [];
  List<Map<String, dynamic>> _topIncomeCategories = [];
  String _selectedCategoryPeriod = 'all'; // 'all', 'monthly', 'yearly'

  // Recurring transactions data
  List<RecurringTransactionModel> _recurringTransactions = [];
  int _dueRecurringTransactionsCount = 0;
  double _totalMonthlyRecurringAmount = 0.0;

  // Credit card data
  List<CreditCardModel> _creditCards = [];
  double _totalCreditCardDebt = 0.0;
  double _totalCreditLimit = 0.0;
  int _overdueCardsCount = 0;
  int _dueSoonCardsCount = 0;

  // Visibility toggle for income and bank balance
  bool _isBalanceVisible = true;
  static const String _balanceVisibilityKey = 'balance_visibility';

  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadVisibilityPreference();
    _loadData();
  }

  /// Load visibility preference from local storage
  Future<void> _loadVisibilityPreference() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final savedVisibility = prefs.getBool(_balanceVisibilityKey) ?? true;
      setState(() {
        _isBalanceVisible = savedVisibility;
      });
    } catch (e) {
      print('Error loading visibility preference: $e');
      // Use default value (true) if error occurs
      setState(() {
        _isBalanceVisible = true;
      });
    }
  }

  /// Save visibility preference to local storage
  Future<void> _saveVisibilityPreference(bool isVisible) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_balanceVisibilityKey, isVisible);
    } catch (e) {
      print('Error saving visibility preference: $e');
    }
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Load all data in parallel for better performance
      await Future.wait([
        _loadBankAccounts(),
        _loadTransactions(),
        _loadUpcomingExpenses(),
        _loadRecurringTransactions(),
        _loadCreditCards(),
      ]);

      _calculateTotals();
      _calculateSpendingTrends(_allTransactions);
      _calculateCashFlowSummary(_allTransactions);
      _calculateTopCategories(_allTransactions);
    } catch (e) {
      print('Error loading dashboard data: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading data: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _loadBankAccounts() async {
    try {
      final accounts = await LocalStorageService.getBankAccounts();
      if (mounted) {
        setState(() {
          _bankAccounts = accounts;
        });
      }
    } catch (e) {
      print('Error loading bank accounts: $e');
    }
  }

  Future<void> _loadTransactions() async {
    try {
      final transactions = await LocalStorageService.getTransactions();
      if (mounted) {
        setState(() {
          _allTransactions = transactions;
          // Get the 5 most recent transactions
          _recentTransactions = transactions.take(5).toList();
        });
      }
    } catch (e) {
      print('Error loading transactions: $e');
    }
  }

  Future<void> _loadUpcomingExpenses() async {
    try {
      final expenses = await LocalStorageService.getUpcomingExpenses();
      final total = expenses.fold<double>(
        0.0,
        (sum, expense) => sum + expense.amount,
      );
      if (mounted) {
        setState(() {
          _totalUpcomingExpenses = total;
        });
      }
    } catch (e) {
      print('Error loading upcoming expenses: $e');
    }
  }

  Future<void> _loadRecurringTransactions() async {
    try {
      final transactions = await LocalStorageService.getRecurringTransactions();
      if (mounted) {
        setState(() {
          _recurringTransactions = transactions;
          _calculateRecurringStats();
        });
      }
    } catch (e) {
      print('Error loading recurring transactions: $e');
    }
  }

  Future<void> _loadCreditCards() async {
    try {
      final cards = await CreditCardService.getCreditCards();
      if (mounted) {
        setState(() {
          _creditCards = cards;
          _calculateCreditCardStats();
        });
      }
    } catch (e) {
      print('Error loading credit cards: $e');
    }
  }

  void _calculateTotals() {
    _totalBalance = _bankAccounts.fold(
      0.0,
      (sum, account) => sum + account.currentAmount,
    );
    _totalIncome = _allTransactions
        .where((t) => t.type == TransactionType.credit)
        .fold(0.0, (sum, t) => sum + t.amount);
    _totalExpenses = _allTransactions
        .where((t) => t.type == TransactionType.debit)
        .fold(0.0, (sum, t) => sum + t.amount);
  }

  void _calculateRecurringStats() {
    // Placeholder for recurring transaction calculations
    _dueRecurringTransactionsCount = _recurringTransactions.length;
    _totalMonthlyRecurringAmount = _recurringTransactions.fold(
      0.0,
      (sum, transaction) => sum + transaction.amount,
    );
  }

  void _calculateCreditCardStats() {
    _totalCreditCardDebt = _creditCards.fold(
      0.0,
      (sum, card) => sum + card.outstandingBalance,
    );
    _totalCreditLimit = _creditCards.fold(
      0.0,
      (sum, card) => sum + card.creditLimit,
    );
    _overdueCardsCount = 0; // Placeholder calculation
    _dueSoonCardsCount = 0; // Placeholder calculation
  }

  void _calculateSpendingTrends(List<TransactionModel> transactions) {
    // Placeholder for spending trends calculation
    _weeklyTrends = [];
    _monthlyTrends = [];
  }

  void _calculateCashFlowSummary(List<TransactionModel> transactions) {
    // Placeholder for cash flow calculation
    _cashFlowData = [];
    _categoryIncome = {};
    _categoryExpenses = {};
  }

  void _calculateTopCategories(List<TransactionModel> transactions) {
    // Placeholder for top categories calculation
    _topExpenseCategories = [];
    _topIncomeCategories = [];
  }

  void _toggleBalanceVisibility() async {
    final newVisibility = !_isBalanceVisible;
    setState(() {
      _isBalanceVisible = newVisibility;
    });
    // Save preference to local storage
    await _saveVisibilityPreference(newVisibility);
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        title: Text(
          'Personal Finance',
          style: theme.appBarTheme.titleTextStyle?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          // Add Transaction Button
          Container(
            margin: const EdgeInsets.only(right: 8),
            child: ElevatedButton.icon(
              onPressed: () async {
                // Navigate to add transaction screen using GoRouter
                await GoRouter.of(
                  context,
                ).pushNamed(GoRouterConstants.addTransaction);
                // Refresh dashboard data when user returns
                _loadData();
              },
              icon: const Icon(Icons.add, size: 20),
              label: const Text(
                'Add',
                style: TextStyle(fontWeight: FontWeight.w600, fontSize: 14),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
                elevation: 2,
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 8,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20),
                ),
              ),
            ),
          ),
          // Profile Button
          IconButton(
            icon: const Icon(Icons.person),
            onPressed: () async {
              await GoRouter.of(context).pushNamed(GoRouterConstants.profile);
              // Refresh data when returning from profile (in case settings changed)
              _loadData();
            },
          ),
        ],
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : RefreshIndicator(
                onRefresh: _loadData,
                child: SingleChildScrollView(
                  physics: const AlwaysScrollableScrollPhysics(),
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      // Balance Section
                      BalanceSection(
                        totalBalance: _totalBalance,
                        totalIncome: _totalIncome,
                        totalExpenses: _totalExpenses,
                        totalUpcomingExpenses: _totalUpcomingExpenses,
                        isBalanceVisible: _isBalanceVisible,
                        onVisibilityToggle: _toggleBalanceVisibility,
                        onRefreshData: _loadData,
                        bankAccounts: _bankAccounts,
                      ),
                      const SizedBox(height: 20),

                      // Bank Accounts Section
                      BankAccountsSection(
                        bankAccounts: _bankAccounts,
                        isBalanceVisible: _isBalanceVisible,
                        onRefreshData: _loadData,
                      ),
                      const SizedBox(height: 20),

                      // Recent Transactions Section
                      RecentTransactionsSection(
                        recentTransactions: _recentTransactions,
                        bankAccounts: _bankAccounts,
                        isBalanceVisible: _isBalanceVisible,
                        onRefreshData: _loadData,
                      ),
                      const SizedBox(height: 20),

                      // Credit Cards Section
                      CreditCardsSection(
                        creditCards: _creditCards,
                        totalCreditCardDebt: _totalCreditCardDebt,
                        totalCreditLimit: _totalCreditLimit,
                        overdueCardsCount: _overdueCardsCount,
                        dueSoonCardsCount: _dueSoonCardsCount,
                        isBalanceVisible: _isBalanceVisible,
                        onRefreshData: _loadData,
                      ),
                      const SizedBox(height: 20),

                      // Cash Flow Summary Section
                      CashFlowSummarySection(
                        cashFlowData: _cashFlowData,
                        categoryIncome: _categoryIncome,
                        categoryExpenses: _categoryExpenses,
                        selectedCashFlowPeriod: _selectedCashFlowPeriod,
                        onPeriodChanged: (period) {
                          setState(() {
                            _selectedCashFlowPeriod = period;
                          });
                          _calculateCashFlowSummary(_allTransactions);
                        },
                      ),
                      const SizedBox(height: 20),

                      // Spending Trends Section
                      SpendingTrendsSection(
                        weeklyTrends: _weeklyTrends,
                        monthlyTrends: _monthlyTrends,
                        selectedTrendPeriod: _selectedTrendPeriod,
                        onPeriodChanged: (period) {
                          setState(() {
                            _selectedTrendPeriod = period;
                          });
                          _calculateSpendingTrends(_allTransactions);
                        },
                      ),
                      const SizedBox(height: 20),

                      // Top Categories Section
                      TopCategoriesSection(
                        topExpenseCategories: _topExpenseCategories,
                        topIncomeCategories: _topIncomeCategories,
                        selectedCategoryPeriod: _selectedCategoryPeriod,
                        onPeriodChanged: (period) {
                          setState(() {
                            _selectedCategoryPeriod = period;
                          });
                          _calculateTopCategories(_allTransactions);
                        },
                      ),
                      const SizedBox(height: 20),

                      // Recurring Transactions Section
                      if (_recurringTransactions.isNotEmpty) ...[
                        RecurringTransactionsSection(
                          recurringTransactions: _recurringTransactions,
                          dueRecurringTransactionsCount:
                              _dueRecurringTransactionsCount,
                          totalMonthlyRecurringAmount:
                              _totalMonthlyRecurringAmount,
                          isBalanceVisible: _isBalanceVisible,
                        ),
                        const SizedBox(height: 24),
                      ],
                    ],
                  ),
                ),
              ),
    );
  }
}
