import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:go_router/go_router.dart';
import 'package:easy_localization/easy_localization.dart';

import '../../services/local_storage_service.dart';
import '../../services/notification_service.dart';
import '../../services/recurring_transaction_service.dart';
import '../../services/credit_card_service.dart';
import '../../utils/helper/route.dart';

import '../../features/models/bank_account_model.dart';
import '../../features/models/transaction_model.dart';
import '../../features/models/upcoming_expense_model.dart';
import '../../features/models/recurring_transaction_model.dart';
import '../../features/models/credit_card_model.dart';

import '../../resources/app_theme.dart';
import '../../resources/app_text_styles.dart';

class DashboardScreen extends StatefulWidget {
  const DashboardScreen({super.key});

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> {
  List<BankAccountModel> _bankAccounts = [];
  List<TransactionModel> _recentTransactions = [];
  List<TransactionModel> _allTransactions = [];
  double _totalBalance = 0.0;
  // ignore: unused_field
  double _totalIncome = 0.0; // Keep for compatibility/future use
  // ignore: unused_field
  double _totalExpenses = 0.0; // Keep for compatibility/future use
  double _currentMonthIncome = 0.0;
  double _currentMonthExpenses = 0.0;
  double _totalUpcomingExpenses = 0.0;

  // Spending trends data
  List<Map<String, dynamic>> _weeklyTrends = [];
  List<Map<String, dynamic>> _monthlyTrends = [];
  String _selectedTrendPeriod = 'monthly'; // 'weekly' or 'monthly'

  // Cash flow summary data
  List<Map<String, dynamic>> _cashFlowData = [];
  Map<String, double> _categoryIncome = {};
  Map<String, double> _categoryExpenses = {};
  String _selectedCashFlowPeriod = 'monthly'; // 'weekly', 'monthly', 'yearly'

  // Top categories data
  List<Map<String, dynamic>> _topExpenseCategories = [];
  List<Map<String, dynamic>> _topIncomeCategories = [];
  String _selectedCategoryPeriod = 'all'; // 'all', 'monthly', 'yearly'

  // Recurring transactions data
  List<RecurringTransactionModel> _recurringTransactions = [];
  int _dueRecurringTransactionsCount = 0;
  double _totalMonthlyRecurringAmount = 0.0;
  DateTime? _lastRecurringProcessingTime;

  // Credit card data
  List<CreditCardModel> _creditCards = [];
  double _totalCreditCardDebt = 0.0;
  double _totalCreditLimit = 0.0;

  int _dueSoonCardsCount = 0;

  // Visibility toggle for income and bank balance
  bool _isBalanceVisible = true;
  static const String _balanceVisibilityKey = 'balance_visibility';

  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadVisibilityPreference();
    _loadData();
  }

  /// Load visibility preference from local storage
  Future<void> _loadVisibilityPreference() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final savedVisibility = prefs.getBool(_balanceVisibilityKey) ?? true;
      setState(() {
        _isBalanceVisible = savedVisibility;
      });
    } catch (e) {
      print('Error loading visibility preference: $e');
      // Use default value (true) if error occurs
      setState(() {
        _isBalanceVisible = true;
      });
    }
  }

  /// Save visibility preference to local storage
  Future<void> _saveVisibilityPreference(bool isVisible) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_balanceVisibilityKey, isVisible);
    } catch (e) {
      print('Error saving visibility preference: $e');
    }
  }

  Future<void> _loadData() async {
    await _loadDataInternal(updateBalances: true);
  }

  Future<void> _processRecurringTransactions() async {
    try {
      final result =
          await RecurringTransactionService.processDueRecurringTransactions();

      final processedCount = result['processed'] as int;
      final failedCount = result['failed'] as int;
      final processedDescriptions =
          result['processedDescriptions'] as List<String>;

      if (processedCount > 0) {
        // Show success message if transactions were processed
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                processedCount == 1
                    ? 'dashboard_processed_1_recurring_transaction'.tr(
                      namedArgs: {'description': processedDescriptions.first},
                    )
                    : 'dashboard_processed_recurring_transactions'.tr(
                      namedArgs: {'count': processedCount.toString()},
                    ),
              ),
              backgroundColor: Colors.green,
              duration: const Duration(seconds: 4),
            ),
          );
        }
      }

      if (failedCount > 0) {
        // Show warning if some transactions failed
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'dashboard_failed_to_process_recurring_transactions'.tr(
                  namedArgs: {'count': failedCount.toString()},
                ),
              ),
              backgroundColor: Colors.orange,
              duration: const Duration(seconds: 3),
            ),
          );
        }
      }
    } catch (e) {
      print('Error processing recurring transactions: $e');
    }
  }

  Future<void> _loadDataAfterBankEdit() async {
    await _loadDataInternal(updateBalances: false);
  }

  Future<void> _loadDataInternal({required bool updateBalances}) async {
    try {
      // Update bank account balances first only if not coming from bank edit
      if (updateBalances) {
        await LocalStorageService.updateBankAccountBalances();
      }

      // Load bank accounts from local storage
      final bankAccounts = await LocalStorageService.getBankAccounts();

      // Load all transactions from local storage
      final allTransactions = await LocalStorageService.getTransactions();

      // Get recent transactions (last 5)
      final recentTransactions = allTransactions.take(5).toList();

      // Calculate totals for each transaction type
      final totalIncome = allTransactions
          .where(
            (t) =>
                t.type == TransactionType.credit &&
                t.category != 'Self Transfer',
          )
          .fold<double>(0.0, (sum, t) => sum + t.amount);

      final totalExpenses = allTransactions
          .where(
            (t) =>
                t.type == TransactionType.debit &&
                t.category != 'Self Transfer',
          )
          .fold<double>(0.0, (sum, t) => sum + t.amount);

      // Calculate current month income and expenses (for accurate dashboard display)
      // Fixed: Dashboard now shows current month data instead of all-time totals
      final now = DateTime.now();
      final currentMonthStart = DateTime(now.year, now.month, 1);
      final currentMonthEnd = DateTime(now.year, now.month + 1, 0, 23, 59, 59);

      final currentMonthTransactions =
          allTransactions
              .where(
                (t) =>
                    (t.date.isAtSameMomentAs(currentMonthStart) ||
                        t.date.isAfter(currentMonthStart)) &&
                    (t.date.isAtSameMomentAs(currentMonthEnd) ||
                        t.date.isBefore(currentMonthEnd)),
              )
              .toList();

      final currentMonthIncome = currentMonthTransactions
          .where(
            (t) =>
                t.type == TransactionType.credit &&
                t.category != 'Self Transfer',
          )
          .fold<double>(0.0, (sum, t) => sum + t.amount);

      final currentMonthExpenses = currentMonthTransactions
          .where(
            (t) =>
                t.type == TransactionType.debit &&
                t.category != 'Self Transfer',
          )
          .fold<double>(0.0, (sum, t) => sum + t.amount);

      // Calculate total upcoming expenses (only unpaid ones)
      final allUpcomingExpenses =
          await LocalStorageService.getUpcomingExpenses();
      final totalUpcomingExpenses = allUpcomingExpenses
          .where((expense) => !expense.isPaid)
          .fold<double>(0.0, (sum, expense) => sum + expense.amount);

      // Calculate total balance from local data (including credit card debt)
      final bankBalance = bankAccounts.fold<double>(
        0.0,
        (sum, account) => sum + account.currentAmount,
      );

      // Get total credit card debt to subtract from net worth
      final totalCreditCardDebt =
          await CreditCardService.getTotalOutstandingBalance();

      // Net worth = Bank Balance - Credit Card Debt
      final totalBalance = bankBalance - totalCreditCardDebt;

      // Calculate spending trends
      _calculateSpendingTrends(allTransactions);

      // Calculate cash flow summary
      _calculateCashFlowSummary(allTransactions);

      // Calculate top categories
      _calculateTopCategories(allTransactions);

      // Load recurring transactions data
      await _loadRecurringTransactionsData();

      // Update credit card dates first, then load credit card data
      await CreditCardService.updateCreditCardDatesAndNotifications();
      await _loadCreditCardData();

      setState(() {
        _bankAccounts = bankAccounts;
        _allTransactions = allTransactions;
        _totalBalance = totalBalance;
        _totalIncome = totalIncome;
        _totalExpenses = totalExpenses;
        _currentMonthIncome = currentMonthIncome;
        _currentMonthExpenses = currentMonthExpenses;
        _totalUpcomingExpenses = totalUpcomingExpenses;
        _recentTransactions = recentTransactions;
        _isLoading = false;
      });
    } catch (e) {
      print('Error loading data: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _calculateSpendingTrends(List<TransactionModel> transactions) {
    final now = DateTime.now();
    final expenses =
        transactions
            .where(
              (t) =>
                  t.type == TransactionType.debit &&
                  t.category != 'Self Transfer',
            )
            .toList();

    // Calculate weekly trends for all weeks of current month
    _weeklyTrends = [];
    final monthStart = DateTime(now.year, now.month, 1);
    final monthEnd = DateTime(now.year, now.month + 1, 0);

    // Find the Monday of the week containing the first day of the month
    final firstWeekStart = monthStart.subtract(
      Duration(days: monthStart.weekday - 1),
    );

    // Calculate how many weeks we need to cover the entire month
    final totalDays = monthEnd.difference(firstWeekStart).inDays + 1;
    final totalWeeks = (totalDays / 7).ceil();

    for (int i = 0; i < totalWeeks; i++) {
      final weekStart = firstWeekStart.add(Duration(days: i * 7));
      final weekEnd = weekStart.add(
        const Duration(days: 6, hours: 23, minutes: 59, seconds: 59),
      );

      // Only include weeks that have at least one day in the current month
      if (weekStart.isBefore(monthEnd.add(const Duration(days: 1))) &&
          weekEnd.isAfter(monthStart.subtract(const Duration(days: 1)))) {
        final weekExpenses =
            expenses
                .where(
                  (t) =>
                      (t.date.isAtSameMomentAs(weekStart) ||
                          t.date.isAfter(weekStart)) &&
                      (t.date.isAtSameMomentAs(weekEnd) ||
                          t.date.isBefore(weekEnd)),
                )
                .toList();

        final totalAmount = weekExpenses.fold<double>(
          0.0,
          (sum, t) => sum + t.amount,
        );

        // Calculate week number in the month
        final weekNumber = i + 1;
        final weekLabel = 'dashboard_week'.tr(
          namedArgs: {'number': weekNumber.toString()},
        );

        _weeklyTrends.add({
          'period': weekLabel,
          'amount': totalAmount,
          'date': weekStart,
          'formattedDate':
              '${DateFormat('MMM dd').format(weekStart)} - ${DateFormat('MMM dd').format(weekEnd)}',
        });
      }
    }

    // Calculate monthly trends (last 6 months)
    _monthlyTrends = [];
    for (int i = 5; i >= 0; i--) {
      final monthStart = DateTime(now.year, now.month - i, 1);
      final monthEnd = DateTime(now.year, now.month - i + 1, 0, 23, 59, 59);

      final monthExpenses =
          expenses
              .where(
                (t) =>
                    (t.date.isAtSameMomentAs(monthStart) ||
                        t.date.isAfter(monthStart)) &&
                    (t.date.isAtSameMomentAs(monthEnd) ||
                        t.date.isBefore(monthEnd)),
              )
              .toList();

      final totalAmount = monthExpenses.fold<double>(
        0.0,
        (sum, t) => sum + t.amount,
      );

      _monthlyTrends.add({
        'period': DateFormat('MMM yy').format(monthStart),
        'amount': totalAmount,
        'date': monthStart,
        'formattedDate': DateFormat('MMMM yyyy').format(monthStart),
      });
    }
  }

  void _calculateCashFlowSummary(List<TransactionModel> transactions) {
    final now = DateTime.now();

    // Calculate cash flow data for different periods
    _cashFlowData = [];

    // Get transactions for the selected period
    switch (_selectedCashFlowPeriod) {
      case 'weekly':
        // All weeks of current month
        final monthStart = DateTime(now.year, now.month, 1);
        final monthEnd = DateTime(now.year, now.month + 1, 0);

        // Find the Monday of the week containing the first day of the month
        final firstWeekStart = monthStart.subtract(
          Duration(days: monthStart.weekday - 1),
        );

        // Calculate how many weeks we need to cover the entire month
        final totalDays = monthEnd.difference(firstWeekStart).inDays + 1;
        final totalWeeks = (totalDays / 7).ceil();

        for (int i = 0; i < totalWeeks; i++) {
          final weekStart = firstWeekStart.add(Duration(days: i * 7));
          final weekEnd = weekStart.add(
            const Duration(days: 6, hours: 23, minutes: 59, seconds: 59),
          );

          // Only include weeks that have at least one day in the current month
          if (weekStart.isBefore(monthEnd.add(const Duration(days: 1))) &&
              weekEnd.isAfter(monthStart.subtract(const Duration(days: 1)))) {
            final weekTransactions =
                transactions
                    .where(
                      (t) =>
                          (t.date.isAtSameMomentAs(weekStart) ||
                              t.date.isAfter(weekStart)) &&
                          (t.date.isAtSameMomentAs(weekEnd) ||
                              t.date.isBefore(weekEnd)),
                    )
                    .toList();

            final weekIncome = weekTransactions
                .where(
                  (t) =>
                      t.type == TransactionType.credit &&
                      t.category != 'Self Transfer',
                )
                .fold<double>(0.0, (sum, t) => sum + t.amount);

            final weekExpenses = weekTransactions
                .where(
                  (t) =>
                      t.type == TransactionType.debit &&
                      t.category != 'Self Transfer',
                )
                .fold<double>(0.0, (sum, t) => sum + t.amount);

            // Calculate week number in the month
            final weekNumber = i + 1;
            final weekLabel = 'dashboard_week'.tr(
              namedArgs: {'number': weekNumber.toString()},
            );

            _cashFlowData.add({
              'period': weekLabel,
              'income': weekIncome,
              'expenses': weekExpenses,
              'net': weekIncome - weekExpenses,
              'formattedDate':
                  '${DateFormat('MMM dd').format(weekStart)} - ${DateFormat('MMM dd').format(weekEnd)}',
            });
          }
        }
        break;

      case 'monthly':
        // Last 6 months
        for (int i = 5; i >= 0; i--) {
          final monthStart = DateTime(now.year, now.month - i, 1);
          final monthEnd = DateTime(now.year, now.month - i + 1, 0, 23, 59, 59);

          final monthTransactions =
              transactions
                  .where(
                    (t) =>
                        (t.date.isAtSameMomentAs(monthStart) ||
                            t.date.isAfter(monthStart)) &&
                        (t.date.isAtSameMomentAs(monthEnd) ||
                            t.date.isBefore(monthEnd)),
                  )
                  .toList();

          final monthIncome = monthTransactions
              .where(
                (t) =>
                    t.type == TransactionType.credit &&
                    t.category != 'Self Transfer',
              )
              .fold<double>(0.0, (sum, t) => sum + t.amount);

          final monthExpenses = monthTransactions
              .where(
                (t) =>
                    t.type == TransactionType.debit &&
                    t.category != 'Self Transfer',
              )
              .fold<double>(0.0, (sum, t) => sum + t.amount);

          _cashFlowData.add({
            'period': DateFormat('MMM yy').format(monthStart),
            'income': monthIncome,
            'expenses': monthExpenses,
            'net': monthIncome - monthExpenses,
            'formattedDate': DateFormat('MMMM yyyy').format(monthStart),
          });
        }
        break;

      case 'yearly':
        // Last 3 years
        for (int i = 2; i >= 0; i--) {
          final yearStart = DateTime(now.year - i, 1, 1);
          final yearEnd = DateTime(now.year - i, 12, 31);

          final yearTransactions =
              transactions
                  .where(
                    (t) =>
                        (t.date.isAtSameMomentAs(yearStart) ||
                            t.date.isAfter(yearStart)) &&
                        (t.date.isAtSameMomentAs(yearEnd) ||
                            t.date.isBefore(yearEnd)),
                  )
                  .toList();

          final yearIncome = yearTransactions
              .where(
                (t) =>
                    t.type == TransactionType.credit &&
                    t.category != 'Self Transfer',
              )
              .fold<double>(0.0, (sum, t) => sum + t.amount);

          final yearExpenses = yearTransactions
              .where(
                (t) =>
                    t.type == TransactionType.debit &&
                    t.category != 'Self Transfer',
              )
              .fold<double>(0.0, (sum, t) => sum + t.amount);

          _cashFlowData.add({
            'period': '${now.year - i}',
            'income': yearIncome,
            'expenses': yearExpenses,
            'net': yearIncome - yearExpenses,
            'formattedDate': '${now.year - i}',
          });
        }
        break;
    }

    // Calculate category breakdown for income and expenses
    _categoryIncome.clear();
    _categoryExpenses.clear();

    for (final transaction in transactions) {
      if (transaction.type == TransactionType.credit &&
          transaction.category != 'Self Transfer') {
        _categoryIncome[transaction.category] =
            (_categoryIncome[transaction.category] ?? 0.0) + transaction.amount;
      } else if (transaction.type == TransactionType.debit &&
          transaction.category != 'Self Transfer') {
        _categoryExpenses[transaction.category] =
            (_categoryExpenses[transaction.category] ?? 0.0) +
            transaction.amount;
      }
    }
  }

  void _calculateTopCategories(List<TransactionModel> transactions) {
    final now = DateTime.now();

    // Filter transactions based on selected period
    List<TransactionModel> filteredTransactions = [];

    switch (_selectedCategoryPeriod) {
      case 'all':
        filteredTransactions = transactions;
        break;
      case 'monthly':
        final monthStart = DateTime(now.year, now.month, 1);
        final monthEnd = DateTime(now.year, now.month + 1, 0, 23, 59, 59);
        filteredTransactions =
            transactions
                .where(
                  (t) =>
                      (t.date.isAtSameMomentAs(monthStart) ||
                          t.date.isAfter(monthStart)) &&
                      (t.date.isAtSameMomentAs(monthEnd) ||
                          t.date.isBefore(monthEnd)),
                )
                .toList();
        break;
      case 'yearly':
        final yearStart = DateTime(now.year, 1, 1);
        final yearEnd = DateTime(now.year, 12, 31);
        filteredTransactions =
            transactions
                .where(
                  (t) =>
                      (t.date.isAtSameMomentAs(yearStart) ||
                          t.date.isAfter(yearStart)) &&
                      (t.date.isAtSameMomentAs(yearEnd) ||
                          t.date.isBefore(yearEnd)),
                )
                .toList();
        break;
    }

    // Calculate expense categories
    Map<String, double> expenseCategories = {};
    Map<String, int> expenseCounts = {};

    for (final transaction in filteredTransactions.where(
      (t) => t.type == TransactionType.debit && t.category != 'Self Transfer',
    )) {
      expenseCategories[transaction.category] =
          (expenseCategories[transaction.category] ?? 0.0) + transaction.amount;
      expenseCounts[transaction.category] =
          (expenseCounts[transaction.category] ?? 0) + 1;
    }

    // Sort expense categories by amount and take top 5
    _topExpenseCategories =
        expenseCategories.entries
            .map(
              (entry) => {
                'category': entry.key,
                'amount': entry.value,
                'count': expenseCounts[entry.key] ?? 0,
                'percentage':
                    entry.value /
                    expenseCategories.values.fold(
                      0.0,
                      (sum, amount) => sum + amount,
                    ) *
                    100,
              },
            )
            .toList()
          ..sort(
            (a, b) => (b['amount'] as double).compareTo(a['amount'] as double),
          );

    _topExpenseCategories = _topExpenseCategories.take(5).toList();

    // Calculate income categories
    Map<String, double> incomeCategories = {};
    Map<String, int> incomeCounts = {};

    for (final transaction in filteredTransactions.where(
      (t) => t.type == TransactionType.credit && t.category != 'Self Transfer',
    )) {
      incomeCategories[transaction.category] =
          (incomeCategories[transaction.category] ?? 0.0) + transaction.amount;
      incomeCounts[transaction.category] =
          (incomeCounts[transaction.category] ?? 0) + 1;
    }

    // Sort income categories by amount and take top 5
    _topIncomeCategories =
        incomeCategories.entries
            .map(
              (entry) => {
                'category': entry.key,
                'amount': entry.value,
                'count': incomeCounts[entry.key] ?? 0,
                'percentage':
                    entry.value /
                    incomeCategories.values.fold(
                      0.0,
                      (sum, amount) => sum + amount,
                    ) *
                    100,
              },
            )
            .toList()
          ..sort(
            (a, b) => (b['amount'] as double).compareTo(a['amount'] as double),
          );

    _topIncomeCategories = _topIncomeCategories.take(5).toList();
  }

  Future<void> _editBankAccount(BankAccountModel account) async {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    // Use GoRouter with extra parameter for BankAccountModel
    final result = await GoRouter.of(context).pushNamed<bool>(
      GoRouterConstants.editBank,
      pathParameters: {'bankId': account.id.toString()},
      extra: account,
    );

    if (result == true) {
      await _loadDataAfterBankEdit();
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('dashboard_bank_account_updated_successfully'.tr()),
          backgroundColor: colorScheme.primary,
        ),
      );
    }
  }

  Future<void> _showAddUpcomingExpenseDialog() async {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final categories = await LocalStorageService.getUpcomingExpenseCategories();

    if (categories.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('dashboard_add_expense_categories_first'.tr()),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    if (_bankAccounts.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('dashboard_add_bank_accounts_first'.tr()),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    String? selectedCategory = categories.first.name;
    String? selectedBankAccountId = _bankAccounts.first.id;
    final TextEditingController amountController = TextEditingController();
    DateTime selectedDate = DateTime.now().add(const Duration(days: 30));
    final GlobalKey<FormState> formKey = GlobalKey<FormState>();

    await showDialog(
      context: context,
      builder:
          (context) => StatefulBuilder(
            builder:
                (context, setState) => AlertDialog(
                  title: Text(
                    'dashboard_add_upcoming_expense'.tr(),
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  content: SizedBox(
                    width: double.maxFinite,
                    child: SingleChildScrollView(
                      child: Form(
                        key: formKey,
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Category Dropdown
                            Text(
                              'dashboard_category'.tr(),
                              style: theme.textTheme.bodyMedium?.copyWith(
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            const SizedBox(height: 8),
                            DropdownButtonFormField<String>(
                              value: selectedCategory,
                              decoration: InputDecoration(
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                contentPadding: const EdgeInsets.symmetric(
                                  horizontal: 12,
                                  vertical: 8,
                                ),
                              ),
                              items:
                                  categories.map((category) {
                                    return DropdownMenuItem<String>(
                                      value: category.name,
                                      child: Text(category.name),
                                    );
                                  }).toList(),
                              onChanged: (value) {
                                setState(() {
                                  selectedCategory = value;
                                });
                              },
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'dashboard_please_select_category'
                                      .tr();
                                }
                                return null;
                              },
                            ),
                            const SizedBox(height: 16),

                            // Bank Account Dropdown
                            Text(
                              'dashboard_bank_account'.tr(),
                              style: theme.textTheme.bodyMedium?.copyWith(
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            const SizedBox(height: 8),
                            DropdownButtonFormField<String>(
                              value: selectedBankAccountId,
                              decoration: InputDecoration(
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                contentPadding: const EdgeInsets.symmetric(
                                  horizontal: 12,
                                  vertical: 8,
                                ),
                              ),
                              items:
                                  _bankAccounts.map((account) {
                                    return DropdownMenuItem<String>(
                                      value: account.id,
                                      child: Text(
                                        '${account.bankName} - ${account.accountNumber}',
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                    );
                                  }).toList(),
                              onChanged: (value) {
                                setState(() {
                                  selectedBankAccountId = value;
                                });
                              },
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'dashboard_please_select_bank_account'
                                      .tr();
                                }
                                return null;
                              },
                            ),
                            const SizedBox(height: 16),

                            // Amount Field
                            Text(
                              'dashboard_amount'.tr(),
                              style: theme.textTheme.bodyMedium?.copyWith(
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            const SizedBox(height: 8),
                            TextFormField(
                              controller: amountController,
                              keyboardType:
                                  const TextInputType.numberWithOptions(
                                    decimal: true,
                                  ),
                              decoration: InputDecoration(
                                hintText: 'dashboard_enter_amount'.tr(),
                                prefixText: '₹ ',
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                contentPadding: const EdgeInsets.symmetric(
                                  horizontal: 12,
                                  vertical: 8,
                                ),
                              ),
                              validator: (value) {
                                if (value == null || value.trim().isEmpty) {
                                  return 'dashboard_please_enter_amount'.tr();
                                }
                                final amount = double.tryParse(value.trim());
                                if (amount == null || amount <= 0) {
                                  return 'dashboard_please_enter_valid_amount'
                                      .tr();
                                }
                                return null;
                              },
                            ),
                            const SizedBox(height: 16),

                            // Due Date Field
                            Text(
                              'dashboard_due_date'.tr(),
                              style: theme.textTheme.bodyMedium?.copyWith(
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            const SizedBox(height: 8),
                            GestureDetector(
                              onTap: () async {
                                final picked = await showDatePicker(
                                  context: context,
                                  initialDate: selectedDate,
                                  firstDate: DateTime.now(),
                                  lastDate: DateTime.now().add(
                                    const Duration(days: 365 * 2),
                                  ),
                                );
                                if (picked != null) {
                                  setState(() {
                                    selectedDate = picked;
                                  });
                                }
                              },
                              child: Container(
                                padding: const EdgeInsets.all(12),
                                decoration: BoxDecoration(
                                  border: Border.all(
                                    color: theme.hintColor.withValues(
                                      alpha: 0.3,
                                    ),
                                  ),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      DateFormat(
                                        'MMM dd, yyyy',
                                      ).format(selectedDate),
                                      style: theme.textTheme.bodyMedium,
                                    ),
                                    const Icon(Icons.calendar_today, size: 20),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  actions: [
                    TextButton(
                      onPressed: () => context.pop(),
                      child: Text('dashboard_cancel'.tr()),
                    ),
                    TextButton(
                      onPressed: () async {
                        if (formKey.currentState!.validate()) {
                          final amount = double.parse(
                            amountController.text.trim(),
                          );

                          final expense = UpcomingExpenseModel(
                            category: selectedCategory!,
                            amount: amount,
                            dueDate: selectedDate,
                            bankAccountId: selectedBankAccountId,
                          );

                          await LocalStorageService.addUpcomingExpense(expense);

                          // Schedule notification for the new expense
                          await NotificationService.scheduleExpenseNotification(
                            expense,
                          );

                          context.pop();
                          await _loadData(); // Refresh dashboard data

                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text(
                                'dashboard_upcoming_expense_added_successfully'
                                    .tr(),
                              ),
                              backgroundColor: Colors.green,
                            ),
                          );
                        }
                      },
                      child: Text(
                        'dashboard_add'.tr(),
                        style: TextStyle(
                          color: colorScheme.primary,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
          ),
    );
  }

  /// Helper function to format amount based on visibility setting
  String _formatAmount(double amount, {bool forceVisible = false}) {
    if (forceVisible || _isBalanceVisible) {
      return '₹${NumberFormat('#,##,###').format(amount)}';
    } else {
      return '****';
    }
  }

  /// Helper function to format compact amount based on visibility setting
  String _formatCompactAmount(double amount, {bool forceVisible = false}) {
    if (forceVisible || _isBalanceVisible) {
      return '₹${NumberFormat.compact().format(amount)}';
    } else {
      return '****';
    }
  }

  /// Helper function to determine if a recurring transaction is income
  bool _isRecurringTransactionIncome(RecurringTransactionModel transaction) {
    final lowerCategory = transaction.category.toLowerCase();
    final lowerDescription = transaction.description.toLowerCase();

    // Common income categories and keywords
    final incomeKeywords = [
      'salary',
      'freelance',
      'investment',
      'dividend',
      'bonus',
      'interest',
      'rental income',
      'profit',
      'commission',
      'allowance',
      'pension',
      'refund',
      'cash back',
      'reward',
      'gift',
      'income',
      'earning',
      'revenue',
    ];

    // Check if category or description contains income keywords
    return incomeKeywords.any(
      (keyword) =>
          lowerCategory.contains(keyword) || lowerDescription.contains(keyword),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        title: Text(
          'dashboard_personal_finance'.tr(),
          style: theme.appBarTheme.titleTextStyle?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          // Add Transaction Button
          Container(
            margin: const EdgeInsets.only(right: 8),
            child: ElevatedButton.icon(
              onPressed: () async {
                // Navigate to add transaction screen using GoRouter
                await GoRouter.of(
                  context,
                ).pushNamed(GoRouterConstants.addTransaction);

                // Refresh dashboard data when user returns
                _loadData();
              },
              icon: const Icon(Icons.add, size: 20, color: Colors.white),
              label: Text(
                'dashboard_add'.tr(),
                style: TextStyle(fontWeight: FontWeight.w600, fontSize: 14),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
                elevation: 2,
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 8,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20),
                ),
              ),
            ),
          ),
        ],
      ),

      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : RefreshIndicator(
                onRefresh: _loadData,
                child: SingleChildScrollView(
                  physics: const AlwaysScrollableScrollPhysics(),
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      // Welcome section
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: colorScheme.primary,
                          borderRadius: BorderRadius.circular(12),
                          boxShadow: [
                            BoxShadow(
                              color: colorScheme.primary.withValues(alpha: 0.3),
                              blurRadius: 8,
                              offset: const Offset(0, 3),
                            ),
                          ],
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  'dashboard_total_balance'.tr(),
                                  style:
                                      theme.textTheme.bodyLarge?.copyWith(
                                        color: colorScheme.onPrimary.withValues(
                                          alpha: 0.9,
                                        ),
                                      ) ??
                                      theme.textTheme.bodySmall?.copyWith(
                                        color: colorScheme.onPrimary.withValues(
                                          alpha: 0.9,
                                        ),
                                      ),
                                ),
                                // Visibility toggle button for income and balance
                                Container(
                                  decoration: BoxDecoration(
                                    color: colorScheme.onPrimary.withValues(
                                      alpha: 0.1,
                                    ),
                                    borderRadius: BorderRadius.circular(16),
                                  ),
                                  child: IconButton(
                                    onPressed: () async {
                                      final newVisibility = !_isBalanceVisible;
                                      setState(() {
                                        _isBalanceVisible = newVisibility;
                                      });
                                      // Save preference to local storage
                                      await _saveVisibilityPreference(
                                        newVisibility,
                                      );
                                    },
                                    icon: Icon(
                                      _isBalanceVisible
                                          ? Icons.visibility
                                          : Icons.visibility_off,
                                      color: colorScheme.onPrimary,
                                      size: 18,
                                    ),
                                    tooltip:
                                        _isBalanceVisible
                                            ? 'dashboard_hide_balance'.tr()
                                            : 'dashboard_show_balance'.tr(),
                                    padding: const EdgeInsets.all(6),
                                    constraints: const BoxConstraints(),
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 4),
                            Text(
                              _formatAmount(_totalBalance),
                              style:
                                  theme.textTheme.headlineMedium?.copyWith(
                                    color: colorScheme.onPrimary,
                                    fontWeight: FontWeight.bold,
                                  ) ??
                                  theme.textTheme.headlineMedium?.copyWith(
                                    color: colorScheme.onPrimary,
                                    fontWeight: FontWeight.bold,
                                  ),
                            ),
                            const SizedBox(height: 8),
                            // First row: Income and Expenses
                            Row(
                              children: [
                                Expanded(
                                  child: _buildStatCard(
                                    'dashboard_month_income'.tr(),
                                    _formatAmount(_currentMonthIncome),
                                    Icons.arrow_upward,
                                    Colors.green,
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: _buildStatCard(
                                    'dashboard_month_expenses'.tr(),
                                    _formatAmount(
                                      _currentMonthExpenses,
                                      forceVisible: true,
                                    ),
                                    Icons.arrow_downward,
                                    colorScheme.error,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            // Second row: Upcoming Expenses (full width)
                            GestureDetector(
                              behavior: HitTestBehavior.opaque,
                              onTap: () => _showAddUpcomingExpenseDialog(),
                              child: _buildUpcomingStatCard(
                                'dashboard_upcoming_expenses'.tr(),
                                _formatAmount(
                                  _totalUpcomingExpenses,
                                  forceVisible: true,
                                ),
                                Icons.schedule,
                                Colors.orange,
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 20),

                      // Bank Accounts section
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'dashboard_accounts'.tr(),
                            style:
                                theme.textTheme.titleLarge?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ) ??
                                theme.textTheme.titleLarge?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                          ),
                          TextButton(
                            onPressed: () async {
                              // Navigate to add bank screen using GoRouter
                              await GoRouter.of(
                                context,
                              ).pushNamed(GoRouterConstants.addBank);

                              // Refresh dashboard data when user returns
                              await _loadData();
                            },
                            child: Text(
                              'dashboard_add_bank'.tr(),
                              style:
                                  theme.textTheme.bodyMedium?.copyWith(
                                    color: colorScheme.primary,
                                    fontWeight: FontWeight.w600,
                                  ) ??
                                  theme.textTheme.bodyMedium?.copyWith(
                                    color: colorScheme.primary,
                                    fontWeight: FontWeight.w600,
                                  ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),

                      // Bank account cards
                      if (_bankAccounts.isEmpty)
                        _buildEmptyBankAccountsCard()
                      else
                        GridView.builder(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          gridDelegate:
                              const SliverGridDelegateWithFixedCrossAxisCount(
                                crossAxisCount: 2,
                                childAspectRatio: 1.3,
                                crossAxisSpacing: 12,
                                mainAxisSpacing: 12,
                              ),
                          itemCount: _bankAccounts.length,
                          itemBuilder: (context, index) {
                            return _buildBankAccountCard(_bankAccounts[index]);
                          },
                        ),
                      const SizedBox(height: 20),

                      // Recent transactions section - only show if there are transactions
                      if (_recentTransactions.isNotEmpty) ...[
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              'dashboard_recent_transactions'.tr(),
                              style:
                                  theme.textTheme.titleLarge?.copyWith(
                                    fontWeight: FontWeight.bold,
                                  ) ??
                                  theme.textTheme.titleLarge?.copyWith(
                                    fontWeight: FontWeight.bold,
                                    color: colorScheme.onSurface,
                                  ),
                            ),
                            TextButton(
                              onPressed: () async {
                                await GoRouter.of(
                                  context,
                                ).pushNamed(GoRouterConstants.transactionList);
                                // Refresh data when returning from transaction list
                                _loadData();
                              },
                              child: Text(
                                'dashboard_view_all'.tr(),
                                style:
                                    theme.textTheme.bodyMedium?.copyWith(
                                      color: colorScheme.primary,
                                      fontWeight: FontWeight.w600,
                                    ) ??
                                    theme.textTheme.bodyMedium?.copyWith(
                                      color: colorScheme.primary,
                                      fontWeight: FontWeight.w600,
                                    ),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),

                        // Recent transactions list
                        ListView.builder(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          itemCount: _recentTransactions.length,
                          itemBuilder: (context, index) {
                            return _buildTransactionCard(
                              _recentTransactions[index],
                            );
                          },
                        ),
                        // const SizedBox(height: 20),
                      ],

                      const SizedBox(height: 20),
                      // Credit Cards section
                      _buildCreditCardSummaryCard(),
                      const SizedBox(height: 20),

                      // Cash Flow Summary section
                      _buildCashFlowSummaryCard(),
                      const SizedBox(height: 20),

                      // Spending Trends section
                      _buildSpendingTrendsCard(),
                      const SizedBox(height: 20),

                      // Top Categories section
                      _buildTopCategoriesCard(),
                      const SizedBox(height: 20),

                      // Recurring Transactions section
                      if (_recurringTransactions.isNotEmpty) ...[
                        _buildRecurringTransactionsCard(),
                        const SizedBox(height: 24),
                      ],
                    ],
                  ),
                ),
              ),
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: colorScheme.onPrimary.withValues(alpha: 0.15),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: colorScheme.onPrimary.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Icon(icon, color: color, size: 14),
              ),
              const SizedBox(width: 6),
              Expanded(
                child: Text(
                  title,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: colorScheme.onPrimary.withValues(alpha: 0.9),
                    fontSize: 10,
                    fontWeight: FontWeight.w500,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          const SizedBox(height: 6),
          Text(
            value,
            style: theme.textTheme.titleSmall?.copyWith(
              color: colorScheme.onPrimary,
              fontWeight: FontWeight.bold,
              fontSize: 12,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Widget _buildUpcomingStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: colorScheme.onPrimary.withValues(alpha: 0.15),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: colorScheme.onPrimary.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Icon(icon, color: color, size: 14),
              ),
              const SizedBox(width: 6),
              Expanded(
                child: Text(
                  title,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: colorScheme.onPrimary.withValues(alpha: 0.9),
                    fontSize: 10,
                    fontWeight: FontWeight.w500,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              // List icon that doesn't overlap
              GestureDetector(
                behavior: HitTestBehavior.opaque,
                onTap: () async {
                  await GoRouter.of(
                    context,
                  ).pushNamed(GoRouterConstants.upcomingExpensesList);
                  // Refresh data when returning from upcoming expenses
                  _loadData();
                },
                child: Container(
                  padding: const EdgeInsets.all(3),
                  decoration: BoxDecoration(
                    color: colorScheme.onPrimary.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Icon(Icons.list, color: color, size: 10),
                ),
              ),
            ],
          ),
          const SizedBox(height: 6),
          Text(
            value,
            style: theme.textTheme.titleSmall?.copyWith(
              color: colorScheme.onPrimary,
              fontWeight: FontWeight.bold,
              fontSize: 12,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyBankAccountsCard() {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      padding: const EdgeInsets.all(32),
      decoration: BoxDecoration(
        color: theme.cardColor,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: AppColorPalette.outline.withValues(alpha: 0.1),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: AppColorPalette.shadow.withValues(alpha: 0.08),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColorPalette.primary10,
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.account_balance_wallet,
              size: 32,
              color: AppColorPalette.primary.withValues(alpha: 0.8),
            ),
          ),
          const SizedBox(height: 16),
          Text(
            'dashboard_no_bank_accounts'.tr(),
            style: AppTextStyles.titleMedium.copyWith(
              color: colorScheme.onSurface,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'dashboard_add_first_bank_account_message'.tr(),
            textAlign: TextAlign.center,
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColorPalette.grey70,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBankAccountCard(BankAccountModel account) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        _editBankAccount(account);
      },
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: theme.cardColor,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: colorScheme.outline.withValues(alpha: 0.1),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: theme.shadowColor.withValues(alpha: 0.08),
              blurRadius: 12,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: colorScheme.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    Icons.account_balance,
                    color: colorScheme.primary,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    account.bankName,
                    style: theme.textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: colorScheme.onSurface,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              _formatAmount(account.currentAmount),
              style: theme.textTheme.titleMedium?.copyWith(
                color:
                    account.currentAmount >= 0
                        ? Colors.green
                        : colorScheme.error,
                fontWeight: FontWeight.bold,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 4),
            Text(
              'dashboard_account_number'.tr(
                namedArgs: {
                  'accountNumber':
                      '****${account.accountNumber.substring(account.accountNumber.length - 4)}',
                },
              ),
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.hintColor,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  // Widget _buildEmptyTransactionsCard() {
  //   return Container(
  //     padding: const EdgeInsets.all(24),
  //     decoration: BoxDecoration(
  //       color: colorScheme.onPrimary,
  //       borderRadius: BorderRadius.circular(16),
  //       boxShadow: [
  //         BoxShadow(
  //           color: theme.hintColor.withValues(alpha: 0.1),
  //           blurRadius: 10,
  //           offset: const Offset(0, 2),
  //         ),
  //       ],
  //     ),
  //     child: Column(
  //       children: [
  //         Icon(Icons.receipt_long, size: 48, color: theme.hintColor.withValues(alpha: 0.5)),
  //         const SizedBox(height: 16),
  //         Text(
  //           'No Transactions',
  //           style: theme.textTheme.titleMedium?.copyWith(
  //             color: theme.hintColor,
  //             fontWeight: FontWeight.bold,
  //           ),
  //         ),
  //         const SizedBox(height: 8),
  //         Text(
  //           'Start adding transactions to track your income and expenses',
  //           textAlign: TextAlign.center,
  //           style: theme.textTheme.bodyMedium?.copyWith(
  //             color: theme.hintColor.withValues(alpha: 0.7),
  //           ),
  //         ),
  //       ],
  //     ),
  //   );
  // }

  Widget _buildTransactionCard(TransactionModel transaction) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    // Handle different transaction types including transfers
    final isIncome = transaction.type == TransactionType.credit;
    final isTransfer = transaction.category == 'Self Transfer';

    final Color color;
    final IconData icon;

    if (isTransfer) {
      // For self transfers: Green for credit (money in), Red for debit (money out)
      if (isIncome) {
        color = AppColorPalette.success; // Money received - green
        icon = Icons.swap_horiz; // Transfer icon
      } else {
        color = AppColorPalette.error; // Money sent - red
        icon = Icons.swap_horiz; // Transfer icon
      }
    } else if (isIncome) {
      color = Colors.green;
      icon = Icons.arrow_upward;
    } else {
      color = Colors.red;
      icon = Icons.arrow_downward;
    }

    // Find bank account for this transaction
    final bankAccount = _bankAccounts.firstWhere(
      (account) => account.id == transaction.bankAccountId,
      orElse:
          () => BankAccountModel(
            bankName: 'Unknown Bank',
            accountNumber: '',
            initialAmount: 0,
            currentAmount: 0,
          ),
    );

    // Get bank color based on bank name
    final bankColor = _getBankColor(bankAccount.bankName);

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.cardColor,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: colorScheme.outline.withValues(alpha: 0.1),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: theme.shadowColor.withValues(alpha: 0.08),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        children: [
          // Transaction type icon
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(icon, color: color, size: 20),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  transaction.category,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: colorScheme.onSurface,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),
                Text(
                  '${transaction.type.name.toUpperCase()} • ${DateFormat('MMM dd, yyyy').format(transaction.date)}',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.hintColor,
                  ),
                ),
                const SizedBox(height: 2),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 2,
                  ),
                  decoration: BoxDecoration(
                    color: bankColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    bankAccount.bankName,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: bankColor,
                      fontWeight: FontWeight.w500,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(width: 12),
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                '${isIncome ? '+' : '-'}${_formatAmount(transaction.amount, forceVisible: !isIncome)}',
                style: theme.textTheme.titleSmall?.copyWith(
                  color: color,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 2),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Text(
                  isIncome ? 'Credit' : 'Debit',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: color,
                    fontWeight: FontWeight.w500,
                    fontSize: 10,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // /// Helper method to get bank icon based on bank name
  // IconData _getBankIcon(String bankName) {
  //   final lowerBankName = bankName.toLowerCase();

  //   if (lowerBankName.contains('State Bank of India (SBI)')) {
  //     return Icons.account_balance;
  //   } else if (lowerBankName.contains('hdfc')) {
  //     return Icons.credit_card;
  //   } else if (lowerBankName.contains('icici')) {
  //     return Icons.business;
  //   } else if (lowerBankName.contains('Axis Bank')) {
  //     return Icons.trending_up;
  //   } else if (lowerBankName.contains('kotak')) {
  //     return Icons.diamond;
  //   } else if (lowerBankName.contains('yes') ||
  //       lowerBankName.contains('yes bank')) {
  //     return Icons.verified;
  //   } else if (lowerBankName.contains('pnb') ||
  //       lowerBankName.contains('punjab')) {
  //     return Icons.agriculture;
  //   } else if (lowerBankName.contains('canara')) {
  //     return Icons.star;
  //   } else if (lowerBankName.contains('union')) {
  //     return Icons.handshake;
  //   } else if (lowerBankName.contains('bank of baroda') ||
  //       lowerBankName.contains('bob')) {
  //     return Icons.flag;
  //   } else if (lowerBankName.contains('idbi') ||
  //       lowerBankName.contains('indian bank')) {
  //     return Icons.location_city;
  //   } else if (lowerBankName.contains('federal')) {
  //     return Icons.account_tree;
  //   } else if (lowerBankName.contains('indusind')) {
  //     return Icons.workspaces;
  //   } else {
  //     return Icons.account_balance; // Default bank icon
  //   }
  // }

  /// Helper method to get bank color based on bank name
  Color _getBankColor(String bankName) {
    final theme = Theme.of(context);
    final lowerBankName = bankName.toLowerCase();

    if (lowerBankName.contains('State Bank of India (SBI)')) {
      return const Color(0xFF1565C0); // SBI Blue
    } else if (lowerBankName.contains('hdfc')) {
      return const Color(0xFFD32F2F); // HDFC Red
    } else if (lowerBankName.contains('icici')) {
      return const Color(0xFFE65100); // ICICI Orange
    } else if (lowerBankName.contains('Axis Bank')) {
      return const Color(0xFF7B1FA2); // Axis Purple
    } else if (lowerBankName.contains('kotak')) {
      return const Color(0xFFD32F2F); // Kotak Red
    } else if (lowerBankName.contains('yes') ||
        lowerBankName.contains('yes bank')) {
      return const Color(0xFF1976D2); // Yes Bank Blue
    } else if (lowerBankName.contains('pnb') ||
        lowerBankName.contains('punjab')) {
      return const Color(0xFF388E3C); // PNB Green
    } else if (lowerBankName.contains('canara')) {
      return const Color(0xFFE65100); // Canara Orange
    } else if (lowerBankName.contains('union')) {
      return const Color(0xFF795548); // Union Brown
    } else if (lowerBankName.contains('bank of baroda') ||
        lowerBankName.contains('bob')) {
      return const Color(0xFF1976D2); // BOB Blue
    } else if (lowerBankName.contains('idbi') ||
        lowerBankName.contains('indian bank')) {
      return const Color(0xFF388E3C); // Green
    } else if (lowerBankName.contains('federal')) {
      return const Color(0xFF7B1FA2); // Purple
    } else if (lowerBankName.contains('indusind')) {
      return const Color(0xFFE65100); // Orange
    } else {
      return theme.hintColor; // Default bank color
    }
  }

  Widget _buildSpendingTrendsCard() {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final trends =
        _selectedTrendPeriod == 'weekly' ? _weeklyTrends : _monthlyTrends;
    final maxAmount =
        trends.isNotEmpty
            ? trends
                .map((t) => t['amount'] as double)
                .reduce((a, b) => a > b ? a : b)
            : 0.0;

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: theme.cardColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: theme.shadowColor.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with period selector
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Text(
                  'dashboard_spending_trends'.tr(),
                  style:
                      theme.textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ) ??
                      theme.textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ),
              const SizedBox(width: 8),
              Container(
                decoration: BoxDecoration(
                  color: colorScheme.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    _buildPeriodButton('dashboard_weekly'.tr(), 'weekly'),
                    _buildPeriodButton('dashboard_monthly'.tr(), 'monthly'),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),

          // Chart
          if (trends.isNotEmpty) ...[
            SizedBox(
              height: 200,
              child: LineChart(
                LineChartData(
                  lineTouchData: LineTouchData(
                    enabled: true,
                    touchTooltipData: LineTouchTooltipData(
                      tooltipBgColor: theme.cardColor,
                      tooltipBorder: BorderSide(
                        color: theme.dividerColor,
                        width: 1,
                      ),
                      tooltipRoundedRadius: 8,
                      getTooltipItems: (List<LineBarSpot> touchedBarSpots) {
                        return touchedBarSpots.map((barSpot) {
                          if (barSpot.x.toInt() >= 0 &&
                              barSpot.x.toInt() < trends.length) {
                            final data = trends[barSpot.x.toInt()];
                            return LineTooltipItem(
                              '${data['formattedDate']}\n',
                              theme.textTheme.bodySmall?.copyWith(
                                    color: theme.textTheme.bodyLarge?.color,
                                    fontWeight: FontWeight.bold,
                                  ) ??
                                  const TextStyle(
                                    color: Colors.black,
                                    fontWeight: FontWeight.bold,
                                  ),
                              children: [
                                TextSpan(
                                  text:
                                      '${'dashboard_expenses'.tr()}: ${_formatCompactAmount(barSpot.y, forceVisible: true)}',
                                  style:
                                      theme.textTheme.bodySmall?.copyWith(
                                        color: colorScheme.error,
                                        fontWeight: FontWeight.w600,
                                      ) ??
                                      theme.textTheme.bodySmall?.copyWith(
                                        color: colorScheme.error,
                                        fontWeight: FontWeight.w600,
                                      ),
                                ),
                              ],
                            );
                          }
                          return null;
                        }).toList();
                      },
                    ),
                  ),
                  gridData: FlGridData(
                    show: true,
                    drawVerticalLine: true,
                    horizontalInterval: maxAmount > 0 ? maxAmount / 4 : 1000,
                    verticalInterval: 1,
                    getDrawingHorizontalLine: (value) {
                      return FlLine(
                        color: theme.hintColor.withValues(alpha: 0.1),
                        strokeWidth: 1,
                      );
                    },
                    getDrawingVerticalLine: (value) {
                      return FlLine(
                        color: theme.hintColor.withValues(alpha: 0.1),
                        strokeWidth: 1,
                      );
                    },
                  ),
                  titlesData: FlTitlesData(
                    show: true,
                    rightTitles: AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                    topTitles: AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                    bottomTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        reservedSize: 30,
                        interval: 1,
                        getTitlesWidget: (double value, TitleMeta meta) {
                          if (value.toInt() >= 0 &&
                              value.toInt() < trends.length) {
                            final period =
                                trends[value.toInt()]['period'] as String;
                            return SideTitleWidget(
                              axisSide: meta.axisSide,
                              child: Text(
                                period,
                                style: theme.textTheme.bodySmall?.copyWith(
                                  color: theme.hintColor,
                                ),
                              ),
                            );
                          }
                          return const Text('');
                        },
                      ),
                    ),
                    leftTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        interval: maxAmount > 0 ? maxAmount / 4 : 1000,
                        getTitlesWidget: (double value, TitleMeta meta) {
                          return SideTitleWidget(
                            axisSide: meta.axisSide,
                            child: Text(
                              '₹${NumberFormat.compact().format(value)}',
                              style: theme.textTheme.bodySmall?.copyWith(
                                color: theme.hintColor,
                              ),
                            ),
                          );
                        },
                        reservedSize: 50,
                      ),
                    ),
                  ),
                  borderData: FlBorderData(
                    show: true,
                    border: Border.all(
                      color: theme.hintColor.withValues(alpha: 0.1),
                    ),
                  ),
                  minX: 0,
                  maxX: (trends.length - 1).toDouble(),
                  minY: 0,
                  maxY: maxAmount > 0 ? maxAmount * 1.1 : 1000,
                  lineBarsData: [
                    LineChartBarData(
                      spots:
                          trends.asMap().entries.map((entry) {
                            return FlSpot(
                              entry.key.toDouble(),
                              entry.value['amount'] as double,
                            );
                          }).toList(),
                      isCurved: true,
                      curveSmoothness: 0.35,
                      preventCurveOverShooting: true,
                      gradient: LinearGradient(
                        colors: [
                          colorScheme.primary,
                          colorScheme.primary.withValues(alpha: 0.8),
                        ],
                      ),
                      barWidth: 3,
                      isStrokeCapRound: true,
                      dotData: FlDotData(
                        show: true,
                        getDotPainter: (spot, percent, barData, index) {
                          return FlDotCirclePainter(
                            radius: 4,
                            color: colorScheme.primary,
                            strokeWidth: 2,
                            strokeColor: colorScheme.onPrimary,
                          );
                        },
                      ),
                      belowBarData: BarAreaData(
                        show: true,
                        gradient: LinearGradient(
                          colors: [
                            colorScheme.primary.withValues(alpha: 0.3),
                            colorScheme.primary.withValues(alpha: 0.1),
                          ],
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),

            // Summary statistics
            const SizedBox(height: 20),
            Row(
              children: [
                Expanded(
                  child: _buildTrendStat(
                    'dashboard_average'.tr(),
                    _formatCompactAmount(
                      trends.fold<double>(0.0, (sum, t) => sum + t['amount']) /
                          trends.length,
                      forceVisible: true,
                    ),
                    Icons.trending_up,
                    Colors.green,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildTrendStat(
                    'dashboard_highest'.tr(),
                    _formatCompactAmount(maxAmount, forceVisible: true),
                    Icons.arrow_upward,
                    Colors.orange,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildTrendStat(
                    'dashboard_lowest'.tr(),
                    _formatCompactAmount(
                      trends
                          .map((t) => t['amount'] as double)
                          .reduce((a, b) => a < b ? a : b),
                      forceVisible: true,
                    ),
                    Icons.arrow_downward,
                    colorScheme.error,
                  ),
                ),
              ],
            ),
          ] else ...[
            // Empty state
            SizedBox(
              height: 200,
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.show_chart,
                      size: 48,
                      color: theme.hintColor.withValues(alpha: 0.5),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'dashboard_no_spending_data_available'.tr(),
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: theme.hintColor,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'dashboard_add_expenses_to_see_trends'.tr(),
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.hintColor.withValues(alpha: 0.7),
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildPeriodButton(String label, String period) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final isSelected = _selectedTrendPeriod == period;
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        setState(() {
          _selectedTrendPeriod = period;
          // Recalculate spending trends data for the new period
          _calculateSpendingTrends(_allTransactions);
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: isSelected ? colorScheme.primary : Colors.transparent,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Text(
          label,
          style: theme.textTheme.bodySmall?.copyWith(
            color: isSelected ? colorScheme.onPrimary : colorScheme.primary,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
            fontSize: 11,
          ),
        ),
      ),
    );
  }

  Widget _buildTrendStat(
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(6),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 8),
          Text(
            value,
            style:
                theme.textTheme.titleSmall?.copyWith(
                  color: color,
                  fontWeight: FontWeight.bold,
                ) ??
                theme.textTheme.titleSmall?.copyWith(
                  color: color,
                  fontWeight: FontWeight.bold,
                ),
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style:
                theme.textTheme.bodySmall?.copyWith(color: theme.hintColor) ??
                theme.textTheme.bodySmall?.copyWith(color: theme.hintColor),
          ),
        ],
      ),
    );
  }

  Widget _buildCashFlowSummaryCard() {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final maxAmount =
        _cashFlowData.isNotEmpty
            ? _cashFlowData
                .map((d) => [d['income'] as double, d['expenses'] as double])
                .expand((i) => i)
                .reduce((a, b) => a > b ? a : b)
            : 0.0;

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: theme.cardColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: theme.shadowColor.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with period selector
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Text(
                  'dashboard_cash_flow'.tr(),
                  style:
                      theme.textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ) ??
                      theme.textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ),
              const SizedBox(width: 8),
              Container(
                decoration: BoxDecoration(
                  color: colorScheme.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    _buildCashFlowPeriodButton(
                      'dashboard_weekly'.tr(),
                      'weekly',
                    ),
                    const SizedBox(width: 2),
                    _buildCashFlowPeriodButton(
                      'dashboard_monthly'.tr(),
                      'monthly',
                    ),
                    const SizedBox(width: 2),
                    _buildCashFlowPeriodButton(
                      'dashboard_yearly'.tr(),
                      'yearly',
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),

          // Chart
          if (_cashFlowData.isNotEmpty) ...[
            SizedBox(
              height: 250,
              child: BarChart(
                BarChartData(
                  alignment: BarChartAlignment.spaceAround,
                  maxY: maxAmount * 1.2,
                  barTouchData: BarTouchData(
                    enabled: true,
                    touchTooltipData: BarTouchTooltipData(
                      tooltipBgColor: theme.cardColor,
                      tooltipBorder: BorderSide(
                        color: theme.dividerColor,
                        width: 1,
                      ),
                      tooltipRoundedRadius: 8,
                      getTooltipItem: (group, groupIndex, rod, rodIndex) {
                        final data = _cashFlowData[group.x.toInt()];
                        return BarTooltipItem(
                          '${data['period']}\n',
                          theme.textTheme.bodySmall?.copyWith(
                                color: theme.textTheme.bodyLarge?.color,
                                fontWeight: FontWeight.bold,
                              ) ??
                              const TextStyle(
                                color: Colors.black,
                                fontWeight: FontWeight.bold,
                              ),
                          children: [
                            TextSpan(
                              text:
                                  '${'dashboard_income'.tr()}: ${_formatCompactAmount(data['income'])}\n',
                              style:
                                  theme.textTheme.bodySmall?.copyWith(
                                    color: const Color(
                                      0xFF2E7D32,
                                    ), // Dark green for better contrast
                                    fontWeight: FontWeight.w600,
                                  ) ??
                                  theme.textTheme.bodySmall?.copyWith(
                                    color: const Color(0xFF2E7D32),
                                    fontWeight: FontWeight.w600,
                                  ),
                            ),
                            TextSpan(
                              text:
                                  '${'dashboard_expenses'.tr()}: ${_formatCompactAmount(data['expenses'], forceVisible: true)}\n',
                              style:
                                  theme.textTheme.bodySmall?.copyWith(
                                    color: const Color(
                                      0xFFD32F2F,
                                    ), // Dark red for better contrast
                                    fontWeight: FontWeight.w600,
                                  ) ??
                                  theme.textTheme.bodySmall?.copyWith(
                                    color: const Color(0xFFD32F2F),
                                    fontWeight: FontWeight.w600,
                                  ),
                            ),
                            TextSpan(
                              text:
                                  '${'dashboard_net'.tr()}: ${_formatCompactAmount(data['net'])}',
                              style:
                                  theme.textTheme.bodySmall?.copyWith(
                                    color:
                                        data['net'] >= 0
                                            ? const Color(
                                              0xFF2E7D32,
                                            ) // Dark green
                                            : const Color(
                                              0xFFD32F2F,
                                            ), // Dark red
                                    fontWeight: FontWeight.bold,
                                  ) ??
                                  theme.textTheme.bodySmall?.copyWith(
                                    color:
                                        data['net'] >= 0
                                            ? const Color(0xFF2E7D32)
                                            : const Color(0xFFD32F2F),
                                    fontWeight: FontWeight.bold,
                                  ),
                            ),
                          ],
                        );
                      },
                    ),
                  ),
                  titlesData: FlTitlesData(
                    show: true,
                    rightTitles: AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                    topTitles: AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                    bottomTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        getTitlesWidget: (double value, TitleMeta meta) {
                          if (value.toInt() >= 0 &&
                              value.toInt() < _cashFlowData.length) {
                            final period =
                                _cashFlowData[value.toInt()]['period']
                                    as String;
                            return SideTitleWidget(
                              axisSide: meta.axisSide,
                              child: Text(
                                period,
                                style:
                                    theme.textTheme.bodySmall?.copyWith(
                                      color: theme.hintColor,
                                    ) ??
                                    theme.textTheme.bodySmall?.copyWith(
                                      color: theme.hintColor,
                                    ),
                              ),
                            );
                          }
                          return const Text('');
                        },
                        reservedSize: 30,
                      ),
                    ),
                    leftTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        interval: maxAmount > 0 ? maxAmount / 4 : 1000,
                        getTitlesWidget: (double value, TitleMeta meta) {
                          return SideTitleWidget(
                            axisSide: meta.axisSide,
                            child: Text(
                              '₹${NumberFormat.compact().format(value)}',
                              style: theme.textTheme.bodySmall?.copyWith(
                                color: theme.hintColor,
                              ),
                            ),
                          );
                        },
                        reservedSize: 50,
                      ),
                    ),
                  ),
                  borderData: FlBorderData(
                    show: true,
                    border: Border.all(color: theme.dividerColor),
                  ),
                  barGroups:
                      _cashFlowData.asMap().entries.map((entry) {
                        final index = entry.key;
                        final data = entry.value;
                        return BarChartGroupData(
                          x: index,
                          barRods: [
                            BarChartRodData(
                              toY: data['income'] as double,
                              color: Colors.green,
                              width: 12,
                              borderRadius: const BorderRadius.only(
                                topLeft: Radius.circular(4),
                                topRight: Radius.circular(4),
                              ),
                            ),
                            BarChartRodData(
                              toY: data['expenses'] as double,
                              color: colorScheme.error,
                              width: 12,
                              borderRadius: const BorderRadius.only(
                                topLeft: Radius.circular(4),
                                topRight: Radius.circular(4),
                              ),
                            ),
                          ],
                        );
                      }).toList(),
                ),
              ),
            ),

            // Legend
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                _buildLegendItem('Income', Colors.green),
                const SizedBox(width: 24),
                _buildLegendItem('Expenses', colorScheme.error),
              ],
            ),

            // Summary statistics
            const SizedBox(height: 20),
            Row(
              children: [
                Expanded(
                  child: _buildCashFlowStat(
                    _getCashFlowPeriodLabel('Income'),
                    _formatCompactAmount(
                      _getCashFlowCurrentPeriodData()['income'] ?? 0.0,
                    ),
                    Icons.trending_up,
                    Colors.green,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildCashFlowStat(
                    _getCashFlowPeriodLabel('Expenses'),
                    _formatCompactAmount(
                      _getCashFlowCurrentPeriodData()['expenses'] ?? 0.0,
                      forceVisible: true,
                    ),
                    Icons.trending_down,
                    colorScheme.error,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildCashFlowStat(
                    _getCashFlowPeriodLabel('Net Flow'),
                    _formatCompactAmount(
                      _getCashFlowCurrentPeriodData()['net'] ?? 0.0,
                    ),
                    Icons.account_balance_wallet,
                    (_getCashFlowCurrentPeriodData()['net'] ?? 0.0) >= 0
                        ? Colors.green
                        : colorScheme.error,
                  ),
                ),
              ],
            ),
          ] else ...[
            // Empty state
            SizedBox(
              height: 250,
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.account_balance_wallet,
                      size: 48,
                      color: theme.hintColor.withValues(alpha: 0.5),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'No cash flow data available',
                      style:
                          theme.textTheme.bodyMedium?.copyWith(
                            color: theme.hintColor,
                          ) ??
                          theme.textTheme.bodyMedium?.copyWith(
                            color: theme.hintColor,
                          ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Add some transactions to see your cash flow',
                      style:
                          theme.textTheme.bodySmall?.copyWith(
                            color: theme.hintColor.withValues(alpha: 0.7),
                          ) ??
                          theme.textTheme.bodySmall?.copyWith(
                            color: theme.hintColor.withValues(alpha: 0.7),
                          ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildCashFlowPeriodButton(String label, String period) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final isSelected = _selectedCashFlowPeriod == period;
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        setState(() {
          _selectedCashFlowPeriod = period;
          // Recalculate cash flow data for the new period
          _calculateCashFlowSummary(_allTransactions);
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
        decoration: BoxDecoration(
          color:
              isSelected
                  ? colorScheme.primary
                  : const Color.fromARGB(0, 103, 34, 34),
          borderRadius: BorderRadius.circular(10),
        ),
        child: Text(
          label,
          style:
              theme.textTheme.bodySmall?.copyWith(
                color: isSelected ? colorScheme.onPrimary : colorScheme.primary,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                fontSize: 11,
              ) ??
              theme.textTheme.bodySmall?.copyWith(
                color: isSelected ? colorScheme.onPrimary : colorScheme.primary,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                fontSize: 11,
              ),
        ),
      ),
    );
  }

  /// Get period-appropriate label for cash flow summary boxes
  String _getCashFlowPeriodLabel(String type) {
    switch (_selectedCashFlowPeriod) {
      case 'weekly':
        return 'This Week $type'; // Sum of all weeks in current month
      case 'monthly':
        return 'This Month $type'; // Current month data only
      case 'yearly':
        return 'This Year $type'; // Current year data only
      default:
        return 'This $type';
    }
  }

  /// Get current period data instead of sum of all periods
  Map<String, double> _getCashFlowCurrentPeriodData() {
    if (_cashFlowData.isEmpty) {
      return {'income': 0.0, 'expenses': 0.0, 'net': 0.0};
    }

    switch (_selectedCashFlowPeriod) {
      case 'weekly':
        // For weekly, sum all weeks in current month
        return {
          'income': _cashFlowData.fold<double>(
            0.0,
            (sum, d) => sum + d['income'],
          ),
          'expenses': _cashFlowData.fold<double>(
            0.0,
            (sum, d) => sum + d['expenses'],
          ),
          'net': _cashFlowData.fold<double>(0.0, (sum, d) => sum + d['net']),
        };
      case 'monthly':
        // For monthly, show only current month (last entry in the list)
        final currentMonth = _cashFlowData.last;
        return {
          'income': currentMonth['income'] as double,
          'expenses': currentMonth['expenses'] as double,
          'net': currentMonth['net'] as double,
        };
      case 'yearly':
        // For yearly, show only current year (last entry in the list)
        final currentYear = _cashFlowData.last;
        return {
          'income': currentYear['income'] as double,
          'expenses': currentYear['expenses'] as double,
          'net': currentYear['net'] as double,
        };
      default:
        return {
          'income': _cashFlowData.fold<double>(
            0.0,
            (sum, d) => sum + d['income'],
          ),
          'expenses': _cashFlowData.fold<double>(
            0.0,
            (sum, d) => sum + d['expenses'],
          ),
          'net': _cashFlowData.fold<double>(0.0, (sum, d) => sum + d['net']),
        };
    }
  }

  Widget _buildLegendItem(String label, Color color) {
    final theme = Theme.of(context);

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(2),
          ),
        ),
        const SizedBox(width: 8),
        Text(
          label,
          style:
              theme.textTheme.bodySmall?.copyWith(color: theme.hintColor) ??
              theme.textTheme.bodySmall?.copyWith(color: theme.hintColor),
        ),
      ],
    );
  }

  Widget _buildCashFlowStat(
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    final theme = Theme.of(context);

    return Container(
      height: 100, // Increased height to accommodate longer text
      padding: const EdgeInsets.all(10), // Reduced padding to give more space
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, color: color, size: 18), // Slightly smaller icon
          const SizedBox(height: 4), // Reduced spacing
          Flexible(
            child: Text(
              value,
              style:
                  theme.textTheme.titleSmall?.copyWith(
                    color: color,
                    fontWeight: FontWeight.bold,
                    fontSize: 13, // Slightly smaller font size
                  ) ??
                  theme.textTheme.titleSmall?.copyWith(
                    color: color,
                    fontWeight: FontWeight.bold,
                    fontSize: 13, // Slightly smaller font size
                  ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          const SizedBox(height: 2),
          Flexible(
            child: Text(
              label,
              style:
                  theme.textTheme.bodySmall?.copyWith(
                    color: theme.hintColor,
                    fontSize: 11, // Smaller font size for labels
                  ) ??
                  theme.textTheme.bodySmall?.copyWith(
                    color: theme.hintColor,
                    fontSize: 11, // Smaller font size for labels
                  ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTopCategoriesCard() {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: theme.cardColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: theme.shadowColor.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with period selector
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Text(
                  'dashboard_top_categories'.tr(),
                  style:
                      theme.textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ) ??
                      theme.textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ),
              const SizedBox(width: 8),
              Container(
                decoration: BoxDecoration(
                  color: colorScheme.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    _buildCategoryPeriodButton('dashboard_all'.tr(), 'all'),
                    _buildCategoryPeriodButton(
                      'dashboard_monthly'.tr(),
                      'monthly',
                    ),
                    _buildCategoryPeriodButton(
                      'dashboard_yearly'.tr(),
                      'yearly',
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),

          // Content
          if (_topExpenseCategories.isNotEmpty ||
              _topIncomeCategories.isNotEmpty) ...[
            // Top Expense Categories
            if (_topExpenseCategories.isNotEmpty) ...[
              Text(
                'dashboard_top_expense_categories'.tr(),
                style:
                    theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: colorScheme.error,
                    ) ??
                    theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: colorScheme.error,
                    ),
              ),
              const SizedBox(height: 12),
              ..._topExpenseCategories.map(
                (category) => _buildCategoryItem(
                  category['category'] as String,
                  category['amount'] as double,
                  category['count'] as int,
                  category['percentage'] as double,
                  colorScheme.error,
                  isIncome: false,
                ),
              ),
              const SizedBox(height: 20),
            ],

            // Top Income Categories
            if (_topIncomeCategories.isNotEmpty) ...[
              Text(
                'dashboard_top_income_categories'.tr(),
                style:
                    theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Colors.green,
                    ) ??
                    theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Colors.green,
                    ),
              ),
              const SizedBox(height: 12),
              ..._topIncomeCategories.map(
                (category) => _buildCategoryItem(
                  category['category'] as String,
                  category['amount'] as double,
                  category['count'] as int,
                  category['percentage'] as double,
                  Colors.green,
                  isIncome: true,
                ),
              ),
            ],
          ] else ...[
            // Empty state
            SizedBox(
              height: 150,
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.category,
                      size: 48,
                      color: theme.hintColor.withValues(alpha: 0.5),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'No category data available',
                      style:
                          theme.textTheme.bodyMedium?.copyWith(
                            color: theme.hintColor,
                          ) ??
                          theme.textTheme.bodyMedium?.copyWith(
                            color: theme.hintColor,
                          ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Add some transactions to see your top categories',
                      style:
                          theme.textTheme.bodySmall?.copyWith(
                            color: theme.hintColor.withValues(alpha: 0.7),
                          ) ??
                          theme.textTheme.bodySmall?.copyWith(
                            color: theme.hintColor.withValues(alpha: 0.7),
                          ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildCategoryPeriodButton(String label, String period) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final isSelected = _selectedCategoryPeriod == period;
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        setState(() {
          _selectedCategoryPeriod = period;
        });
        // Recalculate top categories data for the new period
        _loadData();
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: isSelected ? colorScheme.primary : Colors.transparent,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Text(
          label,
          style:
              theme.textTheme.bodySmall?.copyWith(
                color: isSelected ? colorScheme.onPrimary : colorScheme.primary,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
              ) ??
              theme.textTheme.bodySmall?.copyWith(
                color: isSelected ? colorScheme.onPrimary : colorScheme.primary,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
              ),
        ),
      ),
    );
  }

  Widget _buildCategoryItem(
    String category,
    double amount,
    int count,
    double percentage,
    Color color, {
    bool isIncome = false,
  }) {
    final theme = Theme.of(context);

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.2)),
      ),
      child: Row(
        children: [
          // Category icon
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(_getCategoryIcon(category), color: color, size: 20),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  category,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    Text(
                      _formatCompactAmount(amount, forceVisible: !isIncome),
                      style: theme.textTheme.titleSmall?.copyWith(
                        color: color,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      '(${count} transactions)',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.hintColor,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          // Percentage indicator
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              '${percentage.toStringAsFixed(1)}%',
              style: theme.textTheme.bodySmall?.copyWith(
                color: color,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  IconData _getCategoryIcon(String category) {
    final lowerCategory = category.toLowerCase();

    if (lowerCategory.contains('food') ||
        lowerCategory.contains('restaurant') ||
        lowerCategory.contains('dining')) {
      return Icons.restaurant;
    } else if (lowerCategory.contains('transport') ||
        lowerCategory.contains('fuel') ||
        lowerCategory.contains('gas')) {
      return Icons.directions_car;
    } else if (lowerCategory.contains('shopping') ||
        lowerCategory.contains('clothes') ||
        lowerCategory.contains('fashion')) {
      return Icons.shopping_bag;
    } else if (lowerCategory.contains('entertainment') ||
        lowerCategory.contains('movie') ||
        lowerCategory.contains('game')) {
      return Icons.movie;
    } else if (lowerCategory.contains('health') ||
        lowerCategory.contains('medical') ||
        lowerCategory.contains('pharmacy')) {
      return Icons.local_hospital;
    } else if (lowerCategory.contains('education') ||
        lowerCategory.contains('course') ||
        lowerCategory.contains('book')) {
      return Icons.school;
    } else if (lowerCategory.contains('salary') ||
        lowerCategory.contains('income') ||
        lowerCategory.contains('job')) {
      return Icons.work;
    } else if (lowerCategory.contains('investment') ||
        lowerCategory.contains('dividend') ||
        lowerCategory.contains('profit')) {
      return Icons.trending_up;
    } else if (lowerCategory.contains('gift') ||
        lowerCategory.contains('bonus') ||
        lowerCategory.contains('reward')) {
      return Icons.card_giftcard;
    } else if (lowerCategory.contains('rent') ||
        lowerCategory.contains('home') ||
        lowerCategory.contains('house')) {
      return Icons.home;
    } else if (lowerCategory.contains('utility') ||
        lowerCategory.contains('electricity') ||
        lowerCategory.contains('water')) {
      return Icons.power;
    } else if (lowerCategory.contains('internet') ||
        lowerCategory.contains('phone') ||
        lowerCategory.contains('mobile')) {
      return Icons.wifi;
    } else {
      return Icons.category; // Default category icon
    }
  }

  Future<void> _loadRecurringTransactionsData() async {
    try {
      final recurringTransactions =
          await LocalStorageService.getRecurringTransactions();
      final dueRecurringTransactions =
          await LocalStorageService.getDueRecurringTransactions();
      final summary =
          await RecurringTransactionService.getRecurringTransactionsSummary();
      final lastProcessingTime =
          await RecurringTransactionService.getLastProcessingTime();

      setState(() {
        _recurringTransactions = recurringTransactions;
        _dueRecurringTransactionsCount = dueRecurringTransactions.length;
        _totalMonthlyRecurringAmount = summary['totalMonthlyAmount'] ?? 0.0;
        _lastRecurringProcessingTime = lastProcessingTime;
      });
    } catch (e) {
      print('Error loading recurring transactions data: $e');
    }
  }

  Future<void> _loadCreditCardData() async {
    try {
      final creditCards = await CreditCardService.getCreditCards();
      final totalDebt = await CreditCardService.getTotalOutstandingBalance();
      final totalLimit = await CreditCardService.getTotalCreditLimit();
      final dueSoonCards =
          await CreditCardService.getCardsWithUpcomingDueDates();

      setState(() {
        _creditCards = creditCards;
        _totalCreditCardDebt = totalDebt;
        _totalCreditLimit = totalLimit;
        _dueSoonCardsCount = dueSoonCards.length;
      });
    } catch (e) {
      print('Error loading credit card data: $e');
    }
  }

  Widget _buildCreditCardSummaryCard() {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: theme.cardColor,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: colorScheme.outline.withValues(alpha: 0.1),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: theme.shadowColor.withValues(alpha: 0.08),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              // Icon(Icons.credit_card, color: colorScheme.primary, size: 24),
              // const SizedBox(width: 8),
              Expanded(
                child: Text(
                  'dashboard_credit_cards'.tr(),
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w700,
                    color: colorScheme.onSurface,
                  ),
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 10,
                  vertical: 6,
                ),
                decoration: BoxDecoration(
                  color:
                      _totalCreditCardDebt > 0
                          ? colorScheme.error.withValues(alpha: 0.1)
                          : colorScheme.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '${_creditCards.length} ${_creditCards.length == 1 ? 'dashboard_card'.tr() : 'dashboard_cards'.tr()}',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color:
                        _totalCreditCardDebt > 0
                            ? colorScheme.error
                            : colorScheme.primary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Show different content based on whether cards exist
          if (_creditCards.isEmpty) ...[
            // Empty state
            Column(
              children: [
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: colorScheme.primary.withValues(alpha: 0.1),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.credit_card_outlined,
                    size: 32,
                    color: colorScheme.primary.withValues(alpha: 0.7),
                  ),
                ),
                const SizedBox(height: 16),
                Text(
                  'dashboard_no_credit_cards_added'.tr(),
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: colorScheme.onSurface,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'dashboard_add_credit_cards_message'.tr(),
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),
                _buildPrimaryActionCard(
                  icon: Icons.add_card,
                  title: 'dashboard_add_first_credit_card'.tr(),
                  subtitle: 'dashboard_get_started_credit_card_tracking'.tr(),
                  color: colorScheme.primary,
                  onTap: () async {
                    print('DEBUG: Add Credit Card button pressed');
                    try {
                      print(
                        'DEBUG: Navigating to ${GoRouterConstants.addCreditCard}',
                      );
                      await GoRouter.of(
                        context,
                      ).pushNamed(GoRouterConstants.addCreditCard);
                      print('DEBUG: Navigation successful');
                      _loadData(); // Refresh data when returning
                    } catch (e) {
                      print('DEBUG: Navigation error: $e');
                      // Fallback navigation
                      if (mounted) {
                        Navigator.of(context).pushNamed('/add-credit-card');
                      }
                    }
                  },
                ),
              ],
            ),
          ] else ...[
            // Debt Summary (when cards exist)
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color:
                    _totalCreditCardDebt > 0
                        ? colorScheme.error.withValues(alpha: 0.05)
                        : Colors.green.withValues(alpha: 0.05),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color:
                      _totalCreditCardDebt > 0
                          ? colorScheme.error.withValues(alpha: 0.2)
                          : Colors.green.withValues(alpha: 0.2),
                  width: 1,
                ),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Total Debt',
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: colorScheme.onSurface.withValues(alpha: 0.7),
                          ),
                        ),
                        const SizedBox(height: 6),
                        Text(
                          _isBalanceVisible
                              ? '₹${NumberFormat('#,##,###').format(_totalCreditCardDebt)}'
                              : '₹••••••',
                          style: theme.textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                            color:
                                _totalCreditCardDebt > 0
                                    ? colorScheme.error
                                    : Colors.green,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 20),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Text(
                          'Total Limit',
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: colorScheme.onSurface.withValues(alpha: 0.7),
                          ),
                        ),
                        const SizedBox(height: 6),
                        Text(
                          _isBalanceVisible
                              ? '₹${NumberFormat('#,##,###').format(_totalCreditLimit)}'
                              : '₹••••••',
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                            color: colorScheme.primary,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 16),

            // Alert indicators
            if (_dueSoonCardsCount > 0) ...[
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.orange,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Icon(
                          Icons.schedule,
                          color: Colors.white,
                          size: 14,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          'dashboard_due_soon_count'.tr(
                            namedArgs: {'count': _dueSoonCardsCount.toString()},
                          ),
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
            ],

            // Action buttons (when cards exist)
            Row(
              children: [
                Expanded(
                  child: _buildActionCard(
                    icon: Icons.receipt_long,
                    label: 'dashboard_entries'.tr(),
                    color: colorScheme.tertiary,
                    onTap: () async {
                      await GoRouter.of(
                        context,
                      ).pushNamed(GoRouterConstants.creditCardTransactionList);
                      _loadData(); // Refresh data when returning
                    },
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildActionCard(
                    icon: Icons.add_shopping_cart,
                    label: 'dashboard_add_new'.tr(),
                    color: Colors.green,
                    isPrimary: true,
                    onTap: () async {
                      print('DEBUG: New Transaction button pressed');
                      try {
                        print(
                          'DEBUG: Navigating to ${GoRouterConstants.addCreditCardTransaction}',
                        );
                        await GoRouter.of(
                          context,
                        ).pushNamed(GoRouterConstants.addCreditCardTransaction);
                        print('DEBUG: Navigation successful');
                        _loadData(); // Refresh data when returning
                      } catch (e) {
                        print('DEBUG: Navigation error: $e');
                        // Show error to user
                        if (mounted) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text(
                                'dashboard_navigation_error'.tr(
                                  namedArgs: {'error': e.toString()},
                                ),
                              ),
                              backgroundColor: Colors.red,
                            ),
                          );
                        }
                      }
                    },
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildActionCard({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
    bool isPrimary = false,
  }) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isPrimary ? color.withValues(alpha: 0.1) : theme.cardColor,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color:
                isPrimary
                    ? color.withValues(alpha: 0.3)
                    : colorScheme.outline.withValues(alpha: 0.2),
            width: 1.5,
          ),
          boxShadow: [
            BoxShadow(
              color: theme.shadowColor.withValues(alpha: 0.06),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Container(
            //   padding: const EdgeInsets.all(12),
            //   decoration: BoxDecoration(
            //     color: color.withValues(alpha: isPrimary ? 0.2 : 0.1),
            //     borderRadius: BorderRadius.circular(10),
            //   ),
            // child: Icon(icon, color: color, size: 16),
            // ),
            // const SizedBox(height: 8),
            Text(
              label,
              style: theme.textTheme.bodySmall?.copyWith(
                fontWeight: FontWeight.w600,
                color: isPrimary ? color : colorScheme.onSurface,
              ),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPrimaryActionCard({
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
  }) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.08),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: color.withValues(alpha: 0.2), width: 1.5),
          boxShadow: [
            BoxShadow(
              color: theme.shadowColor.withValues(alpha: 0.1),
              blurRadius: 12,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.15),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(icon, color: color, size: 24),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: theme.textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: color,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: colorScheme.onSurface.withValues(alpha: 0.7),
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              color: color.withValues(alpha: 0.7),
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecurringTransactionsCard() {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: theme.cardColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: theme.shadowColor.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  'dashboard_recurring_transactions'.tr(),
                  style:
                      theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ) ??
                      theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ),
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  TextButton.icon(
                    onPressed: () async {
                      await _processRecurringTransactions();
                      // Reload data after manual processing
                      await _loadData();
                    },
                    icon: const Icon(Icons.refresh, size: 16),
                    label: Text('dashboard_process'.tr()),
                    style: TextButton.styleFrom(foregroundColor: Colors.green),
                  ),
                  TextButton(
                    onPressed: () async {
                      await GoRouter.of(
                        context,
                      ).pushNamed(GoRouterConstants.recurringTransactions);
                      // Refresh data when returning from recurring transactions
                      _loadData();
                    },
                    child: Text(
                      'dashboard_view_all'.tr(),
                      style:
                          theme.textTheme.bodyMedium?.copyWith(
                            color: colorScheme.primary,
                            fontWeight: FontWeight.w600,
                          ) ??
                          theme.textTheme.bodyMedium?.copyWith(
                            color: colorScheme.primary,
                            fontWeight: FontWeight.w600,
                          ),
                    ),
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Recurring transactions summary
          Row(
            children: [
              Expanded(
                child: _buildRecurringSummaryCard(
                  'dashboard_active'.tr(),
                  '${_recurringTransactions.where((t) => t.isActive).length}',
                  Icons.check_circle,
                  Colors.green,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildRecurringSummaryCard(
                  'dashboard_due'.tr(),
                  '$_dueRecurringTransactionsCount',
                  Icons.schedule,
                  _dueRecurringTransactionsCount > 0
                      ? Colors.orange
                      : theme.hintColor,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildRecurringSummaryCard(
                  'dashboard_monthly'.tr(),
                  _formatAmount(_totalMonthlyRecurringAmount),
                  Icons.calendar_month,
                  colorScheme.primary,
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Due recurring transactions alert
          if (_dueRecurringTransactionsCount > 0) ...[
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.orange.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
              ),
              child: Row(
                children: [
                  Icon(Icons.warning_amber, color: Colors.orange, size: 20),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'dashboard_recurring_transactions_due'.tr(
                        namedArgs: {
                          'count': _dueRecurringTransactionsCount.toString(),
                        },
                      ),
                      style:
                          theme.textTheme.bodyMedium?.copyWith(
                            color: Colors.orange,
                            fontWeight: FontWeight.w500,
                          ) ??
                          theme.textTheme.bodyMedium?.copyWith(
                            color: Colors.orange,
                            fontWeight: FontWeight.w500,
                          ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
          ],

          // Top recurring transactions (showing first 3)
          Text(
            'dashboard_recent_recurring'.tr(),
            style:
                theme.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: theme.hintColor,
                ) ??
                theme.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: theme.hintColor,
                ),
          ),
          const SizedBox(height: 12),
          ..._recurringTransactions
              .where((t) => t.isActive)
              .take(3)
              .map(
                (transaction) => _buildRecurringTransactionItem(transaction),
              ),

          // Show more button if there are more recurring transactions
          if (_recurringTransactions.length > 3) ...[
            const SizedBox(height: 12),
            Center(
              child: TextButton(
                onPressed: () async {
                  await GoRouter.of(
                    context,
                  ).pushNamed(GoRouterConstants.recurringTransactions);
                  // Refresh data when returning from recurring transactions
                  _loadData();
                },
                child: Text(
                  'dashboard_view_all_recurring_transactions'.tr(
                    namedArgs: {
                      'count': _recurringTransactions.length.toString(),
                    },
                  ),
                  style:
                      theme.textTheme.bodyMedium?.copyWith(
                        color: colorScheme.primary,
                        fontWeight: FontWeight.w600,
                      ) ??
                      theme.textTheme.bodyMedium?.copyWith(
                        color: colorScheme.primary,
                        fontWeight: FontWeight.w600,
                      ),
                ),
              ),
            ),
          ],

          // Last processing time info
          if (_lastRecurringProcessingTime != null) ...[
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: theme.hintColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.schedule, size: 14, color: theme.hintColor),
                  const SizedBox(width: 6),
                  Text(
                    'dashboard_last_processed'.tr(
                      namedArgs: {
                        'date': DateFormat(
                          'MMM dd, yyyy - hh:mm a',
                        ).format(_lastRecurringProcessingTime!),
                      },
                    ),
                    style:
                        theme.textTheme.bodySmall?.copyWith(
                          color: theme.hintColor,
                          fontSize: 11,
                        ) ??
                        theme.textTheme.bodySmall?.copyWith(
                          color: theme.hintColor,
                          fontSize: 11,
                        ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildRecurringSummaryCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.2)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 8),
          Text(
            value,
            style:
                theme.textTheme.bodyMedium?.copyWith(
                  color: color,
                  fontWeight: FontWeight.bold,
                ) ??
                theme.textTheme.bodyMedium?.copyWith(
                  color: color,
                  fontWeight: FontWeight.bold,
                ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style:
                theme.textTheme.bodySmall?.copyWith(color: theme.hintColor) ??
                theme.textTheme.bodySmall?.copyWith(color: theme.hintColor),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildRecurringTransactionItem(RecurringTransactionModel transaction) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final isDueToday =
        transaction.nextDueDate.day == DateTime.now().day &&
        transaction.nextDueDate.month == DateTime.now().month &&
        transaction.nextDueDate.year == DateTime.now().year;

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: theme.hintColor.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color:
              isDueToday
                  ? Colors.orange.withValues(alpha: 0.3)
                  : theme.hintColor.withValues(alpha: 0.2),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(6),
            decoration: BoxDecoration(
              color: _getFrequencyColor(
                transaction.frequency,
              ).withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              _getFrequencyIcon(transaction.frequency),
              color: _getFrequencyColor(transaction.frequency),
              size: 16,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  transaction.description,
                  style:
                      theme.textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ) ??
                      theme.textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),
                Text(
                  'Next: ${DateFormat('MMM dd').format(transaction.nextDueDate)}',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: isDueToday ? Colors.orange : theme.hintColor,
                    fontWeight:
                        isDueToday ? FontWeight.w600 : FontWeight.normal,
                  ),
                ),
              ],
            ),
          ),
          Text(
            _isRecurringTransactionIncome(transaction)
                ? _formatAmount(transaction.amount)
                : _formatAmount(transaction.amount, forceVisible: true),
            style:
                theme.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color:
                      _isRecurringTransactionIncome(transaction)
                          ? Colors.green
                          : colorScheme.error,
                ) ??
                theme.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color:
                      _isRecurringTransactionIncome(transaction)
                          ? Colors.green
                          : colorScheme.error,
                ),
          ),
        ],
      ),
    );
  }

  Color _getFrequencyColor(RecurringFrequency frequency) {
    final colorScheme = Theme.of(context).colorScheme;
    switch (frequency) {
      case RecurringFrequency.daily:
        return colorScheme.error;
      case RecurringFrequency.weekly:
        return Colors.orange;
      case RecurringFrequency.monthly:
        return colorScheme.primary;
      case RecurringFrequency.quarterly:
        return colorScheme.secondary;
      case RecurringFrequency.halfYearly:
        return Colors.blue;
      case RecurringFrequency.yearly:
        return Colors.green;
    }
  }

  IconData _getFrequencyIcon(RecurringFrequency frequency) {
    switch (frequency) {
      case RecurringFrequency.daily:
        return Icons.calendar_today;
      case RecurringFrequency.weekly:
        return Icons.calendar_view_week;
      case RecurringFrequency.monthly:
        return Icons.calendar_view_month;
      case RecurringFrequency.quarterly:
        return Icons.date_range;
      case RecurringFrequency.halfYearly:
        return Icons.event_note;
      case RecurringFrequency.yearly:
        return Icons.calendar_month;
    }
  }
}
