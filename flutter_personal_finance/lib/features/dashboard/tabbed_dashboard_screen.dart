import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';

import '../../resources/app_theme.dart';
import '../../resources/app_text_styles.dart';

// Import all the screens for the tabs
import 'dashboard_screen.dart';
import '../credit_card/credit_card_list_screen.dart';
import '../investment/investment_dashboard_screen.dart';
import '../profile/profile_screen.dart';

class TabbedDashboardScreen extends StatefulWidget {
  const TabbedDashboardScreen({super.key});

  @override
  State<TabbedDashboardScreen> createState() => _TabbedDashboardScreenState();
}

class _TabbedDashboardScreenState extends State<TabbedDashboardScreen>
    with TickerProviderStateMixin {
  int _currentIndex = 0;
  late PageController _pageController;

  // List of tab items - labels will be translated dynamically in build method
  final List<TabItem> _tabs = [
    TabItem(
      labelKey: 'dashboard_tab_home',
      icon: Icons.home_outlined,
      activeIcon: Icons.home,
      screen: const DashboardScreen(),
    ),
    TabItem(
      labelKey: 'dashboard_tab_credit_cards',
      icon: Icons.credit_card_outlined,
      activeIcon: Icons.credit_card,
      screen: const CreditCardListScreen(),
    ),
    TabItem(
      labelKey: 'dashboard_tab_investment',
      icon: Icons.trending_up_outlined,
      activeIcon: Icons.trending_up,
      screen: const InvestmentDashboardScreen(),
    ),
    TabItem(
      labelKey: 'dashboard_tab_profile',
      icon: Icons.person_outline,
      activeIcon: Icons.person,
      screen: const ProfileScreen(),
    ),
  ];

  @override
  void initState() {
    super.initState();
    _pageController = PageController(initialPage: _currentIndex);
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  void _onTabTapped(int index) {
    setState(() {
      _currentIndex = index;
    });
    _pageController.jumpToPage(index); // Jump directly to target tab
  }

  void _onPageChanged(int index) {
    setState(() {
      _currentIndex = index;
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      body: PageView(
        controller: _pageController,
        onPageChanged: _onPageChanged,
        physics: const NeverScrollableScrollPhysics(), // Disable swipe gestures
        children: _tabs.map((tab) => tab.screen).toList(),
      ),
      bottomNavigationBar: Container(
        decoration: BoxDecoration(
          color: theme.bottomNavigationBarTheme.backgroundColor,
          boxShadow: [
            BoxShadow(
              color: AppColorPalette.shadow.withValues(alpha: 0.1),
              offset: const Offset(0, -2),
              blurRadius: 8,
              spreadRadius: 0,
            ),
          ],
        ),
        child: SafeArea(
          child: Container(
            height: 65,
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children:
                  _tabs.asMap().entries.map((entry) {
                    final index = entry.key;
                    final tab = entry.value;
                    final isSelected = index == _currentIndex;

                    return Expanded(
                      child: GestureDetector(
                        onTap: () => _onTabTapped(index),
                        behavior: HitTestBehavior.opaque,
                        child: Container(
                          height: 49,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(12),
                            color: Colors.transparent,
                          ),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                isSelected ? tab.activeIcon : tab.icon,
                                size: 24,
                                color:
                                    isSelected
                                        ? theme.colorScheme.primary
                                        : theme.colorScheme.onSurface
                                            .withValues(alpha: 0.6),
                              ),
                              const SizedBox(height: 2),
                              Text(
                                tab.labelKey.tr(context: context),
                                style: AppTextStyles.labelSmall.copyWith(
                                  color:
                                      isSelected
                                          ? theme.colorScheme.primary
                                          : theme.colorScheme.onSurface
                                              .withValues(alpha: 0.6),
                                  fontWeight:
                                      isSelected
                                          ? FontWeight.w600
                                          : FontWeight.w400,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ],
                          ),
                        ),
                      ),
                    );
                  }).toList(),
            ),
          ),
        ),
      ),
    );
  }
}

class TabItem {
  final String labelKey;
  final IconData icon;
  final IconData activeIcon;
  final Widget screen;

  const TabItem({
    required this.labelKey,
    required this.icon,
    required this.activeIcon,
    required this.screen,
  });
}
