import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:easy_localization/easy_localization.dart';

import '../../../utils/helper/route.dart';
import '../../models/bank_account_model.dart';

class BankAccountsSection extends StatelessWidget {
  final List<BankAccountModel> bankAccounts;
  final bool isBalanceVisible;
  final VoidCallback onRefreshData;

  const BankAccountsSection({
    super.key,
    required this.bankAccounts,
    required this.isBalanceVisible,
    required this.onRefreshData,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'dashboard_accounts'.tr(),
              style:
                  theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ) ??
                  theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            TextButton(
              onPressed: () async {
                // Navigate to add bank screen using GoRouter
                await GoRouter.of(context).pushNamed(GoRouterConstants.addBank);

                // Refresh dashboard data when user returns
                onRefreshData();
              },
              child: Text(
                'dashboard_add_bank'.tr(),
                style:
                    theme.textTheme.bodyMedium?.copyWith(
                      color: colorScheme.primary,
                      fontWeight: FontWeight.w600,
                    ) ??
                    theme.textTheme.bodyMedium?.copyWith(
                      color: colorScheme.primary,
                      fontWeight: FontWeight.w600,
                    ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),

        // Bank account cards
        if (bankAccounts.isEmpty)
          _buildEmptyBankAccountsCard(context)
        else
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              childAspectRatio: 1.3,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
            ),
            itemCount: bankAccounts.length,
            itemBuilder: (context, index) {
              return _buildBankAccountCard(bankAccounts[index], context);
            },
          ),
      ],
    );
  }

  Widget _buildEmptyBankAccountsCard(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      padding: const EdgeInsets.all(32),
      decoration: BoxDecoration(
        color: theme.cardColor,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: colorScheme.outline.withValues(alpha: 0.1),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: theme.shadowColor.withValues(alpha: 0.08),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: colorScheme.primary.withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.account_balance_outlined,
              size: 32,
              color: colorScheme.primary.withValues(alpha: 0.7),
            ),
          ),
          const SizedBox(height: 16),
          Text(
            'dashboard_no_bank_accounts'.tr(),
            style: theme.textTheme.titleMedium?.copyWith(
              color: colorScheme.onSurface,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'dashboard_add_first_bank'.tr(),
            textAlign: TextAlign.center,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBankAccountCard(BankAccountModel account, BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        _editBankAccount(account, context);
      },
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: theme.cardColor,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: colorScheme.outline.withValues(alpha: 0.1),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: theme.shadowColor.withValues(alpha: 0.08),
              blurRadius: 12,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: colorScheme.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    Icons.account_balance,
                    color: colorScheme.primary,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    account.bankName,
                    style: theme.textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: colorScheme.onSurface,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              _formatAmount(account.currentAmount),
              style: theme.textTheme.titleMedium?.copyWith(
                color:
                    account.currentAmount >= 0
                        ? Colors.green
                        : colorScheme.error,
                fontWeight: FontWeight.bold,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 4),
            Text(
              'Account: ****${account.accountNumber.substring(account.accountNumber.length - 4)}',
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.hintColor,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _editBankAccount(
    BankAccountModel account,
    BuildContext context,
  ) async {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    // Use GoRouter with extra parameter for BankAccountModel
    final result = await GoRouter.of(context).pushNamed<bool>(
      GoRouterConstants.editBank,
      pathParameters: {'bankId': account.id.toString()},
      extra: account,
    );

    if (result == true) {
      onRefreshData();
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('dashboard_account_updated'.tr()),
          backgroundColor: colorScheme.primary,
        ),
      );
    }
  }

  /// Helper function to format amount based on visibility setting
  String _formatAmount(double amount) {
    if (isBalanceVisible) {
      return '₹${NumberFormat('#,##,###').format(amount)}';
    } else {
      return '****';
    }
  }
}
