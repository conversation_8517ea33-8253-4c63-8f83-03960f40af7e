import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:easy_localization/easy_localization.dart';

import '../../../utils/helper/route.dart';
import '../../models/credit_card_model.dart';

class CreditCardsSection extends StatelessWidget {
  final List<CreditCardModel> creditCards;
  final double totalCreditCardDebt;
  final double totalCreditLimit;
  final int overdueCardsCount;
  final int dueSoonCardsCount;
  final bool isBalanceVisible;
  final VoidCallback onRefreshData;

  const CreditCardsSection({
    super.key,
    required this.creditCards,
    required this.totalCreditCardDebt,
    required this.totalCreditLimit,
    required this.overdueCardsCount,
    required this.dueSoonCardsCount,
    required this.isBalanceVisible,
    required this.onRefreshData,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: theme.cardColor,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: colorScheme.outline.withValues(alpha: 0.1),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: theme.shadowColor.withValues(alpha: 0.08),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  'dashboard_credit_cards'.tr(),
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w700,
                    color: colorScheme.onSurface,
                  ),
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 10,
                  vertical: 6,
                ),
                decoration: BoxDecoration(
                  color:
                      totalCreditCardDebt > 0
                          ? colorScheme.error.withValues(alpha: 0.1)
                          : colorScheme.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  'dashboard_credit_card_count'.tr(
                    namedArgs: {'count': creditCards.length.toString()},
                  ),
                  style: theme.textTheme.bodySmall?.copyWith(
                    color:
                        totalCreditCardDebt > 0
                            ? colorScheme.error
                            : colorScheme.primary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Show different content based on whether cards exist
          if (creditCards.isEmpty) ...[
            // Empty state
            _buildEmptyState(context),
          ] else ...[
            // Debt Summary (when cards exist)
            _buildDebtSummary(context),
            const SizedBox(height: 16),

            // Alert indicators
            if (overdueCardsCount > 0 || dueSoonCardsCount > 0) ...[
              _buildAlertIndicators(context),
              const SizedBox(height: 16),
            ],

            // Action buttons (when cards exist)
            _buildActionButtons(context),
          ],
        ],
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: colorScheme.primary.withValues(alpha: 0.1),
            shape: BoxShape.circle,
          ),
          child: Icon(
            Icons.credit_card_outlined,
            size: 32,
            color: colorScheme.primary.withValues(alpha: 0.7),
          ),
        ),
        const SizedBox(height: 16),
        Text(
          'dashboard_no_credit_cards'.tr(),
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: colorScheme.onSurface,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'dashboard_add_credit_cards_desc'.tr(),
          style: theme.textTheme.bodyMedium?.copyWith(
            color: colorScheme.onSurface.withValues(alpha: 0.7),
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 24),
        _buildPrimaryActionCard(
          context: context,
          icon: Icons.add_card,
          title: 'dashboard_add_first_credit_card'.tr(),
          subtitle: 'dashboard_credit_card_subtitle'.tr(),
          color: colorScheme.primary,
          onTap: () async {
            print('DEBUG: Add Credit Card button pressed');
            try {
              print('DEBUG: Navigating to ${GoRouterConstants.addCreditCard}');
              await GoRouter.of(
                context,
              ).pushNamed(GoRouterConstants.addCreditCard);
              print('DEBUG: Navigation successful');
              onRefreshData(); // Refresh data when returning
            } catch (e) {
              print('DEBUG: Navigation error: $e');
              // Fallback navigation
              if (context.mounted) {
                Navigator.of(context).pushNamed('/add-credit-card');
              }
            }
          },
        ),
      ],
    );
  }

  Widget _buildDebtSummary(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color:
            totalCreditCardDebt > 0
                ? colorScheme.error.withValues(alpha: 0.05)
                : Colors.green.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color:
              totalCreditCardDebt > 0
                  ? colorScheme.error.withValues(alpha: 0.2)
                  : Colors.green.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'dashboard_total_debt'.tr(),
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                ),
                const SizedBox(height: 6),
                Text(
                  isBalanceVisible
                      ? '₹${NumberFormat('#,##,###').format(totalCreditCardDebt)}'
                      : '₹••••••',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color:
                        totalCreditCardDebt > 0
                            ? colorScheme.error
                            : Colors.green,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(width: 20),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'dashboard_total_limit'.tr(),
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                ),
                const SizedBox(height: 6),
                Text(
                  isBalanceVisible
                      ? '₹${NumberFormat('#,##,###').format(totalCreditLimit)}'
                      : '₹••••••',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: colorScheme.primary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAlertIndicators(BuildContext context) {
    return Row(
      children: [
        if (overdueCardsCount > 0) ...[
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.red,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(Icons.warning, color: Colors.white, size: 14),
                const SizedBox(width: 4),
                Text(
                  'dashboard_overdue_cards'.tr(
                    namedArgs: {'count': overdueCardsCount.toString()},
                  ),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(width: 8),
        ],
        if (dueSoonCardsCount > 0) ...[
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.orange,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(Icons.schedule, color: Colors.white, size: 14),
                const SizedBox(width: 4),
                Text(
                  'dashboard_due_soon_cards'.tr(
                    namedArgs: {'count': dueSoonCardsCount.toString()},
                  ),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: _buildActionCard(
            context: context,
            icon: Icons.credit_card,
            label: 'dashboard_cards_label'.tr(),
            color: Theme.of(context).colorScheme.primary,
            onTap: () async {
              await GoRouter.of(
                context,
              ).pushNamed(GoRouterConstants.creditCardList);
              onRefreshData(); // Refresh data when returning
            },
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildActionCard(
            context: context,
            icon: Icons.receipt_long,
            label: 'dashboard_entries_label'.tr(),
            color: Theme.of(context).colorScheme.tertiary,
            onTap: () async {
              await GoRouter.of(
                context,
              ).pushNamed(GoRouterConstants.creditCardTransactionList);
              onRefreshData(); // Refresh data when returning
            },
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildActionCard(
            context: context,
            icon: Icons.add_shopping_cart,
            label: 'dashboard_add_new'.tr(),
            color: Colors.green,
            isPrimary: true,
            onTap: () async {
              print('DEBUG: New Transaction button pressed');
              try {
                print(
                  'DEBUG: Navigating to ${GoRouterConstants.addCreditCardTransaction}',
                );
                await GoRouter.of(
                  context,
                ).pushNamed(GoRouterConstants.addCreditCardTransaction);
                print('DEBUG: Navigation successful');
                onRefreshData(); // Refresh data when returning
              } catch (e) {
                print('DEBUG: Navigation error: $e');
                // Show error to user
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Navigation error: $e'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            },
          ),
        ),
      ],
    );
  }

  Widget _buildActionCard({
    required BuildContext context,
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
    bool isPrimary = false,
  }) {
    final theme = Theme.of(context);

    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: color.withValues(alpha: 0.2), width: 1),
        ),
        child: Column(
          children: [
            Icon(icon, color: color, size: 24),
            const SizedBox(height: 6),
            Text(
              label,
              style: theme.textTheme.bodySmall?.copyWith(
                color: color,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPrimaryActionCard({
    required BuildContext context,
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
  }) {
    final theme = Theme.of(context);

    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: color.withValues(alpha: 0.2), width: 1),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(icon, color: color, size: 20),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: color,
                    ),
                  ),
                  Text(
                    subtitle,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: color.withValues(alpha: 0.7),
                    ),
                  ),
                ],
              ),
            ),
            Icon(Icons.arrow_forward_ios, color: color, size: 16),
          ],
        ),
      ),
    );
  }
}
