import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:easy_localization/easy_localization.dart';

import '../../../utils/helper/route.dart';
import '../../models/transaction_model.dart';
import '../../models/bank_account_model.dart';

class RecentTransactionsSection extends StatelessWidget {
  final List<TransactionModel> recentTransactions;
  final List<BankAccountModel> bankAccounts;
  final bool isBalanceVisible;
  final VoidCallback onRefreshData;

  const RecentTransactionsSection({
    super.key,
    required this.recentTransactions,
    required this.bankAccounts,
    required this.isBalanceVisible,
    required this.onRefreshData,
  });

  @override
  Widget build(BuildContext context) {
    if (recentTransactions.isEmpty) {
      return const SizedBox.shrink();
    }

    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'dashboard_recent_transactions'.tr(),
              style:
                  theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ) ??
                  theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: colorScheme.onSurface,
                  ),
            ),
            TextButton(
              onPressed: () async {
                await GoRouter.of(
                  context,
                ).pushNamed(GoRouterConstants.transactionList);
                // Refresh data when returning from transaction list
                onRefreshData();
              },
              child: Text(
                'dashboard_view_all'.tr(),
                style:
                    theme.textTheme.bodyMedium?.copyWith(
                      color: colorScheme.primary,
                      fontWeight: FontWeight.w600,
                    ) ??
                    theme.textTheme.bodyMedium?.copyWith(
                      color: colorScheme.primary,
                      fontWeight: FontWeight.w600,
                    ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),

        // Recent transactions list
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: recentTransactions.length,
          itemBuilder: (context, index) {
            return _buildTransactionCard(recentTransactions[index], context);
          },
        ),
      ],
    );
  }

  Widget _buildTransactionCard(
    TransactionModel transaction,
    BuildContext context,
  ) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final isIncome = transaction.type == TransactionType.credit;
    final color = isIncome ? Colors.green : Colors.red;
    final icon = isIncome ? Icons.arrow_upward : Icons.arrow_downward;

    // Find bank account for this transaction
    final bankAccount = bankAccounts.firstWhere(
      (account) => account.id == transaction.bankAccountId,
      orElse:
          () => BankAccountModel(
            bankName: 'dashboard_unknown_bank'.tr(),
            accountNumber: '',
            initialAmount: 0,
            currentAmount: 0,
          ),
    );

    // Get bank color based on bank name
    final bankColor = _getBankColor(bankAccount.bankName, theme);

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.cardColor,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: colorScheme.outline.withValues(alpha: 0.1),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: theme.shadowColor.withValues(alpha: 0.08),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        children: [
          // Transaction type icon
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(icon, color: color, size: 20),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  transaction.category,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: colorScheme.onSurface,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),
                Text(
                  '${transaction.type.name.toUpperCase()} • ${DateFormat('MMM dd, yyyy').format(transaction.date)}',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.hintColor,
                  ),
                ),
                const SizedBox(height: 2),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 2,
                  ),
                  decoration: BoxDecoration(
                    color: bankColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    bankAccount.bankName,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: bankColor,
                      fontWeight: FontWeight.w500,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(width: 12),
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                '${isIncome ? '+' : '-'}${_formatAmount(transaction.amount, forceVisible: !isIncome)}',
                style: theme.textTheme.titleSmall?.copyWith(
                  color: color,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 2),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Text(
                  isIncome ? 'dashboard_credit'.tr() : 'dashboard_debit'.tr(),
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: color,
                    fontWeight: FontWeight.w500,
                    fontSize: 10,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Helper method to get bank color based on bank name
  Color _getBankColor(String bankName, ThemeData theme) {
    final lowerBankName = bankName.toLowerCase();

    if (lowerBankName.contains('State Bank of India (SBI)')) {
      return const Color(0xFF1565C0); // SBI Blue
    } else if (lowerBankName.contains('hdfc')) {
      return const Color(0xFFD32F2F); // HDFC Red
    } else if (lowerBankName.contains('icici')) {
      return const Color(0xFFE65100); // ICICI Orange
    } else if (lowerBankName.contains('Axis Bank')) {
      return const Color(0xFF7B1FA2); // Axis Purple
    } else if (lowerBankName.contains('kotak')) {
      return const Color(0xFFD32F2F); // Kotak Red
    } else if (lowerBankName.contains('yes') ||
        lowerBankName.contains('yes bank')) {
      return const Color(0xFF1976D2); // Yes Bank Blue
    } else if (lowerBankName.contains('pnb') ||
        lowerBankName.contains('punjab')) {
      return const Color(0xFF388E3C); // PNB Green
    } else if (lowerBankName.contains('canara')) {
      return const Color(0xFFE65100); // Canara Orange
    } else if (lowerBankName.contains('union')) {
      return const Color(0xFF795548); // Union Brown
    } else if (lowerBankName.contains('bank of baroda') ||
        lowerBankName.contains('bob')) {
      return const Color(0xFF1976D2); // BOB Blue
    } else if (lowerBankName.contains('idbi') ||
        lowerBankName.contains('indian bank')) {
      return const Color(0xFF388E3C); // Green
    } else if (lowerBankName.contains('federal')) {
      return const Color(0xFF7B1FA2); // Purple
    } else if (lowerBankName.contains('indusind')) {
      return const Color(0xFFE65100); // Orange
    } else {
      return theme.hintColor; // Default bank color
    }
  }

  /// Helper function to format amount based on visibility setting
  String _formatAmount(double amount, {bool forceVisible = false}) {
    if (forceVisible || isBalanceVisible) {
      return '₹${NumberFormat('#,##,###').format(amount)}';
    } else {
      return '****';
    }
  }
}
