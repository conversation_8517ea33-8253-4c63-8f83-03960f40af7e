import 'package:flutter/material.dart';

import 'package:go_router/go_router.dart';
import 'package:easy_localization/easy_localization.dart';

import '../../../utils/helper/route.dart';
import '../../../services/local_storage_service.dart';
import '../../../services/notification_service.dart';
import '../../models/upcoming_expense_model.dart';
import '../../models/bank_account_model.dart';

class BalanceSection extends StatelessWidget {
  final double totalBalance;
  final double totalIncome;
  final double totalExpenses;
  final double totalUpcomingExpenses;
  final bool isBalanceVisible;
  final VoidCallback onVisibilityToggle;
  final VoidCallback onRefreshData;
  final List<BankAccountModel> bankAccounts;

  const BalanceSection({
    super.key,
    required this.totalBalance,
    required this.totalIncome,
    required this.totalExpenses,
    required this.totalUpcomingExpenses,
    required this.isBalanceVisible,
    required this.onVisibilityToggle,
    required this.onRefreshData,
    required this.bankAccounts,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: colorScheme.primary,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: colorScheme.primary.withValues(alpha: 0.3),
            blurRadius: 8,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'dashboard_total_balance'.tr(),
                style:
                    theme.textTheme.bodyLarge?.copyWith(
                      color: colorScheme.onPrimary.withValues(alpha: 0.9),
                    ) ??
                    theme.textTheme.bodySmall?.copyWith(
                      color: colorScheme.onPrimary.withValues(alpha: 0.9),
                    ),
              ),
              // Visibility toggle button for income and balance
              Container(
                decoration: BoxDecoration(
                  color: colorScheme.onPrimary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: IconButton(
                  onPressed: onVisibilityToggle,
                  icon: Icon(
                    isBalanceVisible ? Icons.visibility : Icons.visibility_off,
                    color: colorScheme.onPrimary,
                    size: 18,
                  ),
                  tooltip:
                      isBalanceVisible
                          ? 'dashboard_hide_balance'.tr()
                          : 'dashboard_show_balance'.tr(),
                  padding: const EdgeInsets.all(6),
                  constraints: const BoxConstraints(),
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            _formatAmount(totalBalance),
            style:
                theme.textTheme.headlineMedium?.copyWith(
                  color: colorScheme.onPrimary,
                  fontWeight: FontWeight.bold,
                ) ??
                theme.textTheme.headlineMedium?.copyWith(
                  color: colorScheme.onPrimary,
                  fontWeight: FontWeight.bold,
                ),
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'dashboard_income'.tr(),
                  _formatAmount(totalIncome),
                  Icons.arrow_upward,
                  Colors.green,
                  context,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildStatCard(
                  'dashboard_expenses'.tr(),
                  _formatAmount(totalExpenses, forceVisible: true),
                  Icons.arrow_downward,
                  colorScheme.error,
                  context,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: GestureDetector(
                  behavior: HitTestBehavior.opaque,
                  onTap: () => _showAddUpcomingExpenseDialog(context),
                  child: _buildUpcomingStatCard(
                    'dashboard_upcoming'.tr(),
                    _formatAmount(totalUpcomingExpenses, forceVisible: true),
                    Icons.schedule,
                    Colors.orange,
                    context,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
    BuildContext context,
  ) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: colorScheme.onPrimary.withValues(alpha: 0.15),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: colorScheme.onPrimary.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Icon(icon, color: color, size: 14),
              ),
              const SizedBox(width: 6),
              Expanded(
                child: Text(
                  title,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: colorScheme.onPrimary.withValues(alpha: 0.9),
                    fontSize: 10,
                    fontWeight: FontWeight.w500,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          const SizedBox(height: 6),
          Text(
            value,
            style: theme.textTheme.titleSmall?.copyWith(
              color: colorScheme.onPrimary,
              fontWeight: FontWeight.bold,
              fontSize: 12,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Widget _buildUpcomingStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
    BuildContext context,
  ) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: colorScheme.onPrimary.withValues(alpha: 0.15),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: colorScheme.onPrimary.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Icon(icon, color: color, size: 14),
              ),
              const SizedBox(width: 6),
              Expanded(
                child: Text(
                  title,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: colorScheme.onPrimary.withValues(alpha: 0.9),
                    fontSize: 10,
                    fontWeight: FontWeight.w500,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              // List icon that doesn't overlap
              GestureDetector(
                behavior: HitTestBehavior.opaque,
                onTap: () async {
                  await GoRouter.of(
                    context,
                  ).pushNamed(GoRouterConstants.upcomingExpensesList);
                  // Refresh data when returning from upcoming expenses
                  onRefreshData();
                },
                child: Container(
                  padding: const EdgeInsets.all(3),
                  decoration: BoxDecoration(
                    color: colorScheme.onPrimary.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Icon(Icons.list, color: color, size: 10),
                ),
              ),
            ],
          ),
          const SizedBox(height: 6),
          Text(
            value,
            style: theme.textTheme.titleSmall?.copyWith(
              color: colorScheme.onPrimary,
              fontWeight: FontWeight.bold,
              fontSize: 12,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  /// Show dialog to add upcoming expense
  Future<void> _showAddUpcomingExpenseDialog(BuildContext context) async {
    if (bankAccounts.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('dashboard_add_bank_first'.tr()),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    final formKey = GlobalKey<FormState>();
    final amountController = TextEditingController();
    String? selectedCategory;
    String? selectedBankAccountId;
    DateTime selectedDate = DateTime.now();

    final categories = [
      'dashboard_category_food',
      'dashboard_category_entertainment',
      'dashboard_category_utilities',
      'dashboard_category_transportation',
      'dashboard_category_healthcare',
      'dashboard_category_shopping',
      'dashboard_category_travel',
      'dashboard_category_bills',
      'dashboard_category_education',
      'dashboard_category_rent',
      'dashboard_category_insurance',
      'dashboard_category_loans',
      'dashboard_category_investment',
      'dashboard_category_other',
    ];

    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return showDialog<void>(
      context: context,
      builder:
          (BuildContext context) => StatefulBuilder(
            builder:
                (context, setState) => AlertDialog(
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                  title: Text(
                    'dashboard_add_upcoming_expense'.tr(),
                    style: theme.textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: colorScheme.onSurface,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  content: SingleChildScrollView(
                    child: Form(
                      key: formKey,
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: [
                          // Category dropdown
                          DropdownButtonFormField<String>(
                            decoration: InputDecoration(
                              labelText: 'dashboard_category_label'.tr(),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                              contentPadding: const EdgeInsets.symmetric(
                                horizontal: 16,
                                vertical: 12,
                              ),
                            ),
                            value: selectedCategory,
                            items:
                                categories.map((category) {
                                  return DropdownMenuItem<String>(
                                    value: category,
                                    child: Text(category.tr()),
                                  );
                                }).toList(),
                            onChanged: (value) {
                              setState(() {
                                selectedCategory = value;
                              });
                            },
                            validator:
                                (value) =>
                                    value == null
                                        ? 'dashboard_select_category'.tr()
                                        : null,
                          ),
                          const SizedBox(height: 16),

                          // Bank account dropdown
                          DropdownButtonFormField<String>(
                            decoration: InputDecoration(
                              labelText: 'dashboard_bank_account_label'.tr(),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                              contentPadding: const EdgeInsets.symmetric(
                                horizontal: 16,
                                vertical: 12,
                              ),
                            ),
                            value: selectedBankAccountId,
                            items:
                                bankAccounts.map((account) {
                                  return DropdownMenuItem<String>(
                                    value: account.id,
                                    child: Text(
                                      '${account.bankName} (...${account.accountNumber.substring(account.accountNumber.length - 4)})',
                                    ),
                                  );
                                }).toList(),
                            onChanged: (value) {
                              setState(() {
                                selectedBankAccountId = value;
                              });
                            },
                            validator:
                                (value) =>
                                    value == null
                                        ? 'dashboard_select_bank_account'.tr()
                                        : null,
                          ),
                          const SizedBox(height: 16),

                          // Amount field
                          TextFormField(
                            controller: amountController,
                            decoration: InputDecoration(
                              labelText: 'dashboard_amount_label'.tr(),
                              prefixText: '₹ ',
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                              contentPadding: const EdgeInsets.symmetric(
                                horizontal: 16,
                                vertical: 12,
                              ),
                            ),
                            keyboardType: TextInputType.number,
                            validator: (value) {
                              if (value == null || value.trim().isEmpty) {
                                return 'dashboard_amount_required'.tr();
                              }
                              final amount = double.tryParse(value.trim());
                              if (amount == null || amount <= 0) {
                                return 'dashboard_amount_invalid'.tr();
                              }
                              return null;
                            },
                          ),
                          const SizedBox(height: 16),

                          // Date picker
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'dashboard_due_date_label'.tr(),
                                style: theme.textTheme.bodyMedium?.copyWith(
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              const SizedBox(height: 8),
                              GestureDetector(
                                onTap: () async {
                                  final picked = await showDatePicker(
                                    context: context,
                                    initialDate: selectedDate,
                                    firstDate: DateTime.now(),
                                    lastDate: DateTime.now().add(
                                      const Duration(days: 365 * 2),
                                    ),
                                  );
                                  if (picked != null) {
                                    setState(() {
                                      selectedDate = picked;
                                    });
                                  }
                                },
                                child: Container(
                                  padding: const EdgeInsets.all(12),
                                  decoration: BoxDecoration(
                                    border: Border.all(
                                      color: theme.hintColor.withValues(
                                        alpha: 0.3,
                                      ),
                                    ),
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Text(
                                        DateFormat(
                                          'MMM dd, yyyy',
                                        ).format(selectedDate),
                                        style: theme.textTheme.bodyMedium,
                                      ),
                                      const Icon(
                                        Icons.calendar_today,
                                        size: 20,
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.of(context).pop(),
                      child: Text('dashboard_cancel'.tr()),
                    ),
                    TextButton(
                      onPressed: () async {
                        if (formKey.currentState!.validate()) {
                          final amount = double.parse(
                            amountController.text.trim(),
                          );

                          final expense = UpcomingExpenseModel(
                            category: selectedCategory!,
                            amount: amount,
                            dueDate: selectedDate,
                            bankAccountId: selectedBankAccountId,
                          );

                          await LocalStorageService.addUpcomingExpense(expense);

                          // Schedule notification for the new expense
                          await NotificationService.scheduleExpenseNotification(
                            expense,
                          );

                          Navigator.of(context).pop();
                          onRefreshData(); // Refresh dashboard data

                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text(
                                'dashboard_upcoming_expense_added'.tr(),
                              ),
                              backgroundColor: Colors.green,
                            ),
                          );
                        }
                      },
                      child: Text(
                        'dashboard_add'.tr(),
                        style: TextStyle(
                          color: colorScheme.primary,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
          ),
    );
  }

  /// Helper function to format amount based on visibility setting
  String _formatAmount(double amount, {bool forceVisible = false}) {
    if (forceVisible || isBalanceVisible) {
      return '₹${NumberFormat('#,##,###').format(amount)}';
    } else {
      return '****';
    }
  }
}
