import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:easy_localization/easy_localization.dart';

import '../../shared/widgtes/common/custom_button.dart';
import '../../shared/widgtes/common/custom_text_field.dart';
import '../../services/local_storage_service.dart';
import '../../features/models/bank_account_model.dart';
import '../../utils/helper/keyboard_dismiss_wrapper.dart';

class AddBankScreen extends StatefulWidget {
  const AddBankScreen({super.key});

  @override
  State<AddBankScreen> createState() => _AddBankScreenState();
}

class _AddBankScreenState extends State<AddBankScreen> {
  final TextEditingController _bankNameController = TextEditingController();
  final TextEditingController _accountNumberController =
      TextEditingController();
  final TextEditingController _initialAmountController =
      TextEditingController();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  bool _isLoading = false;
  String? _errorMessage;

  @override
  void dispose() {
    _bankNameController.dispose();
    _accountNumberController.dispose();
    _initialAmountController.dispose();
    super.dispose();
  }

  Future<void> _handleAddBank() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final bankName = _bankNameController.text.trim();
      final accountNumber = _accountNumberController.text.trim();
      final initialAmount = double.parse(_initialAmountController.text.trim());

      // Create bank account model
      final bankAccount = BankAccountModel(
        bankName: bankName,
        accountNumber: accountNumber,
        initialAmount: initialAmount,
        currentAmount: initialAmount,
      );

      // Save bank account locally
      await LocalStorageService.addBankAccount(bankAccount);

      if (!mounted) return;
      _showSuccessDialog();
    } catch (e) {
      setState(() {
        _errorMessage = 'bank_add_failed'.tr();
      });
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _showSuccessDialog() {
    final theme = Theme.of(context);

    showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (dialogContext) => AlertDialog(
            title: Text(
              'bank_success_title'.tr(),
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            content: Text(
              'bank_add_success'.tr(),
              style: theme.textTheme.bodyMedium,
            ),
            actions: [
              TextButton(
                onPressed: () {
                  // Close dialog first
                  Navigator.of(dialogContext).pop();

                  // Then safely navigate back to previous screen
                  if (mounted) {
                    Navigator.of(
                      context,
                    ).pop(true); // Return true to indicate success
                  }
                },
                child: Text(
                  'bank_ok'.tr(),
                  style: TextStyle(
                    color: theme.colorScheme.primary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return KeyboardDismissWrapper(
      child: Scaffold(
        backgroundColor: theme.scaffoldBackgroundColor,
        appBar: AppBar(
          title: Text(
            'bank_add_title'.tr(),
            style: theme.appBarTheme.titleTextStyle?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          leading: IconButton(
            icon: const Icon(Icons.arrow_back_ios),
            onPressed: () => context.pop(),
          ),
        ),
        body: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                const SizedBox(height: 20),

                // Info card
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: theme.colorScheme.primary.withValues(alpha: 0.3),
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.info_outline,
                        color: theme.colorScheme.primary,
                        size: 20,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          'bank_add_info'.tr(),
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.colorScheme.primary,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 24),

                // Bank Name field
                CustomTextField(
                  controller: _bankNameController,
                  labelText: 'bank_name_label'.tr(),
                  hintText: 'bank_name_hint'.tr(),
                  prefixIcon: Icons.account_balance,
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'bank_name_required'.tr();
                    }
                    if (value.trim().length < 2) {
                      return 'bank_name_min_length'.tr();
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // Account Number field
                CustomTextField(
                  controller: _accountNumberController,
                  labelText: 'bank_account_number_label'.tr(),
                  hintText: 'bank_account_number_hint'.tr(),
                  prefixIcon: Icons.credit_card,
                  keyboardType: TextInputType.number,
                  inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'bank_account_required'.tr();
                    }
                    if (value.trim().length < 8) {
                      return 'bank_account_min_length'.tr();
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // Initial Balance field
                CustomTextField(
                  controller: _initialAmountController,
                  labelText: 'bank_initial_balance_label'.tr(),
                  hintText: 'bank_initial_balance_hint'.tr(),
                  prefixIcon: Icons.currency_rupee,
                  keyboardType: const TextInputType.numberWithOptions(
                    decimal: true,
                  ),
                  inputFormatters: [
                    FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
                  ],
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'bank_balance_required'.tr();
                    }
                    final amount = double.tryParse(value.trim());
                    if (amount == null) {
                      return 'bank_balance_invalid'.tr();
                    }
                    if (amount < 0) {
                      return 'bank_balance_negative'.tr();
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 24),

                // Error message
                if (_errorMessage != null)
                  Container(
                    padding: const EdgeInsets.all(12),
                    margin: const EdgeInsets.only(bottom: 16),
                    decoration: BoxDecoration(
                      color: theme.colorScheme.error.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: theme.colorScheme.error),
                    ),
                    child: Text(
                      _errorMessage!,
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: theme.colorScheme.error,
                      ),
                    ),
                  ),

                // Add Bank button
                CustomButton.elevated(
                  onPressed: _isLoading ? null : _handleAddBank,
                  backgroundColor: theme.colorScheme.primary,
                  child:
                      _isLoading
                          ? SizedBox(
                            height: 20,
                            width: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                colorScheme.onPrimary,
                              ),
                            ),
                          )
                          : Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.add_circle,
                                color: colorScheme.onPrimary,
                              ),
                              const SizedBox(width: 8),
                              Text(
                                'bank_add_button'.tr(),
                                style: theme.textTheme.titleMedium?.copyWith(
                                  color: colorScheme.onPrimary,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
