import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:easy_localization/easy_localization.dart';
import '../../resources/app_theme.dart';
import '../../services/local_storage_service.dart';
import '../../features/models/budget_model.dart';

class BudgetPlannerScreen extends StatefulWidget {
  const BudgetPlannerScreen({super.key});

  @override
  State<BudgetPlannerScreen> createState() => _BudgetPlannerScreenState();
}

class _BudgetPlannerScreenState extends State<BudgetPlannerScreen> {
  List<BudgetModel> _budgets = [];
  String _selectedCategory = '';
  String? validationMessage;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    await _loadBudgets();
  }

  Future<void> _loadBudgets() async {
    try {
      final budgets = await LocalStorageService.getCurrentMonthBudgets();
      setState(() {
        _budgets = budgets;
      });
    } catch (e) {
      debugPrint('Error loading budgets: $e');
    }
  }

  Future<void> _showCategorySelectionDialog() async {
    final allCategories =
        await LocalStorageService.getIncomeExpenseCategories();
    final incomeCategories =
        allCategories.where((c) => c['type'] == 'income').toList();
    final expenseCategories =
        allCategories.where((c) => c['type'] == 'expense').toList();

    if (incomeCategories.isEmpty && expenseCategories.isEmpty) {
      _showSnackBar('budget_no_categories'.tr());
      return;
    }

    String selectedType = 'expense'; // Default to expense

    if (!mounted) return;
    final categoryResult = await showDialog<String>(
      context: context,
      builder:
          (context) => StatefulBuilder(
            builder: (context, setDialogState) {
              final theme = Theme.of(context);

              return AlertDialog(
                title: Text(
                  'budget_select_category'.tr(),
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                contentPadding: const EdgeInsets.all(16),
                content: SizedBox(
                  width: double.maxFinite,
                  child: ConstrainedBox(
                    constraints: BoxConstraints(
                      maxHeight: MediaQuery.of(context).size.height * 0.6,
                      minHeight: 300,
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // Type Selector
                        Container(
                          decoration: BoxDecoration(
                            color: theme.hintColor.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Row(
                            children: [
                              Expanded(
                                child: GestureDetector(
                                  onTap: () {
                                    setDialogState(() {
                                      selectedType = 'income';
                                    });
                                  },
                                  child: Container(
                                    padding: const EdgeInsets.symmetric(
                                      vertical: 10,
                                    ),
                                    decoration: BoxDecoration(
                                      color:
                                          selectedType == 'income'
                                              ? AppColorPalette.primary
                                              : Colors.transparent,
                                      borderRadius: BorderRadius.circular(6),
                                    ),
                                    child: Text(
                                      'budget_income'.tr(),
                                      textAlign: TextAlign.center,
                                      style: TextStyle(
                                        color:
                                            selectedType == 'income'
                                                ? AppColorPalette.white
                                                : AppColorPalette.grey,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                              Expanded(
                                child: GestureDetector(
                                  onTap: () {
                                    setDialogState(() {
                                      selectedType = 'expense';
                                    });
                                  },
                                  child: Container(
                                    padding: const EdgeInsets.symmetric(
                                      vertical: 10,
                                    ),
                                    decoration: BoxDecoration(
                                      color:
                                          selectedType == 'expense'
                                              ? AppColorPalette.primary
                                              : Colors.transparent,
                                      borderRadius: BorderRadius.circular(6),
                                    ),
                                    child: Text(
                                      'budget_expense'.tr(),
                                      textAlign: TextAlign.center,
                                      style: TextStyle(
                                        color:
                                            selectedType == 'expense'
                                                ? AppColorPalette.white
                                                : AppColorPalette.grey,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 12),

                        // Categories List
                        Flexible(
                          child: ListView.builder(
                            shrinkWrap: true,
                            itemCount:
                                (selectedType == 'income'
                                        ? incomeCategories
                                        : expenseCategories)
                                    .length,
                            itemBuilder: (context, index) {
                              final categories =
                                  selectedType == 'income'
                                      ? incomeCategories
                                      : expenseCategories;
                              final category = categories[index];
                              final isSelected =
                                  category['name'] == _selectedCategory;

                              return Container(
                                margin: const EdgeInsets.only(bottom: 6),
                                child: Material(
                                  color: Colors.transparent,
                                  child: InkWell(
                                    onTap: () {
                                      _selectedCategory = category['name'];
                                      context.pop(category['name']);
                                    },
                                    borderRadius: BorderRadius.circular(6),
                                    child: Container(
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 12,
                                        vertical: 10,
                                      ),
                                      decoration: BoxDecoration(
                                        color:
                                            isSelected
                                                ? AppColorPalette.primary10
                                                : AppColorPalette.white,
                                        borderRadius: BorderRadius.circular(6),
                                        border: Border.all(
                                          color:
                                              isSelected
                                                  ? AppColorPalette.primary
                                                  : AppColorPalette.grey30,
                                        ),
                                      ),
                                      child: Row(
                                        children: [
                                          Icon(
                                            category['icon'],
                                            color:
                                                isSelected
                                                    ? AppColorPalette.primary
                                                    : AppColorPalette.grey,
                                            size: 18,
                                          ),
                                          const SizedBox(width: 10),
                                          Expanded(
                                            child: Text(
                                              category['name'],
                                              style: TextStyle(
                                                fontSize: 14,
                                                fontWeight:
                                                    isSelected
                                                        ? FontWeight.w600
                                                        : FontWeight.normal,
                                                color:
                                                    isSelected
                                                        ? AppColorPalette
                                                            .primary
                                                        : AppColorPalette.black,
                                              ),
                                            ),
                                          ),
                                          if (isSelected)
                                            Icon(
                                              Icons.check_circle,
                                              color: AppColorPalette.primary,
                                              size: 18,
                                            ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                              );
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                actions: [
                  TextButton(
                    onPressed: () => context.pop(),
                    child: Text(
                      'budget_cancel'.tr(),
                      style:
                          theme.textTheme.labelLarge?.copyWith(
                            color: theme.hintColor,
                          ) ??
                          TextStyle(color: theme.hintColor),
                    ),
                  ),
                ],
              );
            },
          ),
    );

    if (categoryResult != null) {
      setState(() {
        _selectedCategory = categoryResult;
      });
    }
  }

  Future<void> _showLimitDialog() async {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final limitController = TextEditingController();

    final limitResult = await showDialog<String>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(
              'Set Monthly Limit',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  '${'budget_category_prefix'.tr()} $_selectedCategory',
                  style: theme.textTheme.bodyMedium,
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: limitController,
                  keyboardType: const TextInputType.numberWithOptions(
                    decimal: true,
                  ),
                  decoration: InputDecoration(
                    labelText: 'budget_monthly_limit_label'.tr(),
                    prefixText: '₹ ',
                    border: OutlineInputBorder(),
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => context.pop(),
                child: Text(
                  'budget_cancel'.tr(),
                  style: theme.textTheme.labelLarge?.copyWith(
                    color: theme.hintColor,
                  ),
                ),
              ),
              ElevatedButton(
                onPressed: () {
                  if (limitController.text.isNotEmpty) {
                    context.pop(limitController.text);
                  }
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColorPalette.primary,
                  foregroundColor: colorScheme.onPrimary,
                ),
                child: Text(
                  'budget_set_limit'.tr(),
                  style: theme.textTheme.labelLarge?.copyWith(
                    color: colorScheme.onPrimary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
    );

    if (limitResult != null && limitResult.isNotEmpty) {
      await _createBudget(limitResult);
    }
  }

  Future<void> _showEditLimitDialog(BudgetModel budget) async {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final limitController = TextEditingController(
      text: budget.monthlyLimit.toStringAsFixed(0),
    );

    final limitResult = await showDialog<String>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(
              'Update Monthly Limit',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  '${'budget_category_prefix'.tr()} ${budget.categoryName}',
                  style: theme.textTheme.bodyMedium,
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: limitController,
                  keyboardType: const TextInputType.numberWithOptions(
                    decimal: true,
                  ),
                  decoration: InputDecoration(
                    labelText: 'budget_monthly_limit_label'.tr(),
                    prefixText: '₹ ',
                    border: OutlineInputBorder(),
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => context.pop(),
                child: Text(
                  'budget_cancel'.tr(),
                  style: theme.textTheme.labelLarge?.copyWith(
                    color: theme.hintColor,
                  ),
                ),
              ),
              ElevatedButton(
                onPressed: () {
                  final value = limitController.text.trim();
                  if (value.isNotEmpty) {
                    context.pop(limitController.text);
                  }
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColorPalette.primary,
                  foregroundColor: colorScheme.onPrimary,
                ),
                child: Text(
                  'budget_update'.tr(),
                  style: theme.textTheme.labelLarge?.copyWith(
                    color: colorScheme.onPrimary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
    );

    if (limitResult != null && limitResult.isNotEmpty) {
      await _updateBudget(budget, limitResult);
    }
  }

  Future<void> _showDeleteDialog(BudgetModel budget) async {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    final confirm = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(
              'Delete Budget',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            content: Text(
              'Are you sure you want to delete the budget for ${budget.categoryName}?',
              style: theme.textTheme.bodyMedium,
            ),
            actions: [
              TextButton(
                onPressed: () => context.pop(false),
                child: Text(
                  'Cancel',
                  style: theme.textTheme.labelLarge?.copyWith(
                    color: theme.hintColor,
                  ),
                ),
              ),
              ElevatedButton(
                onPressed: () => context.pop(true),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColorPalette.error,
                  foregroundColor: colorScheme.onError,
                ),
                child: Text(
                  'Delete',
                  style: theme.textTheme.labelLarge?.copyWith(
                    color: colorScheme.onError,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
    );

    if (confirm == true) {
      await _deleteBudget(budget);
    }
  }

  Future<void> _createBudget(String limitText) async {
    try {
      final limit = double.parse(limitText);

      // Check if budget already exists for this category
      final existingBudget = _budgets.firstWhere(
        (b) => b.categoryName == _selectedCategory,
        orElse:
            () => BudgetModel(
              categoryName: '',
              categoryType: 'expense',
              monthlyLimit: 0,
              currentSpent: 0,
              month: DateTime.now().month,
              year: DateTime.now().year,
            ),
      );

      if (existingBudget.categoryName.isNotEmpty) {
        _showSnackBar('Budget already exists for this category');
        return;
      }

      final budget = BudgetModel(
        categoryName: _selectedCategory,
        categoryType: 'expense', // For now, assuming expense
        monthlyLimit: limit,
        currentSpent: 0,
        month: DateTime.now().month,
        year: DateTime.now().year,
      );

      await LocalStorageService.addBudget(budget);
      await _loadBudgets();
      _showSnackBar('Budget created successfully');

      setState(() {
        _selectedCategory = '';
      });
    } catch (e) {
      _showSnackBar('budget_invalid_amount'.tr());
    }
  }

  Future<void> _updateBudget(BudgetModel budget, String limitText) async {
    try {
      final limit = double.parse(limitText);

      final updatedBudget = budget.copyWith(monthlyLimit: limit);
      await LocalStorageService.updateBudget(updatedBudget);
      await _loadBudgets();
      _showSnackBar('budget_updated_success'.tr());
    } catch (e) {
      _showSnackBar('budget_invalid_amount'.tr());
    }
  }

  Future<void> _deleteBudget(BudgetModel budget) async {
    try {
      await LocalStorageService.deleteBudget(budget.id);
      await _loadBudgets();
      _showSnackBar('budget_deleted_success'.tr());
    } catch (e) {
      _showSnackBar('budget_delete_error'.tr());
    }
  }

  void _showSnackBar(String message) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          message,
          style: theme.textTheme.bodyMedium?.copyWith(
            color: colorScheme.onPrimary,
          ),
        ),
        backgroundColor: AppColorPalette.primary,
      ),
    );
  }

  Color _getBudgetProgressColor(BudgetModel budget) {
    switch (budget.progressColor) {
      case 'red':
        return AppColorPalette.error;
      case 'orange':
        return AppColorPalette.warning;
      case 'yellow':
        return Colors.amber[700]!;
      case 'green':
        return AppColorPalette.success;
      default:
        return AppColorPalette.grey;
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        title: Text(
          'budget_title'.tr(),
          style: theme.appBarTheme.titleTextStyle?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios),
          onPressed: () => context.pop(),
        ),
        centerTitle: false,
      ),
      body: Column(
        children: [
          // Add Budget Section
          Container(
            padding: const EdgeInsets.only(top: 16),

            child: ElevatedButton.icon(
              onPressed: () async {
                await _showCategorySelectionDialog();
                if (_selectedCategory.isNotEmpty) {
                  await _showLimitDialog();
                }
              },
              icon: Icon(Icons.add, size: 20, color: colorScheme.onPrimary),
              label: Text(
                'budget_add_new'.tr(),
                style:
                    theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: colorScheme.onPrimary,
                    ) ??
                    TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: colorScheme.onPrimary,
                    ),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColorPalette.primary,
                foregroundColor: colorScheme.onPrimary,
                padding: const EdgeInsets.symmetric(
                  vertical: 14,
                  horizontal: 20,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
                elevation: 1,
              ),
            ),
          ),

          // Budget List
          Expanded(
            child:
                _budgets.isEmpty
                    ? Center(
                      child: Padding(
                        padding: const EdgeInsets.all(32),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Container(
                              padding: const EdgeInsets.all(24),
                              decoration: BoxDecoration(
                                color: AppColorPalette.primary.withValues(
                                  alpha: 0.1,
                                ),
                                shape: BoxShape.circle,
                              ),
                              child: Icon(
                                Icons.account_balance_wallet_outlined,
                                size: 48,
                                color: AppColorPalette.primary,
                              ),
                            ),
                            const SizedBox(height: 24),
                            Text(
                              'budget_no_budgets_title'.tr(),
                              style:
                                  theme.textTheme.titleLarge?.copyWith(
                                    fontWeight: FontWeight.bold,
                                  ) ??
                                  TextStyle(
                                    fontSize: 20,
                                    fontWeight: FontWeight.bold,
                                    color: theme.textTheme.titleLarge?.color,
                                  ),
                            ),
                            const SizedBox(height: 12),
                            Text(
                              'budget_no_budgets_desc'.tr(),
                              textAlign: TextAlign.center,
                              style:
                                  theme.textTheme.bodyLarge?.copyWith(
                                    height: 1.5,
                                  ) ??
                                  TextStyle(
                                    color: theme.hintColor,
                                    fontSize: 16,
                                    height: 1.5,
                                  ),
                            ),
                            const SizedBox(height: 32),
                            ElevatedButton.icon(
                              onPressed: () async {
                                await _showCategorySelectionDialog();
                                if (_selectedCategory.isNotEmpty) {
                                  await _showLimitDialog();
                                }
                              },
                              icon: Icon(
                                Icons.add,
                                size: 20,
                                color: colorScheme.onPrimary,
                              ),
                              label: Text(
                                'budget_create_first'.tr(),
                                style:
                                    theme.textTheme.titleMedium?.copyWith(
                                      fontWeight: FontWeight.w600,
                                      color: colorScheme.onPrimary,
                                    ) ??
                                    TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.w600,
                                      color: colorScheme.onPrimary,
                                    ),
                              ),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: AppColorPalette.primary,
                                foregroundColor: colorScheme.onPrimary,
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 24,
                                  vertical: 14,
                                ),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(10),
                                ),
                                elevation: 1,
                              ),
                            ),
                          ],
                        ),
                      ),
                    )
                    : ListView.builder(
                      padding: const EdgeInsets.all(16),
                      itemCount: _budgets.length,
                      itemBuilder: (context, index) {
                        final budget = _budgets[index];
                        final progressColor = _getBudgetProgressColor(budget);

                        return Container(
                          margin: const EdgeInsets.only(bottom: 12),
                          decoration: BoxDecoration(
                            color: theme.cardColor,
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: progressColor.withValues(alpha: 0.2),
                            ),
                            boxShadow: [
                              BoxShadow(
                                color: theme.shadowColor.withValues(alpha: 0.1),
                                blurRadius: 4,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Padding(
                            padding: const EdgeInsets.all(16),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // Header Row
                                Row(
                                  children: [
                                    Container(
                                      padding: const EdgeInsets.all(6),
                                      decoration: BoxDecoration(
                                        color: (budget.categoryType == 'income'
                                                ? AppColorPalette.success
                                                : AppColorPalette.error)
                                            .withValues(alpha: 0.1),
                                        borderRadius: BorderRadius.circular(6),
                                      ),
                                      child: Icon(
                                        budget.categoryType == 'income'
                                            ? Icons.trending_up
                                            : Icons.trending_down,
                                        color:
                                            budget.categoryType == 'income'
                                                ? AppColorPalette.success
                                                : AppColorPalette.error,
                                        size: 18,
                                      ),
                                    ),
                                    const SizedBox(width: 10),
                                    Expanded(
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            budget.categoryName,
                                            style:
                                                theme.textTheme.titleMedium
                                                    ?.copyWith(
                                                      fontWeight:
                                                          FontWeight.w600,
                                                    ) ??
                                                const TextStyle(
                                                  fontWeight: FontWeight.w600,
                                                  fontSize: 15,
                                                ),
                                          ),
                                          Text(
                                            budget.categoryType == 'income'
                                                ? 'budget_type_income'.tr()
                                                : 'budget_type_expense'.tr(),
                                            style:
                                                theme.textTheme.bodySmall
                                                    ?.copyWith(
                                                      color: theme.hintColor,
                                                      fontWeight:
                                                          FontWeight.w500,
                                                    ) ??
                                                TextStyle(
                                                  fontSize: 10,
                                                  color: theme.hintColor,
                                                  fontWeight: FontWeight.w500,
                                                ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    // Action Buttons
                                    Container(
                                      decoration: BoxDecoration(
                                        color: theme.hintColor.withValues(
                                          alpha: 0.1,
                                        ),
                                        borderRadius: BorderRadius.circular(6),
                                      ),
                                      child: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          IconButton(
                                            onPressed:
                                                () => _showEditLimitDialog(
                                                  budget,
                                                ),
                                            icon: Icon(
                                              Icons.edit_outlined,
                                              size: 16,
                                              color: AppColorPalette.primary,
                                            ),
                                            constraints: const BoxConstraints(
                                              minWidth: 32,
                                              minHeight: 32,
                                            ),
                                            padding: EdgeInsets.zero,
                                          ),
                                          IconButton(
                                            onPressed:
                                                () => _showDeleteDialog(budget),
                                            icon: Icon(
                                              Icons.delete_outline,
                                              size: 16,
                                              color: AppColorPalette.error,
                                            ),
                                            constraints: const BoxConstraints(
                                              minWidth: 32,
                                              minHeight: 32,
                                            ),
                                            padding: EdgeInsets.zero,
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 12),

                                // Amount Display
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          'budget_spent'.tr(),
                                          style:
                                              theme.textTheme.bodySmall
                                                  ?.copyWith(
                                                    color: theme.hintColor,
                                                    fontWeight: FontWeight.w500,
                                                  ) ??
                                              TextStyle(
                                                color: theme.hintColor,
                                                fontSize: 11,
                                                fontWeight: FontWeight.w500,
                                              ),
                                        ),
                                        Text(
                                          '₹${budget.currentSpent.toStringAsFixed(0)}',
                                          style:
                                              theme.textTheme.titleMedium
                                                  ?.copyWith(
                                                    fontWeight: FontWeight.bold,
                                                    color: progressColor,
                                                  ) ??
                                              TextStyle(
                                                fontSize: 16,
                                                fontWeight: FontWeight.bold,
                                                color: progressColor,
                                              ),
                                        ),
                                      ],
                                    ),
                                    Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.end,
                                      children: [
                                        Text(
                                          'budget_budget'.tr(),
                                          style:
                                              theme.textTheme.bodySmall
                                                  ?.copyWith(
                                                    color: theme.hintColor,
                                                    fontWeight: FontWeight.w500,
                                                  ) ??
                                              TextStyle(
                                                color: theme.hintColor,
                                                fontSize: 11,
                                                fontWeight: FontWeight.w500,
                                              ),
                                        ),
                                        Text(
                                          '₹${budget.monthlyLimit.toStringAsFixed(0)}',
                                          style:
                                              theme.textTheme.titleMedium
                                                  ?.copyWith(
                                                    fontWeight: FontWeight.bold,
                                                  ) ??
                                              const TextStyle(
                                                fontSize: 16,
                                                fontWeight: FontWeight.bold,
                                              ),
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 10),

                                // Progress Bar
                                ClipRRect(
                                  borderRadius: BorderRadius.circular(4),
                                  child: LinearProgressIndicator(
                                    value: (budget.progressPercentage / 100)
                                        .clamp(0.0, 1.0),
                                    backgroundColor: theme.hintColor.withValues(
                                      alpha: 0.2,
                                    ),
                                    valueColor: AlwaysStoppedAnimation<Color>(
                                      progressColor,
                                    ),
                                    minHeight: 8,
                                  ),
                                ),
                                const SizedBox(height: 8),

                                // Progress Info
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    if (budget.isExceeded)
                                      Container(
                                        padding: const EdgeInsets.symmetric(
                                          horizontal: 8,
                                          vertical: 4,
                                        ),
                                        decoration: BoxDecoration(
                                          color: AppColorPalette.error
                                              .withValues(alpha: 0.1),
                                          borderRadius: BorderRadius.circular(
                                            12,
                                          ),
                                        ),
                                        child: Row(
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            Icon(
                                              Icons.warning_amber_rounded,
                                              size: 14,
                                              color: AppColorPalette.error,
                                            ),
                                            const SizedBox(width: 4),
                                            Text(
                                              'budget_exceeded'.tr(),
                                              style:
                                                  theme.textTheme.bodySmall
                                                      ?.copyWith(
                                                        color:
                                                            AppColorPalette
                                                                .error,
                                                        fontWeight:
                                                            FontWeight.w600,
                                                      ) ??
                                                  TextStyle(
                                                    color:
                                                        AppColorPalette.error,
                                                    fontWeight: FontWeight.w600,
                                                    fontSize: 11,
                                                  ),
                                            ),
                                          ],
                                        ),
                                      )
                                    else
                                      Text(
                                        'budget_remaining'.tr(
                                          namedArgs: {
                                            'amount': (budget.monthlyLimit -
                                                    budget.currentSpent)
                                                .toStringAsFixed(0),
                                          },
                                        ),
                                        style:
                                            theme.textTheme.bodySmall?.copyWith(
                                              color: AppColorPalette.success,
                                              fontWeight: FontWeight.w500,
                                            ) ??
                                            TextStyle(
                                              color: AppColorPalette.success,
                                              fontSize: 12,
                                              fontWeight: FontWeight.w500,
                                            ),
                                      ),
                                    Container(
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 8,
                                        vertical: 4,
                                      ),
                                      decoration: BoxDecoration(
                                        color: progressColor.withValues(
                                          alpha: 0.1,
                                        ),
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                      child: Text(
                                        '${budget.progressPercentage.toStringAsFixed(1)}%',
                                        style:
                                            theme.textTheme.bodySmall?.copyWith(
                                              color: progressColor,
                                              fontWeight: FontWeight.w600,
                                            ) ??
                                            TextStyle(
                                              color: progressColor,
                                              fontWeight: FontWeight.w600,
                                              fontSize: 11,
                                            ),
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        );
                      },
                    ),
          ),
        ],
      ),
    );
  }
}
