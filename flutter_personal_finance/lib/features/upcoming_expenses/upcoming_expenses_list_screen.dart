import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:go_router/go_router.dart';
import '../../resources/app_theme.dart';
import '../../services/local_storage_service.dart';
import '../../services/notification_service.dart';
import '../../features/models/upcoming_expense_model.dart';
import '../../features/models/bank_account_model.dart';
import '../../features/models/transaction_model.dart';

class UpcomingExpensesListScreen extends StatefulWidget {
  const UpcomingExpensesListScreen({super.key});

  @override
  State<UpcomingExpensesListScreen> createState() =>
      _UpcomingExpensesListScreenState();
}

class _UpcomingExpensesListScreenState
    extends State<UpcomingExpensesListScreen> {
  List<UpcomingExpenseModel> _allExpenses = [];
  List<UpcomingExpenseModel> _filteredExpenses = [];
  List<UpcomingExpenseCategoryModel> _categories = [];
  List<BankAccountModel> _bankAccounts = [];
  bool _isLoading = true;
  bool _showPaidExpenses = false;
  String? _selectedCategoryFilter;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    try {
      final expenses = await LocalStorageService.getUpcomingExpenses();
      final categories =
          await LocalStorageService.getUpcomingExpenseCategories();
      final bankAccounts = await LocalStorageService.getBankAccounts();

      setState(() {
        _allExpenses = expenses;
        _categories = categories;
        _bankAccounts = bankAccounts;
        _isLoading = false;
      });

      _applyFilters();
    } catch (e) {
      print('Error loading upcoming expenses: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _applyFilters() {
    setState(() {
      _filteredExpenses =
          _allExpenses.where((expense) {
            // Filter by paid status
            if (!_showPaidExpenses && expense.isPaid) {
              return false;
            }

            // Filter by category
            if (_selectedCategoryFilter != null &&
                expense.category != _selectedCategoryFilter) {
              return false;
            }

            return true;
          }).toList();

      // Sort by due date (nearest first)
      _filteredExpenses.sort((a, b) => a.dueDate.compareTo(b.dueDate));
    });
  }

  Future<void> _togglePaidStatus(UpcomingExpenseModel expense) async {
    try {
      if (expense.isPaid) {
        // Unpaid it by updating
        final updatedExpense = expense.copyWith(isPaid: false);
        await LocalStorageService.updateUpcomingExpense(updatedExpense);

        // Schedule notification for the unpaid expense
        await NotificationService.scheduleExpenseNotification(updatedExpense);

        _showSnackBar('Expense marked as unpaid');
      } else {
        // Mark as paid
        await LocalStorageService.markUpcomingExpenseAsPaid(expense.id);

        // Cancel notifications for the paid expense
        await NotificationService.cancelExpenseNotifications(expense.id);

        // Create transaction if bank account is selected
        if (expense.bankAccountId != null &&
            expense.bankAccountId!.isNotEmpty) {
          final transaction = TransactionModel(
            bankAccountId: expense.bankAccountId!,
            type: TransactionType.debit, // Expense is a debit
            amount: expense.amount,
            description: 'Upcoming Expense: ${expense.category}',
            category: expense.category,
            date: DateTime.now(), // Use current date as payment date
          );

          await LocalStorageService.addTransaction(transaction);

          // Update bank account balances
          await LocalStorageService.updateBankAccountBalances();

          _showSnackBar('Expense marked as paid and transaction added');
        } else {
          _showSnackBar('Expense marked as paid');
        }
      }

      await _loadData();
    } catch (e) {
      _showSnackBar('Error updating expense status: $e');
    }
  }

  Future<void> _deleteExpense(UpcomingExpenseModel expense) async {
    final theme = Theme.of(context);

    final shouldDelete = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(
              'Delete Expense',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            content: Text(
              'Are you sure you want to delete this ${expense.category} expense?',
              style: theme.textTheme.bodyMedium,
            ),
            actions: [
              TextButton(
                onPressed: () => context.pop(false),
                child: Text(
                  'Cancel',
                  style: theme.textTheme.labelLarge?.copyWith(
                    color: theme.hintColor,
                  ),
                ),
              ),
              TextButton(
                onPressed: () => context.pop(true),
                child: Text(
                  'Delete',
                  style: theme.textTheme.labelLarge?.copyWith(
                    color: theme.colorScheme.error,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
    );

    if (shouldDelete == true) {
      // Cancel notifications before deleting
      await NotificationService.cancelExpenseNotifications(expense.id);

      await LocalStorageService.deleteUpcomingExpense(expense.id);
      await _loadData();
      _showSnackBar('Expense deleted successfully');
    }
  }

  Future<void> _editExpense(UpcomingExpenseModel expense) async {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    String? selectedCategory = expense.category;
    String? selectedBankAccountId = expense.bankAccountId;
    final TextEditingController amountController = TextEditingController(
      text: expense.amount.toString(),
    );
    DateTime selectedDate = expense.dueDate;
    final GlobalKey<FormState> formKey = GlobalKey<FormState>();

    await showDialog(
      context: context,
      builder:
          (context) => StatefulBuilder(
            builder:
                (context, setState) => AlertDialog(
                  title: Text(
                    'Edit Upcoming Expense',
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  content: SizedBox(
                    width: double.maxFinite,
                    child: Form(
                      key: formKey,
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Category Dropdown
                          Text(
                            'Category',
                            style: theme.textTheme.bodyMedium?.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          const SizedBox(height: 8),
                          DropdownButtonFormField<String>(
                            value: selectedCategory,
                            decoration: InputDecoration(
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                              contentPadding: const EdgeInsets.symmetric(
                                horizontal: 12,
                                vertical: 8,
                              ),
                            ),
                            items:
                                _categories.map((category) {
                                  return DropdownMenuItem<String>(
                                    value: category.name,
                                    child: Text(
                                      category.name,
                                      style: theme.textTheme.bodyMedium,
                                    ),
                                  );
                                }).toList(),
                            onChanged: (value) {
                              setState(() {
                                selectedCategory = value;
                              });
                            },
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Please select a category';
                              }
                              return null;
                            },
                          ),
                          const SizedBox(height: 16),

                          // Amount Field
                          Text(
                            'Amount',
                            style: theme.textTheme.bodyMedium?.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          const SizedBox(height: 8),
                          TextFormField(
                            controller: amountController,
                            keyboardType: const TextInputType.numberWithOptions(
                              decimal: true,
                            ),
                            decoration: InputDecoration(
                              hintText: 'Enter amount',
                              prefixText: '₹ ',
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                              contentPadding: const EdgeInsets.symmetric(
                                horizontal: 12,
                                vertical: 8,
                              ),
                            ),
                            validator: (value) {
                              if (value == null || value.trim().isEmpty) {
                                return 'Please enter amount';
                              }
                              final amount = double.tryParse(value.trim());
                              if (amount == null || amount <= 0) {
                                return 'Please enter a valid amount';
                              }
                              return null;
                            },
                          ),
                          const SizedBox(height: 16),

                          // Bank Account Field
                          Text(
                            'Bank Account (Optional)',
                            style: theme.textTheme.bodyMedium?.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          const SizedBox(height: 8),
                          DropdownButtonFormField<String?>(
                            value: selectedBankAccountId,
                            decoration: InputDecoration(
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                              contentPadding: const EdgeInsets.symmetric(
                                horizontal: 12,
                                vertical: 8,
                              ),
                              hintText: 'Select bank account',
                            ),
                            items: [
                              DropdownMenuItem<String?>(
                                value: null,
                                child: Text(
                                  'No Bank Account',
                                  style: theme.textTheme.bodyMedium,
                                ),
                              ),
                              ..._bankAccounts.map((account) {
                                return DropdownMenuItem<String?>(
                                  value: account.id,
                                  child: Text(
                                    '${account.bankName} - ${account.accountNumber}',
                                    overflow: TextOverflow.ellipsis,
                                    style: theme.textTheme.bodyMedium,
                                  ),
                                );
                              }),
                            ],
                            onChanged: (value) {
                              setState(() {
                                selectedBankAccountId = value;
                              });
                            },
                          ),
                          const SizedBox(height: 16),

                          // Due Date Field
                          Text(
                            'Due Date',
                            style: theme.textTheme.bodyMedium?.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          const SizedBox(height: 8),
                          GestureDetector(
                            onTap: () async {
                              final picked = await showDatePicker(
                                context: context,
                                initialDate: selectedDate,
                                firstDate: DateTime.now(),
                                lastDate: DateTime.now().add(
                                  const Duration(days: 365 * 2),
                                ),
                              );
                              if (picked != null) {
                                setState(() {
                                  selectedDate = picked;
                                });
                              }
                            },
                            child: Container(
                              padding: const EdgeInsets.all(12),
                              decoration: BoxDecoration(
                                border: Border.all(
                                  color: theme.hintColor.withValues(alpha: 0.3),
                                ),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    DateFormat(
                                      'MMM dd, yyyy',
                                    ).format(selectedDate),
                                    style: theme.textTheme.bodyMedium,
                                  ),
                                  Icon(
                                    Icons.calendar_today,
                                    size: 20,
                                    color: theme.hintColor,
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  actions: [
                    TextButton(
                      onPressed: () => context.pop(),
                      child: Text(
                        'Cancel',
                        style: theme.textTheme.labelLarge?.copyWith(
                          color: theme.hintColor,
                        ),
                      ),
                    ),
                    TextButton(
                      onPressed: () async {
                        if (formKey.currentState!.validate()) {
                          final amount = double.parse(
                            amountController.text.trim(),
                          );

                          final updatedExpense = expense.copyWith(
                            category: selectedCategory!,
                            amount: amount,
                            dueDate: selectedDate,
                            bankAccountId: selectedBankAccountId,
                          );

                          // Cancel old notifications and schedule new ones
                          await NotificationService.cancelExpenseNotifications(
                            expense.id,
                          );

                          await LocalStorageService.updateUpcomingExpense(
                            updatedExpense,
                          );

                          // Schedule notifications for updated expense if not paid
                          if (!updatedExpense.isPaid) {
                            await NotificationService.scheduleExpenseNotification(
                              updatedExpense,
                            );
                          }

                          context.pop();
                          await _loadData();
                          _showSnackBar('Expense updated successfully');
                        }
                      },
                      child: Text(
                        'Update',
                        style: theme.textTheme.labelLarge?.copyWith(
                          color: theme.colorScheme.primary,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
          ),
    );
  }

  void _showSnackBar(String message) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          message,
          style: theme.textTheme.bodyMedium?.copyWith(
            color: colorScheme.onPrimary,
          ),
        ),
        backgroundColor: theme.colorScheme.primary,
      ),
    );
  }

  BankAccountModel? _getBankAccountById(String? bankAccountId) {
    if (bankAccountId == null) return null;
    return _bankAccounts.firstWhere(
      (account) => account.id == bankAccountId,
      orElse:
          () => BankAccountModel(
            id: '',
            bankName: 'Unknown Bank',
            accountNumber: '',
            initialAmount: 0,
            currentAmount: 0,
          ),
    );
  }

  Color _getStatusColor(UpcomingExpenseModel expense) {
    final theme = Theme.of(context);

    if (expense.isPaid) return AppColorPalette.success;

    final now = DateTime.now();
    final daysUntilDue = expense.dueDate.difference(now).inDays;

    if (daysUntilDue < 0) return theme.colorScheme.error; // Overdue
    if (daysUntilDue <= 7) return AppColorPalette.warning; // Due soon
    return theme.colorScheme.primary; // Normal
  }

  String _getStatusText(UpcomingExpenseModel expense) {
    if (expense.isPaid) return 'Paid';

    final now = DateTime.now();
    final daysUntilDue = expense.dueDate.difference(now).inDays;

    if (daysUntilDue < 0) return 'Overdue';
    if (daysUntilDue == 0) return 'Due Today';
    if (daysUntilDue <= 7) return 'Due in $daysUntilDue days';
    return 'Upcoming';
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        title: Text(
          'Upcoming Expenses',
          style: theme.appBarTheme.titleTextStyle?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : Column(
                children: [
                  // Filters Section
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: theme.cardColor,
                      boxShadow: [
                        BoxShadow(
                          color: theme.shadowColor.withValues(alpha: 0.1),
                          blurRadius: 4,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Column(
                      children: [
                        // Show Paid Toggle
                        Row(
                          children: [
                            Switch(
                              value: _showPaidExpenses,
                              onChanged: (value) {
                                setState(() {
                                  _showPaidExpenses = value;
                                });
                                _applyFilters();
                              },
                              activeColor: theme.colorScheme.primary,
                              inactiveTrackColor: theme.hintColor.withValues(
                                alpha: 0.3,
                              ),
                            ),
                            const SizedBox(width: 8),
                            Text(
                              'Show Paid Expenses',
                              style: theme.textTheme.bodyMedium?.copyWith(
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),

                        // Category Filter
                        Row(
                          children: [
                            Expanded(
                              child: DropdownButtonFormField<String?>(
                                value: _selectedCategoryFilter,
                                decoration: InputDecoration(
                                  labelText: 'Filter by Category',
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  contentPadding: const EdgeInsets.symmetric(
                                    horizontal: 12,
                                    vertical: 8,
                                  ),
                                ),
                                items: [
                                  DropdownMenuItem<String?>(
                                    value: null,
                                    child: Text(
                                      'All Categories',
                                      style: theme.textTheme.bodyMedium,
                                    ),
                                  ),
                                  ..._categories.map((category) {
                                    return DropdownMenuItem<String?>(
                                      value: category.name,
                                      child: Text(
                                        category.name,
                                        style: theme.textTheme.bodyMedium,
                                      ),
                                    );
                                  }),
                                ],
                                onChanged: (value) {
                                  setState(() {
                                    _selectedCategoryFilter = value;
                                  });
                                  _applyFilters();
                                },
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),

                  // Expenses List
                  Expanded(
                    child:
                        _filteredExpenses.isEmpty
                            ? _buildEmptyState()
                            : RefreshIndicator(
                              onRefresh: _loadData,
                              child: ListView.builder(
                                padding: const EdgeInsets.all(16),
                                itemCount: _filteredExpenses.length,
                                itemBuilder: (context, index) {
                                  final expense = _filteredExpenses[index];
                                  return _buildExpenseCard(expense);
                                },
                              ),
                            ),
                  ),
                ],
              ),
    );
  }

  Widget _buildEmptyState() {
    final theme = Theme.of(context);

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.schedule,
            size: 64,
            color: theme.hintColor.withValues(alpha: 0.5),
          ),
          const SizedBox(height: 16),
          Text(
            _showPaidExpenses
                ? 'No paid expenses found'
                : 'No upcoming expenses',
            style: theme.textTheme.titleMedium?.copyWith(
              color: theme.hintColor,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _showPaidExpenses
                ? 'Mark expenses as paid to see them here'
                : 'Add upcoming expenses from the dashboard.\nLink them to bank accounts for automatic transaction creation.',
            textAlign: TextAlign.center,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.hintColor.withValues(alpha: 0.7),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildExpenseCard(UpcomingExpenseModel expense) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final statusColor = _getStatusColor(expense);
    final statusText = _getStatusText(expense);

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.cardColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: statusColor.withValues(alpha: 0.3), width: 1),
        boxShadow: [
          BoxShadow(
            color: theme.shadowColor.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              // Category and Status
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      expense.category,
                      style: theme.textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: statusColor.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        statusText,
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: statusColor,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                    // Bank Account Information
                    if (expense.bankAccountId != null &&
                        expense.bankAccountId!.isNotEmpty) ...[
                      const SizedBox(height: 6),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: theme.colorScheme.primary.withValues(
                            alpha: 0.1,
                          ),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.account_balance,
                              size: 12,
                              color: theme.colorScheme.primary,
                            ),
                            const SizedBox(width: 4),
                            Flexible(
                              child: Text(
                                () {
                                  final bankAccount = _getBankAccountById(
                                    expense.bankAccountId,
                                  );
                                  return bankAccount != null &&
                                          bankAccount.bankName.isNotEmpty
                                      ? '${bankAccount.bankName} (${bankAccount.accountNumber})'
                                      : 'Bank Account Not Found';
                                }(),
                                style: theme.textTheme.bodySmall?.copyWith(
                                  color: theme.colorScheme.primary,
                                  fontWeight: FontWeight.w500,
                                  fontSize: 11,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              // Amount
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    '₹${NumberFormat('#,##,###').format(expense.amount)}',
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: statusColor,
                    ),
                  ),
                  Text(
                    'Due: ${DateFormat('MMM dd, yyyy').format(expense.dueDate)}',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.hintColor,
                    ),
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 12),

          // Action Buttons
          Row(
            children: [
              // Mark as Paid/Unpaid Button
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () => _togglePaidStatus(expense),
                  icon: Icon(
                    expense.isPaid ? Icons.undo : Icons.check_circle,
                    size: 16,
                  ),
                  label: Text(
                    expense.isPaid ? 'Mark Unpaid' : 'Mark Paid',
                    style: theme.textTheme.bodySmall?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor:
                        expense.isPaid
                            ? AppColorPalette.warning.withValues(alpha: 0.1)
                            : AppColorPalette.success.withValues(alpha: 0.1),
                    foregroundColor:
                        expense.isPaid
                            ? AppColorPalette.warning
                            : AppColorPalette.success,
                    elevation: 0,
                    padding: const EdgeInsets.symmetric(vertical: 8),
                  ),
                ),
              ),
              const SizedBox(width: 8),

              // Edit Button
              IconButton(
                onPressed: () => _editExpense(expense),
                icon: Icon(
                  Icons.edit,
                  color: theme.colorScheme.primary,
                  size: 20,
                ),
                style: IconButton.styleFrom(
                  backgroundColor: theme.colorScheme.primary.withValues(
                    alpha: 0.1,
                  ),
                  padding: const EdgeInsets.all(8),
                ),
              ),
              const SizedBox(width: 4),

              // Delete Button
              IconButton(
                onPressed: () => _deleteExpense(expense),
                icon: Icon(
                  Icons.delete,
                  color: theme.colorScheme.error,
                  size: 20,
                ),
                style: IconButton.styleFrom(
                  backgroundColor: theme.colorScheme.error.withValues(
                    alpha: 0.1,
                  ),
                  padding: const EdgeInsets.all(8),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
