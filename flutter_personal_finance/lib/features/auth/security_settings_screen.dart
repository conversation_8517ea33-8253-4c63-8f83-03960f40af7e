import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:easy_localization/easy_localization.dart';
import '../../services/biometric_auth_service.dart';

class SecuritySettingsScreen extends StatefulWidget {
  const SecuritySettingsScreen({super.key});

  @override
  State<SecuritySettingsScreen> createState() => _SecuritySettingsScreenState();
}

class _SecuritySettingsScreenState extends State<SecuritySettingsScreen> {
  bool _authEnabled = false;
  bool _biometricSupported = false;
  String _authMethod = '';
  List<String> _availableBiometrics = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    try {
      // Initialize default settings on first launch
      await BiometricAuthService.initializeDefaults();

      final authRequired = await BiometricAuthService.isAuthRequired();
      final biometricSupported =
          await BiometricAuthService.isBiometricSupported();

      final biometrics = await BiometricAuthService.getAvailableBiometrics();
      final biometricNames =
          biometrics
              .map((type) => BiometricAuthService.getBiometricTypeName(type))
              .toList();

      // Determine auth method
      String authMethod = '';
      if (biometricSupported && biometrics.isNotEmpty) {
        authMethod = 'Biometric (${biometricNames.join(', ')})';
      } else {
        authMethod = 'System Authentication';
      }

      setState(() {
        _authEnabled = authRequired;
        _biometricSupported = biometricSupported;
        _authMethod = authMethod;
        _availableBiometrics = biometricNames;
        _isLoading = false;
      });
    } catch (e) {
      print('Error loading security settings: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _toggleAuthentication(bool enabled) async {
    setState(() {
      _isLoading = true;
    });

    try {
      if (enabled) {
        // Try to enable authentication using system authentication
        // This will use biometric if available, otherwise fall back to system authentication
        final authenticated =
            await BiometricAuthService.authenticateWithSystem();
        if (authenticated) {
          await BiometricAuthService.setAuthRequired(true);
          if (_biometricSupported) {
            await BiometricAuthService.setBiometricEnabled(true);
            _showSnackBar(
              'security_enabled_with'.tr(
                namedArgs: {'method': _availableBiometrics.join(', ')},
              ),
            );
          } else {
            await BiometricAuthService.setBiometricEnabled(false);
            _showSnackBar('security_enabled_system'.tr());
          }
        } else {
          _showSnackBar('security_setup_failed'.tr());
          enabled = false;
        }
      } else {
        // Disable authentication
        await BiometricAuthService.setAuthRequired(false);
        await BiometricAuthService.setBiometricEnabled(false);
        _showSnackBar('security_disabled'.tr());
      }

      setState(() {
        _authEnabled = enabled;
      });
    } catch (e) {
      _showSnackBar('security_update_error'.tr());
      print('Error toggling authentication: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _showSnackBar(String message) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          message,
          style: theme.textTheme.bodyMedium?.copyWith(
            color: colorScheme.onPrimary,
          ),
        ),
        backgroundColor: theme.colorScheme.primary,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        title: Text(
          'security_settings_title'.tr(),
          style: theme.appBarTheme.titleTextStyle?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios),
          onPressed: () => context.pop(),
        ),
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Current Status Card
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: theme.colorScheme.primary.withValues(
                          alpha: 0.05,
                        ),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: theme.colorScheme.primary.withValues(
                            alpha: 0.3,
                          ),
                        ),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(
                                Icons.security,
                                color: theme.colorScheme.primary,
                                size: 20,
                              ),
                              const SizedBox(width: 8),
                              Text(
                                'security_current_status'.tr(),
                                style: theme.textTheme.titleSmall?.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: theme.colorScheme.primary,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Text(
                            _authEnabled
                                ? 'security_auth_enabled'.tr(
                                  namedArgs: {'method': _authMethod},
                                )
                                : 'security_auth_disabled'.tr(),
                            style: theme.textTheme.bodyMedium,
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 24),

                    // Authentication Toggle
                    Text(
                      'security_app_security'.tr(),
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),

                    _buildSettingCard(
                      icon:
                          _biometricSupported ? Icons.fingerprint : Icons.lock,
                      title: 'security_enable_auth'.tr(),
                      subtitle:
                          _biometricSupported
                              ? 'security_secure_biometric'.tr(
                                namedArgs: {
                                  'biometrics': _availableBiometrics.join(', '),
                                },
                              )
                              : 'security_secure_system'.tr(),
                      trailing: Switch(
                        value: _authEnabled,
                        onChanged: _toggleAuthentication,
                        activeColor: theme.colorScheme.primary,
                        inactiveTrackColor: theme.hintColor.withValues(
                          alpha: 0.3,
                        ),
                      ),
                    ),
                    const SizedBox(height: 24),

                    // Information Card
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: theme.hintColor.withValues(alpha: 0.05),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: theme.hintColor.withValues(alpha: 0.2),
                        ),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(
                                Icons.info_outline,
                                color: theme.hintColor,
                                size: 20,
                              ),
                              const SizedBox(width: 8),
                              Text(
                                'security_how_works'.tr(),
                                style: theme.textTheme.titleSmall?.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: theme.hintColor,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 12),
                          if (_biometricSupported) ...[
                            _buildInfoRow(
                              Icons.fingerprint,
                              'security_biometric_auth'.tr(),
                              'security_biometric_desc'.tr(
                                namedArgs: {
                                  'biometrics': _availableBiometrics.join(', '),
                                },
                              ),
                            ),
                            const SizedBox(height: 8),
                          ],
                          _buildInfoRow(
                            Icons.phone_android,
                            'security_system_integration'.tr(),
                            'security_system_desc'.tr(),
                          ),
                          const SizedBox(height: 8),
                          _buildInfoRow(
                            Icons.security,
                            'security_privacy_first'.tr(),
                            'security_privacy_desc'.tr(),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
    );
  }

  Widget _buildSettingCard({
    required IconData icon,
    required String title,
    required String subtitle,
    Widget? trailing,
    VoidCallback? onTap,
  }) {
    final theme = Theme.of(context);

    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: theme.cardColor,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: theme.shadowColor.withValues(alpha: 0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: theme.colorScheme.primary.withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(icon, color: theme.colorScheme.primary, size: 20),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Text(
                    subtitle,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.hintColor,
                    ),
                  ),
                ],
              ),
            ),
            if (trailing != null) trailing,
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(IconData icon, String title, String description) {
    final theme = Theme.of(context);

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(icon, size: 16, color: theme.colorScheme.primary),
        const SizedBox(width: 8),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: theme.textTheme.bodySmall?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              Text(
                description,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.hintColor,
                  height: 1.3,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
