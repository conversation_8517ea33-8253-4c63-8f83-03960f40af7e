import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:easy_localization/easy_localization.dart';
import '../../services/biometric_auth_service.dart';

class AuthenticationScreen extends StatefulWidget {
  const AuthenticationScreen({super.key});

  @override
  State<AuthenticationScreen> createState() => _AuthenticationScreenState();
}

class _AuthenticationScreenState extends State<AuthenticationScreen> {
  bool _isLoading = true;
  bool _biometricSupported = false;
  String _errorMessage = '';
  List<String> _availableBiometrics = [];

  @override
  void initState() {
    super.initState();
    _initAuthentication();
  }

  Future<void> _initAuthentication() async {
    try {
      final biometricSupported =
          await BiometricAuthService.isBiometricSupported();
      final biometricEnabled = await BiometricAuthService.isBiometricEnabled();

      final biometrics = await BiometricAuthService.getAvailableBiometrics();
      final biometricNames =
          biometrics
              .map((type) => BiometricAuthService.getBiometricTypeName(type))
              .toList();

      setState(() {
        _biometricSupported = biometricSupported;
        _availableBiometrics = biometricNames;
        _isLoading = false;
      });

      // Try biometric authentication immediately if available
      if (biometricSupported && biometricEnabled) {
        await _tryBiometricAuth();
      } else {
        // No authentication method set up - proceed without auth
        _onAuthSuccess();
      }
    } catch (e) {
      print('Error initializing authentication: $e');
      setState(() {
        _isLoading = false;
      });
      _onAuthSuccess();
    }
  }

  Future<void> _tryBiometricAuth() async {
    try {
      final result = await BiometricAuthService.authenticateWithBiometrics();
      if (result) {
        _onAuthSuccess();
      } else {
        // Biometric failed, allow access
        setState(() {
          _errorMessage = 'auth_biometric_failed'.tr();
        });
        _onAuthSuccess();
      }
    } catch (e) {
      print('Biometric authentication error: $e');
      setState(() {
        _errorMessage = 'auth_biometric_unavailable'.tr();
      });
      _onAuthSuccess();
    }
  }

  void _onAuthSuccess() {
    context.pop(true);
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return PopScope(
      canPop: false, // Prevent back button
      child: Scaffold(
        backgroundColor: theme.colorScheme.primary,
        body: SafeArea(
          child: _isLoading ? _buildLoadingScreen() : _buildBiometricScreen(),
        ),
      ),
    );
  }

  Widget _buildLoadingScreen() {
    final theme = Theme.of(context);

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(color: theme.colorScheme.onPrimary),
          const SizedBox(height: 24),
          Text(
            'auth_initializing'.tr(),
            style: theme.textTheme.titleMedium?.copyWith(
              color: theme.colorScheme.onPrimary,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBiometricScreen() {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: colorScheme.onPrimary.withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.fingerprint,
              size: 64,
              color: colorScheme.onPrimary,
            ),
          ),
          const SizedBox(height: 32),
          Text(
            'auth_authenticate_continue'.tr(),
            style: theme.textTheme.titleLarge?.copyWith(
              color: colorScheme.onPrimary,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _biometricSupported
                ? 'auth_biometric_instruction'.tr(
                  namedArgs: {'biometrics': _availableBiometrics.join(' or ')},
                )
                : 'auth_auth_required'.tr(),
            textAlign: TextAlign.center,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: colorScheme.onPrimary.withValues(alpha: 0.8),
            ),
          ),
          const SizedBox(height: 32),

          if (_biometricSupported) ...[
            ElevatedButton.icon(
              onPressed: _tryBiometricAuth,
              icon: const Icon(Icons.fingerprint),
              label: Text('auth_authenticate'.tr()),
              style: ElevatedButton.styleFrom(
                backgroundColor: colorScheme.onPrimary,
                foregroundColor: colorScheme.primary,
                padding: const EdgeInsets.symmetric(
                  horizontal: 32,
                  vertical: 16,
                ),
              ),
            ),
          ],

          const SizedBox(height: 16),

          TextButton(
            onPressed: _onAuthSuccess,
            child: Text(
              'auth_skip'.tr(),
              style: TextStyle(
                color: colorScheme.onPrimary.withValues(alpha: 0.8),
                fontWeight: FontWeight.w600,
              ),
            ),
          ),

          if (_errorMessage.isNotEmpty) ...[
            const SizedBox(height: 16),
            Container(
              margin: const EdgeInsets.symmetric(horizontal: 32),
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: theme.colorScheme.error.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: theme.colorScheme.error.withValues(alpha: 0.3),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.error_outline,
                    color: colorScheme.onPrimary,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      _errorMessage,
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: colorScheme.onPrimary,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }
}
