import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:easy_localization/easy_localization.dart';
import '../../services/biometric_auth_service.dart';

class AuthWrapper extends StatefulWidget {
  final Widget child;
  final bool requireAuth;

  const AuthWrapper({super.key, required this.child, this.requireAuth = true});

  @override
  State<AuthWrapper> createState() => _AuthWrapperState();
}

class _AuthWrapperState extends State<AuthWrapper> {
  bool _isAuthenticated = false;
  bool _isLoading = true;
  bool _authRequired = false;

  @override
  void initState() {
    super.initState();
    _checkAuthStatus();
  }

  Future<void> _checkAuthStatus() async {
    try {
      final authRequired = await BiometricAuthService.isAuthRequired();

      if (!widget.requireAuth || !authRequired) {
        setState(() {
          _isAuthenticated = true;
          _authRequired = false;
          _isLoading = false;
        });
        return;
      }

      setState(() {
        _authRequired = true;
        _isLoading = false;
      });

      // Try automatic authentication
      final authResult = await BiometricAuthService.authenticate();

      if (authResult.isSuccess) {
        setState(() {
          _isAuthenticated = true;
        });
      } else if (authResult.biometricFailed || authResult.noAuthMethod) {
        _showSetupAuthDialog();
      }
    } catch (e) {
      print('Auth check error: $e');
      setState(() {
        _isLoading = false;
        _authRequired = false;
        _isAuthenticated = true;
      });
    }
  }

  Future<void> _showSetupAuthDialog() async {
    final theme = Theme.of(context);

    final shouldSetup = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => AlertDialog(
            title: Text(
              'auth_setup_security'.tr(),
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            content: Text(
              'auth_setup_content'.tr(),
              style: theme.textTheme.bodyMedium,
            ),
            actions: [
              TextButton(
                onPressed: () => context.pop(false),
                child: Text('auth_skip_btn'.tr()),
              ),
              TextButton(
                onPressed: () => context.pop(true),
                child: Text(
                  'auth_setup_btn'.tr(),
                  style: TextStyle(
                    color: theme.colorScheme.primary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
    );

    if (shouldSetup == true) {
      await _setupAuthentication();
    } else {
      // User skipped, allow access
      setState(() {
        _isAuthenticated = true;
      });
    }
  }

  Future<void> _setupAuthentication() async {
    // Check if biometric is supported
    final biometricSupported =
        await BiometricAuthService.isBiometricSupported();

    if (biometricSupported) {
      await _setupBiometric();
    } else {
      // No biometric support, allow access without authentication
      setState(() {
        _isAuthenticated = true;
      });
    }
  }

  Future<void> _setupBiometric() async {
    try {
      final authenticated =
          await BiometricAuthService.authenticateWithBiometrics();
      if (authenticated) {
        await BiometricAuthService.setBiometricEnabled(true);
        await BiometricAuthService.setAuthRequired(true);
        setState(() {
          _isAuthenticated = true;
        });
      } else {
        // Biometric setup failed, allow access without authentication
        setState(() {
          _isAuthenticated = true;
        });
      }
    } catch (e) {
      // Biometric setup error, allow access without authentication
      setState(() {
        _isAuthenticated = true;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    if (_isLoading) {
      return Scaffold(
        backgroundColor: theme.scaffoldBackgroundColor,
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(color: theme.colorScheme.primary),
              const SizedBox(height: 16),
              Text(
                'auth_checking'.tr(),
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.hintColor,
                ),
              ),
            ],
          ),
        ),
      );
    }

    if (!_isAuthenticated && _authRequired) {
      return Scaffold(
        backgroundColor: theme.scaffoldBackgroundColor,
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: theme.colorScheme.primary.withValues(alpha: 0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.lock,
                  size: 48,
                  color: theme.colorScheme.primary,
                ),
              ),
              const SizedBox(height: 24),
              Text(
                'auth_required_title'.tr(),
                style: theme.textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'auth_required_subtitle'.tr(),
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.hintColor,
                ),
              ),
              const SizedBox(height: 32),
              ElevatedButton(
                onPressed: _checkAuthStatus,
                style: ElevatedButton.styleFrom(
                  backgroundColor: theme.colorScheme.primary,
                  foregroundColor: colorScheme.onPrimary,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 32,
                    vertical: 12,
                  ),
                ),
                child: Text('auth_authenticate'.tr()),
              ),
            ],
          ),
        ),
      );
    }

    return widget.child;
  }
}
