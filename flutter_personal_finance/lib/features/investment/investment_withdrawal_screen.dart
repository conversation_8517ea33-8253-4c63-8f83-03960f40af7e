import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:easy_localization/easy_localization.dart';
import '../../services/investment_service.dart';
import '../models/investment_model.dart';

class InvestmentWithdrawalScreen extends StatefulWidget {
  final InvestmentModel investment;

  const InvestmentWithdrawalScreen({super.key, required this.investment});

  @override
  State<InvestmentWithdrawalScreen> createState() =>
      _InvestmentWithdrawalScreenState();
}

class _InvestmentWithdrawalScreenState
    extends State<InvestmentWithdrawalScreen> {
  final _formKey = GlobalKey<FormState>();
  final _amountController = TextEditingController();
  final _reasonController = TextEditingController();

  WithdrawalType _selectedType = WithdrawalType.specificAmount;
  InvestmentWithdrawalCalculation? _calculation;
  bool _isLoading = false;
  bool _showCalculation = false;
  InvestmentModel? _currentInvestment;
  bool _isRefreshing = false;

  @override
  void initState() {
    super.initState();
    _currentInvestment = widget.investment;
    _refreshInvestmentData();
  }

  @override
  void dispose() {
    _amountController.dispose();
    _reasonController.dispose();
    super.dispose();
  }

  Future<void> _refreshInvestmentData() async {
    if (mounted) {
      setState(() {
        _isRefreshing = true;
      });
    }

    try {
      final investments = await InvestmentService.getInvestments();
      final latestInvestment = investments.firstWhere(
        (inv) => inv.id == widget.investment.id,
        orElse: () => widget.investment,
      );

      if (mounted) {
        setState(() {
          _currentInvestment = latestInvestment;
          _isRefreshing = false;
        });
      }
    } catch (e) {
      // If there's an error, keep using the original investment data
      if (mounted) {
        setState(() {
          _currentInvestment = widget.investment;
          _isRefreshing = false;
        });
      }
    }
  }

  Future<void> _calculateWithdrawal() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      // Get the latest investment data to ensure calculations are accurate
      final investments = await InvestmentService.getInvestments();
      final latestInvestment = investments.firstWhere(
        (inv) => inv.id == widget.investment.id,
        orElse: () => widget.investment,
      );

      final amount =
          _selectedType == WithdrawalType.percentage ||
                  _selectedType == WithdrawalType.specificAmount
              ? double.parse(_amountController.text)
              : 0.0;

      final calculation = await InvestmentService.calculateWithdrawal(
        investment: latestInvestment,
        withdrawalAmount: amount,
        type: _selectedType,
      );

      setState(() {
        _calculation = calculation;
        _showCalculation = true;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        _showSnackBar(
          'investment_withdrawal_error_calculating'.tr(
            namedArgs: {'error': e.toString()},
          ),
        );
      }
    }
  }

  Future<void> _processWithdrawal() async {
    if (_calculation == null) return;

    setState(() {
      _isLoading = true;
    });

    try {
      // Get the latest investment data to ensure processing is accurate
      final investments = await InvestmentService.getInvestments();
      final latestInvestment = investments.firstWhere(
        (inv) => inv.id == widget.investment.id,
        orElse: () => widget.investment,
      );

      final withdrawal = InvestmentWithdrawalModel(
        investmentId: widget.investment.id,
        amount: _calculation!.withdrawalAmount,
        date: DateTime.now(),
        reason:
            _reasonController.text.isNotEmpty
                ? _reasonController.text
                : _getWithdrawalTypeLabel(_selectedType),
        penaltyAmount: _calculation!.penaltyAmount,
      );

      await InvestmentService.processPartialWithdrawal(
        investment: latestInvestment,
        withdrawal: withdrawal,
      );

      if (mounted) {
        _showSnackBar('investment_withdrawal_success'.tr());
        Navigator.of(context).pop(true);
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        _showSnackBar(
          'investment_withdrawal_error_processing'.tr(
            namedArgs: {'error': e.toString()},
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        title: Text(
          'investment_withdrawal_title'.tr(),
          style: theme.appBarTheme.titleTextStyle?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      _buildInvestmentSummary(),
                      const SizedBox(height: 24),
                      _buildWithdrawalTypeSelection(),
                      const SizedBox(height: 16),
                      if (_needsAmountInput()) ...[
                        _buildAmountInput(),
                        const SizedBox(height: 16),
                      ],
                      _buildReasonInput(),
                      const SizedBox(height: 24),
                      if (!_showCalculation)
                        _buildCalculateButton()
                      else ...[
                        _buildCalculationResults(),
                        const SizedBox(height: 24),
                        _buildConfirmButton(),
                      ],
                      const SizedBox(height: 16),
                      _buildWarningNote(),
                    ],
                  ),
                ),
              ),
    );
  }

  Widget _buildInvestmentSummary() {
    final theme = Theme.of(context);
    final investment = _currentInvestment ?? widget.investment;
    final currentValue = investment.currentValue;
    final totalProfits = currentValue - investment.totalInvested;
    final isProfit = totalProfits >= 0;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    investment.name,
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                if (_isRefreshing)
                  SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      color: theme.colorScheme.primary,
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildSummaryItem(
                    'investment_withdrawal_current_value'.tr(),
                    '₹${currentValue.toStringAsFixed(2)}',
                    Icons.account_balance,
                    theme.colorScheme.primary,
                  ),
                ),
                Expanded(
                  child: _buildSummaryItem(
                    'investment_withdrawal_total_invested'.tr(),
                    '₹${investment.totalInvested.toStringAsFixed(2)}',
                    Icons.trending_up,
                    theme.hintColor,
                  ),
                ),
                Expanded(
                  child: _buildSummaryItem(
                    'investment_withdrawal_profit_loss'.tr(),
                    '${isProfit ? '+' : ''}₹${totalProfits.toStringAsFixed(2)}',
                    isProfit ? Icons.arrow_upward : Icons.arrow_downward,
                    isProfit ? Colors.green : Colors.red,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryItem(
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    final theme = Theme.of(context);
    return Column(
      children: [
        Icon(icon, color: color, size: 20),
        const SizedBox(height: 4),
        Text(
          label,
          style: theme.textTheme.bodySmall?.copyWith(color: theme.hintColor),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 2),
        Text(
          value,
          style: theme.textTheme.labelMedium?.copyWith(
            color: color,
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildWithdrawalTypeSelection() {
    final theme = Theme.of(context);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Withdrawal Type',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            ...WithdrawalType.values
                .where(
                  (type) =>
                      type == WithdrawalType.specificAmount ||
                      type == WithdrawalType.percentage,
                )
                .map((type) => _buildWithdrawalTypeOption(type)),
          ],
        ),
      ),
    );
  }

  Widget _buildWithdrawalTypeOption(WithdrawalType type) {
    final theme = Theme.of(context);
    final isSelected = _selectedType == type;

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: InkWell(
        onTap: () {
          setState(() {
            _selectedType = type;
            _showCalculation = false;
            _amountController.clear();
          });
        },
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            border: Border.all(
              color:
                  isSelected
                      ? theme.colorScheme.primary
                      : theme.hintColor.withValues(alpha: 0.3),
            ),
            borderRadius: BorderRadius.circular(8),
            color:
                isSelected
                    ? theme.colorScheme.primary.withValues(alpha: 0.1)
                    : null,
          ),
          child: Row(
            children: [
              Radio<WithdrawalType>(
                value: type,
                groupValue: _selectedType,
                onChanged: (value) {
                  setState(() {
                    _selectedType = value!;
                    _showCalculation = false;
                    _amountController.clear();
                  });
                },
                activeColor: theme.colorScheme.primary,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _getWithdrawalTypeLabel(type),
                      style: theme.textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: isSelected ? theme.colorScheme.primary : null,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      _getWithdrawalTypeDescription(type),
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.hintColor,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAmountInput() {
    final theme = Theme.of(context);
    final investment = _currentInvestment ?? widget.investment;
    final maxAmount =
        _selectedType == WithdrawalType.percentage
            ? 100.0
            : investment.currentValue;
    final suffix = _selectedType == WithdrawalType.percentage ? '%' : '';

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              _selectedType == WithdrawalType.percentage
                  ? 'Percentage to Withdraw'
                  : 'Amount to Withdraw',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _amountController,
              decoration: InputDecoration(
                labelText:
                    _selectedType == WithdrawalType.percentage
                        ? 'Percentage (%)'
                        : 'Amount (₹)',
                hintText:
                    _selectedType == WithdrawalType.percentage
                        ? 'e.g., 25'
                        : 'e.g., 10000',
                prefixIcon: Icon(
                  _selectedType == WithdrawalType.percentage
                      ? Icons.percent
                      : Icons.currency_rupee,
                ),
                suffix: Text(suffix),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              keyboardType: TextInputType.number,
              inputFormatters: [
                FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
              ],
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter ${_selectedType == WithdrawalType.percentage ? 'percentage' : 'amount'}';
                }
                final amount = double.tryParse(value);
                if (amount == null || amount <= 0) {
                  return 'Please enter a valid ${_selectedType == WithdrawalType.percentage ? 'percentage' : 'amount'}';
                }
                if (amount > maxAmount) {
                  return 'Amount cannot exceed ${_selectedType == WithdrawalType.percentage ? '100%' : '₹${maxAmount.toStringAsFixed(2)}'}';
                }
                return null;
              },
              onChanged: (value) {
                setState(() {
                  _showCalculation = false;
                });
              },
            ),
            const SizedBox(height: 8),
            Text(
              'Maximum: ${_selectedType == WithdrawalType.percentage ? '100%' : '₹${maxAmount.toStringAsFixed(2)}'}',
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.hintColor,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildReasonInput() {
    final theme = Theme.of(context);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Reason for Withdrawal (Optional)',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _reasonController,
              decoration: InputDecoration(
                labelText: 'Reason',
                hintText: 'e.g., Emergency, Home purchase, etc.',
                prefixIcon: const Icon(Icons.note_alt),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              maxLines: 3,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCalculateButton() {
    final theme = Theme.of(context);

    return ElevatedButton.icon(
      onPressed: _calculateWithdrawal,
      style: ElevatedButton.styleFrom(
        backgroundColor: theme.colorScheme.primary,
        foregroundColor: theme.colorScheme.onPrimary,
        padding: const EdgeInsets.symmetric(vertical: 16),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
      icon: const Icon(Icons.calculate, color: Colors.white),
      label: Text(
        'Calculate Withdrawal',
        style: theme.textTheme.labelLarge?.copyWith(
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),
    );
  }

  Widget _buildCalculationResults() {
    if (_calculation == null) return Container();

    final theme = Theme.of(context);
    final hasEarlyPenalty = _calculation!.penaltyAmount > 0;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.receipt_long, color: theme.colorScheme.primary),
                const SizedBox(width: 8),
                Text(
                  'Withdrawal Calculation',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildCalculationRow(
              'Withdrawal Amount',
              '₹${_calculation!.withdrawalAmount.toStringAsFixed(2)}',
            ),
            if (hasEarlyPenalty) ...[
              _buildCalculationRow(
                'Early Withdrawal Penalty',
                '-₹${_calculation!.penaltyAmount.toStringAsFixed(2)}',
                Colors.red,
              ),
              const Divider(),
            ],
            _buildCalculationRow(
              'Net Amount You\'ll Receive',
              '₹${_calculation!.netAmount.toStringAsFixed(2)}',
              Colors.green,
              true,
            ),
            _buildCalculationRow(
              'Remaining Investment Value',
              '₹${_calculation!.remainingValue.toStringAsFixed(2)}',
            ),
            if (_calculation!.isFullWithdrawal) ...[
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.orange.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: Colors.orange.withValues(alpha: 0.3),
                  ),
                ),
                child: Row(
                  children: [
                    Icon(Icons.warning, color: Colors.orange, size: 20),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'This will close your investment completely',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: Colors.orange,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildCalculationRow(
    String label,
    String value, [
    Color? valueColor,
    bool isTotal = false,
  ]) {
    final theme = Theme.of(context);

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 6),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: theme.textTheme.bodyMedium?.copyWith(
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
            ),
          ),
          Text(
            value,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: valueColor,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.w600,
              fontSize: isTotal ? 16 : null,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildConfirmButton() {
    final theme = Theme.of(context);

    return ElevatedButton.icon(
      onPressed: () => _showConfirmationDialog(),
      style: ElevatedButton.styleFrom(
        backgroundColor: theme.colorScheme.error,
        foregroundColor: theme.colorScheme.onError,
        padding: const EdgeInsets.symmetric(vertical: 16),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
      icon: const Icon(Icons.money_off, color: Colors.white),
      label: Text(
        'Confirm Withdrawal',
        style: theme.textTheme.labelLarge?.copyWith(
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),
    );
  }

  Widget _buildWarningNote() {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.orange.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.warning_amber, color: Colors.orange, size: 20),
              const SizedBox(width: 8),
              Text(
                'Important Notes',
                style: theme.textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.orange,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            '• Withdrawals cannot be reversed once processed\n'
            '• Early withdrawal penalties may apply\n'
            '• Consider tax implications on gains\n'
            '• Remaining amount will continue to grow',
            style: theme.textTheme.bodySmall?.copyWith(
              color: Colors.orange.withValues(alpha: 0.8),
            ),
          ),
        ],
      ),
    );
  }

  void _showConfirmationDialog() {
    final theme = Theme.of(context);

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Row(
              children: [
                Icon(Icons.warning, color: theme.colorScheme.error, size: 20),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Confirm Withdrawal',
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            content: ConstrainedBox(
              constraints: const BoxConstraints(maxWidth: 300),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  RichText(
                    text: TextSpan(
                      style: theme.textTheme.bodyMedium,
                      children: [
                        const TextSpan(
                          text: 'Are you sure you want to withdraw ',
                        ),
                        TextSpan(
                          text:
                              '₹${_calculation!.netAmount.toStringAsFixed(2)}',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: theme.colorScheme.error,
                          ),
                        ),
                        const TextSpan(text: '?'),
                      ],
                    ),
                  ),
                  const SizedBox(height: 12),
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.red.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: Colors.red.withValues(alpha: 0.3),
                      ),
                    ),
                    child: Row(
                      children: [
                        Icon(Icons.warning_amber, color: Colors.red, size: 16),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            'This action cannot be undone',
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: Colors.red,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  _processWithdrawal();
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: theme.colorScheme.error,
                  foregroundColor: theme.colorScheme.onError,
                ),
                child: Text('Confirm', style: TextStyle(color: Colors.white)),
              ),
            ],
          ),
    );
  }

  bool _needsAmountInput() {
    return _selectedType == WithdrawalType.specificAmount ||
        _selectedType == WithdrawalType.percentage;
  }

  String _getWithdrawalTypeLabel(WithdrawalType type) {
    switch (type) {
      case WithdrawalType.specificAmount:
        return 'Specific Amount';
      case WithdrawalType.percentage:
        return 'Percentage';
      case WithdrawalType.profitsOnly:
        return 'Profits Only';
      case WithdrawalType.principalOnly:
        return 'Principal Only';
    }
  }

  String _getWithdrawalTypeDescription(WithdrawalType type) {
    switch (type) {
      case WithdrawalType.specificAmount:
        return 'Withdraw a specific rupee amount';
      case WithdrawalType.percentage:
        return 'Withdraw a percentage of total value';
      case WithdrawalType.profitsOnly:
        return 'Withdraw only the gains/profits';
      case WithdrawalType.principalOnly:
        return 'Withdraw only the original investment';
    }
  }

  void _showSnackBar(String message) {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: Text(message)));
  }
}
