import 'package:flutter/material.dart';

class InvestmentExamplesScreen extends StatelessWidget {
  const InvestmentExamplesScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        title: Text(
          'Investment Guide',
          style: theme.appBarTheme.titleTextStyle?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(theme),
            const SizedBox(height: 24),
            _buildExampleCard(
              theme,
              '📊 Scenario 1: Track Existing Investment',
              'I have ₹50,000 already invested in FD earning 7% yearly interest',
              [
                '✅ Go to Investment Dashboard → Add Investment',
                '✅ Name: "HDFC Fixed Deposit"',
                '✅ Category: "Fixed Deposit"',
                '✅ Initial Amount: ₹50,000 (current invested amount)',
                '✅ Return Type: Simple Interest',
                '✅ Expected Return: 7% Yearly',
                '✅ Set maturity date if applicable',
              ],
              'System calculates: Current value grows automatically based on time elapsed!',
              Colors.blue,
            ),
            const SizedBox(height: 16),
            _buildExampleCard(
              theme,
              '💰 Scenario 2: Ongoing SIP Investment',
              'I invested ₹1,38,000 in Mutual Fund SIP, continuing ₹5,000/month',
              [
                '✅ Mark "SIP" category as Investment in Categories screen',
                '✅ Add Investment: Name "Equity Mutual Fund"',
                '✅ Initial Amount: ₹1,38,000 (total invested till today)',
                '✅ Return Type: Compound Interest',
                '✅ Expected Return: 12% Yearly',
                '✅ When you add ₹5,000 expense in "SIP" category →',
                '✅ System automatically adds it to this investment!',
              ],
              'Auto-tracking: Every SIP transaction increases your investment value!',
              Colors.green,
            ),
            const SizedBox(height: 16),
            _buildExampleCard(
              theme,
              '🏠 Scenario 3: Property with Known Value',
              'I bought property for ₹25,00,000, now worth ₹30,00,000',
              [
                '✅ Name: "Residential Property Mumbai"',
                '✅ Category: "Real Estate"',
                '✅ Initial Amount: ₹30,00,000 (current market value)',
                '✅ Return Type: Manual Entry',
                '✅ Update value manually when market changes',
                '✅ Track appreciation over time',
              ],
              'Perfect for assets with irregular value changes!',
              Colors.orange,
            ),
            const SizedBox(height: 16),
            _buildExampleCard(
              theme,
              '💸 Scenario 4: Investment with Withdrawals',
              'I have ₹2,00,000 in investment, need to withdraw ₹50,000',
              [
                '✅ Go to Investment Detail → 3-dot menu → Withdraw',
                '✅ Choose withdrawal type:',
                '  • Specific Amount: ₹50,000',
                '  • Percentage: 25% of total value',
                '✅ System calculates penalty (if applicable)',
                '✅ Shows net amount you\'ll receive after calculations',
                '✅ Remaining investment value clearly displayed',
              ],
              'Smart withdrawal: System automatically handles profit vs principal allocation!',
              Colors.red,
            ),
            const SizedBox(height: 24),
            _buildWorkflowCard(theme),
            const SizedBox(height: 24),
            _buildReturnTypeGuide(theme),
            const SizedBox(height: 24),
            _buildWithdrawalGuide(theme),
            const SizedBox(height: 24),
            _buildQuickTipsCard(theme),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            theme.colorScheme.primary.withValues(alpha: 0.1),
            theme.colorScheme.secondary.withValues(alpha: 0.1),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: theme.colorScheme.primary.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.school, color: theme.colorScheme.primary, size: 28),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  'Complete Investment Management',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.primary,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            'Track all your investments with automatic calculations, smart withdrawals, '
            'and seamless transaction linking. From SIPs to FDs to Real Estate!',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWorkflowCard(ThemeData theme) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.timeline,
                  color: theme.colorScheme.primary,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'Complete Workflow',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildWorkflowStep(
              '1',
              'Setup Categories',
              'Mark categories as "Investment" in Income/Expense Categories',
              theme.colorScheme.primary,
            ),
            _buildWorkflowStep(
              '2',
              'Add Investment',
              'Create investment with proper return type and initial amount',
              Colors.blue,
            ),
            _buildWorkflowStep(
              '3',
              'Auto-Tracking',
              'System links expense transactions to investments automatically',
              Colors.green,
            ),
            _buildWorkflowStep(
              '4',
              'Monitor Growth',
              'View real-time calculations and projections in dashboard',
              Colors.orange,
            ),
            _buildWorkflowStep(
              '5',
              'Smart Withdrawals',
              'Withdraw specific amounts or percentages with automatic calculations',
              Colors.red,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildWorkflowStep(
    String number,
    String title,
    String description,
    Color color,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 28,
            height: 28,
            decoration: BoxDecoration(color: color, shape: BoxShape.circle),
            child: Center(
              child: Text(
                number,
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: 12,
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(fontWeight: FontWeight.bold, color: color),
                ),
                const SizedBox(height: 2),
                Text(description, style: const TextStyle(fontSize: 12)),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWithdrawalGuide(ThemeData theme) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.money_off, color: Colors.red, size: 20),
                const SizedBox(width: 8),
                Text(
                  'Available Withdrawal Options',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            _buildWithdrawalType(
              'Specific Amount',
              'Withdraw exact ₹ amount you need',
              'Need ₹50,000 for emergency expenses',
              Colors.blue,
            ),
            _buildWithdrawalType(
              'Percentage',
              'Withdraw % of total investment value',
              'Take out 25% of total investment portfolio',
              Colors.green,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildWithdrawalType(
    String name,
    String description,
    String example,
    Color color,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            name,
            style: TextStyle(fontWeight: FontWeight.bold, color: color),
          ),
          const SizedBox(height: 4),
          Text(description, style: const TextStyle(fontSize: 12)),
          Text(
            'e.g., $example',
            style: TextStyle(
              fontSize: 11,
              color: color.withValues(alpha: 0.7),
              fontStyle: FontStyle.italic,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildExampleCard(
    ThemeData theme,
    String title,
    String scenario,
    List<String> steps,
    String result,
    Color accentColor,
  ) {
    return Card(
      elevation: 2,
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: accentColor.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: accentColor.withValues(alpha: 0.1),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      _getIconForCase(title),
                      color: accentColor,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      title,
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: accentColor,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: theme.colorScheme.surface,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  scenario,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ),
              const SizedBox(height: 16),
              Text(
                'Step-by-Step:',
                style: theme.textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              ...steps.map(
                (step) => Padding(
                  padding: const EdgeInsets.only(bottom: 4),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        margin: const EdgeInsets.only(top: 6),
                        width: 4,
                        height: 4,
                        decoration: BoxDecoration(
                          color: accentColor,
                          shape: BoxShape.circle,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(step, style: theme.textTheme.bodySmall),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: accentColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: accentColor.withValues(alpha: 0.3)),
                ),
                child: Row(
                  children: [
                    Icon(Icons.lightbulb, color: accentColor, size: 16),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        result,
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: accentColor,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildQuickTipsCard(ThemeData theme) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.tips_and_updates,
                  color: theme.colorScheme.primary,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'Pro Tips',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            _buildTip(
              '🎯',
              'Use "Initial Amount" for total invested till today, not original amount',
            ),
            _buildTip(
              '🔗',
              'Mark expense categories as "Investment" for auto-tracking',
            ),
            _buildTip(
              '⚠️',
              'Set maturity dates to track investment duration and goals',
            ),
            _buildTip(
              '📈',
              'Compound Interest for SIPs/Stocks, Simple Interest for FDs/Bonds',
            ),
            _buildTip(
              '💰',
              'Withdrawals automatically handle profit vs principal allocation intelligently',
            ),
            _buildTip('📊', 'View detailed analytics in the Withdrawals tab'),
          ],
        ),
      ),
    );
  }

  Widget _buildTip(String emoji, String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Text(emoji, style: const TextStyle(fontSize: 16)),
          const SizedBox(width: 8),
          Expanded(child: Text(text, style: const TextStyle(fontSize: 14))),
        ],
      ),
    );
  }

  Widget _buildReturnTypeGuide(ThemeData theme) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.calculate,
                  color: theme.colorScheme.secondary,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'Return Types Explained',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            _buildReturnType(
              'Simple Interest',
              'Fixed returns (FD, Bonds, Government Securities)',
              'P + (P × R × T) - Fixed growth rate',
              Colors.blue,
            ),
            _buildReturnType(
              'Compound Interest',
              'Compounding growth (SIP, Mutual Funds, Stocks)',
              'P × (1 + R)^T - Growth on growth',
              Colors.green,
            ),
            _buildReturnType(
              'Exact Amount',
              'Known maturity value (PPF, NSC, Insurance)',
              'Enter final amount - System calculates returns',
              Colors.orange,
            ),
            _buildReturnType(
              'Manual Entry',
              'Complex or irregular instruments (Real Estate, Gold)',
              'Update values manually as needed',
              Colors.purple,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildReturnType(
    String name,
    String use,
    String formula,
    Color color,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            name,
            style: TextStyle(fontWeight: FontWeight.bold, color: color),
          ),
          const SizedBox(height: 4),
          Text(use, style: const TextStyle(fontSize: 12)),
          Text(
            formula,
            style: TextStyle(
              fontSize: 11,
              color: color.withValues(alpha: 0.7),
              fontStyle: FontStyle.italic,
            ),
          ),
        ],
      ),
    );
  }

  IconData _getIconForCase(String title) {
    if (title.contains('Scenario 1')) return Icons.account_balance;
    if (title.contains('Scenario 2')) return Icons.trending_up;
    if (title.contains('Scenario 3')) return Icons.home;
    if (title.contains('Scenario 4')) return Icons.money_off;
    return Icons.show_chart;
  }
}
