import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../services/investment_service.dart';
import '../models/investment_model.dart';
import 'investment_detail_screen.dart';
import 'add_investment_screen.dart';
import 'investment_examples_screen.dart';

class InvestmentDashboardScreen extends StatefulWidget {
  const InvestmentDashboardScreen({super.key});

  @override
  State<InvestmentDashboardScreen> createState() =>
      _InvestmentDashboardScreenState();
}

class _InvestmentDashboardScreenState extends State<InvestmentDashboardScreen> {
  List<InvestmentModel> _investments = [];
  bool _isLoading = true;
  double _totalPortfolioValue = 0.0;
  double _totalInvested = 0.0;
  double _totalReturns = 0.0;

  // Visibility toggle for amounts
  bool _isAmountVisible = true;
  bool _preferencesLoaded = false;
  static const String _balanceVisibilityKey = 'balance_visibility';

  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  /// Initialize all data including preferences and investments
  Future<void> _initializeData() async {
    await _loadAmountVisibilityPreference();
    await _loadInvestments();
  }

  /// Load amount visibility preference from local storage
  Future<void> _loadAmountVisibilityPreference() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final savedVisibility = prefs.getBool(_balanceVisibilityKey) ?? true;
      print('Loaded preferences: savedVisibility=$savedVisibility');
      if (mounted) {
        setState(() {
          _isAmountVisible = savedVisibility;
          _preferencesLoaded = true;
        });
      }
    } catch (e) {
      print('Error loading amount visibility preference: $e');
      // Use default value (true) if error occurs
      if (mounted) {
        setState(() {
          _isAmountVisible = true;
          _preferencesLoaded = true;
        });
      }
    }
  }

  /// Save amount visibility preference to local storage
  Future<void> _saveAmountVisibilityPreference(bool isVisible) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_balanceVisibilityKey, isVisible);
    } catch (e) {
      print('Error saving amount visibility preference: $e');
    }
  }

  Future<void> _loadInvestments() async {
    setState(() => _isLoading = true);

    try {
      final investments = await InvestmentService.getInvestments();
      final portfolioValue = await InvestmentService.getTotalPortfolioValue();
      final totalInvested = await InvestmentService.getTotalInvestedAmount();
      final totalReturns = await InvestmentService.getTotalReturns();

      setState(() {
        _investments = investments;
        _totalPortfolioValue = portfolioValue;
        _totalInvested = totalInvested;
        _totalReturns = totalReturns;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      _showSnackBar('Error loading investments: $e');
    }
  }

  void _showSnackBar(String message) {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: Text(message)));
  }

  /// Helper function to format amount based on visibility setting
  String _formatAmount(double amount, {bool forceVisible = false}) {
    final shouldShow = forceVisible || _isAmountVisible;
    print(
      '_formatAmount: amount=$amount, _isAmountVisible=$_isAmountVisible, forceVisible=$forceVisible, shouldShow=$shouldShow',
    );
    if (shouldShow) {
      return '₹${NumberFormat('#,##,###').format(amount)}';
    } else {
      return '****';
    }
  }

  /// Helper function to format compact amount based on visibility setting
  String _formatCompactAmount(double amount, {bool forceVisible = false}) {
    if (forceVisible || _isAmountVisible) {
      return '₹${NumberFormat.compact().format(amount)}';
    } else {
      return '****';
    }
  }

  /// Helper function to format percentage based on visibility setting
  String _formatPercentage(
    double percentage, {
    bool isProfit = true,
    bool forceVisible = false,
  }) {
    if (forceVisible || _isAmountVisible) {
      return '${isProfit ? '+' : ''}${percentage.toStringAsFixed(2)}%';
    } else {
      return '**%';
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        title: Text(
          'Investment',
          style: theme.appBarTheme.titleTextStyle?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        automaticallyImplyLeading: false,
        actions: [
          // Amount visibility toggle button
          Container(
            margin: const EdgeInsets.only(right: 8),
            decoration: BoxDecoration(
              color: colorScheme.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: IconButton(
              onPressed: () async {
                final newVisibility = !_isAmountVisible;
                print('Toggle pressed: $_isAmountVisible -> $newVisibility');
                setState(() {
                  _isAmountVisible = newVisibility;
                });
                // Save preference to local storage
                await _saveAmountVisibilityPreference(newVisibility);
                print('State updated and saved: $_isAmountVisible');
              },
              icon: Icon(
                _isAmountVisible ? Icons.visibility : Icons.visibility_off,
                color: colorScheme.primary,
                size: 20,
              ),
              tooltip: _isAmountVisible ? 'Hide Amounts' : 'Show Amounts',
              padding: const EdgeInsets.all(8),
            ),
          ),
          IconButton(
            icon: const Icon(Icons.calculate_outlined),
            onPressed: () => _navigateToCalculator(),
            tooltip: 'Investment Calculator',
          ),
          IconButton(
            icon: const Icon(Icons.help_outline),
            onPressed: () => _navigateToExamples(),
            tooltip: 'View Examples',
          ),
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => _navigateToAddInvestment(),
            tooltip: 'Add Investment',
          ),
        ],
      ),
      body:
          (_isLoading || !_preferencesLoaded)
              ? const Center(child: CircularProgressIndicator())
              : RefreshIndicator(
                onRefresh: _loadInvestments,
                child:
                    _investments.isEmpty
                        ? _buildEmptyState()
                        : SingleChildScrollView(
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // Portfolio Summary at the top (now scrollable)
                              _buildScrollablePortfolioSummary(),
                              const SizedBox(height: 24),
                              // Investment cards
                              ...List.generate(
                                _investments.length,
                                (index) =>
                                    _buildInvestmentCard(_investments[index]),
                              ),
                            ],
                          ),
                        ),
              ),
      floatingActionButton: FloatingActionButton(
        backgroundColor: theme.colorScheme.primary,
        onPressed: _navigateToAddInvestment,
        child: Icon(Icons.add, color: colorScheme.onPrimary),
      ),
    );
  }

  Widget _buildScrollablePortfolioSummary() {
    final theme = Theme.of(context);
    final profitLossPercentage =
        _totalInvested > 0 ? (_totalReturns / _totalInvested) * 100 : 0.0;
    final isProfit = _totalReturns >= 0;

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: Card(
        elevation: 16,
        shadowColor: theme.colorScheme.primary.withValues(alpha: 0.3),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(24)),
        clipBehavior: Clip.hardEdge,
        child: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                theme.colorScheme.primary.withValues(alpha: 0.08),
                theme.colorScheme.tertiary.withValues(alpha: 0.05),
                theme.colorScheme.primary.withValues(alpha: 0.06),
              ],
              stops: const [0.0, 0.5, 1.0],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(24),
            border: Border.all(
              color: theme.colorScheme.primary.withValues(alpha: 0.15),
              width: 1.5,
            ),
          ),
          child: Stack(
            children: [
              // Background decoration
              Positioned(
                top: -50,
                right: -50,
                child: Container(
                  width: 120,
                  height: 120,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: theme.colorScheme.primary.withValues(alpha: 0.04),
                  ),
                ),
              ),
              Positioned(
                bottom: -30,
                left: -30,
                child: Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: theme.colorScheme.tertiary.withValues(alpha: 0.06),
                  ),
                ),
              ),
              // Main content
              Padding(
                padding: const EdgeInsets.all(28),
                child: Column(
                  children: [
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [
                                theme.colorScheme.primary,
                                theme.colorScheme.primary.withValues(
                                  alpha: 0.8,
                                ),
                              ],
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                            ),
                            borderRadius: BorderRadius.circular(16),
                            boxShadow: [
                              BoxShadow(
                                color: theme.colorScheme.primary.withValues(
                                  alpha: 0.3,
                                ),
                                blurRadius: 12,
                                offset: const Offset(0, 4),
                              ),
                            ],
                          ),
                          child: Icon(
                            Icons.account_balance_wallet_outlined,
                            color: theme.colorScheme.onPrimary,
                            size: 32,
                          ),
                        ),
                        const SizedBox(width: 20),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Icon(
                                    Icons.trending_up,
                                    color: theme.colorScheme.primary,
                                    size: 18,
                                  ),
                                  const SizedBox(width: 6),
                                  Text(
                                    'Portfolio Overview',
                                    style: theme.textTheme.bodyMedium?.copyWith(
                                      color: theme.colorScheme.primary,
                                      fontWeight: FontWeight.w600,
                                      letterSpacing: 0.5,
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 8),
                              Text(
                                'Total Portfolio Value',
                                style: theme.textTheme.titleLarge?.copyWith(
                                  color: theme.colorScheme.onSurface,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              const SizedBox(height: 6),
                              Text(
                                _formatAmount(_totalPortfolioValue),
                                style: theme.textTheme.headlineLarge?.copyWith(
                                  color: theme.colorScheme.primary,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 32,
                                  letterSpacing: -0.5,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 32),
                    Container(
                      padding: const EdgeInsets.all(20),
                      decoration: BoxDecoration(
                        color: theme.colorScheme.surface.withValues(alpha: 0.7),
                        borderRadius: BorderRadius.circular(20),
                        border: Border.all(
                          color: theme.colorScheme.outline.withValues(
                            alpha: 0.1,
                          ),
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: theme.shadowColor.withValues(alpha: 0.05),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Row(
                        children: [
                          Expanded(
                            child: _buildEnhancedSummaryItem(
                              'Total Invested',
                              _formatCompactAmount(_totalInvested),
                              Icons.upload_outlined,
                              theme.colorScheme.primary,
                            ),
                          ),
                          Container(
                            width: 1,
                            height: 60,
                            color: theme.dividerColor.withValues(alpha: 0.2),
                          ),
                          Expanded(
                            child: _buildEnhancedSummaryItem(
                              'Total Returns',
                              _formatCompactAmount(_totalReturns),
                              isProfit
                                  ? Icons.trending_up
                                  : Icons.trending_down,
                              isProfit
                                  ? const Color(0xFF4CAF50)
                                  : const Color(0xFFFF5252),
                            ),
                          ),
                          Container(
                            width: 1,
                            height: 60,
                            color: theme.dividerColor.withValues(alpha: 0.2),
                          ),
                          Expanded(
                            child: _buildEnhancedSummaryItem(
                              'Total \nReturn %',
                              _formatPercentage(
                                profitLossPercentage,
                                isProfit: isProfit,
                              ),
                              isProfit ? Icons.show_chart : Icons.trending_down,
                              isProfit
                                  ? const Color(0xFF4CAF50)
                                  : const Color(0xFFFF5252),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEnhancedSummaryItem(
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    final theme = Theme.of(context);

    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(10),
          ),
          child: Icon(icon, color: color, size: 20),
        ),
        const SizedBox(height: 12),
        Text(
          label,
          style: theme.textTheme.bodySmall?.copyWith(
            color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
            fontSize: 12,
            fontWeight: FontWeight.w500,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 6),
        Text(
          value,
          style: theme.textTheme.titleMedium?.copyWith(
            color: color,
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildEmptyState() {
    final theme = Theme.of(context);

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: theme.hintColor.withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.trending_up,
                size: 64,
                color: theme.hintColor.withValues(alpha: 0.5),
              ),
            ),
            const SizedBox(height: 24),
            Text(
              'No Investments Yet',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: theme.hintColor,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              'Start building your investment portfolio by adding your first investment',
              textAlign: TextAlign.center,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.hintColor.withValues(alpha: 0.8),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInvestmentCard(InvestmentModel investment) {
    final theme = Theme.of(context);
    // For interest-based investments, show calculated returns; for manual entry, show stored value
    final currentValue =
        (investment.returnType == InvestmentReturnType.simpleInterest ||
                investment.returnType == InvestmentReturnType.compoundInterest)
            ? investment.calculateCurrentValue()
            : investment.currentValue;
    final profitLoss = currentValue - investment.totalInvested;
    final profitLossPercentage =
        investment.totalInvested > 0
            ? (profitLoss / investment.totalInvested) * 100
            : 0.0;
    final isProfit = profitLoss >= 0;

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Card(
        elevation: 3,
        shadowColor: theme.shadowColor.withValues(alpha: 0.1),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            color: theme.cardColor,
            border: Border.all(
              color:
                  isProfit
                      ? const Color(0xFF4CAF50).withValues(alpha: 0.2)
                      : const Color(0xFFFF5252).withValues(alpha: 0.2),
              width: 1,
            ),
          ),
          child: InkWell(
            onTap: () => _navigateToInvestmentDetail(investment),
            borderRadius: BorderRadius.circular(16),
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header Section
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              const Color(0xFF5C6BC0), // Muted indigo
                              const Color(0xFF7986CB), // Lighter indigo
                            ],
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                          ),
                          borderRadius: BorderRadius.circular(12),
                          boxShadow: [
                            BoxShadow(
                              color: const Color(
                                0xFF5C6BC0,
                              ).withValues(alpha: 0.2),
                              blurRadius: 6,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Icon(
                          Icons.trending_up,
                          color: theme.colorScheme.onPrimary,
                          size: 24,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              investment.category,
                              style: theme.textTheme.titleMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Row(
                              children: [
                                Expanded(
                                  child: Text(
                                    investment.name,
                                    style: theme.textTheme.bodyMedium?.copyWith(
                                      color: theme.hintColor,
                                    ),
                                  ),
                                ),
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 8,
                                    vertical: 4,
                                  ),
                                  decoration: BoxDecoration(
                                    color: _getStatusColor(
                                      investment.status,
                                    ).withValues(alpha: 0.1),
                                    borderRadius: BorderRadius.circular(12),
                                    border: Border.all(
                                      color: _getStatusColor(
                                        investment.status,
                                      ).withValues(alpha: 0.3),
                                    ),
                                  ),
                                  child: Text(
                                    investment.status.name.toUpperCase(),
                                    style: theme.textTheme.bodySmall?.copyWith(
                                      color: _getStatusColor(investment.status),
                                      fontWeight: FontWeight.bold,
                                      fontSize: 10,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),

                  // Financial Information Section
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: theme.colorScheme.surface.withValues(alpha: 0.3),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: theme.dividerColor.withValues(alpha: 0.3),
                      ),
                    ),
                    child: Column(
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: _buildFinancialItem(
                                'Current Value',
                                _formatAmount(currentValue),
                                const Color(0xFF5C6BC0),
                                Icons.account_balance,
                              ),
                            ),
                            Container(
                              width: 1,
                              height: 40,
                              color: theme.dividerColor.withValues(alpha: 0.5),
                            ),
                            Expanded(
                              child: _buildFinancialItem(
                                'Invested',
                                _formatAmount(investment.totalInvested),
                                theme.hintColor,
                                Icons.trending_up,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 12),
                        Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color:
                                isProfit
                                    ? const Color(
                                      0xFF4CAF50,
                                    ).withValues(alpha: 0.1)
                                    : const Color(
                                      0xFFFF5252,
                                    ).withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color:
                                  isProfit
                                      ? const Color(
                                        0xFF4CAF50,
                                      ).withValues(alpha: 0.2)
                                      : const Color(
                                        0xFFFF5252,
                                      ).withValues(alpha: 0.2),
                            ),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Row(
                                children: [
                                  Icon(
                                    isProfit
                                        ? Icons.trending_up
                                        : Icons.trending_down,
                                    color:
                                        isProfit
                                            ? const Color(0xFF4CAF50)
                                            : const Color(0xFFFF5252),
                                    size: 20,
                                  ),
                                  const SizedBox(width: 8),
                                  Text(
                                    'Profit & Loss',
                                    style: theme.textTheme.bodyMedium?.copyWith(
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ],
                              ),
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.end,
                                children: [
                                  Text(
                                    _isAmountVisible
                                        ? '${isProfit ? '+' : ''}₹${profitLoss.toStringAsFixed(2)}'
                                        : '${isProfit ? '+' : ''}****',
                                    style: theme.textTheme.labelLarge?.copyWith(
                                      color:
                                          isProfit
                                              ? const Color(0xFF4CAF50)
                                              : const Color(0xFFFF5252),
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  Text(
                                    _formatPercentage(
                                      profitLossPercentage,
                                      isProfit: isProfit,
                                    ),
                                    style: theme.textTheme.bodySmall?.copyWith(
                                      color:
                                          isProfit
                                              ? const Color(0xFF4CAF50)
                                              : const Color(0xFFFF5252),
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildFinancialItem(
    String label,
    String value,
    Color color,
    IconData icon,
  ) {
    final theme = Theme.of(context);

    return Column(
      children: [
        Icon(icon, color: color, size: 18),
        const SizedBox(height: 4),
        Text(
          label,
          style: theme.textTheme.bodySmall?.copyWith(
            color: theme.hintColor,
            fontSize: 11,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 2),
        Text(
          value,
          style: theme.textTheme.labelMedium?.copyWith(
            color: color,
            fontWeight: FontWeight.bold,
            fontSize: 13,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Color _getStatusColor(InvestmentStatus status) {
    switch (status) {
      case InvestmentStatus.active:
        return const Color(0xFF4CAF50); // Professional green
      case InvestmentStatus.matured:
        return const Color(0xFF2196F3); // Professional blue
      case InvestmentStatus.withdrawn:
        return const Color(0xFF757575); // Professional grey
      case InvestmentStatus.paused:
        return const Color(0xFFFF9800); // Professional orange
    }
  }

  void _navigateToAddInvestment() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const AddInvestmentScreen()),
    );

    if (result == true) {
      _loadInvestments();
    }
  }

  void _navigateToInvestmentDetail(InvestmentModel investment) async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => InvestmentDetailScreen(investment: investment),
      ),
    );

    if (result == true) {
      _loadInvestments();
    }
  }

  void _navigateToExamples() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const InvestmentExamplesScreen()),
    );
  }

  void _navigateToCalculator() {
    // Navigate to calculator using AppRouter
    context.pushNamed('investmentCalculator');
  }
}
