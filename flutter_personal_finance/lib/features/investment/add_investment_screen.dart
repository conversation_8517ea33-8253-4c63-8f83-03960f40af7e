import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:easy_localization/easy_localization.dart';

import '../../services/investment_service.dart';
import '../../services/local_storage_service.dart';
import '../../utils/helper/keyboard_dismiss_wrapper.dart';
import '../models/investment_model.dart';

class AddInvestmentScreen extends StatefulWidget {
  final InvestmentModel? existingInvestment;

  const AddInvestmentScreen({super.key, this.existingInvestment});

  @override
  State<AddInvestmentScreen> createState() => _AddInvestmentScreenState();
}

class _AddInvestmentScreenState extends State<AddInvestmentScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _categoryController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _initialAmountController = TextEditingController();
  final _expectedReturnController = TextEditingController();

  InvestmentReturnType _selectedReturnType =
      InvestmentReturnType.compoundInterest;
  InvestmentFrequency _selectedFrequency = InvestmentFrequency.yearly;
  InvestmentStatus _selectedStatus = InvestmentStatus.active;
  DateTime _startDate = DateTime.now();
  DateTime? _maturityDate;

  bool _hasMaturityDate = false;
  bool _isLoading = false;

  List<Map<String, dynamic>> _investmentCategories = [];

  @override
  void initState() {
    super.initState();
    _loadInvestmentCategories();

    if (widget.existingInvestment != null) {
      _populateFields();
    }
  }

  void _populateFields() {
    final investment = widget.existingInvestment!;
    _nameController.text = investment.name;
    _categoryController.text = investment.category;
    _descriptionController.text = investment.description;
    _initialAmountController.text = investment.initialAmount.toString();
    _expectedReturnController.text = investment.expectedReturnRate.toString();
    _selectedReturnType = investment.returnType;
    _selectedFrequency = investment.returnFrequency;
    _selectedStatus = investment.status;
    _startDate = investment.startDate;
    _maturityDate = investment.maturityDate;
    _hasMaturityDate = investment.maturityDate != null;
  }

  Future<void> _loadInvestmentCategories() async {
    final categories = await LocalStorageService.getIncomeExpenseCategories();
    setState(() {
      _investmentCategories =
          categories.where((cat) => cat['isInvestment'] == true).toList();
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isEditing = widget.existingInvestment != null;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        title: Text(
          isEditing
              ? 'investment_edit_title'.tr()
              : 'investment_add_title'.tr(),
          style: theme.appBarTheme.titleTextStyle?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios),
          onPressed: () => Navigator.of(context).pop(),
        ),
        actions: [
          if (!isEditing)
            IconButton(
              icon: const Icon(Icons.help_outline),
              onPressed: () => _showUseCaseGuide(),
            ),
          TextButton(
            onPressed: _isLoading ? null : _saveInvestment,
            child: Text(
              isEditing ? 'investment_update'.tr() : 'investment_save'.tr(),
              style: theme.textTheme.labelLarge?.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
      body: KeyboardDismissWrapper(
        child:
            _isLoading
                ? const Center(child: CircularProgressIndicator())
                : Form(
                  key: _formKey,
                  child: ListView(
                    padding: const EdgeInsets.all(16),
                    children: [
                      _buildBasicInfoSection(),
                      const SizedBox(height: 24),
                      _buildReturnDetailsSection(),
                      const SizedBox(height: 24),
                      _buildDatesSection(),
                      const SizedBox(height: 24),
                      _buildAdvancedOptionsSection(),
                      const SizedBox(height: 32),
                    ],
                  ),
                ),
      ),
    );
  }

  Widget _buildBasicInfoSection() {
    final theme = Theme.of(context);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'investment_basic_info'.tr(),
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _nameController,
              decoration: InputDecoration(
                labelText: 'investment_name_label'.tr(),
                hintText: 'investment_name_hint'.tr(),
                prefixIcon: Icon(Icons.trending_up),
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'investment_name_required'.tr();
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<String>(
              value:
                  _categoryController.text.isNotEmpty
                      ? _categoryController.text
                      : null,
              decoration: InputDecoration(
                labelText: 'investment_category_label'.tr(),
                prefixIcon: Icon(Icons.category),
              ),
              items:
                  _investmentCategories.map((category) {
                    return DropdownMenuItem<String>(
                      value: category['name'],
                      child: Text(category['name']),
                    );
                  }).toList(),
              onChanged: (value) {
                setState(() {
                  _categoryController.text = value ?? '';
                });
              },
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'investment_category_required'.tr();
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _descriptionController,
              decoration: InputDecoration(
                labelText: 'investment_description_label'.tr(),
                hintText: 'investment_description_hint'.tr(),
                prefixIcon: Icon(Icons.description),
              ),
              maxLines: 3,
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _initialAmountController,
              decoration: InputDecoration(
                labelText: 'investment_initial_amount_label'.tr(),
                hintText: 'investment_initial_amount_hint'.tr(),
                prefixIcon: Icon(Icons.currency_rupee),
                helperText: 'investment_initial_amount_helper'.tr(),
              ),
              keyboardType: const TextInputType.numberWithOptions(
                decimal: true,
              ),
              inputFormatters: [
                FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}')),
              ],
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'investment_initial_amount_required'.tr();
                }
                final amount = double.tryParse(value);
                if (amount == null || amount <= 0) {
                  return 'investment_amount_invalid'.tr();
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildReturnDetailsSection() {
    final theme = Theme.of(context);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'investment_return_details'.tr(),
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<InvestmentReturnType>(
              value: _selectedReturnType,
              decoration: InputDecoration(
                labelText: 'investment_return_type_label'.tr(),
                prefixIcon: Icon(Icons.calculate),
              ),
              items:
                  InvestmentReturnType.values.map((type) {
                    return DropdownMenuItem<InvestmentReturnType>(
                      value: type,
                      child: Text(_getReturnTypeLabel(type)),
                    );
                  }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedReturnType = value!;
                });
              },
            ),
            const SizedBox(height: 16),
            if (_selectedReturnType != InvestmentReturnType.exactAmount &&
                _selectedReturnType != InvestmentReturnType.manualEntry) ...[
              Container(
                padding: const EdgeInsets.all(12),
                margin: const EdgeInsets.only(bottom: 16),
                decoration: BoxDecoration(
                  color: Colors.blue.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
                ),
                child: Row(
                  children: [
                    Icon(Icons.info, color: Colors.blue, size: 16),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'investment_return_auto_calc'.tr(),
                        style: TextStyle(
                          color: Colors.blue.withValues(alpha: 0.8),
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              TextFormField(
                controller: _expectedReturnController,
                decoration: InputDecoration(
                  labelText: 'investment_return_rate_label'.tr(),
                  hintText: 'investment_return_rate_hint'.tr(),
                  prefixIcon: Icon(Icons.percent),
                ),
                keyboardType: const TextInputType.numberWithOptions(
                  decimal: true,
                ),
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}')),
                ],
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'investment_return_rate_required'.tr();
                  }
                  final rate = double.tryParse(value);
                  if (rate == null || rate < 0) {
                    return 'investment_return_rate_invalid'.tr();
                  }
                  if (rate > 100) {
                    return 'investment_return_rate_high'.tr();
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<InvestmentFrequency>(
                value: _selectedFrequency,
                decoration: InputDecoration(
                  labelText: 'investment_return_frequency_label'.tr(),
                  prefixIcon: Icon(Icons.schedule),
                ),
                items:
                    InvestmentFrequency.values.map((frequency) {
                      return DropdownMenuItem<InvestmentFrequency>(
                        value: frequency,
                        child: Text(_getFrequencyLabel(frequency)),
                      );
                    }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedFrequency = value!;
                  });
                },
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildDatesSection() {
    final theme = Theme.of(context);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'investment_dates'.tr(),
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            ListTile(
              contentPadding: EdgeInsets.zero,
              leading: const Icon(Icons.calendar_today),
              title: Text('investment_start_date'.tr()),
              subtitle: Text(
                '${_startDate.day}/${_startDate.month}/${_startDate.year}',
              ),
              onTap: () => _selectStartDate(),
            ),
            const Divider(),
            CheckboxListTile(
              contentPadding: EdgeInsets.zero,
              title: Text('investment_has_maturity'.tr()),
              value: _hasMaturityDate,
              onChanged: (value) {
                setState(() {
                  _hasMaturityDate = value ?? false;
                  if (!_hasMaturityDate) {
                    _maturityDate = null;
                  }
                });
              },
            ),
            if (_hasMaturityDate) ...[
              const Divider(),
              ListTile(
                contentPadding: EdgeInsets.zero,
                leading: const Icon(Icons.event),
                title: Text('investment_maturity_date'.tr()),
                subtitle: Text(
                  _maturityDate != null
                      ? '${_maturityDate!.day}/${_maturityDate!.month}/${_maturityDate!.year}'
                      : 'investment_select_maturity'.tr(),
                ),
                onTap: () => _selectMaturityDate(),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildAdvancedOptionsSection() {
    final theme = Theme.of(context);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'investment_advanced_options'.tr(),
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<InvestmentStatus>(
              value: _selectedStatus,
              decoration: InputDecoration(
                labelText: 'investment_status_label'.tr(),
                prefixIcon: Icon(Icons.flag),
              ),
              items:
                  InvestmentStatus.values.map((status) {
                    return DropdownMenuItem<InvestmentStatus>(
                      value: status,
                      child: Text(_getStatusLabel(status)),
                    );
                  }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedStatus = value!;
                });
              },
            ),
            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }

  String _getReturnTypeLabel(InvestmentReturnType type) {
    switch (type) {
      case InvestmentReturnType.simpleInterest:
        return 'investment_simple_interest'.tr();
      case InvestmentReturnType.compoundInterest:
        return 'investment_compound_interest'.tr();
      case InvestmentReturnType.exactAmount:
        return 'investment_exact_amount'.tr();
      case InvestmentReturnType.manualEntry:
        return 'investment_manual_entry'.tr();
    }
  }

  String _getFrequencyLabel(InvestmentFrequency frequency) {
    switch (frequency) {
      case InvestmentFrequency.monthly:
        return 'investment_monthly'.tr();
      case InvestmentFrequency.quarterly:
        return 'investment_quarterly'.tr();
      case InvestmentFrequency.halfYearly:
        return 'investment_half_yearly'.tr();
      case InvestmentFrequency.yearly:
        return 'investment_yearly'.tr();
    }
  }

  String _getStatusLabel(InvestmentStatus status) {
    switch (status) {
      case InvestmentStatus.active:
        return 'investment_active'.tr();
      case InvestmentStatus.matured:
        return 'investment_matured'.tr();
      case InvestmentStatus.withdrawn:
        return 'investment_withdrawn'.tr();
      case InvestmentStatus.paused:
        return 'investment_paused'.tr();
    }
  }

  Future<void> _selectStartDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _startDate,
      firstDate: DateTime(2000),
      lastDate: DateTime.now(),
    );

    if (date != null) {
      setState(() {
        _startDate = date;
      });
    }
  }

  Future<void> _selectMaturityDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _maturityDate ?? _startDate.add(const Duration(days: 365)),
      firstDate: _startDate,
      lastDate: DateTime.now().add(const Duration(days: 365 * 50)),
    );

    if (date != null) {
      setState(() {
        _maturityDate = date;
      });
    }
  }

  Future<void> _saveInvestment() async {
    if (!_formKey.currentState!.validate()) return;

    if (_hasMaturityDate && _maturityDate == null) {
      _showSnackBar('investment_select_maturity_error'.tr());
      return;
    }

    setState(() => _isLoading = true);

    try {
      final initialAmount = double.parse(_initialAmountController.text);
      final expectedReturn =
          _expectedReturnController.text.isNotEmpty
              ? double.parse(_expectedReturnController.text)
              : 0.0;

      final investment = InvestmentModel(
        id: widget.existingInvestment?.id,
        name: _nameController.text.trim(),
        category: _categoryController.text.trim(),
        categoryIds: [
          _investmentCategories.firstWhere(
            (cat) => cat['name'] == _categoryController.text.trim(),
          )['id'],
        ],
        description: _descriptionController.text.trim(),
        initialAmount: initialAmount,
        returnType: _selectedReturnType,
        expectedReturnRate: expectedReturn,
        returnFrequency: _selectedFrequency,
        startDate: _startDate,
        maturityDate: _hasMaturityDate ? _maturityDate : null,
        status: _selectedStatus,
      );

      if (widget.existingInvestment != null) {
        await InvestmentService.updateInvestment(investment);
        _showSnackBar('investment_updated_success'.tr());
      } else {
        await InvestmentService.addInvestment(investment);
        _showSnackBar('investment_added_success'.tr());
      }

      Navigator.of(context).pop(true);
    } catch (e) {
      _showSnackBar(
        'investment_save_error'.tr(namedArgs: {'error': e.toString()}),
      );
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _showSnackBar(String message) {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: Text(message)));
  }

  void _showUseCaseGuide() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        final theme = Theme.of(context);
        return AlertDialog(
          title: Row(
            children: [
              Icon(Icons.lightbulb_outline, color: theme.colorScheme.primary),
              const SizedBox(width: 8),
              const Text('Investment Use Cases'),
            ],
          ),
          content: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildUseCaseCard(
                  '📊 Case 1: Track Existing Investment',
                  'You already have ₹10,000 invested and want to see future returns.',
                  [
                    '• Enter ₹10,000 as Initial Amount',
                    '• Choose Simple/Compound Interest',
                    '• Set expected return rate & frequency',
                    '• System calculates future growth',
                  ],
                ),
                const SizedBox(height: 16),
                _buildUseCaseCard(
                  '💰 Case 2: Existing + Ongoing SIP',
                  'You have ₹10,000 invested and continue SIP monthly.',
                  [
                    '• Enter ₹10,000 as Initial Amount',
                    '• Mark categories as "Investment"',
                    '• New expense transactions auto-link',
                    '• Track total invested + returns',
                  ],
                ),
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.blue.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: Colors.blue.withValues(alpha: 0.3),
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(Icons.info, color: Colors.blue, size: 16),
                          const SizedBox(width: 8),
                          Text(
                            'Return Types Explained:',
                            style: theme.textTheme.titleSmall?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: Colors.blue,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Text(
                        '• Simple Interest: Fixed rate (e.g., 8% yearly)\n'
                        '• Compound Interest: Compound growth (e.g., 12% yearly)\n'
                        '• Exact Amount: Known final value\n'
                        '• Manual Entry: Update values manually',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: Colors.blue.withValues(alpha: 0.8),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Got it!'),
            ),
          ],
        );
      },
    );
  }

  Widget _buildUseCaseCard(
    String title,
    String description,
    List<String> steps,
  ) {
    final theme = Theme.of(context);
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: theme.dividerColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: theme.textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            description,
            style: theme.textTheme.bodySmall?.copyWith(color: theme.hintColor),
          ),
          const SizedBox(height: 8),
          ...steps.map(
            (step) => Padding(
              padding: const EdgeInsets.only(bottom: 2),
              child: Text(step, style: theme.textTheme.bodySmall),
            ),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _nameController.dispose();
    _categoryController.dispose();
    _descriptionController.dispose();
    _initialAmountController.dispose();
    _expectedReturnController.dispose();

    super.dispose();
  }
}
