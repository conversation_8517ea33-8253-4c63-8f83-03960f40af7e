import 'dart:io';
import 'dart:math' as math;
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import 'package:path_provider/path_provider.dart';

import '../../resources/app_theme.dart';
import '../../utils/helper/keyboard_dismiss_wrapper.dart';

/// Investment Calculator Screen with SIP, Lumpsum, and Hybrid calculations
class InvestmentCalculatorScreen extends StatefulWidget {
  const InvestmentCalculatorScreen({super.key});

  @override
  State<InvestmentCalculatorScreen> createState() =>
      _InvestmentCalculatorScreenState();
}

class _InvestmentCalculatorScreenState extends State<InvestmentCalculatorScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(
      length: 5,
      vsync: this,
      animationDuration: Duration.zero,
    );
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return KeyboardDismissWrapper(
      child: Scaffold(
        backgroundColor: theme.scaffoldBackgroundColor,
        appBar: AppBar(
          title: Text(
            'Investment Calculator',
            style: theme.appBarTheme.titleTextStyle?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          leading: IconButton(
            icon: const Icon(Icons.arrow_back_ios),
            onPressed: () => Navigator.of(context).pop(),
          ),
          bottom: TabBar(
            controller: _tabController,
            isScrollable: true,
            labelColor: Colors.white,
            unselectedLabelColor: Colors.white.withValues(alpha: 0.7),
            indicatorColor: colorScheme.primary,
            indicatorWeight: 3,
            indicatorSize: TabBarIndicatorSize.tab,
            dividerColor: Colors.transparent,
            labelStyle: theme.textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.w600,
              fontSize: 12,
            ),
            unselectedLabelStyle: theme.textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.w400,
              fontSize: 12,
            ),
            indicator: BoxDecoration(
              borderRadius: const BorderRadius.vertical(
                bottom: Radius.circular(3),
              ),
              color:
                  theme.brightness == Brightness.dark
                      ? Colors.transparent
                      : colorScheme.primary,
            ),
            labelPadding: const EdgeInsets.symmetric(horizontal: 8),
            tabs: const [
              Tab(icon: Icon(Icons.repeat, size: 20), text: 'SIP'),
              Tab(icon: Icon(Icons.monetization_on, size: 20), text: 'Lumpsum'),
              Tab(
                icon: Icon(Icons.trending_up, size: 20),
                text: 'SIP + Lumpsum',
              ),
              Tab(
                icon: Icon(Icons.account_balance, size: 20),
                text: 'Lumpsum + SIP',
              ),
              Tab(icon: Icon(Icons.auto_graph, size: 20), text: 'Step-up SIP'),
            ],
          ),
        ),
        body: TabBarView(
          controller: _tabController,
          children: const [
            SipCalculatorTab(),
            LumpsumCalculatorTab(),
            HybridCalculatorTab(),
            LumpsumSipCalculatorTab(),
            StepUpSipCalculatorTab(),
          ],
        ),
      ),
    );
  }
}

/// SIP Calculator Tab
class SipCalculatorTab extends StatefulWidget {
  const SipCalculatorTab({super.key});

  @override
  State<SipCalculatorTab> createState() => _SipCalculatorTabState();
}

class _SipCalculatorTabState extends State<SipCalculatorTab> {
  final _formKey = GlobalKey<FormState>();
  final _monthlyAmountController = TextEditingController();
  final _periodController = TextEditingController();
  final _returnRateController = TextEditingController();

  double _totalInvested = 0.0;
  double _maturityAmount = 0.0;
  double _totalReturns = 0.0;
  List<Map<String, dynamic>> _yearWiseData = [];
  List<FlSpot> _investmentChartData = [];
  List<FlSpot> _returnsChartData = [];
  bool _isExporting = false;

  @override
  void dispose() {
    _monthlyAmountController.dispose();
    _periodController.dispose();
    _returnRateController.dispose();
    super.dispose();
  }

  Future<void> _exportToPDF() async {
    if (_yearWiseData.isEmpty) return;

    setState(() {
      _isExporting = true;
    });

    try {
      final inputs = {
        'Monthly SIP Amount': NumberFormat(
          '#,##,###',
        ).format(double.tryParse(_monthlyAmountController.text) ?? 0),
        'Investment Period': _periodController.text + ' Years',
        'Expected Annual Return (CAGR)': _returnRateController.text + '%',
      };

      final results = {
        'Total Invested': _totalInvested,
        'Maturity Amount': _maturityAmount,
        'Total Returns': _totalReturns,
      };

      final pdf = await InvestmentCalculatorPDFHelper.generatePDF(
        calculatorType: 'SIP',
        inputs: inputs,
        results: results,
        yearWiseData: _yearWiseData,
      );

      await _previewPDF(
        pdf,
        'SIP_Calculator_Report_' +
            DateFormat('yyyy_MM_dd').format(DateTime.now()) +
            '.pdf',
      );
    } catch (e) {
      _showSnackBar('Failed to generate PDF: $e');
    } finally {
      setState(() {
        _isExporting = false;
      });
    }
  }

  Future<void> _previewPDF(pw.Document pdf, String filename) async {
    final pdfBytes = await pdf.save();

    Navigator.of(context).push(
      MaterialPageRoute(
        builder:
            (context) => _InvestmentPDFViewerScreen(
              pdfBytes: pdfBytes,
              filename: filename,
            ),
      ),
    );
  }

  void _showSnackBar(String message) {
    final theme = Theme.of(context);
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: theme.colorScheme.primary,
      ),
    );
  }

  void _calculateSIP() {
    if (_formKey.currentState?.validate() ?? false) {
      final monthlyAmount = double.tryParse(_monthlyAmountController.text) ?? 0;
      final periodInYears = double.tryParse(_periodController.text) ?? 0;
      final annualReturnRate = double.tryParse(_returnRateController.text) ?? 0;

      if (monthlyAmount > 0 && periodInYears > 0 && annualReturnRate > 0) {
        final monthlyReturnRate = annualReturnRate / 100 / 12;
        final totalMonths = periodInYears * 12;

        // Generate year-wise data and chart data
        final yearWiseData = <Map<String, dynamic>>[];
        final investmentChartData = <FlSpot>[];
        final returnsChartData = <FlSpot>[];

        for (int year = 1; year <= periodInYears; year++) {
          final monthsToDate = year * 12;

          // Calculate value at end of each year
          final valueAtYear =
              monthlyAmount *
              (((1 + monthlyReturnRate).pow(monthsToDate.toDouble()) - 1) /
                  monthlyReturnRate) *
              (1 + monthlyReturnRate);

          final investedToDate = monthlyAmount * monthsToDate;
          final returnsToDate = valueAtYear - investedToDate;

          yearWiseData.add({
            'year': year,
            'invested': investedToDate,
            'value': valueAtYear,
            'returns': returnsToDate,
          });

          investmentChartData.add(FlSpot(year.toDouble(), investedToDate));
          returnsChartData.add(FlSpot(year.toDouble(), returnsToDate));
        }

        // SIP Formula: M = P × [{(1 + i)^n - 1} / i] × (1 + i)
        // This uses CAGR (Compound Annual Growth Rate) for calculations
        final maturityAmount =
            monthlyAmount *
            (((1 + monthlyReturnRate).pow(totalMonths) - 1) /
                monthlyReturnRate) *
            (1 + monthlyReturnRate);

        setState(() {
          _totalInvested = monthlyAmount * totalMonths;
          _maturityAmount = maturityAmount;
          _totalReturns = _maturityAmount - _totalInvested;
          _yearWiseData = yearWiseData;
          _investmentChartData = investmentChartData;
          _returnsChartData = returnsChartData;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildCalculatorCard(
              title: 'SIP Details',
              children: [
                _buildInputField(
                  controller: _monthlyAmountController,
                  label: 'Monthly SIP Amount',
                  prefix: '',
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value?.isEmpty ?? true) {
                      return 'Please enter monthly amount';
                    }
                    final amount = double.tryParse(value!);
                    if (amount == null || amount <= 0) {
                      return 'Please enter a valid amount';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                _buildInputField(
                  controller: _periodController,
                  label: 'Investment Period',
                  suffix: 'Years',
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value?.isEmpty ?? true) {
                      return 'Please enter investment period';
                    }
                    final period = double.tryParse(value!);
                    if (period == null || period <= 0) {
                      return 'Please enter a valid period';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                _buildInputField(
                  controller: _returnRateController,
                  label: 'Expected Annual Return (CAGR)',
                  suffix: '%',
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value?.isEmpty ?? true) {
                      return 'Please enter expected return rate';
                    }
                    final rate = double.tryParse(value!);
                    if (rate == null || rate <= 0) {
                      return 'Please enter a valid return rate';
                    }
                    if (rate > 50) {
                      return 'Return rate seems too high. Please check.';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 24),
                SizedBox(
                  width: double.infinity,
                  height: 50,
                  child: ElevatedButton(
                    onPressed: _calculateSIP,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: theme.colorScheme.primary,
                      foregroundColor: theme.colorScheme.onPrimary,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: const Text(
                      'Calculate SIP',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ],
            ),
            if (_maturityAmount > 0) ...[
              const SizedBox(height: 24),
              _buildResultCard(),
              const SizedBox(height: 16),
              _buildExportButton(),
              if (_investmentChartData.isNotEmpty) ...[
                const SizedBox(height: 24),
                _buildGrowthChart(),
                const SizedBox(height: 24),
                _buildYearWiseBreakdown(),
              ],
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildCalculatorCard({
    required String title,
    required List<Widget> children,
  }) {
    final theme = Theme.of(context);

    return Card(
      elevation: 4,
      shadowColor: theme.shadowColor.withValues(alpha: 0.2),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.primary,
              ),
            ),
            const SizedBox(height: 20),
            ...children,
          ],
        ),
      ),
    );
  }

  Widget _buildInputField({
    required TextEditingController controller,
    required String label,
    String? prefix,
    String? suffix,
    TextInputType? keyboardType,
    String? Function(String?)? validator,
  }) {
    final theme = Theme.of(context);

    return TextFormField(
      controller: controller,
      keyboardType: keyboardType,
      validator: validator,
      decoration: InputDecoration(
        labelText: label,
        prefixText: prefix,
        suffixText: suffix,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: theme.colorScheme.outline),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: theme.colorScheme.outline.withValues(alpha: 0.5),
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: theme.colorScheme.primary, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: theme.colorScheme.error),
        ),
        filled: true,
        fillColor: theme.colorScheme.surface,
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 16,
        ),
      ),
    );
  }

  Widget _buildResultCard() {
    final theme = Theme.of(context);

    return Card(
      elevation: 6,
      shadowColor: AppColorPalette.success.withValues(alpha: 0.2),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(
          color: AppColorPalette.success.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            colors: [
              AppColorPalette.success.withValues(alpha: 0.05),
              AppColorPalette.success.withValues(alpha: 0.02),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.analytics_outlined,
                  color: AppColorPalette.success,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'SIP Results',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColorPalette.success,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            _buildResultItem(
              'Total Invested',
              _totalInvested,
              Icons.upload_outlined,
              theme.colorScheme.primary,
            ),
            const SizedBox(height: 12),
            _buildResultItem(
              'Maturity Amount',
              _maturityAmount,
              Icons.account_balance_wallet_outlined,
              AppColorPalette.success,
            ),
            const SizedBox(height: 12),
            _buildResultItem(
              'Total Returns',
              _totalReturns,
              Icons.trending_up,
              AppColorPalette.success,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildResultItem(
    String label,
    double amount,
    IconData icon,
    Color color,
  ) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface.withValues(alpha: 0.8),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.2)),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: color, size: 20),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  NumberFormat('#,##,###').format(amount),
                  style: theme.textTheme.titleMedium?.copyWith(
                    color: color,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildExportButton() {
    final theme = Theme.of(context);

    return SizedBox(
      width: double.infinity,
      height: 50,
      child: ElevatedButton.icon(
        onPressed: _yearWiseData.isEmpty || _isExporting ? null : _exportToPDF,
        icon:
            _isExporting
                ? SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      theme.colorScheme.onPrimary,
                    ),
                  ),
                )
                : const Icon(Icons.picture_as_pdf),
        label: Text(
          _isExporting ? 'Generating PDF...' : 'Export PDF Report',
          style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColorPalette.success,
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      ),
    );
  }

  Widget _buildGrowthChart() {
    final theme = Theme.of(context);

    return Card(
      elevation: 4,
      shadowColor: theme.shadowColor.withValues(alpha: 0.2),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'SIP Growth Chart',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.primary,
              ),
            ),
            const SizedBox(height: 20),
            SizedBox(
              height: 250,
              child: LineChart(
                LineChartData(
                  gridData: FlGridData(
                    show: true,
                    drawVerticalLine: true,
                    drawHorizontalLine: true,
                    horizontalInterval: _maturityAmount / 5,
                    verticalInterval: 1,
                    getDrawingHorizontalLine: (value) {
                      return FlLine(
                        color: theme.colorScheme.outline.withValues(alpha: 0.2),
                        strokeWidth: 1,
                      );
                    },
                    getDrawingVerticalLine: (value) {
                      return FlLine(
                        color: theme.colorScheme.outline.withValues(alpha: 0.2),
                        strokeWidth: 1,
                      );
                    },
                  ),
                  titlesData: FlTitlesData(
                    show: true,
                    rightTitles: const AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                    topTitles: const AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                    bottomTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        reservedSize: 30,
                        interval: 1,
                        getTitlesWidget: (value, meta) {
                          return SideTitleWidget(
                            axisSide: meta.axisSide,
                            child: Text(
                              value.toInt().toString(),
                              style: theme.textTheme.bodySmall,
                            ),
                          );
                        },
                      ),
                    ),
                    leftTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        interval: _maturityAmount / 5,
                        reservedSize: 60,
                        getTitlesWidget: (value, meta) {
                          return SideTitleWidget(
                            axisSide: meta.axisSide,
                            child: Text(
                              NumberFormat.compact().format(value),
                              style: theme.textTheme.bodySmall,
                            ),
                          );
                        },
                      ),
                    ),
                  ),
                  borderData: FlBorderData(
                    show: true,
                    border: Border.all(
                      color: theme.colorScheme.outline.withValues(alpha: 0.3),
                    ),
                  ),
                  minX: 1,
                  maxX: _yearWiseData.length.toDouble(),
                  minY: 0,
                  maxY: _maturityAmount * 1.1,
                  lineBarsData: [
                    // Investment line
                    LineChartBarData(
                      spots: _investmentChartData,
                      isCurved: true,
                      color: theme.colorScheme.primary,
                      barWidth: 3,
                      isStrokeCapRound: true,
                      dotData: FlDotData(
                        show: true,
                        getDotPainter: (spot, percent, barData, index) {
                          return FlDotCirclePainter(
                            radius: 4,
                            color: theme.colorScheme.primary,
                            strokeWidth: 2,
                            strokeColor: Colors.white,
                          );
                        },
                      ),
                    ),
                    // Returns line
                    LineChartBarData(
                      spots: _returnsChartData,
                      isCurved: true,
                      color: AppColorPalette.success,
                      barWidth: 3,
                      isStrokeCapRound: true,
                      dotData: FlDotData(
                        show: true,
                        getDotPainter: (spot, percent, barData, index) {
                          return FlDotCirclePainter(
                            radius: 4,
                            color: AppColorPalette.success,
                            strokeWidth: 2,
                            strokeColor: Colors.white,
                          );
                        },
                      ),
                      belowBarData: BarAreaData(
                        show: true,
                        gradient: LinearGradient(
                          colors: [
                            AppColorPalette.success.withValues(alpha: 0.2),
                            AppColorPalette.success.withValues(alpha: 0.05),
                          ],
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                        ),
                      ),
                    ),
                  ],
                  lineTouchData: LineTouchData(
                    enabled: true,
                    touchTooltipData: LineTouchTooltipData(
                      tooltipBgColor: theme.colorScheme.primary,
                      getTooltipItems: (touchedSpots) {
                        return touchedSpots.map((spot) {
                          return LineTooltipItem(
                            'Year ' +
                                spot.x.toInt().toString() +
                                '\n₹' +
                                NumberFormat('#,##,###').format(spot.y),
                            const TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          );
                        }).toList();
                      },
                    ),
                  ),
                ),
              ),
            ),
            const SizedBox(height: 16),
            // Legend
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                _buildLegendItem(
                  'Investment',
                  theme.colorScheme.primary,
                  theme,
                ),
                const SizedBox(width: 24),
                _buildLegendItem('Returns', AppColorPalette.success, theme),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLegendItem(String label, Color color, ThemeData theme) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 16,
          height: 3,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(2),
          ),
        ),
        const SizedBox(width: 8),
        Text(
          label,
          style: theme.textTheme.bodySmall?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Widget _buildYearWiseBreakdown() {
    final theme = Theme.of(context);

    return Card(
      elevation: 4,
      shadowColor: theme.shadowColor.withValues(alpha: 0.2),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Year-wise Breakdown',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.primary,
              ),
            ),
            const SizedBox(height: 20),
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: DataTable(
                columns: [
                  DataColumn(
                    label: Text(
                      'Year',
                      style: theme.textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  DataColumn(
                    label: Text(
                      'Invested',
                      style: theme.textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  DataColumn(
                    label: Text(
                      'Portfolio Value',
                      style: theme.textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  DataColumn(
                    label: Text(
                      'Returns',
                      style: theme.textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
                rows:
                    _yearWiseData.map((data) {
                      return DataRow(
                        cells: [
                          DataCell(
                            Text(
                              data['year'].toString(),
                              style: theme.textTheme.bodyMedium,
                            ),
                          ),
                          DataCell(
                            Text(
                              NumberFormat('#,##,###').format(data['invested']),
                              style: theme.textTheme.bodyMedium?.copyWith(
                                color: theme.colorScheme.primary,
                              ),
                            ),
                          ),
                          DataCell(
                            Text(
                              NumberFormat('#,##,###').format(data['value']),
                              style: theme.textTheme.bodyMedium?.copyWith(
                                color: AppColorPalette.success,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                          DataCell(
                            Text(
                              NumberFormat('#,##,###').format(data['returns']),
                              style: theme.textTheme.bodyMedium?.copyWith(
                                color:
                                    data['returns'] >= 0
                                        ? AppColorPalette.success
                                        : AppColorPalette.error,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ],
                      );
                    }).toList(),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Lumpsum Calculator Tab
class LumpsumCalculatorTab extends StatefulWidget {
  const LumpsumCalculatorTab({super.key});

  @override
  State<LumpsumCalculatorTab> createState() => _LumpsumCalculatorTabState();
}

class _LumpsumCalculatorTabState extends State<LumpsumCalculatorTab> {
  final _formKey = GlobalKey<FormState>();
  final _initialAmountController = TextEditingController();
  final _periodController = TextEditingController();
  final _returnRateController = TextEditingController();

  double _initialInvestment = 0.0;
  double _maturityAmount = 0.0;
  double _totalReturns = 0.0;
  List<Map<String, dynamic>> _yearWiseData = [];
  List<FlSpot> _investmentChartData = [];
  List<FlSpot> _returnsChartData = [];
  bool _isExporting = false;

  @override
  void dispose() {
    _initialAmountController.dispose();
    _periodController.dispose();
    _returnRateController.dispose();
    super.dispose();
  }

  Future<void> _exportToPDF() async {
    if (_yearWiseData.isEmpty) return;

    setState(() {
      _isExporting = true;
    });

    try {
      final inputs = {
        'Initial Investment Amount': NumberFormat(
          '#,##,###',
        ).format(double.tryParse(_initialAmountController.text) ?? 0),
        'Investment Duration': _periodController.text + ' Years',
        'Expected Annual Return (CAGR)': _returnRateController.text + '%',
      };

      final results = {
        'Initial Investment': _initialInvestment,
        'Maturity Amount': _maturityAmount,
        'Total Returns': _totalReturns,
      };

      final pdf = await InvestmentCalculatorPDFHelper.generatePDF(
        calculatorType: 'LUMPSUM',
        inputs: inputs,
        results: results,
        yearWiseData: _yearWiseData,
      );

      await _previewPDF(
        pdf,
        'Lumpsum_Calculator_Report_' +
            DateFormat('yyyy_MM_dd').format(DateTime.now()) +
            '.pdf',
      );
    } catch (e) {
      _showSnackBar('Failed to generate PDF: $e');
    } finally {
      setState(() {
        _isExporting = false;
      });
    }
  }

  Future<void> _previewPDF(pw.Document pdf, String filename) async {
    final pdfBytes = await pdf.save();

    Navigator.of(context).push(
      MaterialPageRoute(
        builder:
            (context) => _InvestmentPDFViewerScreen(
              pdfBytes: pdfBytes,
              filename: filename,
            ),
      ),
    );
  }

  void _showSnackBar(String message) {
    final theme = Theme.of(context);
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: theme.colorScheme.primary,
      ),
    );
  }

  void _calculateLumpsum() {
    if (_formKey.currentState?.validate() ?? false) {
      final initialAmount = double.tryParse(_initialAmountController.text) ?? 0;
      final periodInYears = double.tryParse(_periodController.text) ?? 0;
      final annualReturnRate = double.tryParse(_returnRateController.text) ?? 0;

      if (initialAmount > 0 && periodInYears > 0 && annualReturnRate > 0) {
        // Generate year-wise data and chart data
        final yearWiseData = <Map<String, dynamic>>[];
        final investmentChartData = <FlSpot>[];
        final returnsChartData = <FlSpot>[];

        for (int year = 1; year <= periodInYears; year++) {
          // Calculate value at end of each year using compound interest
          final valueAtYear =
              initialAmount *
              (1 + (annualReturnRate / 100)).pow(year.toDouble());
          final returnsToDate = valueAtYear - initialAmount;

          yearWiseData.add({
            'year': year,
            'invested': initialAmount,
            'value': valueAtYear,
            'returns': returnsToDate,
          });

          investmentChartData.add(FlSpot(year.toDouble(), initialAmount));
          returnsChartData.add(FlSpot(year.toDouble(), returnsToDate));
        }

        // Compound Interest Formula: A = P(1 + r)^t
        // Uses CAGR (Compound Annual Growth Rate) for yearly compounding
        final maturityAmount =
            initialAmount * (1 + (annualReturnRate / 100)).pow(periodInYears);

        setState(() {
          _initialInvestment = initialAmount;
          _maturityAmount = maturityAmount;
          _totalReturns = _maturityAmount - _initialInvestment;
          _yearWiseData = yearWiseData;
          _investmentChartData = investmentChartData;
          _returnsChartData = returnsChartData;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildCalculatorCard(
              title: 'Lumpsum Investment Details',
              children: [
                _buildInputField(
                  controller: _initialAmountController,
                  label: 'Initial Investment Amount',
                  prefix: '',
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value?.isEmpty ?? true) {
                      return 'Please enter initial amount';
                    }
                    final amount = double.tryParse(value!);
                    if (amount == null || amount <= 0) {
                      return 'Please enter a valid amount';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                _buildInputField(
                  controller: _periodController,
                  label: 'Investment Duration',
                  suffix: 'Years',
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value?.isEmpty ?? true) {
                      return 'Please enter investment duration';
                    }
                    final period = double.tryParse(value!);
                    if (period == null || period <= 0) {
                      return 'Please enter a valid duration';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                _buildInputField(
                  controller: _returnRateController,
                  label: 'Expected Annual Return (CAGR)',
                  suffix: '%',
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value?.isEmpty ?? true) {
                      return 'Please enter expected return rate';
                    }
                    final rate = double.tryParse(value!);
                    if (rate == null || rate <= 0) {
                      return 'Please enter a valid return rate';
                    }
                    if (rate > 50) {
                      return 'Return rate seems too high. Please check.';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 24),
                SizedBox(
                  width: double.infinity,
                  height: 50,
                  child: ElevatedButton(
                    onPressed: _calculateLumpsum,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: theme.colorScheme.primary,
                      foregroundColor: theme.colorScheme.onPrimary,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: const Text(
                      'Calculate Lumpsum',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ],
            ),
            if (_maturityAmount > 0) ...[
              const SizedBox(height: 24),
              _buildLumpsumResultCard(),
              const SizedBox(height: 16),
              _buildLumpsumExportButton(),
              if (_investmentChartData.isNotEmpty) ...[
                const SizedBox(height: 24),
                _buildLumpsumGrowthChart(),
                const SizedBox(height: 24),
                _buildLumpsumYearWiseBreakdown(),
              ],
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildCalculatorCard({
    required String title,
    required List<Widget> children,
  }) {
    final theme = Theme.of(context);

    return Card(
      elevation: 4,
      shadowColor: theme.shadowColor.withValues(alpha: 0.2),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.primary,
              ),
            ),
            const SizedBox(height: 20),
            ...children,
          ],
        ),
      ),
    );
  }

  Widget _buildInputField({
    required TextEditingController controller,
    required String label,
    String? prefix,
    String? suffix,
    TextInputType? keyboardType,
    String? Function(String?)? validator,
  }) {
    final theme = Theme.of(context);

    return TextFormField(
      controller: controller,
      keyboardType: keyboardType,
      validator: validator,
      decoration: InputDecoration(
        labelText: label,
        prefixText: prefix,
        suffixText: suffix,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: theme.colorScheme.outline),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: theme.colorScheme.outline.withValues(alpha: 0.5),
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: theme.colorScheme.primary, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: theme.colorScheme.error),
        ),
        filled: true,
        fillColor: theme.colorScheme.surface,
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 16,
        ),
      ),
    );
  }

  Widget _buildLumpsumResultCard() {
    final theme = Theme.of(context);

    return Card(
      elevation: 6,
      shadowColor: AppColorPalette.info.withValues(alpha: 0.2),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(
          color: AppColorPalette.info.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            colors: [
              AppColorPalette.info.withValues(alpha: 0.05),
              AppColorPalette.info.withValues(alpha: 0.02),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.pie_chart_outline,
                  color: AppColorPalette.info,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'Lumpsum Results',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColorPalette.info,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            _buildResultItem(
              'Initial Investment',
              _initialInvestment,
              Icons.monetization_on_outlined,
              theme.colorScheme.primary,
            ),
            const SizedBox(height: 12),
            _buildResultItem(
              'Maturity Amount',
              _maturityAmount,
              Icons.account_balance_wallet_outlined,
              AppColorPalette.info,
            ),
            const SizedBox(height: 12),
            _buildResultItem(
              'Total Returns',
              _totalReturns,
              Icons.trending_up,
              AppColorPalette.success,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildResultItem(
    String label,
    double amount,
    IconData icon,
    Color color,
  ) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface.withValues(alpha: 0.8),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.2)),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: color, size: 20),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  NumberFormat('#,##,###').format(amount),
                  style: theme.textTheme.titleMedium?.copyWith(
                    color: color,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLumpsumExportButton() {
    final theme = Theme.of(context);

    return SizedBox(
      width: double.infinity,
      height: 50,
      child: ElevatedButton.icon(
        onPressed: _yearWiseData.isEmpty || _isExporting ? null : _exportToPDF,
        icon:
            _isExporting
                ? SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      theme.colorScheme.onPrimary,
                    ),
                  ),
                )
                : const Icon(Icons.picture_as_pdf),
        label: Text(
          _isExporting ? 'Generating PDF...' : 'Export PDF Report',
          style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColorPalette.info,
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      ),
    );
  }

  Widget _buildLumpsumGrowthChart() {
    final theme = Theme.of(context);

    return Card(
      elevation: 4,
      shadowColor: theme.shadowColor.withValues(alpha: 0.2),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Lumpsum Growth Chart',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.primary,
              ),
            ),
            const SizedBox(height: 20),
            SizedBox(
              height: 250,
              child: LineChart(
                LineChartData(
                  gridData: FlGridData(
                    show: true,
                    drawVerticalLine: true,
                    drawHorizontalLine: true,
                    horizontalInterval: _maturityAmount / 5,
                    verticalInterval: 1,
                    getDrawingHorizontalLine: (value) {
                      return FlLine(
                        color: theme.colorScheme.outline.withValues(alpha: 0.2),
                        strokeWidth: 1,
                      );
                    },
                    getDrawingVerticalLine: (value) {
                      return FlLine(
                        color: theme.colorScheme.outline.withValues(alpha: 0.2),
                        strokeWidth: 1,
                      );
                    },
                  ),
                  titlesData: FlTitlesData(
                    show: true,
                    rightTitles: const AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                    topTitles: const AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                    bottomTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        reservedSize: 30,
                        interval: 1,
                        getTitlesWidget: (value, meta) {
                          return SideTitleWidget(
                            axisSide: meta.axisSide,
                            child: Text(
                              value.toInt().toString(),
                              style: theme.textTheme.bodySmall,
                            ),
                          );
                        },
                      ),
                    ),
                    leftTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        interval: _maturityAmount / 5,
                        reservedSize: 60,
                        getTitlesWidget: (value, meta) {
                          return SideTitleWidget(
                            axisSide: meta.axisSide,
                            child: Text(
                              NumberFormat.compact().format(value),
                              style: theme.textTheme.bodySmall,
                            ),
                          );
                        },
                      ),
                    ),
                  ),
                  borderData: FlBorderData(
                    show: true,
                    border: Border.all(
                      color: theme.colorScheme.outline.withValues(alpha: 0.3),
                    ),
                  ),
                  minX: 1,
                  maxX: _yearWiseData.length.toDouble(),
                  minY: 0,
                  maxY: _maturityAmount * 1.1,
                  lineBarsData: [
                    // Investment line
                    LineChartBarData(
                      spots: _investmentChartData,
                      isCurved: true,
                      color: theme.colorScheme.primary,
                      barWidth: 3,
                      isStrokeCapRound: true,
                      dotData: FlDotData(
                        show: true,
                        getDotPainter: (spot, percent, barData, index) {
                          return FlDotCirclePainter(
                            radius: 4,
                            color: theme.colorScheme.primary,
                            strokeWidth: 2,
                            strokeColor: Colors.white,
                          );
                        },
                      ),
                    ),
                    // Returns line
                    LineChartBarData(
                      spots: _returnsChartData,
                      isCurved: true,
                      color: AppColorPalette.info,
                      barWidth: 3,
                      isStrokeCapRound: true,
                      dotData: FlDotData(
                        show: true,
                        getDotPainter: (spot, percent, barData, index) {
                          return FlDotCirclePainter(
                            radius: 4,
                            color: AppColorPalette.info,
                            strokeWidth: 2,
                            strokeColor: Colors.white,
                          );
                        },
                      ),
                      belowBarData: BarAreaData(
                        show: true,
                        gradient: LinearGradient(
                          colors: [
                            AppColorPalette.info.withValues(alpha: 0.2),
                            AppColorPalette.info.withValues(alpha: 0.05),
                          ],
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                        ),
                      ),
                    ),
                  ],
                  lineTouchData: LineTouchData(
                    enabled: true,
                    touchTooltipData: LineTouchTooltipData(
                      tooltipBgColor: theme.colorScheme.primary,
                      getTooltipItems: (touchedSpots) {
                        return touchedSpots.map((spot) {
                          return LineTooltipItem(
                            'Year ' +
                                spot.x.toInt().toString() +
                                '\n₹' +
                                NumberFormat('#,##,###').format(spot.y),
                            const TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          );
                        }).toList();
                      },
                    ),
                  ),
                ),
              ),
            ),
            const SizedBox(height: 16),
            // Legend
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                _buildLumpsumLegendItem(
                  'Investment',
                  theme.colorScheme.primary,
                  theme,
                ),
                const SizedBox(width: 24),
                _buildLumpsumLegendItem('Returns', AppColorPalette.info, theme),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLumpsumLegendItem(String label, Color color, ThemeData theme) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 16,
          height: 3,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(2),
          ),
        ),
        const SizedBox(width: 8),
        Text(
          label,
          style: theme.textTheme.bodySmall?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Widget _buildLumpsumYearWiseBreakdown() {
    final theme = Theme.of(context);

    return Card(
      elevation: 4,
      shadowColor: theme.shadowColor.withValues(alpha: 0.2),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Year-wise Breakdown',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.primary,
              ),
            ),
            const SizedBox(height: 20),
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: DataTable(
                columns: [
                  DataColumn(
                    label: Text(
                      'Year',
                      style: theme.textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  DataColumn(
                    label: Text(
                      'Invested',
                      style: theme.textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  DataColumn(
                    label: Text(
                      'Portfolio Value',
                      style: theme.textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  DataColumn(
                    label: Text(
                      'Returns',
                      style: theme.textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
                rows:
                    _yearWiseData.map((data) {
                      return DataRow(
                        cells: [
                          DataCell(
                            Text(
                              data['year'].toString(),
                              style: theme.textTheme.bodyMedium,
                            ),
                          ),
                          DataCell(
                            Text(
                              NumberFormat('#,##,###').format(data['invested']),
                              style: theme.textTheme.bodyMedium?.copyWith(
                                color: theme.colorScheme.primary,
                              ),
                            ),
                          ),
                          DataCell(
                            Text(
                              NumberFormat('#,##,###').format(data['value']),
                              style: theme.textTheme.bodyMedium?.copyWith(
                                color: AppColorPalette.info,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                          DataCell(
                            Text(
                              NumberFormat('#,##,###').format(data['returns']),
                              style: theme.textTheme.bodyMedium?.copyWith(
                                color:
                                    data['returns'] >= 0
                                        ? AppColorPalette.success
                                        : AppColorPalette.error,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ],
                      );
                    }).toList(),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Hybrid Calculator Tab - SIP → Stop → Lumpsum Continues
class HybridCalculatorTab extends StatefulWidget {
  const HybridCalculatorTab({super.key});

  @override
  State<HybridCalculatorTab> createState() => _HybridCalculatorTabState();
}

class _HybridCalculatorTabState extends State<HybridCalculatorTab> {
  final _formKey = GlobalKey<FormState>();
  final _sipAmountController = TextEditingController();
  final _sipPeriodController = TextEditingController();
  final _totalPeriodController = TextEditingController();
  final _returnRateController = TextEditingController();

  double _totalSipInvested = 0.0;
  double _valueAtSipEnd = 0.0;
  double _finalMaturityAmount = 0.0;
  double _totalReturns = 0.0;
  List<Map<String, dynamic>> _yearWiseData = [];
  List<FlSpot> _chartData = [];

  @override
  void dispose() {
    _sipAmountController.dispose();
    _sipPeriodController.dispose();
    _totalPeriodController.dispose();
    _returnRateController.dispose();
    super.dispose();
  }

  void _calculateHybrid() {
    if (_formKey.currentState?.validate() ?? false) {
      final sipAmount = double.tryParse(_sipAmountController.text) ?? 0;
      final sipPeriodInYears = double.tryParse(_sipPeriodController.text) ?? 0;
      final totalPeriodInYears =
          double.tryParse(_totalPeriodController.text) ?? 0;
      final annualReturnRate = double.tryParse(_returnRateController.text) ?? 0;

      if (sipAmount > 0 &&
          sipPeriodInYears > 0 &&
          totalPeriodInYears > sipPeriodInYears &&
          annualReturnRate > 0) {
        final monthlyReturnRate = annualReturnRate / 100 / 12;
        final sipTotalMonths = sipPeriodInYears * 12;
        final postSipYears = totalPeriodInYears - sipPeriodInYears;

        // Step 1: Calculate SIP maturity value using CAGR
        final sipMaturityAmount =
            sipAmount *
            (((1 + monthlyReturnRate).pow(sipTotalMonths) - 1) /
                monthlyReturnRate) *
            (1 + monthlyReturnRate);

        // Step 2: Calculate final value after lumpsum growth using CAGR
        final finalAmount =
            sipMaturityAmount *
            (1 + (annualReturnRate / 100)).pow(postSipYears);

        setState(() {
          _totalSipInvested = sipAmount * sipTotalMonths;
          _valueAtSipEnd = sipMaturityAmount;
          _finalMaturityAmount = finalAmount;
          _totalReturns = _finalMaturityAmount - _totalSipInvested;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildInfoCard(),
            const SizedBox(height: 16),
            _buildCalculatorCard(
              title: 'SIP + Lumpsum Details',
              children: [
                _buildInputField(
                  controller: _sipAmountController,
                  label: 'Monthly SIP Amount',
                  prefix: '',
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value?.isEmpty ?? true) {
                      return 'Please enter SIP amount';
                    }
                    final amount = double.tryParse(value!);
                    if (amount == null || amount <= 0) {
                      return 'Please enter a valid amount';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                _buildInputField(
                  controller: _sipPeriodController,
                  label: 'SIP Period',
                  suffix: 'Years',
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value?.isEmpty ?? true) {
                      return 'Please enter SIP period';
                    }
                    final period = double.tryParse(value!);
                    if (period == null || period <= 0) {
                      return 'Please enter a valid period';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                _buildInputField(
                  controller: _totalPeriodController,
                  label: 'Total Investment Period',
                  suffix: 'Years',
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value?.isEmpty ?? true) {
                      return 'Please enter total period';
                    }
                    final totalPeriod = double.tryParse(value!);
                    final sipPeriod =
                        double.tryParse(_sipPeriodController.text) ?? 0;
                    if (totalPeriod == null || totalPeriod <= sipPeriod) {
                      return 'Total period must be greater than SIP period';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                _buildInputField(
                  controller: _returnRateController,
                  label: 'Expected Annual Return (CAGR)',
                  suffix: '%',
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value?.isEmpty ?? true) {
                      return 'Please enter expected return rate';
                    }
                    final rate = double.tryParse(value!);
                    if (rate == null || rate <= 0) {
                      return 'Please enter a valid return rate';
                    }
                    if (rate > 50) {
                      return 'Return rate seems too high. Please check.';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 24),
                SizedBox(
                  width: double.infinity,
                  height: 50,
                  child: ElevatedButton(
                    onPressed: _calculateHybrid,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: theme.colorScheme.primary,
                      foregroundColor: theme.colorScheme.onPrimary,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: const Text(
                      'Calculate SIP + Lumpsum',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ],
            ),
            if (_finalMaturityAmount > 0) ...[
              const SizedBox(height: 24),
              _buildHybridResultCard(),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildInfoCard() {
    final theme = Theme.of(context);

    return Card(
      elevation: 2,
      shadowColor: AppColorPalette.info.withValues(alpha: 0.1),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: AppColorPalette.info.withValues(alpha: 0.2)),
      ),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            colors: [
              AppColorPalette.info.withValues(alpha: 0.03),
              AppColorPalette.info.withValues(alpha: 0.01),
            ],
          ),
        ),
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Icon(Icons.info_outline, color: AppColorPalette.info, size: 20),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                'This calculator shows returns when you invest via SIP for a certain period, then stop SIP but let the fund continue growing as lumpsum.',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.8),
                  height: 1.4,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCalculatorCard({
    required String title,
    required List<Widget> children,
  }) {
    final theme = Theme.of(context);

    return Card(
      elevation: 4,
      shadowColor: theme.shadowColor.withValues(alpha: 0.2),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.primary,
              ),
            ),
            const SizedBox(height: 20),
            ...children,
          ],
        ),
      ),
    );
  }

  Widget _buildInputField({
    required TextEditingController controller,
    required String label,
    String? prefix,
    String? suffix,
    TextInputType? keyboardType,
    String? Function(String?)? validator,
  }) {
    final theme = Theme.of(context);

    return TextFormField(
      controller: controller,
      keyboardType: keyboardType,
      validator: validator,
      decoration: InputDecoration(
        labelText: label,
        prefixText: prefix,
        suffixText: suffix,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: theme.colorScheme.outline),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: theme.colorScheme.outline.withValues(alpha: 0.5),
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: theme.colorScheme.primary, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: theme.colorScheme.error),
        ),
        filled: true,
        fillColor: theme.colorScheme.surface,
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 16,
        ),
      ),
    );
  }

  Widget _buildHybridResultCard() {
    final theme = Theme.of(context);

    return Card(
      elevation: 6,
      shadowColor: AppColorPalette.tertiary.withValues(alpha: 0.2),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(
          color: AppColorPalette.tertiary.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            colors: [
              AppColorPalette.tertiary.withValues(alpha: 0.05),
              AppColorPalette.tertiary.withValues(alpha: 0.02),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.auto_graph,
                  color: AppColorPalette.tertiary,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'Hybrid Investment Results',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColorPalette.tertiary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            _buildResultItem(
              'Total SIP Invested',
              _totalSipInvested,
              Icons.repeat,
              theme.colorScheme.primary,
            ),
            const SizedBox(height: 12),
            _buildResultItem(
              'Value at SIP End',
              _valueAtSipEnd,
              Icons.pause_circle_outline,
              AppColorPalette.warning,
            ),
            const SizedBox(height: 12),
            _buildResultItem(
              'Final Maturity Amount',
              _finalMaturityAmount,
              Icons.account_balance_wallet_outlined,
              AppColorPalette.tertiary,
            ),
            const SizedBox(height: 12),
            _buildResultItem(
              'Total Returns',
              _totalReturns,
              Icons.trending_up,
              AppColorPalette.success,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildResultItem(
    String label,
    double amount,
    IconData icon,
    Color color,
  ) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface.withValues(alpha: 0.8),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.2)),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: color, size: 20),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  NumberFormat('#,##,###').format(amount),
                  style: theme.textTheme.titleMedium?.copyWith(
                    color: color,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

/// Lumpsum + SIP Calculator Tab - First Lumpsum Investment, then SIP
class LumpsumSipCalculatorTab extends StatefulWidget {
  const LumpsumSipCalculatorTab({super.key});

  @override
  State<LumpsumSipCalculatorTab> createState() =>
      _LumpsumSipCalculatorTabState();
}

class _LumpsumSipCalculatorTabState extends State<LumpsumSipCalculatorTab> {
  final _formKey = GlobalKey<FormState>();
  final _lumpsumAmountController = TextEditingController();
  final _sipAmountController = TextEditingController();
  final _sipStartYearController = TextEditingController();
  final _totalPeriodController = TextEditingController();
  final _returnRateController = TextEditingController();

  double _initialLumpsum = 0.0;
  double _totalSipInvested = 0.0;
  double _finalMaturityAmount = 0.0;
  double _totalReturns = 0.0;
  double _lumpsumGrowthAtSipStart = 0.0;
  List<Map<String, dynamic>> _yearWiseData = [];
  List<FlSpot> _investmentChartData = [];
  List<FlSpot> _returnsChartData = [];
  bool _isExporting = false;

  @override
  void dispose() {
    _lumpsumAmountController.dispose();
    _sipAmountController.dispose();
    _sipStartYearController.dispose();
    _totalPeriodController.dispose();
    _returnRateController.dispose();
    super.dispose();
  }

  Future<void> _exportToPDF() async {
    if (_yearWiseData.isEmpty) return;

    setState(() {
      _isExporting = true;
    });

    try {
      final inputs = {
        'Initial Lumpsum Amount': NumberFormat(
          '#,##,###',
        ).format(double.tryParse(_lumpsumAmountController.text) ?? 0),
        'Monthly SIP Amount': NumberFormat(
          '#,##,###',
        ).format(double.tryParse(_sipAmountController.text) ?? 0),
        'SIP Start Year': _sipStartYearController.text + ' Years',
        'Total Investment Period': _totalPeriodController.text + ' Years',
        'Expected Annual Return (CAGR)': _returnRateController.text + '%',
      };

      final results = {
        'Initial Lumpsum': _initialLumpsum,
        'Total SIP Invested': _totalSipInvested,
        'Lumpsum Value at SIP Start': _lumpsumGrowthAtSipStart,
        'Final Maturity Amount': _finalMaturityAmount,
        'Total Returns': _totalReturns,
      };

      final pdf = await InvestmentCalculatorPDFHelper.generatePDF(
        calculatorType: 'LUMPSUM + SIP',
        inputs: inputs,
        results: results,
        yearWiseData: _yearWiseData,
      );

      await _previewPDF(
        pdf,
        'LumpsumSip_Calculator_Report_' +
            DateFormat('yyyy_MM_dd').format(DateTime.now()) +
            '.pdf',
      );
    } catch (e) {
      _showSnackBar('Failed to generate PDF: $e');
    } finally {
      setState(() {
        _isExporting = false;
      });
    }
  }

  Future<void> _previewPDF(pw.Document pdf, String filename) async {
    final pdfBytes = await pdf.save();

    Navigator.of(context).push(
      MaterialPageRoute(
        builder:
            (context) => _InvestmentPDFViewerScreen(
              pdfBytes: pdfBytes,
              filename: filename,
            ),
      ),
    );
  }

  void _showSnackBar(String message) {
    final theme = Theme.of(context);
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: theme.colorScheme.primary,
      ),
    );
  }

  void _calculateLumpsumSip() {
    if (_formKey.currentState?.validate() ?? false) {
      final lumpsumAmount = double.tryParse(_lumpsumAmountController.text) ?? 0;
      final sipAmount = double.tryParse(_sipAmountController.text) ?? 0;
      final sipStartYear = double.tryParse(_sipStartYearController.text) ?? 0;
      final totalPeriodInYears =
          double.tryParse(_totalPeriodController.text) ?? 0;
      final annualReturnRate = double.tryParse(_returnRateController.text) ?? 0;

      if (lumpsumAmount > 0 &&
          sipAmount > 0 &&
          sipStartYear > 0 &&
          totalPeriodInYears > sipStartYear &&
          annualReturnRate > 0) {
        final monthlyReturnRate = annualReturnRate / 100 / 12;
        final yearlyReturnRate = annualReturnRate / 100;

        // Generate year-wise data and chart data
        final yearWiseData = <Map<String, dynamic>>[];
        final investmentChartData = <FlSpot>[];
        final returnsChartData = <FlSpot>[];

        double totalInvested = lumpsumAmount;

        for (int year = 1; year <= totalPeriodInYears; year++) {
          double portfolioValue;
          double currentInvested = totalInvested;

          if (year <= sipStartYear) {
            // Phase 1: Only lumpsum growth
            portfolioValue =
                lumpsumAmount * (1 + yearlyReturnRate).pow(year.toDouble());
          } else {
            // Phase 2: Lumpsum growth + SIP
            final lumpsumGrowthYears = year.toDouble();
            final sipYears = year - sipStartYear;
            final sipMonths = sipYears * 12;

            // Lumpsum growth for full period
            final lumpsumValue =
                lumpsumAmount * (1 + yearlyReturnRate).pow(lumpsumGrowthYears);

            // SIP growth from start year
            final sipValue =
                sipAmount *
                (((1 + monthlyReturnRate).pow(sipMonths.toDouble()) - 1) /
                    monthlyReturnRate) *
                (1 + monthlyReturnRate);

            portfolioValue = lumpsumValue + sipValue;
            currentInvested = lumpsumAmount + (sipAmount * sipMonths);
            totalInvested = currentInvested;
          }

          final returns = portfolioValue - currentInvested;

          yearWiseData.add({
            'year': year,
            'invested': currentInvested,
            'value': portfolioValue,
            'returns': returns,
            'phase': year <= sipStartYear ? 'Lumpsum Only' : 'Lumpsum + SIP',
          });

          investmentChartData.add(FlSpot(year.toDouble(), currentInvested));
          returnsChartData.add(FlSpot(year.toDouble(), returns));
        }

        // Calculate final values
        final lumpsumGrowthAtStart =
            lumpsumAmount * (1 + yearlyReturnRate).pow(sipStartYear);
        final sipPeriodYears = totalPeriodInYears - sipStartYear;
        final sipMonths = sipPeriodYears * 12;

        final finalLumpsumValue =
            lumpsumAmount * (1 + yearlyReturnRate).pow(totalPeriodInYears);
        final finalSipValue =
            sipAmount *
            (((1 + monthlyReturnRate).pow(sipMonths) - 1) / monthlyReturnRate) *
            (1 + monthlyReturnRate);

        setState(() {
          _initialLumpsum = lumpsumAmount;
          _totalSipInvested = sipAmount * sipMonths;
          _finalMaturityAmount = finalLumpsumValue + finalSipValue;
          _totalReturns =
              _finalMaturityAmount - (lumpsumAmount + _totalSipInvested);
          _lumpsumGrowthAtSipStart = lumpsumGrowthAtStart;
          _yearWiseData = yearWiseData;
          _investmentChartData = investmentChartData;
          _returnsChartData = returnsChartData;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildInfoCard(),
            const SizedBox(height: 16),
            _buildCalculatorCard(
              title: 'Lumpsum + SIP Details',
              children: [
                _buildInputField(
                  controller: _lumpsumAmountController,
                  label: 'Initial Lumpsum Amount',
                  prefix: '',
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value?.isEmpty ?? true) {
                      return 'Please enter lumpsum amount';
                    }
                    final amount = double.tryParse(value!);
                    if (amount == null || amount <= 0) {
                      return 'Please enter a valid amount';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                _buildInputField(
                  controller: _sipAmountController,
                  label: 'Monthly SIP Amount',
                  prefix: '',
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value?.isEmpty ?? true) {
                      return 'Please enter SIP amount';
                    }
                    final amount = double.tryParse(value!);
                    if (amount == null || amount <= 0) {
                      return 'Please enter a valid amount';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                _buildInputField(
                  controller: _sipStartYearController,
                  label: 'SIP Start Year',
                  suffix: 'Years',
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value?.isEmpty ?? true) {
                      return 'Please enter SIP start year';
                    }
                    final year = double.tryParse(value!);
                    if (year == null || year <= 0) {
                      return 'Please enter a valid year';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                _buildInputField(
                  controller: _totalPeriodController,
                  label: 'Total Investment Period',
                  suffix: 'Years',
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value?.isEmpty ?? true) {
                      return 'Please enter total period';
                    }
                    final totalPeriod = double.tryParse(value!);
                    final sipStartYear =
                        double.tryParse(_sipStartYearController.text) ?? 0;
                    if (totalPeriod == null || totalPeriod <= sipStartYear) {
                      return 'Total period must be greater than SIP start year';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                _buildInputField(
                  controller: _returnRateController,
                  label: 'Expected Annual Return (CAGR)',
                  suffix: '%',
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value?.isEmpty ?? true) {
                      return 'Please enter expected return rate';
                    }
                    final rate = double.tryParse(value!);
                    if (rate == null || rate <= 0) {
                      return 'Please enter a valid return rate';
                    }
                    if (rate > 50) {
                      return 'Return rate seems too high. Please check.';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 24),
                SizedBox(
                  width: double.infinity,
                  height: 50,
                  child: ElevatedButton(
                    onPressed: _calculateLumpsumSip,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: theme.colorScheme.primary,
                      foregroundColor: theme.colorScheme.onPrimary,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: const Text(
                      'Calculate Lumpsum + SIP',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ],
            ),
            if (_finalMaturityAmount > 0) ...[
              const SizedBox(height: 24),
              _buildLumpsumSipResultCard(),
              const SizedBox(height: 16),
              _buildLumpsumSipExportButton(),
              if (_investmentChartData.isNotEmpty) ...[
                const SizedBox(height: 24),
                _buildLumpsumSipGrowthChart(),
                const SizedBox(height: 24),
                _buildLumpsumSipYearWiseBreakdown(),
              ],
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildInfoCard() {
    final theme = Theme.of(context);

    return Card(
      elevation: 2,
      shadowColor: AppColorPalette.warning.withValues(alpha: 0.1),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: AppColorPalette.warning.withValues(alpha: 0.2)),
      ),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            colors: [
              AppColorPalette.warning.withValues(alpha: 0.03),
              AppColorPalette.warning.withValues(alpha: 0.01),
            ],
          ),
        ),
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Icon(Icons.info_outline, color: AppColorPalette.warning, size: 20),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                'This calculator shows returns when you start with a lumpsum investment and then begin SIP after some years.',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.8),
                  height: 1.4,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCalculatorCard({
    required String title,
    required List<Widget> children,
  }) {
    final theme = Theme.of(context);

    return Card(
      elevation: 4,
      shadowColor: theme.shadowColor.withValues(alpha: 0.2),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.primary,
              ),
            ),
            const SizedBox(height: 20),
            ...children,
          ],
        ),
      ),
    );
  }

  Widget _buildInputField({
    required TextEditingController controller,
    required String label,
    String? prefix,
    String? suffix,
    TextInputType? keyboardType,
    String? Function(String?)? validator,
  }) {
    final theme = Theme.of(context);

    return TextFormField(
      controller: controller,
      keyboardType: keyboardType,
      validator: validator,
      decoration: InputDecoration(
        labelText: label,
        prefixText: prefix,
        suffixText: suffix,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: theme.colorScheme.outline),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: theme.colorScheme.outline.withValues(alpha: 0.5),
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: theme.colorScheme.primary, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: theme.colorScheme.error),
        ),
        filled: true,
        fillColor: theme.colorScheme.surface,
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 16,
        ),
      ),
    );
  }

  Widget _buildLumpsumSipResultCard() {
    final theme = Theme.of(context);

    return Card(
      elevation: 6,
      shadowColor: AppColorPalette.warning.withValues(alpha: 0.2),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(
          color: AppColorPalette.warning.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            colors: [
              AppColorPalette.warning.withValues(alpha: 0.05),
              AppColorPalette.warning.withValues(alpha: 0.02),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.account_balance,
                  color: AppColorPalette.warning,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'Lumpsum + SIP Results',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColorPalette.warning,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            _buildResultItem(
              'Initial Lumpsum',
              _initialLumpsum,
              Icons.monetization_on_outlined,
              theme.colorScheme.primary,
            ),
            const SizedBox(height: 12),
            _buildResultItem(
              'Total SIP Invested',
              _totalSipInvested,
              Icons.repeat,
              AppColorPalette.info,
            ),
            const SizedBox(height: 12),
            _buildResultItem(
              'Lumpsum Value at SIP Start',
              _lumpsumGrowthAtSipStart,
              Icons.trending_up,
              AppColorPalette.tertiary,
            ),
            const SizedBox(height: 12),
            _buildResultItem(
              'Final Maturity Amount',
              _finalMaturityAmount,
              Icons.account_balance_wallet_outlined,
              AppColorPalette.warning,
            ),
            const SizedBox(height: 12),
            _buildResultItem(
              'Total Returns',
              _totalReturns,
              Icons.analytics,
              AppColorPalette.success,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildResultItem(
    String label,
    double amount,
    IconData icon,
    Color color,
  ) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface.withValues(alpha: 0.8),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.2)),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: color, size: 20),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  NumberFormat('#,##,###').format(amount),
                  style: theme.textTheme.titleMedium?.copyWith(
                    color: color,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLumpsumSipExportButton() {
    final theme = Theme.of(context);

    return SizedBox(
      width: double.infinity,
      height: 50,
      child: ElevatedButton.icon(
        onPressed: _yearWiseData.isEmpty || _isExporting ? null : _exportToPDF,
        icon:
            _isExporting
                ? SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      theme.colorScheme.onPrimary,
                    ),
                  ),
                )
                : const Icon(Icons.picture_as_pdf),
        label: Text(
          _isExporting ? 'Generating PDF...' : 'Export PDF Report',
          style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColorPalette.warning,
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      ),
    );
  }

  Widget _buildLumpsumSipGrowthChart() {
    final theme = Theme.of(context);

    return Card(
      elevation: 4,
      shadowColor: theme.shadowColor.withValues(alpha: 0.2),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Lumpsum + SIP Growth Chart',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.primary,
              ),
            ),
            const SizedBox(height: 20),
            SizedBox(
              height: 250,
              child: LineChart(
                LineChartData(
                  gridData: FlGridData(
                    show: true,
                    drawVerticalLine: true,
                    drawHorizontalLine: true,
                    horizontalInterval: _finalMaturityAmount / 5,
                    verticalInterval: 1,
                    getDrawingHorizontalLine: (value) {
                      return FlLine(
                        color: theme.colorScheme.outline.withValues(alpha: 0.2),
                        strokeWidth: 1,
                      );
                    },
                    getDrawingVerticalLine: (value) {
                      return FlLine(
                        color: theme.colorScheme.outline.withValues(alpha: 0.2),
                        strokeWidth: 1,
                      );
                    },
                  ),
                  titlesData: FlTitlesData(
                    show: true,
                    rightTitles: const AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                    topTitles: const AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                    bottomTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        reservedSize: 30,
                        interval: 1,
                        getTitlesWidget: (value, meta) {
                          return SideTitleWidget(
                            axisSide: meta.axisSide,
                            child: Text(
                              value.toInt().toString(),
                              style: theme.textTheme.bodySmall,
                            ),
                          );
                        },
                      ),
                    ),
                    leftTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        interval: _finalMaturityAmount / 5,
                        reservedSize: 60,
                        getTitlesWidget: (value, meta) {
                          return SideTitleWidget(
                            axisSide: meta.axisSide,
                            child: Text(
                              NumberFormat.compact().format(value),
                              style: theme.textTheme.bodySmall,
                            ),
                          );
                        },
                      ),
                    ),
                  ),
                  borderData: FlBorderData(
                    show: true,
                    border: Border.all(
                      color: theme.colorScheme.outline.withValues(alpha: 0.3),
                    ),
                  ),
                  minX: 1,
                  maxX: _yearWiseData.length.toDouble(),
                  minY: 0,
                  maxY: _finalMaturityAmount * 1.1,
                  lineBarsData: [
                    // Investment line
                    LineChartBarData(
                      spots: _investmentChartData,
                      isCurved: true,
                      color: theme.colorScheme.primary,
                      barWidth: 3,
                      isStrokeCapRound: true,
                      dotData: FlDotData(
                        show: true,
                        getDotPainter: (spot, percent, barData, index) {
                          return FlDotCirclePainter(
                            radius: 4,
                            color: theme.colorScheme.primary,
                            strokeWidth: 2,
                            strokeColor: Colors.white,
                          );
                        },
                      ),
                    ),
                    // Returns line
                    LineChartBarData(
                      spots: _returnsChartData,
                      isCurved: true,
                      color: AppColorPalette.warning,
                      barWidth: 3,
                      isStrokeCapRound: true,
                      dotData: FlDotData(
                        show: true,
                        getDotPainter: (spot, percent, barData, index) {
                          return FlDotCirclePainter(
                            radius: 4,
                            color: AppColorPalette.warning,
                            strokeWidth: 2,
                            strokeColor: Colors.white,
                          );
                        },
                      ),
                      belowBarData: BarAreaData(
                        show: true,
                        gradient: LinearGradient(
                          colors: [
                            AppColorPalette.warning.withValues(alpha: 0.2),
                            AppColorPalette.warning.withValues(alpha: 0.05),
                          ],
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                        ),
                      ),
                    ),
                  ],
                  lineTouchData: LineTouchData(
                    enabled: true,
                    touchTooltipData: LineTouchTooltipData(
                      tooltipBgColor: theme.colorScheme.primary,
                      getTooltipItems: (touchedSpots) {
                        return touchedSpots.map((spot) {
                          final isInvestment = spot.barIndex == 0;
                          final label = isInvestment ? 'Invested' : 'Returns';
                          return LineTooltipItem(
                            label +
                                ': ' +
                                NumberFormat('#,##,###').format(spot.y),
                            const TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                              fontSize: 12,
                            ),
                          );
                        }).toList();
                      },
                    ),
                  ),
                ),
              ),
            ),
            const SizedBox(height: 16),
            // Legend
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                _buildLumpsumSipLegendItem(
                  'Investment',
                  theme.colorScheme.primary,
                  theme,
                ),
                const SizedBox(width: 24),
                _buildLumpsumSipLegendItem(
                  'Returns',
                  AppColorPalette.warning,
                  theme,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLumpsumSipLegendItem(
    String label,
    Color color,
    ThemeData theme,
  ) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 16,
          height: 3,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(2),
          ),
        ),
        const SizedBox(width: 8),
        Text(
          label,
          style: theme.textTheme.bodySmall?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Widget _buildLumpsumSipYearWiseBreakdown() {
    final theme = Theme.of(context);

    return Card(
      elevation: 4,
      shadowColor: theme.shadowColor.withValues(alpha: 0.2),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Year-wise Breakdown',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.primary,
              ),
            ),
            const SizedBox(height: 20),
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: DataTable(
                columns: [
                  DataColumn(
                    label: Text(
                      'Year',
                      style: theme.textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  DataColumn(
                    label: Text(
                      'Phase',
                      style: theme.textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  DataColumn(
                    label: Text(
                      'Invested',
                      style: theme.textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  DataColumn(
                    label: Text(
                      'Portfolio Value',
                      style: theme.textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  DataColumn(
                    label: Text(
                      'Returns',
                      style: theme.textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
                rows:
                    _yearWiseData.map((data) {
                      return DataRow(
                        cells: [
                          DataCell(
                            Text(
                              data['year'].toString(),
                              style: theme.textTheme.bodyMedium,
                            ),
                          ),
                          DataCell(
                            Text(
                              data['phase'],
                              style: theme.textTheme.bodySmall?.copyWith(
                                color:
                                    data['phase'] == 'Lumpsum Only'
                                        ? AppColorPalette.info
                                        : AppColorPalette.warning,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                          DataCell(
                            Text(
                              NumberFormat('#,##,###').format(data['invested']),
                              style: theme.textTheme.bodyMedium?.copyWith(
                                color: theme.colorScheme.primary,
                              ),
                            ),
                          ),
                          DataCell(
                            Text(
                              NumberFormat('#,##,###').format(data['value']),
                              style: theme.textTheme.bodyMedium?.copyWith(
                                color: AppColorPalette.warning,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                          DataCell(
                            Text(
                              NumberFormat('#,##,###').format(data['returns']),
                              style: theme.textTheme.bodyMedium?.copyWith(
                                color:
                                    data['returns'] >= 0
                                        ? AppColorPalette.success
                                        : AppColorPalette.error,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ],
                      );
                    }).toList(),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// PDF Export Helper Functions
class InvestmentCalculatorPDFHelper {
  static Future<pw.Document> generatePDF({
    required String calculatorType,
    required Map<String, dynamic> inputs,
    required Map<String, dynamic> results,
    required List<Map<String, dynamic>> yearWiseData,
  }) async {
    final pdf = pw.Document();

    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(32),
        build: (pw.Context context) {
          return [
            // Header
            pw.Container(
              padding: const pw.EdgeInsets.all(20),
              decoration: pw.BoxDecoration(
                gradient: const pw.LinearGradient(
                  colors: [PdfColors.blue900, PdfColors.blue700],
                ),
                borderRadius: pw.BorderRadius.circular(12),
              ),
              child: pw.Column(
                children: [
                  pw.Row(
                    mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                    children: [
                      pw.Column(
                        crossAxisAlignment: pw.CrossAxisAlignment.start,
                        children: [
                          pw.Text(
                            'PERSONAL FINANCE',
                            style: pw.TextStyle(
                              fontSize: 14,
                              fontWeight: pw.FontWeight.bold,
                              color: PdfColors.white,
                              letterSpacing: 1.2,
                            ),
                          ),
                          pw.Text(
                            'Investment Management System',
                            style: const pw.TextStyle(
                              fontSize: 10,
                              color: PdfColors.grey300,
                            ),
                          ),
                        ],
                      ),
                      pw.Container(
                        padding: const pw.EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 6,
                        ),
                        decoration: pw.BoxDecoration(
                          color: PdfColors.grey300,
                          borderRadius: pw.BorderRadius.circular(20),
                        ),
                        child: pw.Text(
                          'CALCULATOR REPORT',
                          style: pw.TextStyle(
                            fontSize: 10,
                            fontWeight: pw.FontWeight.bold,
                            color: PdfColors.white,
                            letterSpacing: 1,
                          ),
                        ),
                      ),
                    ],
                  ),
                  pw.SizedBox(height: 16),
                  pw.Container(
                    width: double.infinity,
                    height: 1,
                    color: PdfColors.grey400,
                  ),
                  pw.SizedBox(height: 16),
                  pw.Center(
                    child: pw.Text(
                      '$calculatorType INVESTMENT CALCULATOR',
                      style: pw.TextStyle(
                        fontSize: 22,
                        fontWeight: pw.FontWeight.bold,
                        color: PdfColors.white,
                        letterSpacing: 2,
                      ),
                    ),
                  ),
                  pw.SizedBox(height: 8),
                  pw.Text(
                    'Generated on ' +
                        DateFormat(
                          'MMMM dd, yyyy, hh:mm a',
                        ).format(DateTime.now()),
                    style: const pw.TextStyle(
                      fontSize: 11,
                      color: PdfColors.grey300,
                    ),
                  ),
                ],
              ),
            ),

            pw.SizedBox(height: 24),

            // Input Parameters
            pw.Container(
              padding: const pw.EdgeInsets.all(20),
              decoration: pw.BoxDecoration(
                color: PdfColors.grey50,
                border: pw.Border.all(color: PdfColors.grey300),
                borderRadius: pw.BorderRadius.circular(8),
              ),
              child: pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  pw.Text(
                    'INPUT PARAMETERS',
                    style: pw.TextStyle(
                      fontSize: 12,
                      fontWeight: pw.FontWeight.bold,
                      color: PdfColors.grey800,
                      letterSpacing: 1,
                    ),
                  ),
                  pw.SizedBox(height: 12),
                  pw.Wrap(
                    spacing: 20,
                    runSpacing: 8,
                    children:
                        inputs.entries.map((entry) {
                          return pw.Column(
                            crossAxisAlignment: pw.CrossAxisAlignment.start,
                            children: [
                              pw.Text(
                                entry.key,
                                style: pw.TextStyle(
                                  fontSize: 9,
                                  color: PdfColors.grey600,
                                  fontWeight: pw.FontWeight.bold,
                                ),
                              ),
                              pw.SizedBox(height: 2),
                              pw.Text(
                                entry.value.toString(),
                                style: const pw.TextStyle(
                                  fontSize: 11,
                                  color: PdfColors.black,
                                ),
                              ),
                            ],
                          );
                        }).toList(),
                  ),
                ],
              ),
            ),

            pw.SizedBox(height: 30),

            // Results Summary
            pw.Container(
              padding: const pw.EdgeInsets.all(20),
              decoration: pw.BoxDecoration(
                color: PdfColors.green50,
                border: pw.Border.all(color: PdfColors.green700, width: 2),
                borderRadius: pw.BorderRadius.circular(8),
              ),
              child: pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  pw.Text(
                    'INVESTMENT RESULTS',
                    style: pw.TextStyle(
                      fontSize: 14,
                      fontWeight: pw.FontWeight.bold,
                      color: PdfColors.green700,
                      letterSpacing: 1,
                    ),
                  ),
                  pw.SizedBox(height: 16),
                  pw.Wrap(
                    spacing: 30,
                    runSpacing: 12,
                    children:
                        results.entries.map((entry) {
                          return pw.Column(
                            crossAxisAlignment: pw.CrossAxisAlignment.start,
                            children: [
                              pw.Text(
                                entry.key,
                                style: pw.TextStyle(
                                  fontSize: 10,
                                  color: PdfColors.grey600,
                                  fontWeight: pw.FontWeight.bold,
                                ),
                              ),
                              pw.SizedBox(height: 4),
                              pw.Text(
                                entry.value is double
                                    ? NumberFormat(
                                      '#,##,###',
                                    ).format(entry.value)
                                    : entry.value.toString(),
                                style: pw.TextStyle(
                                  fontSize: 14,
                                  color: PdfColors.green700,
                                  fontWeight: pw.FontWeight.bold,
                                ),
                              ),
                            ],
                          );
                        }).toList(),
                  ),
                ],
              ),
            ),

            pw.SizedBox(height: 30),

            // Year-wise Breakdown Table
            if (yearWiseData.isNotEmpty) ...[
              pw.Text(
                'YEAR-WISE BREAKDOWN',
                style: pw.TextStyle(
                  fontSize: 14,
                  fontWeight: pw.FontWeight.bold,
                  color: PdfColors.grey800,
                  letterSpacing: 1,
                ),
              ),
              pw.SizedBox(height: 16),
              pw.Table(
                border: pw.TableBorder.all(color: PdfColors.grey300),
                children: [
                  // Header row
                  pw.TableRow(
                    decoration: const pw.BoxDecoration(
                      color: PdfColors.grey100,
                    ),
                    children: [
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(8),
                        child: pw.Text(
                          'Year',
                          style: pw.TextStyle(
                            fontWeight: pw.FontWeight.bold,
                            fontSize: 10,
                          ),
                        ),
                      ),
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(8),
                        child: pw.Text(
                          'Invested',
                          style: pw.TextStyle(
                            fontWeight: pw.FontWeight.bold,
                            fontSize: 10,
                          ),
                        ),
                      ),
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(8),
                        child: pw.Text(
                          'Portfolio Value',
                          style: pw.TextStyle(
                            fontWeight: pw.FontWeight.bold,
                            fontSize: 10,
                          ),
                        ),
                      ),
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(8),
                        child: pw.Text(
                          'Returns',
                          style: pw.TextStyle(
                            fontWeight: pw.FontWeight.bold,
                            fontSize: 10,
                          ),
                        ),
                      ),
                      if (yearWiseData.first.containsKey('phase'))
                        pw.Padding(
                          padding: const pw.EdgeInsets.all(8),
                          child: pw.Text(
                            'Phase',
                            style: pw.TextStyle(
                              fontWeight: pw.FontWeight.bold,
                              fontSize: 10,
                            ),
                          ),
                        ),
                    ],
                  ),
                  // Data rows
                  ...yearWiseData.take(20).map((data) {
                    return pw.TableRow(
                      children: [
                        pw.Padding(
                          padding: const pw.EdgeInsets.all(6),
                          child: pw.Text(
                            data['year'].toString(),
                            style: const pw.TextStyle(fontSize: 9),
                          ),
                        ),
                        pw.Padding(
                          padding: const pw.EdgeInsets.all(6),
                          child: pw.Text(
                            NumberFormat('#,##,###').format(data['invested']),
                            style: const pw.TextStyle(
                              fontSize: 9,
                              color: PdfColors.blue700,
                            ),
                          ),
                        ),
                        pw.Padding(
                          padding: const pw.EdgeInsets.all(6),
                          child: pw.Text(
                            NumberFormat('#,##,###').format(data['value']),
                            style: const pw.TextStyle(
                              fontSize: 9,
                              color: PdfColors.green700,
                            ),
                          ),
                        ),
                        pw.Padding(
                          padding: const pw.EdgeInsets.all(6),
                          child: pw.Text(
                            NumberFormat('#,##,###').format(data['returns']),
                            style: pw.TextStyle(
                              fontSize: 9,
                              color:
                                  data['returns'] >= 0
                                      ? PdfColors.green700
                                      : PdfColors.red700,
                            ),
                          ),
                        ),
                        if (data.containsKey('phase'))
                          pw.Padding(
                            padding: const pw.EdgeInsets.all(6),
                            child: pw.Text(
                              data['phase'],
                              style: const pw.TextStyle(
                                fontSize: 8,
                                color: PdfColors.grey600,
                              ),
                            ),
                          ),
                      ],
                    );
                  }),
                ],
              ),
              if (yearWiseData.length > 20)
                pw.Padding(
                  padding: const pw.EdgeInsets.only(top: 8),
                  child: pw.Text(
                    'Note: Only first 20 years shown. Full data calculated for ' +
                        yearWiseData.length.toString() +
                        ' years.',
                    style: pw.TextStyle(
                      fontSize: 8,
                      color: PdfColors.grey600,
                      fontStyle: pw.FontStyle.italic,
                    ),
                  ),
                ),
            ],

            pw.SizedBox(height: 40),

            // Footer
            pw.Container(
              padding: const pw.EdgeInsets.all(16),
              decoration: pw.BoxDecoration(
                color: PdfColors.grey100,
                borderRadius: pw.BorderRadius.circular(8),
                border: pw.Border.all(color: PdfColors.grey300),
              ),
              child: pw.Column(
                children: [
                  pw.Row(
                    mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                    children: [
                      pw.Text(
                        'PERSONAL FINANCE',
                        style: pw.TextStyle(
                          fontSize: 11,
                          fontWeight: pw.FontWeight.bold,
                          color: PdfColors.grey800,
                          letterSpacing: 1,
                        ),
                      ),
                      pw.Text(
                        'Auto-generated Report',
                        style: const pw.TextStyle(
                          fontSize: 9,
                          color: PdfColors.grey600,
                        ),
                      ),
                    ],
                  ),
                  pw.SizedBox(height: 8),
                  pw.Container(
                    width: double.infinity,
                    height: 1,
                    color: PdfColors.grey300,
                  ),
                  pw.SizedBox(height: 8),
                  pw.Center(
                    child: pw.Text(
                      'Note: All calculations are based on compound annual growth rate (CAGR). Past performance does not guarantee future results.',
                      style: pw.TextStyle(
                        fontSize: 8,
                        color: PdfColors.grey500,
                        fontStyle: pw.FontStyle.italic,
                      ),
                      textAlign: pw.TextAlign.center,
                    ),
                  ),
                ],
              ),
            ),
          ];
        },
      ),
    );

    return pdf;
  }
}

/// PDF Viewer Screen
class _InvestmentPDFViewerScreen extends StatefulWidget {
  final Uint8List pdfBytes;
  final String filename;

  const _InvestmentPDFViewerScreen({
    required this.pdfBytes,
    required this.filename,
  });

  @override
  State<_InvestmentPDFViewerScreen> createState() =>
      _InvestmentPDFViewerScreenState();
}

class _InvestmentPDFViewerScreenState
    extends State<_InvestmentPDFViewerScreen> {
  final TransformationController _transformationController =
      TransformationController();
  double _currentScale = 1.0;

  void _zoomIn() {
    setState(() {
      _currentScale = (_currentScale * 1.2).clamp(0.5, 3.0);
      _transformationController.value =
          Matrix4.identity()..scale(_currentScale);
    });
  }

  void _zoomOut() {
    setState(() {
      _currentScale = (_currentScale / 1.2).clamp(0.5, 3.0);
      _transformationController.value =
          Matrix4.identity()..scale(_currentScale);
    });
  }

  void _resetZoom() {
    setState(() {
      _currentScale = 1.0;
      _transformationController.value = Matrix4.identity();
    });
  }

  @override
  void dispose() {
    _transformationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        title: Text(
          'Investment Report',
          style: theme.appBarTheme.titleTextStyle?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios),
          onPressed: () => Navigator.of(context).pop(),
        ),
        actions: [
          // Zoom Out button
          IconButton(icon: const Icon(Icons.zoom_out), onPressed: _zoomOut),

          // Reset Zoom button
          IconButton(
            icon: const Icon(Icons.center_focus_strong),
            onPressed: _resetZoom,
          ),

          // Zoom In button
          IconButton(icon: const Icon(Icons.zoom_in), onPressed: _zoomIn),

          // Save button
          IconButton(
            icon: const Icon(Icons.save),
            onPressed: () async {
              try {
                final dir = await getApplicationDocumentsDirectory();
                final file = File(
                  dir.path +
                      '/' +
                      widget.filename.replaceAll(
                        '.pdf',
                        '_' +
                            DateFormat('HHmm').format(DateTime.now()) +
                            '.pdf',
                      ),
                );
                await file.writeAsBytes(widget.pdfBytes);

                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('PDF saved to: ' + file.path),
                    backgroundColor: colorScheme.primary,
                  ),
                );
              } catch (e) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('Failed to save PDF: $e'),
                    backgroundColor: colorScheme.error,
                  ),
                );
              }
            },
          ),

          // Share button
          IconButton(
            icon: Icon(Icons.share, color: colorScheme.onPrimary),
            onPressed: () async {
              try {
                await Printing.sharePdf(
                  bytes: widget.pdfBytes,
                  filename: widget.filename,
                );
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: const Text('PDF shared successfully!'),
                    backgroundColor: colorScheme.primary,
                  ),
                );
              } catch (e) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('Failed to share PDF: $e'),
                    backgroundColor: colorScheme.error,
                  ),
                );
              }
            },
          ),

          // Print button
          IconButton(
            icon: Icon(Icons.print, color: colorScheme.onPrimary),
            onPressed: () async {
              try {
                await Printing.layoutPdf(
                  onLayout: (PdfPageFormat format) async => widget.pdfBytes,
                  name: widget.filename,
                );
              } catch (e) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('Failed to print PDF: $e'),
                    backgroundColor: colorScheme.error,
                  ),
                );
              }
            },
          ),
        ],
      ),
      body: InteractiveViewer(
        transformationController: _transformationController,
        minScale: 0.5,
        maxScale: 3.0,
        child: PdfPreview(
          build: (format) => widget.pdfBytes,
          allowPrinting: false,
          allowSharing: false,
          canChangePageFormat: false,
          canChangeOrientation: false,
          canDebug: false,
          initialPageFormat: PdfPageFormat.a4,
          pdfFileName: widget.filename,
        ),
      ),
    );
  }
}

/// Step-up SIP Calculator Tab
class StepUpSipCalculatorTab extends StatefulWidget {
  const StepUpSipCalculatorTab({super.key});

  @override
  State<StepUpSipCalculatorTab> createState() => _StepUpSipCalculatorTabState();
}

class _StepUpSipCalculatorTabState extends State<StepUpSipCalculatorTab> {
  final List<SipPeriod> _sipPeriods = [SipPeriod(amount: 0, months: 0)];
  final TextEditingController _returnRateController = TextEditingController(
    text: '12',
  );
  final TextEditingController _totalYearsController = TextEditingController(
    text: '15',
  );

  double _totalInvested = 0;
  double _maturityAmount = 0;
  double _totalReturns = 0;
  List<Map<String, dynamic>> _yearWiseData = [];
  bool _isExporting = false;

  @override
  void initState() {
    super.initState();
    _calculateStepUpSip();
  }

  @override
  void dispose() {
    _returnRateController.dispose();
    _totalYearsController.dispose();
    for (var period in _sipPeriods) {
      period.dispose();
    }
    super.dispose();
  }

  void _addSipPeriod() {
    setState(() {
      _sipPeriods.add(SipPeriod(amount: 0, months: 0));
    });
    _calculateStepUpSip();
  }

  void _removeSipPeriod(int index) {
    if (_sipPeriods.length > 1) {
      setState(() {
        _sipPeriods[index].dispose();
        _sipPeriods.removeAt(index);
      });
      _calculateStepUpSip();
    }
  }

  void _calculateStepUpSip() {
    final double returnRate = double.tryParse(_returnRateController.text) ?? 12;
    final int totalYears = int.tryParse(_totalYearsController.text) ?? 15;
    final int totalMonths = totalYears * 12;

    double totalInvested = 0;
    double maturityAmount = 0;
    List<Map<String, dynamic>> yearWiseData = [];

    // Validate that total period months don't exceed total investment years
    int totalPeriodMonths = _sipPeriods.fold(
      0,
      (sum, period) => sum + period.months,
    );
    if (totalPeriodMonths > totalMonths) {
      // Adjust the last period
      if (_sipPeriods.isNotEmpty) {
        int excess = totalPeriodMonths - totalMonths;
        _sipPeriods.last.months = math.max(1, _sipPeriods.last.months - excess);
      }
    }

    double monthlyRate = returnRate / 12 / 100;
    int currentMonth = 0;
    double currentValue = 0;

    // Process each SIP period using the formula: FV = PMT × (1 + r)^n
    for (var period in _sipPeriods) {
      double amount = period.amount;
      int months = period.months;

      for (
        int month = 0;
        month < months && currentMonth < totalMonths;
        month++
      ) {
        totalInvested += amount;
        currentMonth++;

        // Calculate the future value of this payment
        int remainingMonths = totalMonths - currentMonth;
        currentValue += amount * math.pow(1 + monthlyRate, remainingMonths);
      }
    }

    // SIP contributions stop after defined periods end.
    // The invested amount continues to compound till maturity without new contributions.

    maturityAmount = currentValue;

    // Generate year-wise data
    double runningInvested = 0;
    int totalSipMonths = _getTotalPeriodMonths();

    for (int year = 1; year <= totalYears; year++) {
      double yearlyInvested = 0;

      // Calculate investments for this year (only if within SIP period)
      int yearStartMonth = (year - 1) * 12;
      int yearEndMonth = math.min(year * 12, totalMonths);

      for (int month = yearStartMonth; month < yearEndMonth; month++) {
        if (month < totalSipMonths) {
          double monthlyAmount = _getAmountForMonth(month);
          yearlyInvested += monthlyAmount;
        }
      }

      runningInvested += yearlyInvested;

      // Calculate value at end of year: all past investments compounded to this year end
      double yearEndValue = 0;
      int currentMonthForCalc = 0;

      for (var period in _sipPeriods) {
        for (
          int month = 0;
          month < period.months && currentMonthForCalc < totalMonths;
          month++
        ) {
          int monthsFromInvestmentToYearEnd = (year * 12) - currentMonthForCalc;
          if (monthsFromInvestmentToYearEnd > 0) {
            yearEndValue +=
                period.amount *
                math.pow(1 + monthlyRate, monthsFromInvestmentToYearEnd);
          }
          currentMonthForCalc++;
        }
      }

      yearWiseData.add({
        'year': year,
        'invested': runningInvested,
        'value': yearEndValue,
        'returns': yearEndValue - runningInvested,
      });
    }

    setState(() {
      _totalInvested = totalInvested;
      _maturityAmount = maturityAmount;
      _totalReturns = maturityAmount - totalInvested;
      _yearWiseData = yearWiseData;
    });
  }

  double _getAmountForMonth(int monthIndex) {
    int currentMonth = 0;
    for (var period in _sipPeriods) {
      if (monthIndex < currentMonth + period.months) {
        return period.amount;
      }
      currentMonth += period.months;
    }
    // If month is beyond all defined periods, return 0 (no more SIP contributions)
    return 0;
  }

  int _getTotalPeriodMonths() {
    return _sipPeriods.fold(0, (sum, period) => sum + period.months);
  }

  Widget _buildStepUpInputField({
    required TextEditingController controller,
    required String label,
    String? prefix,
    String? suffix,
    TextInputType? keyboardType,
    String? Function(String?)? validator,
    void Function(String)? onChanged,
  }) {
    final theme = Theme.of(context);

    return TextFormField(
      controller: controller,
      keyboardType: keyboardType,
      validator: validator,
      onChanged: onChanged,
      decoration: InputDecoration(
        labelText: label,
        prefixText: prefix,
        suffixText: suffix,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: theme.colorScheme.outline),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: theme.colorScheme.outline.withValues(alpha: 0.5),
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: theme.colorScheme.primary, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: theme.colorScheme.error),
        ),
        filled: true,
        fillColor: theme.colorScheme.surface,
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 16,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Text(
            'Step-up SIP Calculator',
            style: theme.textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: colorScheme.primary,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Calculate returns for variable SIP amounts over different periods',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
          const SizedBox(height: 24),

          // SIP Periods Card
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'SIP Periods',
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      IconButton(
                        onPressed: _addSipPeriod,
                        icon: const Icon(Icons.add_circle),
                        color: colorScheme.primary,
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // SIP Periods List
                  ...List.generate(_sipPeriods.length, (index) {
                    return _buildSipPeriodRow(index, theme, colorScheme);
                  }),

                  const SizedBox(height: 16),

                  // Total period summary
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: colorScheme.primaryContainer.withValues(
                        alpha: 0.3,
                      ),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Column(
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              'SIP Period:',
                              style: theme.textTheme.bodyMedium?.copyWith(
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            Text(
                              '${_getTotalPeriodMonths()} months',
                              style: theme.textTheme.bodyMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: colorScheme.primary,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 4),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              'Remaining Period:',
                              style: theme.textTheme.bodySmall?.copyWith(
                                fontWeight: FontWeight.w500,
                                color: theme.colorScheme.onSurface.withValues(
                                  alpha: 0.7,
                                ),
                              ),
                            ),
                            Text(
                              '${math.max(0, (int.tryParse(_totalYearsController.text) ?? 15) * 12 - _getTotalPeriodMonths())} months (Growth only)',
                              style: theme.textTheme.bodySmall?.copyWith(
                                fontWeight: FontWeight.w600,
                                color: AppColorPalette.info,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Investment Parameters
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Investment Parameters',
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),

                  Row(
                    children: [
                      Expanded(
                        child: _buildStepUpInputField(
                          controller: _totalYearsController,
                          label: 'Total Investment Years',
                          suffix: 'Years',
                          keyboardType: TextInputType.number,
                          onChanged: (_) => _calculateStepUpSip(),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: _buildStepUpInputField(
                          controller: _returnRateController,
                          label: 'Expected Annual Return',
                          suffix: '%',
                          keyboardType: TextInputType.number,
                          onChanged: (_) => _calculateStepUpSip(),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 24),

          // Results
          if (_totalInvested > 0) ...[
            Text(
              'Investment Summary',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            Row(
              children: [
                Expanded(
                  child: _buildResultCard(
                    'Total Invested',
                    _totalInvested,
                    AppColorPalette.info,
                    theme,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildResultCard(
                    'Maturity Amount',
                    _maturityAmount,
                    AppColorPalette.success,
                    theme,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildResultCard(
                    'Total Returns',
                    _totalReturns,
                    AppColorPalette.warning,
                    theme,
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildSipPeriodRow(
    int index,
    ThemeData theme,
    ColorScheme colorScheme,
  ) {
    final period = _sipPeriods[index];

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: colorScheme.outline.withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: _buildStepUpInputField(
              controller: period.amountController,
              label: 'Amount',
              prefix: '₹',
              keyboardType: TextInputType.number,
              onChanged: (value) {
                period.amount = double.tryParse(value) ?? 0;
                _calculateStepUpSip();
              },
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: _buildStepUpInputField(
              controller: period.monthsController,
              label: 'Months',
              keyboardType: TextInputType.number,
              onChanged: (value) {
                period.months = int.tryParse(value) ?? 1;
                _calculateStepUpSip();
              },
            ),
          ),
          const SizedBox(width: 8),
          if (_sipPeriods.length > 1)
            IconButton(
              onPressed: () => _removeSipPeriod(index),
              icon: const Icon(Icons.remove_circle),
              color: AppColorPalette.error,
            ),
        ],
      ),
    );
  }

  Widget _buildResultCard(
    String label,
    double amount,
    Color color,
    ThemeData theme,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            NumberFormat('#,##,###').format(amount),
            style: theme.textTheme.titleMedium?.copyWith(
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }
}

/// Helper class to manage SIP periods
class SipPeriod {
  double amount;
  int months;
  final TextEditingController amountController;
  final TextEditingController monthsController;

  SipPeriod({required this.amount, required this.months})
    : amountController = TextEditingController(text: amount.toString()),
      monthsController = TextEditingController(text: months.toString());

  void dispose() {
    amountController.dispose();
    monthsController.dispose();
  }
}

/// Extension to add pow functionality to double
extension NumExtensions on double {
  double pow(double exponent) {
    return math.pow(this, exponent).toDouble();
  }
}
