import 'dart:math' as math;
import 'package:flutter/material.dart';

import 'package:easy_localization/easy_localization.dart';

import '../../services/investment_service.dart';
import '../models/investment_model.dart';
import 'add_investment_screen.dart';
import 'investment_withdrawal_screen.dart';

class InvestmentDetailScreen extends StatefulWidget {
  final InvestmentModel investment;

  const InvestmentDetailScreen({super.key, required this.investment});

  @override
  State<InvestmentDetailScreen> createState() => _InvestmentDetailScreenState();
}

class _InvestmentDetailScreenState extends State<InvestmentDetailScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  late InvestmentModel _investment;
  List<InvestmentEntryModel> _entries = [];
  List<InvestmentWithdrawalModel> _withdrawals = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _investment = widget.investment;
    _loadInvestmentData();
  }

  Future<void> _loadInvestmentData() async {
    setState(() => _isLoading = true);

    try {
      final entries =
          await InvestmentService.getInvestmentEntriesByInvestmentId(
            _investment.id,
          );
      final withdrawals =
          await InvestmentService.getInvestmentWithdrawalsByInvestmentId(
            _investment.id,
          );

      // Refresh investment data
      final investments = await InvestmentService.getInvestments();
      final updatedInvestment = investments.firstWhere(
        (inv) => inv.id == _investment.id,
        orElse: () => _investment,
      );

      setState(() {
        _investment = updatedInvestment;
        _entries = entries;
        _withdrawals = withdrawals;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      _showSnackBar(
        'investment_detail_error_loading'.tr(
          namedArgs: {'error': e.toString()},
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        title: Text(
          _investment.name,
          style: theme.appBarTheme.titleTextStyle?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios),
          onPressed: () => Navigator.of(context).pop(true),
        ),
        actions: [
          PopupMenuButton<String>(
            icon: const Icon(Icons.more_vert),
            onSelected: (value) {
              switch (value) {
                case 'investment_detail_edit':
                  _editInvestment();
                  break;
                case 'investment_detail_withdraw':
                  _showWithdrawDialog();
                  break;
                case 'investment_detail_delete':
                  _deleteInvestment();
                  break;
              }
            },
            itemBuilder:
                (context) => [
                  PopupMenuItem(
                    value: 'investment_detail_edit',
                    child: Row(
                      children: [
                        Icon(Icons.edit, size: 16),
                        SizedBox(width: 8),
                        Text('investment_detail_edit'.tr()),
                      ],
                    ),
                  ),
                  PopupMenuItem(
                    value: 'investment_detail_withdraw',
                    child: Row(
                      children: [
                        Icon(Icons.money_off, size: 16),
                        SizedBox(width: 8),
                        Text('investment_detail_withdraw'.tr()),
                      ],
                    ),
                  ),
                  PopupMenuItem(
                    value: 'investment_detail_delete',
                    child: Row(
                      children: [
                        Icon(Icons.delete, size: 16),
                        SizedBox(width: 8),
                        Text('investment_detail_delete'.tr()),
                      ],
                    ),
                  ),
                ],
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: colorScheme.onPrimary,
          unselectedLabelColor: colorScheme.onPrimary.withValues(alpha: 0.7),
          indicatorColor: colorScheme.onPrimary,
          tabs: [
            Tab(text: 'investment_detail_tab_overview'.tr()),
            Tab(text: 'investment_detail_tab_entries'.tr()),
            Tab(text: 'investment_detail_tab_withdrawals'.tr()),
          ],
        ),
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : Column(
                children: [
                  _buildInvestmentSummary(),
                  Expanded(
                    child: TabBarView(
                      controller: _tabController,
                      children: [
                        _buildOverviewTab(),
                        _buildEntriesTab(),
                        _buildWithdrawalsTab(),
                      ],
                    ),
                  ),
                ],
              ),
    );
  }

  Widget _buildInvestmentSummary() {
    final theme = Theme.of(context);
    // For interest-based investments, show calculated returns; for manual entry, show stored value
    final currentValue =
        (_investment.returnType == InvestmentReturnType.simpleInterest ||
                _investment.returnType == InvestmentReturnType.compoundInterest)
            ? _investment.calculateCurrentValue()
            : _investment.currentValue;
    final profitLoss = currentValue - _investment.totalInvested;
    final profitLossPercentage =
        _investment.totalInvested > 0
            ? (profitLoss / _investment.totalInvested) * 100
            : 0.0;
    final isProfit = profitLoss >= 0;

    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            isProfit ? Colors.green : Colors.red,
            (isProfit ? Colors.green : Colors.red).withValues(alpha: 0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: theme.shadowColor.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Text(
            'investment_detail_current_value'.tr(),
            style: theme.textTheme.titleMedium?.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '₹${currentValue.toStringAsFixed(2)}',
            style: theme.textTheme.headlineMedium?.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            '${isProfit ? '+' : ''}₹${profitLoss.toStringAsFixed(2)} (${isProfit ? '+' : ''}${profitLossPercentage.toStringAsFixed(2)}%)',
            style: theme.textTheme.titleSmall?.copyWith(
              color: Colors.white.withValues(alpha: 0.9),
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildSummaryItem(
                  'investment_detail_invested'.tr(),
                  '₹${_investment.totalInvested.toStringAsFixed(2)}',
                  Icons.trending_up,
                  Colors.white,
                ),
              ),
              Container(
                width: 1,
                height: 40,
                color: Colors.white.withValues(alpha: 0.3),
              ),
              Expanded(
                child: _buildSummaryItem(
                  'investment_detail_expected'.tr(),
                  '${_investment.expectedReturnRate.toStringAsFixed(1)}%',
                  Icons.show_chart,
                  Colors.white,
                ),
              ),
              Container(
                width: 1,
                height: 40,
                color: Colors.white.withValues(alpha: 0.3),
              ),
              Expanded(
                child: _buildSummaryItem(
                  'investment_detail_status'.tr(),
                  _investment.status.name.toUpperCase(),
                  Icons.flag,
                  Colors.white,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryItem(
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    final theme = Theme.of(context);

    return Column(
      children: [
        Icon(icon, color: color, size: 20),
        const SizedBox(height: 4),
        Text(
          label,
          style: theme.textTheme.bodySmall?.copyWith(
            color: color.withValues(alpha: 0.8),
          ),
        ),
        const SizedBox(height: 2),
        Text(
          value,
          style: theme.textTheme.labelMedium?.copyWith(
            color: color,
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildOverviewTab() {
    final theme = Theme.of(context);

    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'investment_detail_investment_details'.tr(),
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                _buildDetailRow(
                  'investment_detail_category'.tr(),
                  _investment.category,
                ),
                _buildDetailRow(
                  'investment_detail_description'.tr(),
                  _investment.description,
                ),
                _buildDetailRow(
                  'investment_detail_return_type'.tr(),
                  _getReturnTypeLabel(_investment.returnType),
                ),
                if (_investment.expectedReturnRate > 0)
                  _buildDetailRow(
                    'investment_detail_expected_return'.tr(),
                    '${_investment.expectedReturnRate}% ${_getFrequencyLabel(_investment.returnFrequency)}',
                  ),
                _buildDetailRow(
                  'investment_detail_start_date'.tr(),
                  DateFormat('dd/MM/yyyy').format(_investment.startDate),
                ),
                if (_investment.maturityDate != null) ...[
                  _buildDetailRow(
                    'investment_detail_maturity_date'.tr(),
                    DateFormat('dd/MM/yyyy').format(_investment.maturityDate!),
                  ),
                  if (_investment.returnType ==
                          InvestmentReturnType.simpleInterest ||
                      _investment.returnType ==
                          InvestmentReturnType.compoundInterest)
                    _buildDetailRow(
                      'investment_detail_projected_maturity'.tr(),
                      '₹${_calculateMaturityValue().toStringAsFixed(2)}',
                    ),
                ],
              ],
            ),
          ),
        ),
        const SizedBox(height: 16),
        Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'investment_detail_financial_summary'.tr(),
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                _buildDetailRow(
                  'investment_detail_initial_amount'.tr(),
                  '₹${_investment.initialAmount.toStringAsFixed(2)}',
                ),
                _buildDetailRow(
                  'investment_detail_total_invested'.tr(),
                  '₹${_investment.totalInvested.toStringAsFixed(2)}',
                ),
                _buildDetailRow(
                  'investment_detail_current_value'.tr(),
                  '₹${((_investment.returnType == InvestmentReturnType.simpleInterest || _investment.returnType == InvestmentReturnType.compoundInterest) ? _investment.calculateCurrentValue() : _investment.currentValue).toStringAsFixed(2)}',
                ),
                _buildDetailRow(
                  'investment_detail_total_entries'.tr(),
                  '${_entries.length}',
                ),
                _buildDetailRow(
                  'investment_detail_total_withdrawals'.tr(),
                  '${_withdrawals.length}',
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDetailRow(String label, String value) {
    final theme = Theme.of(context);

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            flex: 2,
            child: Text(
              label,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.hintColor,
              ),
            ),
          ),
          Expanded(
            flex: 3,
            child: Text(
              value,
              style: theme.textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEntriesTab() {
    if (_entries.isEmpty) {
      return _buildEmptyEntriesState();
    }

    return RefreshIndicator(
      onRefresh: _loadInvestmentData,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _entries.length,
        itemBuilder: (context, index) {
          final entry = _entries[index];
          return _buildEntryCard(entry);
        },
      ),
    );
  }

  Widget _buildWithdrawalsTab() {
    if (_withdrawals.isEmpty) {
      return _buildEmptyWithdrawalsState();
    }

    return RefreshIndicator(
      onRefresh: _loadInvestmentData,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _withdrawals.length + 1, // +1 for analytics header
        itemBuilder: (context, index) {
          if (index == 0) {
            return _buildWithdrawalAnalytics();
          }
          final withdrawal = _withdrawals[index - 1];
          return _buildWithdrawalCard(withdrawal);
        },
      ),
    );
  }

  Widget _buildEntryCard(InvestmentEntryModel entry) {
    final theme = Theme.of(context);

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.green.withValues(alpha: 0.1),
            shape: BoxShape.circle,
          ),
          child: const Icon(Icons.add, color: Colors.green, size: 20),
        ),
        title: Text(
          entry.description,
          style: theme.textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        subtitle: Text(
          DateFormat('dd/MM/yyyy').format(entry.date),
          style: theme.textTheme.bodySmall?.copyWith(color: theme.hintColor),
        ),
        trailing: Text(
          '+₹${entry.amount.toStringAsFixed(2)}',
          style: theme.textTheme.labelLarge?.copyWith(
            color: Colors.green,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  Widget _buildWithdrawalCard(InvestmentWithdrawalModel withdrawal) {
    final theme = Theme.of(context);

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.red.withValues(alpha: 0.1),
            shape: BoxShape.circle,
          ),
          child: const Icon(Icons.remove, color: Colors.red, size: 20),
        ),
        title: Text(
          withdrawal.reason,
          style: theme.textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              DateFormat('dd/MM/yyyy').format(withdrawal.date),
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.hintColor,
              ),
            ),
            if (withdrawal.penaltyAmount > 0)
              Text(
                'Penalty: ₹${withdrawal.penaltyAmount.toStringAsFixed(2)}',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: Colors.orange,
                ),
              ),
          ],
        ),
        trailing: Text(
          '-₹${withdrawal.amount.toStringAsFixed(2)}',
          style: theme.textTheme.labelLarge?.copyWith(
            color: Colors.red,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  Widget _buildWithdrawalAnalytics() {
    if (_withdrawals.isEmpty) return Container();

    final theme = Theme.of(context);
    final totalWithdrawn = _withdrawals.fold<double>(
      0.0,
      (sum, withdrawal) => sum + withdrawal.amount,
    );
    final withdrawalCount = _withdrawals.length;
    final avgWithdrawal = totalWithdrawn / withdrawalCount;

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(Icons.analytics, color: theme.colorScheme.primary),
                  const SizedBox(width: 8),
                  Text(
                    'investment_detail_withdrawal_summary'.tr(),
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: _buildAnalyticsItem(
                      'investment_detail_total_withdrawn'.tr(),
                      '₹${totalWithdrawn.toStringAsFixed(2)}',
                      Icons.money_off,
                      Colors.red,
                    ),
                  ),
                  Expanded(
                    child: _buildAnalyticsItem(
                      'investment_detail_transactions'.tr(),
                      '$withdrawalCount',
                      Icons.swap_horiz,
                      theme.colorScheme.primary,
                    ),
                  ),
                  Expanded(
                    child: _buildAnalyticsItem(
                      'investment_detail_average_amount'.tr(),
                      '₹${avgWithdrawal.toStringAsFixed(2)}',
                      Icons.trending_down,
                      theme.hintColor,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAnalyticsItem(
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    final theme = Theme.of(context);

    return Column(
      children: [
        Icon(icon, color: color, size: 20),
        const SizedBox(height: 4),
        Text(
          label,
          style: theme.textTheme.bodySmall?.copyWith(color: theme.hintColor),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 2),
        Text(
          value,
          style: theme.textTheme.labelMedium?.copyWith(
            color: color,
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildEmptyEntriesState() {
    final theme = Theme.of(context);

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.add_circle_outline,
              size: 64,
              color: theme.hintColor.withValues(alpha: 0.5),
            ),
            const SizedBox(height: 16),
            Text(
              'investment_detail_no_entries_title'.tr(),
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: theme.hintColor,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'investment_detail_no_entries_desc'.tr(),
              textAlign: TextAlign.center,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.hintColor.withValues(alpha: 0.8),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyWithdrawalsState() {
    final theme = Theme.of(context);

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.money_off_outlined,
              size: 64,
              color: theme.hintColor.withValues(alpha: 0.5),
            ),
            const SizedBox(height: 16),
            Text(
              'investment_detail_no_withdrawals_title'.tr(),
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: theme.hintColor,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'investment_detail_no_withdrawals_desc'.tr(),
              textAlign: TextAlign.center,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.hintColor.withValues(alpha: 0.8),
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _getReturnTypeLabel(InvestmentReturnType type) {
    switch (type) {
      case InvestmentReturnType.simpleInterest:
        return 'investment_simple_interest'.tr();
      case InvestmentReturnType.compoundInterest:
        return 'investment_compound_interest'.tr();
      case InvestmentReturnType.exactAmount:
        return 'investment_exact_amount'.tr();
      case InvestmentReturnType.manualEntry:
        return 'investment_manual_entry'.tr();
    }
  }

  String _getFrequencyLabel(InvestmentFrequency frequency) {
    switch (frequency) {
      case InvestmentFrequency.monthly:
        return 'investment_monthly'.tr();
      case InvestmentFrequency.quarterly:
        return 'investment_quarterly'.tr();
      case InvestmentFrequency.halfYearly:
        return 'investment_half_yearly'.tr();
      case InvestmentFrequency.yearly:
        return 'investment_yearly'.tr();
    }
  }

  double _calculateMaturityValue() {
    if (_investment.maturityDate == null) return _investment.currentValue;

    final totalDays =
        _investment.maturityDate!.difference(_investment.startDate).inDays;
    final yearsPassed = totalDays / 365.0;

    switch (_investment.returnType) {
      case InvestmentReturnType.simpleInterest:
        return _investment.totalInvested *
            (1 + (_investment.expectedReturnRate / 100) * yearsPassed);

      case InvestmentReturnType.compoundInterest:
        final compoundingsPerYear = _getCompoundingsPerYear();
        return _investment.totalInvested *
            math.pow(
              1 + (_investment.expectedReturnRate / 100) / compoundingsPerYear,
              compoundingsPerYear * yearsPassed,
            );

      default:
        return _investment.currentValue;
    }
  }

  int _getCompoundingsPerYear() {
    switch (_investment.returnFrequency) {
      case InvestmentFrequency.monthly:
        return 12;
      case InvestmentFrequency.quarterly:
        return 4;
      case InvestmentFrequency.halfYearly:
        return 2;
      case InvestmentFrequency.yearly:
        return 1;
    }
  }

  Future<void> _editInvestment() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder:
            (context) => AddInvestmentScreen(existingInvestment: _investment),
      ),
    );

    if (result == true) {
      _loadInvestmentData();
    }
  }

  Future<void> _showWithdrawDialog() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder:
            (context) => InvestmentWithdrawalScreen(investment: _investment),
      ),
    );

    if (result == true) {
      _loadInvestmentData();
    }
  }

  Future<void> _deleteInvestment() async {
    final shouldDelete = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text('investment_detail_delete_title'.tr()),
            content: Text(
              'investment_detail_delete_confirm'.tr(
                namedArgs: {'name': _investment.name},
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: Text('investment_detail_cancel'.tr()),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                child: Text(
                  'investment_detail_delete'.tr(),
                  style: TextStyle(color: Colors.red),
                ),
              ),
            ],
          ),
    );

    if (shouldDelete == true) {
      try {
        await InvestmentService.deleteInvestment(_investment.id);
        _showSnackBar('investment_detail_deleted_success'.tr());
        Navigator.of(context).pop(true);
      } catch (e) {
        _showSnackBar(
          'investment_detail_delete_error'.tr(
            namedArgs: {'error': e.toString()},
          ),
        );
      }
    }
  }

  void _showSnackBar(String message) {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: Text(message)));
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }
}
