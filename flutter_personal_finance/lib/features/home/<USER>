import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import '../../utils/helper/route.dart';
import '../../utils/helper/shared_preference.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  @override
  void initState() {
    super.initState();

    Future.delayed(Duration(seconds: 2), () {
      if (!mounted) return;
      AppRouter.pushNamed(context, GoRouterConstants.details);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Text('${'Home'.tr()} ${SharedPreferencesHelper.getUserName()}'),
      ),
    );
  }
}
