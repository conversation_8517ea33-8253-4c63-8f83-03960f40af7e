import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:go_router/go_router.dart';
import '../../resources/app_theme.dart';
import '../../services/local_storage_service.dart';
import '../../services/transaction_service.dart';
import '../../features/models/transaction_model.dart';
import '../../features/models/bank_account_model.dart';

class TransactionListScreen extends StatefulWidget {
  const TransactionListScreen({super.key});

  @override
  State<TransactionListScreen> createState() => _TransactionListScreenState();
}

class _TransactionListScreenState extends State<TransactionListScreen> {
  List<TransactionModel> _allTransactions = [];
  List<TransactionModel> _filteredTransactions = [];
  List<BankAccountModel> _bankAccounts = [];

  bool _isLoading = true;
  bool _isLoadingMore = false;
  bool _hasMoreTransactions = true;
  int _currentPage = 0;
  final int _pageSize = 50;

  // Filter state
  TransactionType? _selectedType;
  String? _selectedCategory;
  String? _selectedBankAccountId;
  DateTimeRange? _selectedDateRange;
  String _searchQuery = '';

  // Controllers
  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  // Categories for filtering
  final Set<String> _allCategories = {};

  @override
  void initState() {
    super.initState();
    _loadInitialData();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _searchController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      if (!_isLoadingMore && _hasMoreTransactions) {
        _loadMoreTransactions();
      }
    }
  }

  Future<void> _loadInitialData() async {
    setState(() {
      _isLoading = true;
      _currentPage = 0;
      _hasMoreTransactions = true;
    });

    try {
      // Load bank accounts and initial transactions
      await Future.wait([_loadBankAccounts(), _loadTransactions(reset: true)]);
    } catch (e) {
      print('Error loading initial data: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _loadBankAccounts() async {
    try {
      final bankAccounts = await LocalStorageService.getBankAccounts();
      setState(() {
        _bankAccounts = bankAccounts;
      });
    } catch (e) {
      print('Error loading bank accounts: $e');
    }
  }

  Future<void> _loadTransactions({bool reset = false}) async {
    try {
      // Load all transactions from local storage
      final allLocalTransactions = await LocalStorageService.getTransactions();

      // For local storage, we'll implement simple pagination by slicing the list
      final offset = reset ? 0 : _currentPage * _pageSize;
      final endIndex = offset + _pageSize;

      List<TransactionModel> newTransactions = [];
      if (offset < allLocalTransactions.length) {
        final actualEndIndex =
            endIndex > allLocalTransactions.length
                ? allLocalTransactions.length
                : endIndex;
        newTransactions = allLocalTransactions.sublist(offset, actualEndIndex);
      }

      setState(() {
        if (reset) {
          _allTransactions = newTransactions;
          _currentPage = 0;
        } else {
          _allTransactions.addAll(newTransactions);
          _currentPage++;
        }

        // Check if we have more transactions to load
        _hasMoreTransactions = endIndex < allLocalTransactions.length;

        // Extract unique categories
        final categories = _allTransactions.map((t) => t.category).toSet();
        _allCategories.clear();
        _allCategories.addAll(categories);
      });

      _applyFilters();
    } catch (e) {
      print('Error loading transactions: $e');
    }
  }

  Future<void> _loadMoreTransactions() async {
    if (_isLoadingMore || !_hasMoreTransactions) return;

    setState(() => _isLoadingMore = true);

    try {
      await _loadTransactions();
    } finally {
      setState(() => _isLoadingMore = false);
    }
  }

  void _applyFilters() {
    List<TransactionModel> filtered = List.from(_allTransactions);

    // Filter by type
    if (_selectedType != null) {
      filtered = filtered.where((t) => t.type == _selectedType).toList();
    }

    // Filter by category
    if (_selectedCategory != null) {
      filtered =
          filtered.where((t) => t.category == _selectedCategory).toList();
    }

    // Filter by bank account
    if (_selectedBankAccountId != null) {
      filtered =
          filtered
              .where((t) => t.bankAccountId == _selectedBankAccountId)
              .toList();
    }

    // Filter by date range
    if (_selectedDateRange != null) {
      // Ensure end date includes the full day (23:59:59)
      final rangeStart = _selectedDateRange!.start;
      final rangeEnd = DateTime(
        _selectedDateRange!.end.year,
        _selectedDateRange!.end.month,
        _selectedDateRange!.end.day,
        23,
        59,
        59,
      );

      filtered =
          filtered.where((t) {
            return (t.date.isAtSameMomentAs(rangeStart) ||
                    t.date.isAfter(rangeStart)) &&
                (t.date.isAtSameMomentAs(rangeEnd) ||
                    t.date.isBefore(rangeEnd));
          }).toList();
    }

    // Filter by search query
    if (_searchQuery.isNotEmpty) {
      filtered =
          filtered.where((t) {
            return t.description.toLowerCase().contains(
                  _searchQuery.toLowerCase(),
                ) ||
                t.category.toLowerCase().contains(_searchQuery.toLowerCase());
          }).toList();
    }

    // Sort by date and time (newest first)
    filtered.sort((a, b) => b.createdAt.compareTo(a.createdAt));

    setState(() {
      _filteredTransactions = filtered;
    });
  }

  void _clearFilters() {
    setState(() {
      _selectedType = null;
      _selectedCategory = null;
      _selectedBankAccountId = null;
      _selectedDateRange = null;
      _searchQuery = '';
      _searchController.clear();
    });
    _applyFilters();
  }

  Future<void> _selectDateRange() async {
    final picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(
        const Duration(days: 365 * 5),
      ), // Allow 5 years into the future
      initialDateRange: _selectedDateRange,
    );

    if (picked != null) {
      setState(() {
        _selectedDateRange = picked;
      });
      _applyFilters();
    }
  }

  void _showFilterBottomSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder:
          (context) => DraggableScrollableSheet(
            initialChildSize: 0.7,
            maxChildSize: 0.9,
            minChildSize: 0.5,
            expand: false,
            builder:
                (context, scrollController) =>
                    _buildFilterSheet(scrollController),
          ),
    );
  }

  Future<void> _deleteTransaction(TransactionModel transaction) async {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    // Show confirmation dialog
    final confirmed = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Delete Transaction'),
            content: Text(
              'Are you sure you want to delete this transaction?\n\n'
              '${transaction.category} - ₹${NumberFormat('#,##,###').format(transaction.amount)}\n'
              '${DateFormat('MMM dd, yyyy').format(transaction.date)}',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                style: TextButton.styleFrom(foregroundColor: colorScheme.error),
                child: const Text('Delete'),
              ),
            ],
          ),
    );

    if (confirmed == true) {
      try {
        // Delete the transaction using TransactionService
        await TransactionService.deleteTransaction(transaction.id);

        // Reload transactions to reflect the change
        await _loadInitialData();

        // Show success message
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Text('Transaction deleted successfully'),
              backgroundColor: AppColorPalette.success,
              duration: const Duration(seconds: 2),
            ),
          );
        }
      } catch (e) {
        print('Error deleting transaction: $e');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Text('Failed to delete transaction'),
              backgroundColor: colorScheme.error,
              duration: const Duration(seconds: 3),
            ),
          );
        }
      }
    }
  }

  Widget _buildFilterSheet(ScrollController scrollController) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Handle bar
          Center(
            child: Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: theme.hintColor.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          ),
          const SizedBox(height: 20),

          Text(
            'Filter Transactions',
            style: theme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 20),

          Expanded(
            child: ListView(
              controller: scrollController,
              children: [
                // Transaction Type Filter
                _buildFilterSection(
                  'Transaction Type',
                  DropdownButton<TransactionType?>(
                    value: _selectedType,
                    isExpanded: true,
                    hint: const Text('All Types'),
                    items: [
                      const DropdownMenuItem(
                        value: null,
                        child: Text('All Types'),
                      ),
                      const DropdownMenuItem(
                        value: TransactionType.credit,
                        child: Text('Income'),
                      ),
                      const DropdownMenuItem(
                        value: TransactionType.debit,
                        child: Text('Expense'),
                      ),
                    ],
                    onChanged: (value) {
                      setState(() => _selectedType = value);
                    },
                  ),
                ),
                const SizedBox(height: 16),

                // Category Filter
                if (_allCategories.isNotEmpty)
                  _buildFilterSection(
                    'Category',
                    DropdownButton<String?>(
                      value: _selectedCategory,
                      isExpanded: true,
                      hint: const Text('All Categories'),
                      items: [
                        const DropdownMenuItem(
                          value: null,
                          child: Text('All Categories'),
                        ),
                        ..._allCategories.map(
                          (category) => DropdownMenuItem(
                            value: category,
                            child: Text(category),
                          ),
                        ),
                      ],
                      onChanged: (value) {
                        setState(() => _selectedCategory = value);
                      },
                    ),
                  ),
                const SizedBox(height: 16),

                // Bank Account Filter
                if (_bankAccounts.isNotEmpty)
                  _buildFilterSection(
                    'Bank Account',
                    DropdownButton<String?>(
                      value: _selectedBankAccountId,
                      isExpanded: true,
                      hint: const Text('All Accounts'),
                      items: [
                        const DropdownMenuItem(
                          value: null,
                          child: Text('All Accounts'),
                        ),
                        ..._bankAccounts.map(
                          (account) => DropdownMenuItem(
                            value: account.id,
                            child: Text(account.bankName),
                          ),
                        ),
                      ],
                      onChanged: (value) {
                        setState(() => _selectedBankAccountId = value);
                      },
                    ),
                  ),
                const SizedBox(height: 16),

                // Date Range Filter
                _buildFilterSection(
                  'Date Range',
                  GestureDetector(
                    onTap: _selectDateRange,
                    child: Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: theme.hintColor.withValues(alpha: 0.3),
                        ),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            _selectedDateRange == null
                                ? 'Select Date Range'
                                : '${DateFormat('MMM dd, yyyy').format(_selectedDateRange!.start)} - ${DateFormat('MMM dd, yyyy').format(_selectedDateRange!.end)}',
                            style: theme.textTheme.bodyMedium,
                          ),
                          const Icon(Icons.calendar_today),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 20),

          // Action buttons
          Row(
            children: [
              Expanded(
                child: OutlinedButton(
                  onPressed: () {
                    _clearFilters();
                    context.pop();
                  },
                  child: const Text('Clear All'),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: ElevatedButton(
                  onPressed: () {
                    _applyFilters();
                    context.pop();
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: colorScheme.primary,
                    foregroundColor: colorScheme.onPrimary,
                  ),
                  child: const Text('Apply Filters'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      padding: const EdgeInsets.all(16),
      child: Container(
        decoration: BoxDecoration(
          color: theme.cardColor,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: colorScheme.outline.withValues(alpha: 0.2),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: theme.shadowColor.withValues(alpha: 0.08),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: TextField(
          controller: _searchController,
          style: theme.textTheme.bodyMedium?.copyWith(
            color: colorScheme.onSurface,
          ),
          decoration: InputDecoration(
            hintText: 'Search transactions, categories...',
            hintStyle: theme.textTheme.bodyMedium?.copyWith(
              color: theme.hintColor,
            ),
            prefixIcon: Icon(
              Icons.search,
              color: colorScheme.primary,
              size: 20,
            ),
            suffixIcon:
                _searchQuery.isNotEmpty
                    ? Container(
                      margin: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: colorScheme.outline.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: IconButton(
                        icon: Icon(
                          Icons.close,
                          color: theme.hintColor,
                          size: 16,
                        ),
                        onPressed: () {
                          _searchController.clear();
                          setState(() => _searchQuery = '');
                          _applyFilters();
                        },
                      ),
                    )
                    : null,
            border: InputBorder.none,
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 16,
            ),
          ),
          onChanged: (value) {
            setState(() => _searchQuery = value);
            _applyFilters();
          },
        ),
      ),
    );
  }

  Widget _buildFilterSection(String title, Widget child) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: theme.textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: theme.hintColor,
          ),
        ),
        const SizedBox(height: 8),
        child,
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        title: Text(
          'All Transactions (${_allTransactions.length})',
          style: theme.appBarTheme.titleTextStyle?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios),
          onPressed: () => context.pop(),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _showFilterBottomSheet,
          ),
        ],
      ),
      body: Column(
        children: [
          _buildSearchBar(),
          if (_hasActiveFilters()) _buildActiveFiltersBar(),

          // Hint about delete functionality
          if (_filteredTransactions.isNotEmpty && !_hasActiveFilters())
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              color: colorScheme.primary.withValues(alpha: 0.05),
              child: Row(
                children: [
                  Icon(
                    Icons.info_outline,
                    size: 16,
                    color: colorScheme.primary.withValues(alpha: 0.7),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Swipe left or tap delete icon to remove transactions',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: colorScheme.primary.withValues(alpha: 0.8),
                        fontSize: 12,
                      ),
                    ),
                  ),
                ],
              ),
            ),

          // Transactions list
          Expanded(
            child:
                _isLoading
                    ? const Center(child: CircularProgressIndicator())
                    : _filteredTransactions.isEmpty
                    ? _buildEmptyState()
                    : RefreshIndicator(
                      onRefresh: _loadInitialData,
                      child: ListView.builder(
                        controller: _scrollController,
                        padding: const EdgeInsets.all(16),
                        itemCount:
                            _filteredTransactions.length +
                            (_hasMoreTransactions ? 1 : 0),
                        itemBuilder: (context, index) {
                          if (index == _filteredTransactions.length) {
                            // Load more indicator
                            return _buildLoadMoreIndicator();
                          }
                          final transaction = _filteredTransactions[index];
                          return _buildTransactionCard(transaction);
                        },
                      ),
                    ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadMoreIndicator() {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      padding: const EdgeInsets.all(16),
      child: Center(
        child:
            _isLoadingMore
                ? const CircularProgressIndicator()
                : _hasMoreTransactions
                ? ElevatedButton(
                  onPressed: _loadMoreTransactions,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: colorScheme.primary,
                    foregroundColor: colorScheme.onPrimary,
                  ),
                  child: const Text('Load More Transactions'),
                )
                : Text(
                  'No more transactions',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.hintColor,
                  ),
                ),
      ),
    );
  }

  bool _hasActiveFilters() {
    return _selectedType != null ||
        _selectedCategory != null ||
        _selectedBankAccountId != null ||
        _selectedDateRange != null ||
        _searchQuery.isNotEmpty;
  }

  Widget _buildActiveFiltersBar() {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      color: colorScheme.primary.withValues(alpha: 0.1),
      child: Row(
        children: [
          Expanded(
            child: Wrap(
              spacing: 8,
              runSpacing: 4,
              children: [
                if (_selectedType != null)
                  _buildFilterChip(
                    _selectedType == TransactionType.credit
                        ? 'Income'
                        : 'Expense',
                    () => setState(() => _selectedType = null),
                  ),
                if (_selectedCategory != null)
                  _buildFilterChip(
                    _selectedCategory!,
                    () => setState(() => _selectedCategory = null),
                  ),
                if (_selectedBankAccountId != null)
                  _buildFilterChip(
                    _bankAccounts
                        .firstWhere((b) => b.id == _selectedBankAccountId)
                        .bankName,
                    () => setState(() => _selectedBankAccountId = null),
                  ),
                if (_selectedDateRange != null)
                  _buildFilterChip(
                    'Date Range',
                    () => setState(() => _selectedDateRange = null),
                  ),
              ],
            ),
          ),
          TextButton(
            onPressed: _clearFilters,
            child: Text(
              'Clear All',
              style: theme.textTheme.bodySmall?.copyWith(
                color: colorScheme.primary,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String label, VoidCallback onRemove) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: colorScheme.primary,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            label,
            style: theme.textTheme.bodySmall?.copyWith(
              color: colorScheme.onPrimary,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(width: 4),
          GestureDetector(
            onTap: () {
              onRemove();
              _applyFilters();
            },
            child: Icon(Icons.close, size: 16, color: colorScheme.onPrimary),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.receipt_long,
            size: 64,
            color: theme.hintColor.withValues(alpha: 0.5),
          ),
          const SizedBox(height: 16),
          Text(
            _hasActiveFilters()
                ? 'No transactions found'
                : 'No transactions yet',
            style: theme.textTheme.titleMedium?.copyWith(
              color: theme.hintColor,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _hasActiveFilters()
                ? 'Try adjusting your filters or search terms'
                : 'Start adding transactions to see them here',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.hintColor.withValues(alpha: 0.7),
            ),
            textAlign: TextAlign.center,
          ),
          if (_hasActiveFilters()) ...[
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _clearFilters,
              style: ElevatedButton.styleFrom(
                backgroundColor: colorScheme.primary,
                foregroundColor: colorScheme.onPrimary,
              ),
              child: const Text('Clear Filters'),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildTransactionCard(TransactionModel transaction) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    // Handle different transaction types including transfers
    final isIncome = transaction.type == TransactionType.credit;
    final isTransfer = transaction.category == 'Self Transfer';

    final Color color;
    final IconData icon;

    if (isTransfer) {
      // For self transfers: Red for debit (money out), Blue for credit (money in)
      if (isIncome) {
        color = AppColorPalette.success; // Money received - blue
        icon = Icons.swap_horiz; // Incoming arrow
      } else {
        color = AppColorPalette.error; // Money sent - red
        icon = Icons.swap_horiz; // Outgoing arrow
      }
    } else if (isIncome) {
      color = AppColorPalette.success;
      icon = Icons.arrow_upward;
    } else {
      color = colorScheme.error;
      icon = Icons.arrow_downward;
    }

    // Find bank account for this transaction
    final bankAccount = _bankAccounts.firstWhere(
      (account) => account.id == transaction.bankAccountId,
      orElse:
          () => BankAccountModel(
            bankName: 'Unknown Bank',
            accountNumber: '',
            initialAmount: 0,
            currentAmount: 0,
          ),
    );

    // Get bank color based on bank name
    final bankColor = _getBankColor(bankAccount.bankName);

    return Dismissible(
      key: Key(transaction.id),
      direction: DismissDirection.endToStart,
      background: Container(
        margin: const EdgeInsets.only(bottom: 8),
        padding: const EdgeInsets.symmetric(horizontal: 20),
        decoration: BoxDecoration(
          color: colorScheme.error,
          borderRadius: BorderRadius.circular(12),
        ),
        alignment: Alignment.centerRight,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(Icons.delete, color: colorScheme.onPrimary, size: 24),
            const SizedBox(width: 8),
            Text(
              'Delete',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: colorScheme.onPrimary,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
      confirmDismiss: (direction) async {
        // Don't auto-dismiss, we'll handle deletion manually
        _deleteTransaction(transaction);
        return false;
      },
      child: Container(
        margin: const EdgeInsets.only(bottom: 8),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: theme.cardColor,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: theme.shadowColor.withValues(alpha: 0.1),
              blurRadius: 5,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            // Transaction type icon
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(icon, color: color, size: 20),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          transaction.category,
                          style: theme.textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '${transaction.type.name.toUpperCase()} • ${DateFormat('MMM dd, yyyy').format(transaction.date)}',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.hintColor,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    bankAccount.bankName,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: bankColor,
                      fontWeight: FontWeight.w500,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),

            const SizedBox(width: 8),
            Text(
              '${isIncome ? '+' : '-'}₹${NumberFormat('#,##,###').format(transaction.amount)}',
              style: theme.textTheme.titleSmall?.copyWith(
                color: color,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Helper method to get bank color based on bank name
  Color _getBankColor(String bankName) {
    final lowerBankName = bankName.toLowerCase();

    if (lowerBankName.contains('sbi') || lowerBankName.contains('state bank')) {
      return const Color(0xFF1565C0); // SBI Blue
    } else if (lowerBankName.contains('hdfc')) {
      return const Color(0xFFD32F2F); // HDFC Red
    } else if (lowerBankName.contains('icici')) {
      return const Color(0xFFE65100); // ICICI Orange
    } else if (lowerBankName.contains('axis')) {
      return const Color(0xFF7B1FA2); // Axis Purple
    } else if (lowerBankName.contains('kotak')) {
      return const Color(0xFFD32F2F); // Kotak Red
    } else if (lowerBankName.contains('yes') ||
        lowerBankName.contains('yes bank')) {
      return const Color(0xFF1976D2); // Yes Bank Blue
    } else if (lowerBankName.contains('pnb') ||
        lowerBankName.contains('punjab')) {
      return const Color(0xFF388E3C); // PNB Green
    } else if (lowerBankName.contains('canara')) {
      return const Color(0xFFE65100); // Canara Orange
    } else if (lowerBankName.contains('union')) {
      return const Color(0xFF795548); // Union Brown
    } else if (lowerBankName.contains('bank of baroda') ||
        lowerBankName.contains('bob')) {
      return const Color(0xFF1976D2); // BOB Blue
    } else if (lowerBankName.contains('idbi') ||
        lowerBankName.contains('indian bank')) {
      return const Color(0xFF388E3C); // Green
    } else if (lowerBankName.contains('federal')) {
      return const Color(0xFF7B1FA2); // Purple
    } else if (lowerBankName.contains('indusind')) {
      return const Color(0xFFE65100); // Orange
    } else {
      return Theme.of(context).colorScheme.primary; // Default bank color
    }
  }
}
