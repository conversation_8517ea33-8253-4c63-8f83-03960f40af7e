import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import '../../resources/app_theme.dart';
import '../../shared/widgtes/common/custom_button.dart';
import '../../shared/widgtes/common/custom_text_field.dart';
import '../../services/local_storage_service.dart';
import '../../features/models/transaction_model.dart';
import '../../features/models/bank_account_model.dart';
import '../../features/models/recurring_transaction_model.dart';
import '../../utils/helper/keyboard_dismiss_wrapper.dart';
import '../../utils/helper/route.dart';
import '../../services/recurring_transaction_service.dart';

class AddTransactionScreen extends StatefulWidget {
  final TransactionType? initialType;

  const AddTransactionScreen({super.key, this.initialType});

  @override
  State<AddTransactionScreen> createState() => _AddTransactionScreenState();
}

enum TransactionTypeEnum { income, expense, transfer }

class _AddTransactionScreenState extends State<AddTransactionScreen> {
  final TextEditingController _amountController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  TransactionTypeEnum _selectedTypeEnum = TransactionTypeEnum.expense;
  TransactionType _selectedType = TransactionType.debit;
  BankAccountModel? _selectedBankAccount;
  BankAccountModel? _selectedFromAccount;
  BankAccountModel? _selectedToAccount;
  DateTime _selectedDate = DateTime.now();
  List<BankAccountModel> _bankAccounts = [];
  List<Map<String, dynamic>> _categories = [];
  String? _selectedCategory;

  // Recurring transaction fields
  bool _isRecurring = false;
  RecurringFrequency _selectedFrequency = RecurringFrequency.monthly;
  DateTime? _endDate;
  bool _hasEndDate = false;

  bool _isLoading = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    if (widget.initialType != null) {
      _selectedType = widget.initialType!;
      _selectedTypeEnum =
          widget.initialType! == TransactionType.credit
              ? TransactionTypeEnum.income
              : TransactionTypeEnum.expense;
    }
    _loadBankAccounts();
    _loadCategories();
  }

  @override
  void dispose() {
    _amountController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  Future<void> _loadBankAccounts() async {
    try {
      final bankAccounts = await LocalStorageService.getBankAccounts();

      setState(() {
        _bankAccounts = bankAccounts;
        if (_bankAccounts.isNotEmpty) {
          _selectedBankAccount = _bankAccounts.first;
          _selectedFromAccount = _bankAccounts.first;
          if (_bankAccounts.length > 1) {
            _selectedToAccount = _bankAccounts[1];
          }
        }
      });
    } catch (e) {
      print('Error loading bank accounts: $e');
    }
  }

  Future<void> _loadCategories() async {
    try {
      final categories = await LocalStorageService.getIncomeExpenseCategories();
      setState(() {
        _categories = categories;
      });
    } catch (e) {
      print('Error loading categories: $e');
    }
  }

  Future<void> _handleAddTransaction() async {
    if (!_formKey.currentState!.validate()) return;

    if (_selectedTypeEnum == TransactionTypeEnum.transfer) {
      return _handleTransfer();
    }

    if (_selectedBankAccount == null) {
      setState(() {
        _errorMessage = 'Please select a bank account';
      });
      return;
    }

    if (_selectedCategory == null) {
      setState(() {
        _errorMessage = 'Please select a category';
      });
      return;
    }

    if (_isRecurring && _descriptionController.text.trim().isEmpty) {
      setState(() {
        _errorMessage = 'Please enter a description for recurring transaction';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final amount = double.parse(_amountController.text.trim());
      final description =
          _isRecurring
              ? _descriptionController.text.trim()
              : _selectedType.name.toUpperCase();

      // Create transaction model
      final transaction = TransactionModel(
        bankAccountId: _selectedBankAccount!.id,
        type: _selectedType,
        amount: amount,
        description: description,
        category: _selectedCategory!,
        date: _selectedDate,
        createdAt: _selectedDate,
        updatedAt: DateTime.now(),
      );

      // Save transaction locally
      await LocalStorageService.addTransaction(transaction);

      // If it's a recurring transaction, create the recurring transaction
      if (_isRecurring) {
        await RecurringTransactionService.createRecurringTransaction(
          bankAccountId: _selectedBankAccount!.id,
          description: description,
          category: _selectedCategory!,
          amount: amount,
          frequency: _selectedFrequency,
          startDate: _selectedDate,
          endDate: _hasEndDate ? _endDate : null,
        );
      }

      // Update bank account balances
      await LocalStorageService.updateBankAccountBalances();

      if (mounted) {
        _showSuccessDialog();
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to add transaction. Please try again.';
      });
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _handleTransfer() async {
    if (_selectedFromAccount == null || _selectedToAccount == null) {
      setState(() {
        _errorMessage = 'Please select both source and destination accounts';
      });
      return;
    }

    if (_selectedFromAccount!.id == _selectedToAccount!.id) {
      setState(() {
        _errorMessage = 'Source and destination accounts must be different';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final amount = double.parse(_amountController.text.trim());

      // Create two transactions: debit from source, credit to destination
      final debitTransaction = TransactionModel(
        bankAccountId: _selectedFromAccount!.id,
        type: TransactionType.debit,
        amount: amount,
        description: 'Transfer to ${_selectedToAccount!.bankName}',
        category: 'Self Transfer',
        date: _selectedDate,
        createdAt: _selectedDate,
        updatedAt: DateTime.now(),
      );

      final creditTransaction = TransactionModel(
        bankAccountId: _selectedToAccount!.id,
        type: TransactionType.credit,
        amount: amount,
        description: 'Transfer from ${_selectedFromAccount!.bankName}',
        category: 'Self Transfer',
        date: _selectedDate,
        createdAt: _selectedDate,
        updatedAt: DateTime.now(),
      );

      // Save both transactions
      await LocalStorageService.addTransaction(debitTransaction);
      await LocalStorageService.addTransaction(creditTransaction);

      // Update bank account balances
      await LocalStorageService.updateBankAccountBalances();

      if (mounted) {
        _showSuccessDialog();
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to process transfer. Please try again.';
      });
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _showSuccessDialog() {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    String successMessage;
    if (_selectedTypeEnum == TransactionTypeEnum.transfer) {
      successMessage = 'Transfer completed successfully!';
    } else {
      successMessage =
          '${_selectedTypeEnum == TransactionTypeEnum.income ? 'Income' : 'Expense'} added successfully!';
    }

    showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (dialogContext) => AlertDialog(
            title: Text(
              'Success',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            content: Text(successMessage, style: theme.textTheme.bodyMedium),
            actions: [
              TextButton(
                onPressed: () {
                  // Close dialog only, don't navigate back automatically
                  Navigator.of(dialogContext).pop();
                },
                child: Text(
                  'OK',
                  style: theme.textTheme.labelLarge?.copyWith(
                    color: colorScheme.primary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
    );
  }

  Future<void> _selectDate() async {
    final pickedDate = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(
        const Duration(days: 365 * 5),
      ), // Allow 5 years into future
    );

    if (pickedDate != null) {
      // After selecting date, show time picker
      if (mounted) {
        final pickedTime = await showTimePicker(
          context: context,
          initialTime: TimeOfDay.fromDateTime(_selectedDate),
        );

        if (pickedTime != null) {
          // Combine selected date with selected time
          final combinedDateTime = DateTime(
            pickedDate.year,
            pickedDate.month,
            pickedDate.day,
            pickedTime.hour,
            pickedTime.minute,
          );

          setState(() {
            _selectedDate = combinedDateTime;
          });
        } else {
          // If user cancels time picker, still use the selected date with current time
          final now = DateTime.now();
          final combinedDateTime = DateTime(
            pickedDate.year,
            pickedDate.month,
            pickedDate.day,
            now.hour,
            now.minute,
          );

          setState(() {
            _selectedDate = combinedDateTime;
          });
        }
      }
    }
  }

  void _showBankAccountDialog() {
    final theme = Theme.of(context);

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(
              'Select Bank Account',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            content: SizedBox(
              width: double.maxFinite,
              child: ListView.builder(
                shrinkWrap: true,
                itemCount: _bankAccounts.length,
                itemBuilder:
                    (context, index) => ListTile(
                      title: Text(
                        _bankAccounts[index].bankName,
                        style: theme.textTheme.bodyMedium,
                      ),
                      subtitle: Text(
                        'A/C: ${_bankAccounts[index].accountNumber}',
                        style: theme.textTheme.bodySmall,
                      ),
                      onTap: () {
                        setState(() {
                          _selectedBankAccount = _bankAccounts[index];
                        });
                        Navigator.of(context).pop();
                      },
                    ),
              ),
            ),
          ),
    );
  }

  void _showCategoryDialog() {
    final theme = Theme.of(context);

    // Filter categories based on selected transaction type
    final filteredCategories =
        _categories.where((category) {
          return category['type'] ==
              (_selectedTypeEnum == TransactionTypeEnum.income
                  ? 'income'
                  : 'expense');
        }).toList();

    final isIncome = _selectedTypeEnum == TransactionTypeEnum.income;
    final typeText = isIncome ? 'Income' : 'Expense';
    final typeColor =
        isIncome ? theme.colorScheme.primary : theme.colorScheme.error;

    showDialog(
      context: context,
      builder:
          (context) => _CategorySelectionDialog(
            categories: filteredCategories,
            isIncome: isIncome,
            typeText: typeText,
            typeColor: typeColor,
            onCategorySelected: (categoryName) {
              setState(() {
                _selectedCategory = categoryName;
              });
            },
            onAddCategory: () => _showAddCategoryDialog(),
          ),
    );
  }

  void _showAddCategoryDialog() {
    final theme = Theme.of(context);

    final TextEditingController nameController = TextEditingController();
    final GlobalKey<FormState> formKey = GlobalKey<FormState>();
    String selectedType =
        _selectedTypeEnum == TransactionTypeEnum.income ? 'income' : 'expense';
    bool isInvestment = false; // New investment flag

    showDialog(
      context: context,
      builder:
          (context) => StatefulBuilder(
            builder:
                (context, setState) => AlertDialog(
                  title: Row(
                    children: [
                      Expanded(
                        child: Text(
                          'Add Category',
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      // Category Type Label (moved from center)
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color:
                              selectedType == 'income'
                                  ? theme.colorScheme.primary.withValues(
                                    alpha: 0.1,
                                  )
                                  : theme.colorScheme.error.withValues(
                                    alpha: 0.1,
                                  ),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color:
                                selectedType == 'income'
                                    ? theme.colorScheme.primary
                                    : theme.colorScheme.error,
                          ),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              selectedType == 'income'
                                  ? Icons.add_circle
                                  : Icons.remove_circle,
                              color:
                                  selectedType == 'income'
                                      ? theme.colorScheme.primary
                                      : theme.colorScheme.error,
                              size: 14,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              selectedType == 'income' ? 'Income' : 'Expense',
                              style: theme.textTheme.bodySmall?.copyWith(
                                color:
                                    selectedType == 'income'
                                        ? theme.colorScheme.primary
                                        : theme.colorScheme.error,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  content: SizedBox(
                    width: double.maxFinite,
                    child: Form(
                      key: formKey,
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          // Category Type Switch
                          Container(
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: theme.hintColor.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Row(
                              children: [
                                Icon(
                                  Icons.category,
                                  color: theme.hintColor,
                                  size: 20,
                                ),
                                const SizedBox(width: 12),
                                Text(
                                  'Category Type',
                                  style: theme.textTheme.bodyMedium?.copyWith(
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                                const Spacer(),
                                // Toggle Switch
                                GestureDetector(
                                  onTap: () {
                                    setState(() {
                                      selectedType =
                                          selectedType == 'income'
                                              ? 'expense'
                                              : 'income';
                                      // Reset investment flag when switching to income
                                      if (selectedType == 'income') {
                                        isInvestment = false;
                                      }
                                    });
                                  },
                                  child: Container(
                                    width: 60,
                                    height: 30,
                                    decoration: BoxDecoration(
                                      color:
                                          selectedType == 'income'
                                              ? theme.colorScheme.primary
                                              : theme.colorScheme.error,
                                      borderRadius: BorderRadius.circular(15),
                                    ),
                                    child: Stack(
                                      children: [
                                        Positioned(
                                          left:
                                              selectedType == 'income' ? 32 : 2,
                                          top: 2,
                                          child: Container(
                                            width: 26,
                                            height: 26,
                                            decoration: const BoxDecoration(
                                              color: Colors.white,
                                              shape: BoxShape.circle,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(height: 16),

                          // Investment Flag Checkbox (only for expense categories)
                          if (selectedType == 'expense') ...[
                            Container(
                              padding: const EdgeInsets.all(12),
                              decoration: BoxDecoration(
                                color: Colors.green.withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(
                                  color: Colors.green.withValues(alpha: 0.3),
                                ),
                              ),
                              child: Row(
                                children: [
                                  Icon(
                                    Icons.trending_up,
                                    color: Colors.green,
                                    size: 20,
                                  ),
                                  const SizedBox(width: 12),
                                  Expanded(
                                    child: Text(
                                      'Investment Category',
                                      style: theme.textTheme.bodyMedium
                                          ?.copyWith(
                                            fontWeight: FontWeight.w600,
                                          ),
                                    ),
                                  ),
                                  Checkbox(
                                    value: isInvestment,
                                    onChanged: (value) {
                                      setState(() {
                                        isInvestment = value ?? false;
                                      });
                                    },
                                    activeColor: Colors.green,
                                  ),
                                ],
                              ),
                            ),
                            const SizedBox(height: 16),
                          ],

                          // Category Name Field
                          TextFormField(
                            controller: nameController,
                            decoration: InputDecoration(
                              labelText: 'Category Name',
                              hintText:
                                  selectedType == 'income'
                                      ? 'e.g., Salary, Bonus, Investment'
                                      : 'e.g., Food, Transport, Shopping',
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                              prefixIcon: Icon(
                                selectedType == 'income'
                                    ? Icons.add_circle
                                    : Icons.remove_circle,
                                color:
                                    selectedType == 'income'
                                        ? theme.colorScheme.primary
                                        : theme.colorScheme.error,
                              ),
                            ),
                            validator: (value) {
                              if (value == null || value.trim().isEmpty) {
                                return 'Please enter category name';
                              }
                              if (value.trim().length < 2) {
                                return 'Category name must be at least 2 characters';
                              }
                              // Check if category already exists
                              final exists = _categories.any(
                                (cat) =>
                                    cat['name'].toString().toLowerCase() ==
                                        value.trim().toLowerCase() &&
                                    cat['type'] == selectedType,
                              );
                              if (exists) {
                                return 'This category already exists';
                              }
                              return null;
                            },
                            textCapitalization: TextCapitalization.words,
                            autofocus: true,
                          ),
                        ],
                      ),
                    ),
                  ),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.of(context).pop(),
                      child: Text(
                        'Cancel',
                        style: theme.textTheme.labelLarge?.copyWith(
                          color: theme.hintColor,
                        ),
                      ),
                    ),
                    TextButton(
                      onPressed: () async {
                        if (formKey.currentState!.validate()) {
                          try {
                            final categoryName = nameController.text.trim();

                            // Add category to local storage with investment flag
                            await LocalStorageService.addIncomeExpenseCategory({
                              'id':
                                  DateTime.now().millisecondsSinceEpoch
                                      .toString(),
                              'name': categoryName,
                              'type': selectedType,
                              'isInvestment':
                                  selectedType == 'expense'
                                      ? isInvestment
                                      : false,
                              'createdAt': DateTime.now().toIso8601String(),
                              'updatedAt': DateTime.now().toIso8601String(),
                            });

                            // Reload categories
                            await _loadCategories();

                            // Set as selected category
                            this.setState(() {
                              _selectedCategory = categoryName;
                            });

                            Navigator.of(context).pop();

                            // Show success message
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text(
                                  'Category "$categoryName" added successfully!',
                                ),
                                backgroundColor: theme.colorScheme.primary,
                              ),
                            );

                            // Reopen category selection dialog with updated categories
                            Future.delayed(
                              const Duration(milliseconds: 300),
                              () {
                                if (mounted) {
                                  _showCategoryDialog();
                                }
                              },
                            );
                          } catch (e) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text(
                                  'Failed to add category. Please try again.',
                                ),
                                backgroundColor: theme.colorScheme.error,
                              ),
                            );
                          }
                        }
                      },
                      child: Text(
                        'Add',
                        style: theme.textTheme.labelLarge?.copyWith(
                          color: theme.colorScheme.primary,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
          ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    // Updated to handle three types
    final typeText =
        _selectedTypeEnum == TransactionTypeEnum.income
            ? 'Income'
            : _selectedTypeEnum == TransactionTypeEnum.expense
            ? 'Expense'
            : 'Transfer';
    final typeColor =
        _selectedTypeEnum == TransactionTypeEnum.income
            ? theme.colorScheme.primary
            : _selectedTypeEnum == TransactionTypeEnum.expense
            ? theme.colorScheme.error
            : Colors.blue;
    final typeIcon =
        _selectedTypeEnum == TransactionTypeEnum.income
            ? Icons.add_circle
            : _selectedTypeEnum == TransactionTypeEnum.expense
            ? Icons.remove_circle
            : Icons.swap_horiz;

    return KeyboardDismissWrapper(
      child: Scaffold(
        backgroundColor: theme.scaffoldBackgroundColor,
        appBar: AppBar(
          backgroundColor: typeColor,
          foregroundColor: colorScheme.onPrimary,
          title: Text(
            'Add $typeText',
            style: theme.appBarTheme.titleTextStyle?.copyWith(
              fontWeight: FontWeight.bold,
              color: colorScheme.onPrimary,
            ),
          ),
          leading: IconButton(
            icon: const Icon(Icons.arrow_back_ios),
            color: colorScheme.onPrimary,
            onPressed: () => Navigator.of(context).pop(),
          ),
          actions: [
            IconButton(
              icon: const Icon(Icons.repeat),
              color: colorScheme.onPrimary,
              onPressed: () {
                AppRouter.pushNamed(
                  context,
                  GoRouterConstants.recurringTransactions,
                );
              },
              tooltip: 'Recurring Transactions',
            ),
          ],
        ),
        body: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // Transaction type toggle (Income/Expense/Transfer)
                Container(
                  padding: const EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    color: theme.hintColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Row(
                    children: [
                      // Income
                      Expanded(
                        child: GestureDetector(
                          onTap: () {
                            setState(() {
                              _selectedTypeEnum = TransactionTypeEnum.income;
                              _selectedType = TransactionType.credit;
                              _selectedCategory = null;
                            });
                          },
                          child: Container(
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            decoration: BoxDecoration(
                              color:
                                  _selectedTypeEnum ==
                                          TransactionTypeEnum.income
                                      ? theme.colorScheme.primary
                                      : Colors.transparent,
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.add_circle,
                                  color:
                                      _selectedTypeEnum ==
                                              TransactionTypeEnum.income
                                          ? colorScheme.onPrimary
                                          : theme.colorScheme.primary,
                                  size: 16,
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  'Income',
                                  style: theme.textTheme.titleSmall?.copyWith(
                                    color:
                                        _selectedTypeEnum ==
                                                TransactionTypeEnum.income
                                            ? colorScheme.onPrimary
                                            : theme.colorScheme.primary,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 4),
                      // Expense
                      Expanded(
                        child: GestureDetector(
                          onTap: () {
                            setState(() {
                              _selectedTypeEnum = TransactionTypeEnum.expense;
                              _selectedType = TransactionType.debit;
                              _selectedCategory = null;
                            });
                          },
                          child: Container(
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            decoration: BoxDecoration(
                              color:
                                  _selectedTypeEnum ==
                                          TransactionTypeEnum.expense
                                      ? theme.colorScheme.error
                                      : Colors.transparent,
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.remove_circle,
                                  color:
                                      _selectedTypeEnum ==
                                              TransactionTypeEnum.expense
                                          ? colorScheme.onPrimary
                                          : theme.colorScheme.error,
                                  size: 16,
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  'Expense',
                                  style: theme.textTheme.titleSmall?.copyWith(
                                    color:
                                        _selectedTypeEnum ==
                                                TransactionTypeEnum.expense
                                            ? colorScheme.onPrimary
                                            : theme.colorScheme.error,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 4),
                      // Transfer
                      Expanded(
                        child: GestureDetector(
                          onTap: () {
                            setState(() {
                              _selectedTypeEnum = TransactionTypeEnum.transfer;
                              _selectedCategory = 'Self Transfer';
                            });
                          },
                          child: Container(
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            decoration: BoxDecoration(
                              color:
                                  _selectedTypeEnum ==
                                          TransactionTypeEnum.transfer
                                      ? Colors.blue
                                      : Colors.transparent,
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.swap_horiz,
                                  color:
                                      _selectedTypeEnum ==
                                              TransactionTypeEnum.transfer
                                          ? Colors.white
                                          : Colors.blue,
                                  size: 16,
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  'Transfer',
                                  style: theme.textTheme.titleSmall?.copyWith(
                                    color:
                                        _selectedTypeEnum ==
                                                TransactionTypeEnum.transfer
                                            ? Colors.white
                                            : Colors.blue,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 24),

                // Amount field
                CustomTextField(
                  controller: _amountController,
                  labelText: 'Amount',
                  hintText: 'Enter amount',
                  prefixIcon: Icons.currency_rupee,
                  keyboardType: const TextInputType.numberWithOptions(
                    decimal: true,
                  ),
                  inputFormatters: [
                    FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
                  ],
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter amount';
                    }
                    final amount = double.tryParse(value);
                    if (amount == null) {
                      return 'Please enter a valid amount';
                    }
                    if (amount <= 0) {
                      return 'Amount must be greater than 0';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // Show different fields based on selected type
                if (_selectedTypeEnum == TransactionTypeEnum.transfer) ...[
                  // Transfer From
                  GestureDetector(
                    onTap:
                        _bankAccounts.isEmpty ? null : _showFromAccountDialog,
                    child: Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: theme.hintColor.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: theme.hintColor.withValues(alpha: 0.3),
                        ),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            Icons.account_balance,
                            color: theme.hintColor,
                            size: 20,
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Transfer From',
                                  style: theme.textTheme.bodyMedium?.copyWith(
                                    fontWeight: FontWeight.w600,
                                    color: theme.hintColor,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  _selectedFromAccount?.bankName ??
                                      'Select source account',
                                  style: theme.textTheme.bodyMedium,
                                ),
                              ],
                            ),
                          ),
                          if (_bankAccounts.isNotEmpty)
                            Icon(Icons.arrow_drop_down, color: theme.hintColor),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Transfer To
                  GestureDetector(
                    onTap: _bankAccounts.isEmpty ? null : _showToAccountDialog,
                    child: Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: theme.hintColor.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: theme.hintColor.withValues(alpha: 0.3),
                        ),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            Icons.account_balance,
                            color: theme.hintColor,
                            size: 20,
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Transfer To',
                                  style: theme.textTheme.bodyMedium?.copyWith(
                                    fontWeight: FontWeight.w600,
                                    color: theme.hintColor,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  _selectedToAccount?.bankName ??
                                      'Select destination account',
                                  style: theme.textTheme.bodyMedium,
                                ),
                              ],
                            ),
                          ),
                          if (_bankAccounts.isNotEmpty)
                            Icon(Icons.arrow_drop_down, color: theme.hintColor),
                        ],
                      ),
                    ),
                  ),
                ] else ...[
                  // Bank Account Selection (for Income/Expense)
                  GestureDetector(
                    onTap:
                        _bankAccounts.isEmpty ? null : _showBankAccountDialog,
                    child: Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: theme.hintColor.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: theme.hintColor.withValues(alpha: 0.3),
                        ),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            Icons.account_balance,
                            color: theme.hintColor,
                            size: 20,
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Bank Account',
                                  style: theme.textTheme.bodyMedium?.copyWith(
                                    fontWeight: FontWeight.w600,
                                    color: theme.hintColor,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  _selectedBankAccount?.bankName ??
                                      'Select bank account',
                                  style: theme.textTheme.bodyMedium,
                                ),
                              ],
                            ),
                          ),
                          if (_bankAccounts.isNotEmpty)
                            Icon(Icons.arrow_drop_down, color: theme.hintColor),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Category Selection (for Income/Expense only)
                  GestureDetector(
                    onTap: _showCategoryDialog,
                    child: Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: theme.hintColor.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: theme.hintColor.withValues(alpha: 0.3),
                        ),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            Icons.category,
                            color: theme.hintColor,
                            size: 20,
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Category',
                                  style: theme.textTheme.bodyMedium?.copyWith(
                                    fontWeight: FontWeight.w600,
                                    color: theme.hintColor,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  _selectedCategory ?? 'Select category',
                                  style: theme.textTheme.bodyMedium,
                                ),
                              ],
                            ),
                          ),
                          Icon(Icons.arrow_drop_down, color: theme.hintColor),
                        ],
                      ),
                    ),
                  ),
                ],
                const SizedBox(height: 16),

                // Date Selection
                GestureDetector(
                  onTap: _selectDate,
                  child: Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: theme.hintColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: theme.hintColor.withValues(alpha: 0.3),
                      ),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Icons.calendar_today,
                          color: theme.hintColor,
                          size: 20,
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Date & Time',
                                style: theme.textTheme.bodyMedium?.copyWith(
                                  fontWeight: FontWeight.w600,
                                  color: theme.hintColor,
                                ),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                DateFormat(
                                  'MMM dd, yyyy • hh:mm a',
                                ).format(_selectedDate),
                                style: theme.textTheme.bodyMedium,
                              ),
                              const SizedBox(height: 2),
                              Text(
                                'Past and future dates allowed',
                                style: theme.textTheme.bodySmall?.copyWith(
                                  color: theme.hintColor.withValues(alpha: 0.6),
                                ),
                              ),
                            ],
                          ),
                        ),
                        Icon(Icons.arrow_drop_down, color: theme.hintColor),
                      ],
                    ),
                  ),
                ),

                // Only show recurring options for Income/Expense, not Transfer
                if (_selectedTypeEnum != TransactionTypeEnum.transfer) ...[
                  const SizedBox(height: 16),

                  // Recurring Transaction Toggle
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: theme.hintColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: theme.hintColor.withValues(alpha: 0.3),
                      ),
                    ),
                    child: Row(
                      children: [
                        Icon(Icons.repeat, color: theme.hintColor, size: 20),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Recurring Transaction',
                                style: theme.textTheme.bodyMedium?.copyWith(
                                  fontWeight: FontWeight.w600,
                                  color: theme.hintColor,
                                ),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                'Mark as recurring to automatically create transactions',
                                style: theme.textTheme.bodySmall?.copyWith(
                                  color: theme.hintColor.withValues(alpha: 0.7),
                                ),
                              ),
                            ],
                          ),
                        ),
                        Switch(
                          value: _isRecurring,
                          onChanged: (value) {
                            setState(() {
                              _isRecurring = value;
                              if (!value) {
                                _endDate = null;
                                _hasEndDate = false;
                              }
                            });
                          },
                          activeColor: theme.colorScheme.primary,
                          inactiveTrackColor: theme.hintColor.withValues(
                            alpha: 0.3,
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Recurring Transaction Options (only show if recurring is enabled)
                  if (_isRecurring) ...[
                    const SizedBox(height: 16),

                    // Description field for recurring transactions
                    CustomTextField(
                      controller: _descriptionController,
                      labelText: 'Description',
                      hintText: 'Enter transaction description',
                      prefixIcon: Icons.description,
                      validator: (value) {
                        if (_isRecurring &&
                            (value == null || value.trim().isEmpty)) {
                          return 'Please enter a description';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),

                    // Frequency Selection
                    GestureDetector(
                      onTap: _showFrequencyDialog,
                      child: Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: theme.hintColor.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: theme.hintColor.withValues(alpha: 0.3),
                          ),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              Icons.calendar_today,
                              color: theme.hintColor,
                              size: 20,
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'Frequency',
                                    style: theme.textTheme.bodyMedium?.copyWith(
                                      fontWeight: FontWeight.w600,
                                      color: theme.hintColor,
                                    ),
                                  ),
                                  const SizedBox(height: 4),
                                  Text(
                                    _getFrequencyDisplayName(
                                      _selectedFrequency,
                                    ),
                                    style: theme.textTheme.bodyMedium,
                                  ),
                                ],
                              ),
                            ),
                            Icon(Icons.arrow_drop_down, color: theme.hintColor),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),

                    // End Date Toggle
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: theme.hintColor.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: theme.hintColor.withValues(alpha: 0.3),
                        ),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            Icons.event_busy,
                            color: theme.hintColor,
                            size: 20,
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Set End Date',
                                  style: theme.textTheme.bodyMedium?.copyWith(
                                    fontWeight: FontWeight.w600,
                                    color: theme.hintColor,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  'Optional: Set when recurring should stop',
                                  style: theme.textTheme.bodySmall?.copyWith(
                                    color: theme.hintColor.withValues(
                                      alpha: 0.7,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Switch(
                            value: _hasEndDate,
                            onChanged: (value) {
                              setState(() {
                                _hasEndDate = value;
                                if (!value) {
                                  _endDate = null;
                                }
                              });
                            },
                            activeColor: theme.colorScheme.primary,
                            inactiveTrackColor: theme.hintColor.withValues(
                              alpha: 0.3,
                            ),
                          ),
                        ],
                      ),
                    ),

                    // End Date Selection (only show if end date is enabled)
                    if (_hasEndDate) ...[
                      const SizedBox(height: 16),
                      GestureDetector(
                        onTap: _selectEndDate,
                        child: Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: theme.hintColor.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: theme.hintColor.withValues(alpha: 0.3),
                            ),
                          ),
                          child: Row(
                            children: [
                              Icon(
                                Icons.event_busy,
                                color: theme.hintColor,
                                size: 20,
                              ),
                              const SizedBox(width: 12),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'End Date',
                                      style: theme.textTheme.bodyMedium
                                          ?.copyWith(
                                            fontWeight: FontWeight.w600,
                                            color: theme.hintColor,
                                          ),
                                    ),
                                    const SizedBox(height: 4),
                                    Text(
                                      _endDate != null
                                          ? DateFormat(
                                            'MMM dd, yyyy',
                                          ).format(_endDate!)
                                          : 'Select end date',
                                      style: theme.textTheme.bodyMedium,
                                    ),
                                  ],
                                ),
                              ),
                              Icon(
                                Icons.arrow_drop_down,
                                color: theme.hintColor,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ],
                ],

                const SizedBox(height: 24),

                // Error message
                if (_errorMessage != null)
                  Container(
                    padding: const EdgeInsets.all(12),
                    margin: const EdgeInsets.only(bottom: 16),
                    decoration: BoxDecoration(
                      color: colorScheme.error.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: colorScheme.error),
                    ),
                    child: Text(
                      _errorMessage!,
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: colorScheme.error,
                      ),
                    ),
                  ),

                // Add Transaction button
                CustomButton.elevated(
                  onPressed: _isLoading ? null : _handleAddTransaction,
                  backgroundColor: typeColor,
                  child:
                      _isLoading
                          ? SizedBox(
                            height: 20,
                            width: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                _selectedTypeEnum ==
                                        TransactionTypeEnum.transfer
                                    ? Colors.white
                                    : colorScheme.onPrimary,
                              ),
                            ),
                          )
                          : Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                typeIcon,
                                color:
                                    _selectedTypeEnum ==
                                            TransactionTypeEnum.transfer
                                        ? Colors.white
                                        : colorScheme.onPrimary,
                              ),
                              const SizedBox(width: 8),
                              Text(
                                _selectedTypeEnum ==
                                        TransactionTypeEnum.transfer
                                    ? 'Transfer Money'
                                    : 'Add $typeText',
                                style: theme.textTheme.titleMedium?.copyWith(
                                  color:
                                      _selectedTypeEnum ==
                                              TransactionTypeEnum.transfer
                                          ? Colors.white
                                          : colorScheme.onPrimary,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                ),
                const SizedBox(height: 20),

                // Warning if no bank accounts
                if (_bankAccounts.isEmpty)
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: AppColorPalette.warning.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: AppColorPalette.warning.withValues(alpha: 0.3),
                      ),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Icons.warning_amber,
                          color: AppColorPalette.warning,
                          size: 20,
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Text(
                            'You need to add a bank account first before adding transactions.',
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: AppColorPalette.warning,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _showFrequencyDialog() {
    final theme = Theme.of(context);

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(
              'Select Frequency',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            content: SizedBox(
              width: double.maxFinite,
              child: ListView.builder(
                shrinkWrap: true,
                itemCount: RecurringFrequency.values.length,
                itemBuilder: (context, index) {
                  final frequency = RecurringFrequency.values[index];
                  return ListTile(
                    title: Text(
                      _getFrequencyDisplayName(frequency),
                      style: theme.textTheme.bodyMedium,
                    ),
                    onTap: () {
                      setState(() {
                        _selectedFrequency = frequency;
                      });
                      Navigator.of(context).pop();
                    },
                  );
                },
              ),
            ),
          ),
    );
  }

  Future<void> _selectEndDate() async {
    final pickedDate = await showDatePicker(
      context: context,
      initialDate: _endDate ?? DateTime.now().add(const Duration(days: 30)),
      firstDate: _selectedDate.add(
        const Duration(days: 1),
      ), // Must be after selected transaction date
      lastDate: DateTime.now().add(
        const Duration(days: 365 * 20),
      ), // Allow 20 years into future
    );

    if (pickedDate != null) {
      setState(() {
        _endDate = pickedDate;
      });
    }
  }

  String _getFrequencyDisplayName(RecurringFrequency frequency) {
    switch (frequency) {
      case RecurringFrequency.daily:
        return 'Daily';
      case RecurringFrequency.weekly:
        return 'Weekly';
      case RecurringFrequency.monthly:
        return 'Monthly';
      case RecurringFrequency.quarterly:
        return 'Quarterly';
      case RecurringFrequency.halfYearly:
        return 'Half Yearly';
      case RecurringFrequency.yearly:
        return 'Yearly';
    }
  }

  // New methods for transfer account dialogs
  void _showFromAccountDialog() {
    final theme = Theme.of(context);

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(
              'Select Source Account',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            content: SizedBox(
              width: double.maxFinite,
              child: ListView.builder(
                shrinkWrap: true,
                itemCount: _bankAccounts.length,
                itemBuilder:
                    (context, index) => ListTile(
                      title: Text(
                        _bankAccounts[index].bankName,
                        style: theme.textTheme.bodyMedium,
                      ),
                      subtitle: Text(
                        'A/C: ${_bankAccounts[index].accountNumber}',
                        style: theme.textTheme.bodySmall,
                      ),
                      onTap: () {
                        setState(() {
                          _selectedFromAccount = _bankAccounts[index];
                        });
                        Navigator.of(context).pop();
                      },
                    ),
              ),
            ),
          ),
    );
  }

  void _showToAccountDialog() {
    final theme = Theme.of(context);
    final availableAccounts =
        _bankAccounts
            .where((account) => account.id != _selectedFromAccount?.id)
            .toList();

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(
              'Select Destination Account',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            content: SizedBox(
              width: double.maxFinite,
              child:
                  availableAccounts.isEmpty
                      ? Text(
                        'No other accounts available for transfer',
                        style: theme.textTheme.bodyMedium,
                      )
                      : ListView.builder(
                        shrinkWrap: true,
                        itemCount: availableAccounts.length,
                        itemBuilder:
                            (context, index) => ListTile(
                              title: Text(
                                availableAccounts[index].bankName,
                                style: theme.textTheme.bodyMedium,
                              ),
                              subtitle: Text(
                                'A/C: ${availableAccounts[index].accountNumber}',
                                style: theme.textTheme.bodySmall,
                              ),
                              onTap: () {
                                setState(() {
                                  _selectedToAccount = availableAccounts[index];
                                });
                                Navigator.of(context).pop();
                              },
                            ),
                      ),
            ),
          ),
    );
  }
}

class _CategorySelectionDialog extends StatefulWidget {
  final List<Map<String, dynamic>> categories;
  final bool isIncome;
  final String typeText;
  final Color typeColor;
  final Function(String) onCategorySelected;
  final VoidCallback onAddCategory;

  const _CategorySelectionDialog({
    required this.categories,
    required this.isIncome,
    required this.typeText,
    required this.typeColor,
    required this.onCategorySelected,
    required this.onAddCategory,
  });

  @override
  State<_CategorySelectionDialog> createState() =>
      _CategorySelectionDialogState();
}

class _CategorySelectionDialogState extends State<_CategorySelectionDialog> {
  final TextEditingController _searchController = TextEditingController();
  List<Map<String, dynamic>> _filteredCategories = [];

  @override
  void initState() {
    super.initState();
    _filteredCategories = widget.categories;
    _searchController.addListener(_filterCategories);
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _filterCategories() {
    final query = _searchController.text.toLowerCase();
    setState(() {
      _filteredCategories =
          widget.categories
              .where(
                (category) =>
                    category['name'].toString().toLowerCase().contains(query),
              )
              .toList();
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return AlertDialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      title: Column(
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: widget.typeColor.withValues(alpha: 0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  widget.isIncome ? Icons.add_circle : Icons.remove_circle,
                  color: widget.typeColor,
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  'Select ${widget.typeText} Category',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: widget.typeColor,
                  ),
                ),
              ),
            ],
          ),
          if (widget.categories.isNotEmpty) ...[
            const SizedBox(height: 16),
            // Search field
            TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search categories...',
                prefixIcon: Icon(Icons.search, color: widget.typeColor),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: widget.typeColor, width: 2),
                ),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
              ),
            ),
          ],
        ],
      ),
      content: SizedBox(
        width: double.maxFinite,
        height: widget.categories.isEmpty ? 200 : 300,
        child:
            widget.categories.isEmpty
                ? _buildEmptyState(theme)
                : _filteredCategories.isEmpty
                ? _buildNoResultsState(theme)
                : _buildCategoryList(theme),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: Text('Cancel', style: TextStyle(color: theme.hintColor)),
        ),
        ElevatedButton.icon(
          onPressed: () {
            Navigator.of(context).pop();
            widget.onAddCategory();
          },
          icon: const Icon(Icons.add, size: 20),
          label: const Text('Add Category'),
          style: ElevatedButton.styleFrom(
            backgroundColor: widget.typeColor,
            foregroundColor: theme.colorScheme.onPrimary,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildEmptyState(ThemeData theme) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: widget.typeColor.withValues(alpha: 0.1),
            shape: BoxShape.circle,
          ),
          child: Icon(
            Icons.category_outlined,
            color: widget.typeColor,
            size: 48,
          ),
        ),
        const SizedBox(height: 16),
        Text(
          'No ${widget.typeText} Categories',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: widget.typeColor,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'Create your first ${widget.typeText.toLowerCase()} category to get started',
          textAlign: TextAlign.center,
          style: theme.textTheme.bodyMedium?.copyWith(color: theme.hintColor),
        ),
      ],
    );
  }

  Widget _buildNoResultsState(ThemeData theme) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(Icons.search_off, color: theme.hintColor, size: 48),
        const SizedBox(height: 16),
        Text(
          'No categories found',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: theme.hintColor,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'Try a different search term or add a new category',
          textAlign: TextAlign.center,
          style: theme.textTheme.bodyMedium?.copyWith(
            color: theme.hintColor.withValues(alpha: 0.7),
          ),
        ),
      ],
    );
  }

  Widget _buildCategoryList(ThemeData theme) {
    return ListView.builder(
      shrinkWrap: true,
      itemCount: _filteredCategories.length,
      itemBuilder: (context, index) {
        final category = _filteredCategories[index];
        final categoryName = category['name'].toString();

        final isInvestment = category['isInvestment'] ?? false;

        return Card(
          margin: const EdgeInsets.only(bottom: 6),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
              border:
                  isInvestment
                      ? Border.all(color: Colors.green.withValues(alpha: 0.3))
                      : null,
            ),
            child: ListTile(
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 12,
                vertical: 4,
              ),
              leading: Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: widget.typeColor.withValues(alpha: 0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  _getCategoryIcon(categoryName),
                  color: widget.typeColor,
                  size: 18,
                ),
              ),
              title: Row(
                children: [
                  Expanded(
                    child: Text(
                      categoryName,
                      style: theme.textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  if (isInvestment) ...[
                    const SizedBox(width: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 6,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.green.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.trending_up,
                            size: 10,
                            color: Colors.green,
                          ),
                          const SizedBox(width: 2),
                          // Text(
                          //   'Investment',
                          //   style: theme.textTheme.bodySmall?.copyWith(
                          //     color: Colors.green,
                          //     fontWeight: FontWeight.bold,
                          //     fontSize: 10,
                          //   ),
                          // ),
                        ],
                      ),
                    ),
                  ],
                ],
              ),
              trailing: Icon(
                Icons.arrow_forward_ios,
                color: theme.hintColor,
                size: 14,
              ),
              onTap: () {
                widget.onCategorySelected(categoryName);
                Navigator.of(context).pop();
              },
            ),
          ),
        );
      },
    );
  }

  IconData _getCategoryIcon(String categoryName) {
    final lowerCategory = categoryName.toLowerCase();

    if (lowerCategory.contains('food') ||
        lowerCategory.contains('restaurant') ||
        lowerCategory.contains('dining')) {
      return Icons.restaurant;
    } else if (lowerCategory.contains('transport') ||
        lowerCategory.contains('fuel') ||
        lowerCategory.contains('gas')) {
      return Icons.directions_car;
    } else if (lowerCategory.contains('shopping') ||
        lowerCategory.contains('clothes') ||
        lowerCategory.contains('fashion')) {
      return Icons.shopping_bag;
    } else if (lowerCategory.contains('entertainment') ||
        lowerCategory.contains('movie') ||
        lowerCategory.contains('game')) {
      return Icons.movie;
    } else if (lowerCategory.contains('health') ||
        lowerCategory.contains('medical') ||
        lowerCategory.contains('pharmacy')) {
      return Icons.local_hospital;
    } else if (lowerCategory.contains('education') ||
        lowerCategory.contains('course') ||
        lowerCategory.contains('book')) {
      return Icons.school;
    } else if (lowerCategory.contains('salary') ||
        lowerCategory.contains('income') ||
        lowerCategory.contains('job')) {
      return Icons.work;
    } else if (lowerCategory.contains('investment') ||
        lowerCategory.contains('dividend') ||
        lowerCategory.contains('profit')) {
      return Icons.trending_up;
    } else if (lowerCategory.contains('gift') ||
        lowerCategory.contains('bonus') ||
        lowerCategory.contains('reward')) {
      return Icons.card_giftcard;
    } else if (lowerCategory.contains('rent') ||
        lowerCategory.contains('home') ||
        lowerCategory.contains('house')) {
      return Icons.home;
    } else if (lowerCategory.contains('utility') ||
        lowerCategory.contains('electricity') ||
        lowerCategory.contains('water')) {
      return Icons.power;
    } else if (lowerCategory.contains('internet') ||
        lowerCategory.contains('phone') ||
        lowerCategory.contains('mobile')) {
      return Icons.wifi;
    } else {
      return Icons.category;
    }
  }
}
