import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../resources/app_theme.dart';
import '../../services/local_storage_service.dart';
import '../../services/recurring_transaction_service.dart';
import '../../features/models/recurring_transaction_model.dart';
import '../../features/models/bank_account_model.dart';

class RecurringTransactionsListScreen extends StatefulWidget {
  const RecurringTransactionsListScreen({super.key});

  @override
  State<RecurringTransactionsListScreen> createState() =>
      _RecurringTransactionsListScreenState();
}

class _RecurringTransactionsListScreenState
    extends State<RecurringTransactionsListScreen> {
  List<RecurringTransactionModel> _allRecurringTransactions = [];
  List<RecurringTransactionModel> _filteredRecurringTransactions = [];
  List<BankAccountModel> _bankAccounts = [];
  bool _isLoading = true;
  bool _showInactiveTransactions = false;
  RecurringFrequency? _selectedFrequencyFilter;
  String? _selectedBankAccountFilter;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    try {
      final recurringTransactions =
          await LocalStorageService.getRecurringTransactions();
      final bankAccounts = await LocalStorageService.getBankAccounts();

      setState(() {
        _allRecurringTransactions = recurringTransactions;
        _bankAccounts = bankAccounts;
        _isLoading = false;
      });

      _applyFilters();
    } catch (e) {
      print('Error loading recurring transactions: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _applyFilters() {
    setState(() {
      _filteredRecurringTransactions =
          _allRecurringTransactions.where((transaction) {
            // Filter by active status
            if (!_showInactiveTransactions && !transaction.isActive) {
              return false;
            }

            // Filter by frequency
            if (_selectedFrequencyFilter != null &&
                transaction.frequency != _selectedFrequencyFilter) {
              return false;
            }

            // Filter by bank account
            if (_selectedBankAccountFilter != null &&
                transaction.bankAccountId != _selectedBankAccountFilter) {
              return false;
            }

            return true;
          }).toList();

      // Sort by next due date (nearest first)
      _filteredRecurringTransactions.sort(
        (a, b) => a.nextDueDate.compareTo(b.nextDueDate),
      );
    });
  }

  Future<void> _toggleActiveStatus(
    RecurringTransactionModel transaction,
  ) async {
    try {
      await RecurringTransactionService.toggleRecurringTransactionStatus(
        transaction.id,
      );

      await _loadData();
      _showSnackBar(
        transaction.isActive
            ? 'Transaction deactivated'
            : 'Transaction activated',
      );
    } catch (e) {
      _showSnackBar('Error updating transaction status');
    }
  }

  Future<void> _editRecurringTransaction(
    RecurringTransactionModel transaction,
  ) async {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    final TextEditingController amountController = TextEditingController(
      text: transaction.amount.toString(),
    );
    DateTime selectedDate = transaction.nextDueDate;
    final GlobalKey<FormState> formKey = GlobalKey<FormState>();

    final result = await showDialog<bool>(
      context: context,
      builder:
          (context) => StatefulBuilder(
            builder:
                (context, setState) => AlertDialog(
                  title: Text(
                    'Edit Recurring Transaction',
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  content: SizedBox(
                    width: double.maxFinite,
                    child: Form(
                      key: formKey,
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Transaction Description (read-only)
                          Text(
                            'Transaction: ${transaction.description}',
                            style: theme.textTheme.bodyMedium?.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          const SizedBox(height: 16),

                          // Amount Field
                          Text(
                            'Amount',
                            style: theme.textTheme.bodyMedium?.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          const SizedBox(height: 8),
                          TextFormField(
                            controller: amountController,
                            keyboardType: const TextInputType.numberWithOptions(
                              decimal: true,
                            ),
                            decoration: InputDecoration(
                              hintText: 'Enter amount',
                              prefixText: '₹ ',
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                              contentPadding: const EdgeInsets.symmetric(
                                horizontal: 12,
                                vertical: 8,
                              ),
                            ),
                            validator: (value) {
                              if (value == null || value.trim().isEmpty) {
                                return 'Please enter amount';
                              }
                              final amount = double.tryParse(value.trim());
                              if (amount == null || amount <= 0) {
                                return 'Please enter a valid amount';
                              }
                              return null;
                            },
                          ),
                          const SizedBox(height: 16),

                          // Next Due Date Field
                          Text(
                            'Next Due Date',
                            style: theme.textTheme.bodyMedium?.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          const SizedBox(height: 8),
                          GestureDetector(
                            onTap: () async {
                              final picked = await showDatePicker(
                                context: context,
                                initialDate: selectedDate,
                                firstDate: DateTime.now(),
                                lastDate: DateTime.now().add(
                                  const Duration(days: 365 * 2),
                                ),
                              );
                              if (picked != null) {
                                setState(() {
                                  selectedDate = picked;
                                });
                              }
                            },
                            child: Container(
                              padding: const EdgeInsets.all(12),
                              decoration: BoxDecoration(
                                border: Border.all(
                                  color: theme.hintColor.withValues(alpha: 0.3),
                                ),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    DateFormat(
                                      'MMM dd, yyyy',
                                    ).format(selectedDate),
                                    style: theme.textTheme.bodyMedium,
                                  ),
                                  Icon(
                                    Icons.calendar_today,
                                    size: 20,
                                    color: theme.hintColor,
                                  ),
                                ],
                              ),
                            ),
                          ),
                          const SizedBox(height: 16),

                          // Frequency Info (read-only)
                          Container(
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: theme.hintColor.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Row(
                              children: [
                                Icon(
                                  _getFrequencyIcon(transaction.frequency),
                                  color: _getFrequencyColor(
                                    transaction.frequency,
                                  ),
                                  size: 20,
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  'Frequency: ${transaction.frequencyDisplayName}',
                                  style: theme.textTheme.bodyMedium?.copyWith(
                                    color: theme.hintColor,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.of(context).pop(false),
                      child: Text(
                        'Cancel',
                        style: theme.textTheme.labelLarge?.copyWith(
                          color: theme.hintColor,
                        ),
                      ),
                    ),
                    TextButton(
                      onPressed: () async {
                        if (formKey.currentState!.validate()) {
                          Navigator.of(context).pop(true);
                        }
                      },
                      child: Text(
                        'Update',
                        style: theme.textTheme.labelLarge?.copyWith(
                          color: theme.colorScheme.primary,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
          ),
    );

    if (result == true) {
      try {
        final amount = double.parse(amountController.text.trim());

        // Update the recurring transaction
        final updatedTransaction = transaction.copyWith(
          amount: amount,
          nextDueDate: selectedDate,
          updatedAt: DateTime.now(),
        );

        await LocalStorageService.updateRecurringTransaction(
          updatedTransaction,
        );

        await _loadData();
        _showSnackBar('Recurring transaction updated successfully');
      } catch (e) {
        _showSnackBar('Error updating recurring transaction: $e');
      }
    }
  }

  Future<void> _deleteRecurringTransaction(
    RecurringTransactionModel transaction,
  ) async {
    final theme = Theme.of(context);

    final confirmed = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(
              'Delete Recurring Transaction',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            content: Text(
              'Are you sure you want to delete "${transaction.description}"? This action cannot be undone.',
              style: theme.textTheme.bodyMedium,
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: Text(
                  'Cancel',
                  style: theme.textTheme.labelLarge?.copyWith(
                    color: theme.hintColor,
                  ),
                ),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                style: TextButton.styleFrom(
                  foregroundColor: theme.colorScheme.error,
                ),
                child: Text(
                  'Delete',
                  style: theme.textTheme.labelLarge?.copyWith(
                    color: theme.colorScheme.error,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
    );

    if (confirmed == true) {
      try {
        await RecurringTransactionService.deleteRecurringTransaction(
          transaction.id,
        );

        await _loadData();
        _showSnackBar('Recurring transaction deleted');
      } catch (e) {
        _showSnackBar('Error deleting recurring transaction');
      }
    }
  }

  void _showSnackBar(String message) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          message,
          style: theme.textTheme.bodyMedium?.copyWith(
            color: colorScheme.onPrimary,
          ),
        ),
        backgroundColor: theme.colorScheme.primary,
      ),
    );
  }

  String _getBankAccountName(String bankAccountId) {
    final bankAccount = _bankAccounts.firstWhere(
      (account) => account.id == bankAccountId,
      orElse:
          () => BankAccountModel(
            bankName: 'Unknown Bank',
            accountNumber: 'Unknown',
            initialAmount: 0.0,
            currentAmount: 0.0,
          ),
    );
    return bankAccount.bankName;
  }

  Color _getFrequencyColor(RecurringFrequency frequency) {
    final theme = Theme.of(context);
    switch (frequency) {
      case RecurringFrequency.daily:
        return theme.colorScheme.error;
      case RecurringFrequency.weekly:
        return AppColorPalette.warning;
      case RecurringFrequency.monthly:
        return theme.colorScheme.primary;
      case RecurringFrequency.quarterly:
        return theme.colorScheme.secondary;
      case RecurringFrequency.halfYearly:
        return theme.colorScheme.tertiary;
      case RecurringFrequency.yearly:
        return AppColorPalette.success;
    }
  }

  IconData _getFrequencyIcon(RecurringFrequency frequency) {
    switch (frequency) {
      case RecurringFrequency.daily:
        return Icons.calendar_today;
      case RecurringFrequency.weekly:
        return Icons.calendar_view_week;
      case RecurringFrequency.monthly:
        return Icons.calendar_view_month;
      case RecurringFrequency.quarterly:
        return Icons.date_range;
      case RecurringFrequency.halfYearly:
        return Icons.event_note;
      case RecurringFrequency.yearly:
        return Icons.calendar_month;
    }
  }

  String _getFrequencyDisplayName(RecurringFrequency frequency) {
    switch (frequency) {
      case RecurringFrequency.daily:
        return 'Daily';
      case RecurringFrequency.weekly:
        return 'Weekly';
      case RecurringFrequency.monthly:
        return 'Monthly';
      case RecurringFrequency.quarterly:
        return 'Quarterly';
      case RecurringFrequency.halfYearly:
        return 'Half Yearly';
      case RecurringFrequency.yearly:
        return 'Yearly';
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        title: Text(
          'Recurring Transactions',
          style: theme.appBarTheme.titleTextStyle?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios),
          onPressed: () => Navigator.of(context).pop(),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _showFilterDialog,
          ),
        ],
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : _filteredRecurringTransactions.isEmpty
              ? _buildEmptyState()
              : _buildRecurringTransactionsList(),
    );
  }

  Widget _buildEmptyState() {
    final theme = Theme.of(context);

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.repeat,
            size: 64,
            color: theme.hintColor.withValues(alpha: 0.5),
          ),
          const SizedBox(height: 16),
          Text(
            'No Recurring Transactions',
            style: theme.textTheme.titleMedium?.copyWith(
              color: theme.hintColor,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Add recurring transactions to automatically\ncreate transactions on due dates',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.hintColor.withValues(alpha: 0.7),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildRecurringTransactionsList() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _filteredRecurringTransactions.length,
      itemBuilder: (context, index) {
        final transaction = _filteredRecurringTransactions[index];
        return _buildRecurringTransactionCard(transaction);
      },
      // Add physics for better scrolling behavior
      physics: const AlwaysScrollableScrollPhysics(),
    );
  }

  Widget _buildRecurringTransactionCard(RecurringTransactionModel transaction) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final isOverdue = transaction.nextDueDate.isBefore(DateTime.now());
    final isDueToday =
        transaction.nextDueDate.day == DateTime.now().day &&
        transaction.nextDueDate.month == DateTime.now().month &&
        transaction.nextDueDate.year == DateTime.now().year;

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      color: theme.cardColor,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header row with icon, title, status, and amount
            Row(
              children: [
                // Frequency icon
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: _getFrequencyColor(
                      transaction.frequency,
                    ).withValues(alpha: 0.1),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    _getFrequencyIcon(transaction.frequency),
                    color: _getFrequencyColor(transaction.frequency),
                    size: 24,
                  ),
                ),
                const SizedBox(width: 12),
                // Title and status
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              transaction.description,
                              style: theme.textTheme.titleMedium?.copyWith(
                                fontWeight: FontWeight.w600,
                                color:
                                    transaction.isActive
                                        ? theme.textTheme.titleMedium?.color
                                        : theme.hintColor,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          if (!transaction.isActive) ...[
                            const SizedBox(width: 8),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 6,
                                vertical: 2,
                              ),
                              decoration: BoxDecoration(
                                color: theme.hintColor.withValues(alpha: 0.2),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Text(
                                'Inactive',
                                style: theme.textTheme.bodySmall?.copyWith(
                                  color: theme.hintColor,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ],
                        ],
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '₹${transaction.amount.toStringAsFixed(0)}',
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: theme.colorScheme.error,
                        ),
                      ),
                    ],
                  ),
                ),
                // Action buttons
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    IconButton(
                      icon: Icon(
                        Icons.edit,
                        size: 20,
                        color: theme.colorScheme.primary,
                      ),
                      onPressed: () => _editRecurringTransaction(transaction),
                      tooltip: 'Edit',
                    ),
                    IconButton(
                      icon: Icon(
                        transaction.isActive ? Icons.pause : Icons.play_arrow,
                        size: 20,
                        color:
                            transaction.isActive
                                ? AppColorPalette.warning
                                : AppColorPalette.success,
                      ),
                      onPressed: () => _toggleActiveStatus(transaction),
                      tooltip: transaction.isActive ? 'Pause' : 'Activate',
                    ),
                    IconButton(
                      icon: Icon(
                        Icons.delete,
                        size: 20,
                        color: theme.colorScheme.error,
                      ),
                      onPressed: () => _deleteRecurringTransaction(transaction),
                      tooltip: 'Delete',
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 12),
            // Bank account and category info
            Wrap(
              spacing: 16,
              runSpacing: 8,
              children: [
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.account_balance,
                      size: 16,
                      color: theme.hintColor,
                    ),
                    const SizedBox(width: 4),
                    Flexible(
                      child: Text(
                        _getBankAccountName(transaction.bankAccountId),
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.hintColor,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(Icons.category, size: 16, color: theme.hintColor),
                    const SizedBox(width: 4),
                    Flexible(
                      child: Text(
                        transaction.category,
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.hintColor,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 8),
            // Due date and frequency
            Row(
              children: [
                Icon(Icons.calendar_today, size: 16, color: theme.hintColor),
                const SizedBox(width: 4),
                Expanded(
                  child: Text(
                    'Next due: ${DateFormat('MMM dd, yyyy').format(transaction.nextDueDate)}',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color:
                          isOverdue
                              ? theme.colorScheme.error
                              : isDueToday
                              ? AppColorPalette.warning
                              : theme.hintColor,
                      fontWeight:
                          isOverdue || isDueToday
                              ? FontWeight.w600
                              : FontWeight.normal,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                const SizedBox(width: 8),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 6,
                    vertical: 2,
                  ),
                  decoration: BoxDecoration(
                    color: _getFrequencyColor(
                      transaction.frequency,
                    ).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    transaction.frequencyDisplayName,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: _getFrequencyColor(transaction.frequency),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _showFilterDialog() {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    showDialog(
      context: context,
      builder:
          (context) => StatefulBuilder(
            builder:
                (context, setState) => AlertDialog(
                  title: Text(
                    'Filter Recurring Transactions',
                    style: theme.textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  content: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Show inactive transactions toggle
                      SwitchListTile(
                        title: Text(
                          'Show Inactive',
                          style: theme.textTheme.bodyMedium,
                        ),
                        subtitle: Text(
                          'Include inactive recurring transactions',
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.hintColor,
                          ),
                        ),
                        value: _showInactiveTransactions,
                        onChanged: (value) {
                          setState(() {
                            _showInactiveTransactions = value;
                          });
                        },
                        activeColor: theme.colorScheme.primary,
                        inactiveTrackColor: theme.hintColor.withValues(
                          alpha: 0.3,
                        ),
                      ),
                      const Divider(),
                      // Frequency filter
                      DropdownButtonFormField<RecurringFrequency?>(
                        decoration: const InputDecoration(
                          labelText: 'Frequency',
                          border: OutlineInputBorder(),
                        ),
                        value: _selectedFrequencyFilter,
                        items: [
                          DropdownMenuItem(
                            value: null,
                            child: Text(
                              'All Frequencies',
                              style: theme.textTheme.bodyMedium,
                            ),
                          ),
                          ...RecurringFrequency.values.map(
                            (frequency) =>
                                DropdownMenuItem<RecurringFrequency?>(
                                  value: frequency,
                                  child: Text(
                                    _getFrequencyDisplayName(frequency),
                                    style: theme.textTheme.bodyMedium,
                                  ),
                                ),
                          ),
                        ],
                        onChanged: (value) {
                          setState(() {
                            _selectedFrequencyFilter = value;
                          });
                        },
                      ),
                      const SizedBox(height: 16),
                      // Bank account filter
                      DropdownButtonFormField<String?>(
                        decoration: const InputDecoration(
                          labelText: 'Bank Account',
                          border: OutlineInputBorder(),
                        ),
                        value: _selectedBankAccountFilter,
                        items: [
                          DropdownMenuItem(
                            value: null,
                            child: Text(
                              'All Bank Accounts',
                              style: theme.textTheme.bodyMedium,
                            ),
                          ),
                          ..._bankAccounts.map(
                            (account) => DropdownMenuItem(
                              value: account.id,
                              child: Text(
                                account.bankName,
                                style: theme.textTheme.bodyMedium,
                              ),
                            ),
                          ),
                        ],
                        onChanged: (value) {
                          setState(() {
                            _selectedBankAccountFilter = value;
                          });
                        },
                      ),
                    ],
                  ),
                  actions: [
                    TextButton(
                      onPressed: () {
                        setState(() {
                          _showInactiveTransactions = false;
                          _selectedFrequencyFilter = null;
                          _selectedBankAccountFilter = null;
                        });
                      },
                      child: Text(
                        'Clear All',
                        style: theme.textTheme.labelLarge?.copyWith(
                          color: theme.hintColor,
                        ),
                      ),
                    ),
                    TextButton(
                      onPressed: () => Navigator.of(context).pop(),
                      child: Text(
                        'Cancel',
                        style: theme.textTheme.labelLarge?.copyWith(
                          color: theme.hintColor,
                        ),
                      ),
                    ),
                    ElevatedButton(
                      onPressed: () {
                        Navigator.of(context).pop();
                        _applyFilters();
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: theme.colorScheme.primary,
                        foregroundColor: colorScheme.onPrimary,
                      ),
                      child: Text(
                        'Apply',
                        style: theme.textTheme.labelLarge?.copyWith(
                          color: colorScheme.onPrimary,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
          ),
    );
  }
}
