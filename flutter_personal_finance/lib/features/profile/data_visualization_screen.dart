import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:go_router/go_router.dart';
import 'package:fl_chart/fl_chart.dart';
import '../../resources/app_theme.dart';
import '../../services/local_storage_service.dart';
import '../../features/models/transaction_model.dart';

class DataVisualizationScreen extends StatefulWidget {
  const DataVisualizationScreen({super.key});

  @override
  State<DataVisualizationScreen> createState() =>
      _DataVisualizationScreenState();
}

class _DataVisualizationScreenState extends State<DataVisualizationScreen> {
  List<TransactionModel> _allTransactions = [];
  bool _isLoading = true;
  String _selectedPeriod = 'last6months'; // last6months, last12months, thisyear
  String _selectedChartType =
      'overview'; // overview, categories, trends, cashflow

  // Chart data
  List<PieChartSectionData> _categoryPieData = [];
  List<BarChartGroupData> _monthlyBarData = [];
  List<FlSpot> _trendLineData = [];
  List<FlSpot> _cashFlowData = [];
  Map<String, List<FlSpot>> _categoryTrendData = {};

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() => _isLoading = true);

    try {
      final transactions = await LocalStorageService.getTransactions();

      setState(() {
        _allTransactions = transactions;
      });

      await _generateChartData();
    } catch (e) {
      print('Error loading visualization data: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _generateChartData() async {
    try {
      _generateCategoryPieChart();
      _generateMonthlyBarChart();
      _generateTrendLineChart();
      _generateCashFlowChart();
      _generateCategoryTrendChart();
    } catch (e) {
      print('Error generating chart data: $e');
      // Initialize empty data on error to prevent crashes
      setState(() {
        _categoryPieData = [];
        _monthlyBarData = [];
        _trendLineData = [];
        _cashFlowData = [];
        _categoryTrendData = {};
      });
    }
  }

  void _generateCategoryPieChart() {
    final categoryTotals = <String, double>{};
    final DateTime cutoffDate = _getCutoffDate();

    // Calculate category totals for the selected period
    for (final transaction in _allTransactions) {
      if (transaction.type == TransactionType.debit &&
          transaction.date.isAfter(cutoffDate)) {
        categoryTotals[transaction.category] =
            (categoryTotals[transaction.category] ?? 0) + transaction.amount;
      }
    }

    // Convert to pie chart data
    final pieData = <PieChartSectionData>[];
    final colors = [
      AppColorPalette.primary,
      AppColorPalette.success,
      AppColorPalette.warning,
      Colors.purple,
      Colors.orange,
      Colors.teal,
      Colors.indigo,
      Colors.pink,
    ];

    final sortedCategories =
        categoryTotals.entries.toList()
          ..sort((a, b) => b.value.compareTo(a.value));

    final total = categoryTotals.values.fold(0.0, (sum, value) => sum + value);

    for (int i = 0; i < sortedCategories.length && i < 8; i++) {
      final entry = sortedCategories[i];
      final percentage = total > 0 ? (entry.value / total) * 100 : 0;

      pieData.add(
        PieChartSectionData(
          color: colors[i % colors.length],
          value: entry.value,
          title: '${percentage.toStringAsFixed(1)}%',
          radius: 100,
          titleStyle: const TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
      );
    }

    setState(() => _categoryPieData = pieData);
  }

  void _generateMonthlyBarChart() {
    final monthlyIncome = <String, double>{};
    final monthlyExpenses = <String, double>{};
    final DateTime cutoffDate = _getCutoffDate();

    // Calculate monthly totals
    for (final transaction in _allTransactions) {
      if (transaction.date.isAfter(cutoffDate)) {
        final monthKey = DateFormat('yyyy-MM').format(transaction.date);

        if (transaction.type == TransactionType.credit) {
          monthlyIncome[monthKey] =
              (monthlyIncome[monthKey] ?? 0) + transaction.amount;
        } else {
          monthlyExpenses[monthKey] =
              (monthlyExpenses[monthKey] ?? 0) + transaction.amount;
        }
      }
    }

    // Get all months in range
    final allMonths = <String>{};
    allMonths.addAll(monthlyIncome.keys);
    allMonths.addAll(monthlyExpenses.keys);
    final sortedMonths = allMonths.toList()..sort();

    // Convert to bar chart data
    final barData = <BarChartGroupData>[];

    for (int i = 0; i < sortedMonths.length; i++) {
      final month = sortedMonths[i];
      final income = monthlyIncome[month] ?? 0;
      final expenses = monthlyExpenses[month] ?? 0;

      barData.add(
        BarChartGroupData(
          x: i,
          barRods: [
            BarChartRodData(
              toY: income,
              color: AppColorPalette.success,
              width: 15,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(4),
                topRight: Radius.circular(4),
              ),
            ),
            BarChartRodData(
              toY: expenses,
              color: Colors.red,
              width: 15,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(4),
                topRight: Radius.circular(4),
              ),
            ),
          ],
        ),
      );
    }

    setState(() => _monthlyBarData = barData);
  }

  void _generateTrendLineChart() {
    final monthlyNetFlow = <String, double>{};
    final DateTime cutoffDate = _getCutoffDate();

    // Calculate monthly net flow (income - expenses)
    for (final transaction in _allTransactions) {
      if (transaction.date.isAfter(cutoffDate)) {
        final monthKey = DateFormat('yyyy-MM').format(transaction.date);

        if (transaction.type == TransactionType.credit) {
          monthlyNetFlow[monthKey] =
              (monthlyNetFlow[monthKey] ?? 0) + transaction.amount;
        } else {
          monthlyNetFlow[monthKey] =
              (monthlyNetFlow[monthKey] ?? 0) - transaction.amount;
        }
      }
    }

    // Convert to line chart data
    final lineData = <FlSpot>[];
    final sortedMonths = monthlyNetFlow.keys.toList()..sort();

    for (int i = 0; i < sortedMonths.length; i++) {
      final netFlow = monthlyNetFlow[sortedMonths[i]] ?? 0;
      lineData.add(FlSpot(i.toDouble(), netFlow));
    }

    setState(() => _trendLineData = lineData);
  }

  void _generateCashFlowChart() {
    final dailyFlow = <String, double>{};
    final DateTime last30Days = DateTime.now().subtract(
      const Duration(days: 30),
    );

    // Calculate daily cash flow for last 30 days
    for (final transaction in _allTransactions) {
      if (transaction.date.isAfter(last30Days)) {
        final dayKey = DateFormat('yyyy-MM-dd').format(transaction.date);

        if (transaction.type == TransactionType.credit) {
          dailyFlow[dayKey] = (dailyFlow[dayKey] ?? 0) + transaction.amount;
        } else {
          dailyFlow[dayKey] = (dailyFlow[dayKey] ?? 0) - transaction.amount;
        }
      }
    }

    // Convert to line chart data
    final flowData = <FlSpot>[];
    final sortedDays = dailyFlow.keys.toList()..sort();

    for (int i = 0; i < sortedDays.length; i++) {
      final flow = dailyFlow[sortedDays[i]] ?? 0;
      flowData.add(FlSpot(i.toDouble(), flow));
    }

    setState(() => _cashFlowData = flowData);
  }

  void _generateCategoryTrendChart() {
    final categoryMonthlyData = <String, Map<String, double>>{};
    final DateTime cutoffDate = _getCutoffDate();

    // Calculate monthly spending by category
    for (final transaction in _allTransactions) {
      if (transaction.type == TransactionType.debit &&
          transaction.date.isAfter(cutoffDate)) {
        final monthKey = DateFormat('yyyy-MM').format(transaction.date);

        categoryMonthlyData[transaction.category] =
            categoryMonthlyData[transaction.category] ?? {};
        categoryMonthlyData[transaction.category]![monthKey] =
            (categoryMonthlyData[transaction.category]![monthKey] ?? 0) +
            transaction.amount;
      }
    }

    // Convert to line chart data for top 5 categories
    final sortedCategories =
        categoryMonthlyData.entries.toList()..sort((a, b) {
          final aTotal = a.value.values.fold(0.0, (sum, val) => sum + val);
          final bTotal = b.value.values.fold(0.0, (sum, val) => sum + val);
          return bTotal.compareTo(aTotal);
        });

    final allMonths = <String>{};
    for (final category in categoryMonthlyData.values) {
      allMonths.addAll(category.keys);
    }
    final sortedMonths = allMonths.toList()..sort();

    final trendData = <String, List<FlSpot>>{};

    for (int i = 0; i < sortedCategories.length && i < 5; i++) {
      final categoryName = sortedCategories[i].key;
      final categoryData = sortedCategories[i].value;
      final spots = <FlSpot>[];

      for (int j = 0; j < sortedMonths.length; j++) {
        final amount = categoryData[sortedMonths[j]] ?? 0;
        spots.add(FlSpot(j.toDouble(), amount));
      }

      trendData[categoryName] = spots;
    }

    setState(() => _categoryTrendData = trendData);
  }

  DateTime _getCutoffDate() {
    final now = DateTime.now();
    switch (_selectedPeriod) {
      case 'last6months':
        // For exactly 6 months of data, we need to go back 5 months
        // This ensures we get current month + 5 previous months = 6 months total
        // DateTime constructor handles negative months automatically
        return DateTime(now.year, now.month - 5, 1);
      case 'last12months':
        // For exactly 12 months of data, we need to go back 11 months
        // This ensures we get current month + 11 previous months = 12 months total
        return DateTime(now.year, now.month - 11, 1);
      case 'thisyear':
        return DateTime(now.year, 1, 1);
      default:
        return DateTime(now.year, now.month - 5, 1);
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        title: Text(
          'Analysis',
          style: theme.appBarTheme.titleTextStyle?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios),
          onPressed: () => context.pop(),
        ),
        actions: [
          IconButton(icon: const Icon(Icons.refresh), onPressed: _loadData),
        ],
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : Column(
                children: [
                  _buildControlsSection(),
                  Expanded(
                    child: RefreshIndicator(
                      onRefresh: _loadData,
                      child: SingleChildScrollView(
                        padding: const EdgeInsets.all(16),
                        child: _buildSelectedChart(),
                      ),
                    ),
                  ),
                ],
              ),
    );
  }

  Widget _buildControlsSection() {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      padding: const EdgeInsets.all(16),
      color: colorScheme.primary.withValues(alpha: 0.1),
      child: Column(
        children: [
          // Period selector
          Row(
            children: [
              Text(
                'Period: ',
                style: theme.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              Expanded(
                child: SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Row(
                    children: [
                      _buildPeriodChip('last6months', 'Last 6 Months'),
                      _buildPeriodChip('last12months', 'Last 12 Months'),
                      _buildPeriodChip('thisyear', 'This Year'),
                    ],
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          // Chart type selector
          Row(
            children: [
              Text(
                'Chart: ',
                style: theme.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              Expanded(
                child: SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Row(
                    children: [
                      _buildChartTypeChip(
                        'overview',
                        'Overview',
                        Icons.dashboard,
                      ),
                      _buildChartTypeChip(
                        'categories',
                        'Categories',
                        Icons.pie_chart,
                      ),
                      _buildChartTypeChip(
                        'trends',
                        'Trends',
                        Icons.trending_up,
                      ),
                      _buildChartTypeChip(
                        'cashflow',
                        'Cash Flow',
                        Icons.waterfall_chart,
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPeriodChip(String value, String label) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final isSelected = _selectedPeriod == value;

    return GestureDetector(
      onTap: () {
        setState(() => _selectedPeriod = value);
        _generateChartData();
      },
      child: Container(
        margin: const EdgeInsets.only(right: 8),
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: isSelected ? colorScheme.primary : Colors.transparent,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: isSelected ? colorScheme.primary : theme.hintColor,
          ),
        ),
        child: Text(
          label,
          style: theme.textTheme.bodySmall?.copyWith(
            color: isSelected ? colorScheme.onPrimary : theme.hintColor,
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
          ),
        ),
      ),
    );
  }

  Widget _buildChartTypeChip(String value, String label, IconData icon) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final isSelected = _selectedChartType == value;

    return GestureDetector(
      onTap: () {
        setState(() => _selectedChartType = value);
      },
      child: Container(
        margin: const EdgeInsets.only(right: 8),
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: isSelected ? colorScheme.secondary : Colors.transparent,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: isSelected ? colorScheme.secondary : theme.hintColor,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 16,
              color: isSelected ? colorScheme.onSecondary : theme.hintColor,
            ),
            const SizedBox(width: 4),
            Text(
              label,
              style: theme.textTheme.bodySmall?.copyWith(
                color: isSelected ? colorScheme.onSecondary : theme.hintColor,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSelectedChart() {
    try {
      switch (_selectedChartType) {
        case 'overview':
          return _buildOverviewCharts();
        case 'categories':
          return _buildCategoryCharts();
        case 'trends':
          return _buildTrendCharts();
        case 'cashflow':
          return _buildCashFlowCharts();
        default:
          return _buildOverviewCharts();
      }
    } catch (e) {
      print('Error building chart: $e');
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 48,
              color: Theme.of(context).hintColor,
            ),
            const SizedBox(height: 16),
            Text(
              'Unable to load chart data',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).hintColor,
              ),
            ),
            const SizedBox(height: 8),
            ElevatedButton(onPressed: _loadData, child: const Text('Retry')),
          ],
        ),
      );
    }
  }

  Widget _buildOverviewCharts() {
    return Column(
      children: [
        _buildIncomeExpenseBarChart(),
        const SizedBox(height: 20),
        _buildNetFlowTrendChart(),
        const SizedBox(height: 20),
        _buildQuickStats(),
      ],
    );
  }

  Widget _buildIncomeExpenseBarChart() {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: theme.cardColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: theme.shadowColor.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.bar_chart, color: AppColorPalette.primary, size: 24),
              const SizedBox(width: 12),
              Text(
                'Monthly Income vs Expenses',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          // Legend
          Row(
            children: [
              Container(
                width: 16,
                height: 16,
                decoration: BoxDecoration(
                  color: AppColorPalette.success,
                  borderRadius: BorderRadius.circular(4),
                ),
              ),
              const SizedBox(width: 8),
              Text('Income', style: theme.textTheme.bodySmall),
              const SizedBox(width: 20),
              Container(
                width: 16,
                height: 16,
                decoration: BoxDecoration(
                  color: Colors.red,
                  borderRadius: BorderRadius.circular(4),
                ),
              ),
              const SizedBox(width: 8),
              Text('Expenses', style: theme.textTheme.bodySmall),
            ],
          ),
          const SizedBox(height: 16),
          SizedBox(
            height: 300,
            child:
                _monthlyBarData.isEmpty
                    ? Center(
                      child: Text(
                        'No data available',
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: theme.hintColor,
                        ),
                      ),
                    )
                    : BarChart(
                      BarChartData(
                        alignment: BarChartAlignment.spaceAround,
                        maxY: _getMaxBarValue() * 1.2,
                        barTouchData: BarTouchData(
                          touchTooltipData: BarTouchTooltipData(
                            tooltipBgColor: theme.cardColor,
                            getTooltipItem: (group, groupIndex, rod, rodIndex) {
                              final isIncome = rodIndex == 0;
                              return BarTooltipItem(
                                '${isIncome ? 'Income' : 'Expenses'}\n₹${NumberFormat('#,##,###').format(rod.toY)}',
                                theme.textTheme.bodySmall ?? const TextStyle(),
                              );
                            },
                          ),
                        ),
                        titlesData: FlTitlesData(
                          show: true,
                          bottomTitles: AxisTitles(
                            sideTitles: SideTitles(
                              showTitles: true,
                              getTitlesWidget: (value, meta) {
                                // Show abbreviated month names
                                if (value.toInt() < _monthlyBarData.length) {
                                  return Text(
                                    'M${value.toInt() + 1}',
                                    style: theme.textTheme.bodySmall,
                                  );
                                }
                                return const Text('');
                              },
                            ),
                          ),
                          leftTitles: AxisTitles(
                            sideTitles: SideTitles(
                              showTitles: true,
                              reservedSize: 60,
                              getTitlesWidget: (value, meta) {
                                return Text(
                                  '₹${_formatCurrency(value)}',
                                  style: theme.textTheme.bodySmall,
                                );
                              },
                            ),
                          ),
                          topTitles: const AxisTitles(
                            sideTitles: SideTitles(showTitles: false),
                          ),
                          rightTitles: const AxisTitles(
                            sideTitles: SideTitles(showTitles: false),
                          ),
                        ),
                        gridData: FlGridData(
                          show: true,
                          drawVerticalLine: false,
                          horizontalInterval: (_getMaxBarValue() / 5).clamp(
                            1.0,
                            double.infinity,
                          ),
                          getDrawingHorizontalLine: (value) {
                            return FlLine(
                              color: theme.hintColor.withValues(alpha: 0.2),
                              strokeWidth: 1,
                            );
                          },
                        ),
                        borderData: FlBorderData(show: false),
                        barGroups: _monthlyBarData,
                      ),
                    ),
          ),
        ],
      ),
    );
  }

  Widget _buildNetFlowTrendChart() {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: theme.cardColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: theme.shadowColor.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.trending_up, color: AppColorPalette.primary, size: 24),
              const SizedBox(width: 12),
              Text(
                'Net Cash Flow Trend',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          SizedBox(
            height: 250,
            child:
                _trendLineData.isEmpty
                    ? Center(
                      child: Text(
                        'No trend data available',
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: theme.hintColor,
                        ),
                      ),
                    )
                    : LineChart(
                      LineChartData(
                        gridData: FlGridData(
                          show: true,
                          drawVerticalLine: false,
                          horizontalInterval: (_getMaxLineValue() / 5).clamp(
                            1.0,
                            double.infinity,
                          ),
                          getDrawingHorizontalLine: (value) {
                            return FlLine(
                              color: theme.hintColor.withValues(alpha: 0.2),
                              strokeWidth: 1,
                            );
                          },
                        ),
                        titlesData: FlTitlesData(
                          leftTitles: AxisTitles(
                            sideTitles: SideTitles(
                              showTitles: true,
                              reservedSize: 60,
                              getTitlesWidget: (value, meta) {
                                return Text(
                                  '₹${_formatCurrency(value)}',
                                  style: theme.textTheme.bodySmall,
                                );
                              },
                            ),
                          ),
                          bottomTitles: AxisTitles(
                            sideTitles: SideTitles(
                              showTitles: true,
                              getTitlesWidget: (value, meta) {
                                return Text(
                                  'M${value.toInt() + 1}',
                                  style: theme.textTheme.bodySmall,
                                );
                              },
                            ),
                          ),
                          topTitles: const AxisTitles(
                            sideTitles: SideTitles(showTitles: false),
                          ),
                          rightTitles: const AxisTitles(
                            sideTitles: SideTitles(showTitles: false),
                          ),
                        ),
                        borderData: FlBorderData(show: false),
                        lineBarsData: [
                          LineChartBarData(
                            spots: _trendLineData,
                            isCurved: true,
                            color: AppColorPalette.primary,
                            barWidth: 3,
                            belowBarData: BarAreaData(
                              show: true,
                              color: AppColorPalette.primary.withValues(
                                alpha: 0.2,
                              ),
                            ),
                            dotData: FlDotData(
                              show: true,
                              getDotPainter: (spot, percent, barData, index) {
                                return FlDotCirclePainter(
                                  radius: 4,
                                  color: AppColorPalette.primary,
                                  strokeWidth: 2,
                                  strokeColor: theme.cardColor,
                                );
                              },
                            ),
                          ),
                        ],
                        lineTouchData: LineTouchData(
                          touchTooltipData: LineTouchTooltipData(
                            tooltipBgColor: theme.cardColor,
                            getTooltipItems: (List<LineBarSpot> touchedSpots) {
                              return touchedSpots.map((
                                LineBarSpot touchedSpot,
                              ) {
                                return LineTooltipItem(
                                  'Net Flow\n₹${NumberFormat('#,##,###').format(touchedSpot.y)}',
                                  theme.textTheme.bodySmall ??
                                      const TextStyle(),
                                );
                              }).toList();
                            },
                          ),
                        ),
                      ),
                    ),
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryCharts() {
    return Column(
      children: [
        _buildCategoryPieChart(),
        const SizedBox(height: 20),
        _buildCategoryTrendLines(),
      ],
    );
  }

  Widget _buildCategoryPieChart() {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: theme.cardColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: theme.shadowColor.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.pie_chart, color: AppColorPalette.warning, size: 24),
              const SizedBox(width: 12),
              Text(
                'Expense Categories Distribution',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          SizedBox(
            height: 300,
            child:
                _categoryPieData.isEmpty
                    ? Center(
                      child: Text(
                        'No category data available',
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: theme.hintColor,
                        ),
                      ),
                    )
                    : Row(
                      children: [
                        Expanded(
                          flex: 2,
                          child: PieChart(
                            PieChartData(
                              sections: _categoryPieData,
                              centerSpaceRadius: 40,
                              sectionsSpace: 2,
                              pieTouchData: PieTouchData(
                                touchCallback: (
                                  FlTouchEvent event,
                                  pieTouchResponse,
                                ) {
                                  // Handle touch interactions if needed
                                },
                              ),
                            ),
                          ),
                        ),
                        Expanded(child: _buildPieLegend()),
                      ],
                    ),
          ),
        ],
      ),
    );
  }

  Widget _buildPieLegend() {
    final theme = Theme.of(context);
    final colors = [
      AppColorPalette.primary,
      AppColorPalette.success,
      AppColorPalette.warning,
      Colors.purple,
      Colors.orange,
      Colors.teal,
      Colors.indigo,
      Colors.pink,
    ];

    // Get category names and values for legend
    final categoryTotals = <String, double>{};
    final DateTime cutoffDate = _getCutoffDate();

    for (final transaction in _allTransactions) {
      if (transaction.type == TransactionType.debit &&
          transaction.date.isAfter(cutoffDate)) {
        categoryTotals[transaction.category] =
            (categoryTotals[transaction.category] ?? 0) + transaction.amount;
      }
    }

    final sortedCategories =
        categoryTotals.entries.toList()
          ..sort((a, b) => b.value.compareTo(a.value));

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.center,
      children:
          sortedCategories.take(8).toList().asMap().entries.map((entry) {
            final index = entry.key;
            final categoryEntry = entry.value;

            return Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: Row(
                children: [
                  Container(
                    width: 12,
                    height: 12,
                    decoration: BoxDecoration(
                      color: colors[index % colors.length],
                      shape: BoxShape.circle,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      categoryEntry.key,
                      style: theme.textTheme.bodySmall,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
    );
  }

  Widget _buildCategoryTrendLines() {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: theme.cardColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: theme.shadowColor.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.multiline_chart,
                color: AppColorPalette.success,
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                'Category Spending Trends',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          SizedBox(
            height: 300,
            child:
                _categoryTrendData.isEmpty
                    ? Center(
                      child: Text(
                        'No category trend data available',
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: theme.hintColor,
                        ),
                      ),
                    )
                    : LineChart(
                      LineChartData(
                        gridData: FlGridData(
                          show: true,
                          drawVerticalLine: false,
                          getDrawingHorizontalLine: (value) {
                            return FlLine(
                              color: theme.hintColor.withValues(alpha: 0.2),
                              strokeWidth: 1,
                            );
                          },
                        ),
                        titlesData: FlTitlesData(
                          leftTitles: AxisTitles(
                            sideTitles: SideTitles(
                              showTitles: true,
                              reservedSize: 60,
                              getTitlesWidget: (value, meta) {
                                return Text(
                                  '₹${_formatCurrency(value)}',
                                  style: theme.textTheme.bodySmall,
                                );
                              },
                            ),
                          ),
                          bottomTitles: AxisTitles(
                            sideTitles: SideTitles(
                              showTitles: true,
                              getTitlesWidget: (value, meta) {
                                return Text(
                                  'M${value.toInt() + 1}',
                                  style: theme.textTheme.bodySmall,
                                );
                              },
                            ),
                          ),
                          topTitles: const AxisTitles(
                            sideTitles: SideTitles(showTitles: false),
                          ),
                          rightTitles: const AxisTitles(
                            sideTitles: SideTitles(showTitles: false),
                          ),
                        ),
                        borderData: FlBorderData(show: false),
                        lineBarsData:
                            _categoryTrendData.entries
                                .toList()
                                .asMap()
                                .entries
                                .map((entry) {
                                  final index = entry.key;
                                  final categoryData = entry.value;
                                  final colors = [
                                    AppColorPalette.primary,
                                    AppColorPalette.success,
                                    AppColorPalette.warning,
                                    Colors.purple,
                                    Colors.orange,
                                  ];

                                  return LineChartBarData(
                                    spots: categoryData.value,
                                    isCurved: true,
                                    color: colors[index % colors.length],
                                    barWidth: 2,
                                    dotData: FlDotData(show: false),
                                  );
                                })
                                .toList(),
                      ),
                    ),
          ),
        ],
      ),
    );
  }

  Widget _buildTrendCharts() {
    return _buildNetFlowTrendChart();
  }

  Widget _buildCashFlowCharts() {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: theme.cardColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: theme.shadowColor.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.waterfall_chart,
                color: AppColorPalette.primary,
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                'Daily Cash Flow (Last 30 Days)',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          SizedBox(
            height: 300,
            child:
                _cashFlowData.isEmpty
                    ? Center(
                      child: Text(
                        'No cash flow data available',
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: theme.hintColor,
                        ),
                      ),
                    )
                    : LineChart(
                      LineChartData(
                        gridData: FlGridData(
                          show: true,
                          drawVerticalLine: false,
                          getDrawingHorizontalLine: (value) {
                            return FlLine(
                              color:
                                  value == 0
                                      ? theme.hintColor.withValues(alpha: 0.5)
                                      : theme.hintColor.withValues(alpha: 0.2),
                              strokeWidth: value == 0 ? 2 : 1,
                            );
                          },
                        ),
                        titlesData: FlTitlesData(
                          leftTitles: AxisTitles(
                            sideTitles: SideTitles(
                              showTitles: true,
                              reservedSize: 60,
                              getTitlesWidget: (value, meta) {
                                return Text(
                                  '₹${_formatCurrency(value)}',
                                  style: theme.textTheme.bodySmall,
                                );
                              },
                            ),
                          ),
                          bottomTitles: AxisTitles(
                            sideTitles: SideTitles(
                              showTitles: true,
                              interval: 5,
                              getTitlesWidget: (value, meta) {
                                return Text(
                                  'D${value.toInt() + 1}',
                                  style: theme.textTheme.bodySmall,
                                );
                              },
                            ),
                          ),
                          topTitles: const AxisTitles(
                            sideTitles: SideTitles(showTitles: false),
                          ),
                          rightTitles: const AxisTitles(
                            sideTitles: SideTitles(showTitles: false),
                          ),
                        ),
                        borderData: FlBorderData(show: false),
                        lineBarsData: [
                          LineChartBarData(
                            spots: _cashFlowData,
                            isCurved: false,
                            color: AppColorPalette.primary,
                            barWidth: 2,
                            belowBarData: BarAreaData(
                              show: true,
                              color: AppColorPalette.primary.withValues(
                                alpha: 0.1,
                              ),
                              cutOffY: 0,
                              applyCutOffY: true,
                            ),
                            aboveBarData: BarAreaData(
                              show: true,
                              color: AppColorPalette.success.withValues(
                                alpha: 0.1,
                              ),
                              cutOffY: 0,
                              applyCutOffY: true,
                            ),
                            dotData: FlDotData(
                              show: true,
                              getDotPainter: (spot, percent, barData, index) {
                                final color =
                                    spot.y >= 0
                                        ? AppColorPalette.success
                                        : Colors.red;
                                return FlDotCirclePainter(
                                  radius: 3,
                                  color: color,
                                  strokeWidth: 1,
                                  strokeColor: theme.cardColor,
                                );
                              },
                            ),
                          ),
                        ],
                      ),
                    ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickStats() {
    final theme = Theme.of(context);
    final DateTime cutoffDate = _getCutoffDate();

    // Calculate quick stats for the selected period
    double totalIncome = 0;
    double totalExpenses = 0;

    for (final transaction in _allTransactions) {
      if (transaction.date.isAfter(cutoffDate)) {
        if (transaction.type == TransactionType.credit) {
          totalIncome += transaction.amount;
        } else {
          totalExpenses += transaction.amount;
        }
      }
    }

    final netFlow = totalIncome - totalExpenses;
    final savingsRate = totalIncome > 0 ? (netFlow / totalIncome) * 100 : 0;

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: theme.cardColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: theme.shadowColor.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.analytics, color: AppColorPalette.primary, size: 24),
              const SizedBox(width: 12),
              Text(
                'Quick Statistics',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'Total Income',
                  '₹${NumberFormat('#,##,###').format(totalIncome)}',
                  AppColorPalette.success,
                  Icons.trending_up,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  'Total Expenses',
                  '₹${NumberFormat('#,##,###').format(totalExpenses)}',
                  Colors.red,
                  Icons.trending_down,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'Net Flow',
                  '₹${NumberFormat('#,##,###').format(netFlow)}',
                  netFlow >= 0 ? AppColorPalette.success : Colors.red,
                  netFlow >= 0 ? Icons.add : Icons.remove,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  'Savings Rate',
                  '${savingsRate.toStringAsFixed(1)}%',
                  AppColorPalette.primary,
                  Icons.savings,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    Color color,
    IconData icon,
  ) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 20),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  title,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: color,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: theme.textTheme.titleMedium?.copyWith(
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  double _getMaxBarValue() {
    if (_monthlyBarData.isEmpty) return 100;

    double max = 0;
    for (final group in _monthlyBarData) {
      for (final rod in group.barRods) {
        if (rod.toY > max) max = rod.toY;
      }
    }
    // Ensure we never return 0 as it can cause chart rendering issues
    return max > 0 ? max : 100;
  }

  double _getMaxLineValue() {
    if (_trendLineData.isEmpty) return 100;

    double max = _trendLineData.first.y;
    double min = _trendLineData.first.y;

    for (final spot in _trendLineData) {
      if (spot.y > max) max = spot.y;
      if (spot.y < min) min = spot.y;
    }

    final range = (max - min).abs();
    // Ensure we never return 0 as it can cause chart rendering issues
    return range > 0 ? range : 100;
  }

  String _formatCurrency(double value) {
    if (value.abs() >= 100000) {
      return '${(value / 100000).toStringAsFixed(1)}L';
    } else if (value.abs() >= 1000) {
      return '${(value / 1000).toStringAsFixed(1)}K';
    } else {
      return value.toStringAsFixed(0);
    }
  }
}
