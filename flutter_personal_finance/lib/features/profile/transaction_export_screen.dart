import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import 'package:path_provider/path_provider.dart';
import '../../resources/app_theme.dart';
import '../../services/local_storage_service.dart';
import '../../services/investment_service.dart';
import '../../features/models/bank_account_model.dart';
import '../../features/models/transaction_model.dart';
import '../../features/models/investment_model.dart';

class TransactionExportScreen extends StatefulWidget {
  const TransactionExportScreen({super.key});

  @override
  State<TransactionExportScreen> createState() =>
      _TransactionExportScreenState();
}

class _TransactionExportScreenState extends State<TransactionExportScreen> {
  List<BankAccountModel> _bankAccounts = [];
  List<TransactionModel> _filteredTransactions = [];
  List<InvestmentModel> _investments = [];
  bool _isLoading = true;
  bool _isExporting = false;

  // Filter options
  BankAccountModel? _selectedBankAccount;
  DateTimeRange? _selectedDateRange;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    try {
      final bankAccounts = await LocalStorageService.getBankAccounts();
      final investments = await InvestmentService.getInvestments();

      setState(() {
        _bankAccounts = bankAccounts;
        _investments = investments;
        _isLoading = false;
      });

      // Apply filters and load transactions
      await _applyFilters();
    } catch (e) {
      print('Error loading data: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _applyFilters() async {
    try {
      final allTransactions = await LocalStorageService.getTransactions();

      List<TransactionModel> filtered = allTransactions;

      // Filter by bank account
      if (_selectedBankAccount != null) {
        filtered =
            filtered
                .where(
                  (transaction) =>
                      transaction.bankAccountId == _selectedBankAccount!.id,
                )
                .toList();
      }

      // Filter by date range
      if (_selectedDateRange != null) {
        final startDate = DateTime(
          _selectedDateRange!.start.year,
          _selectedDateRange!.start.month,
          _selectedDateRange!.start.day,
        );
        final endDate = DateTime(
          _selectedDateRange!.end.year,
          _selectedDateRange!.end.month,
          _selectedDateRange!.end.day,
          23,
          59,
          59,
        );

        filtered =
            filtered.where((transaction) {
              final transactionDate = transaction.date;
              return transactionDate.isAfter(
                    startDate.subtract(const Duration(seconds: 1)),
                  ) &&
                  transactionDate.isBefore(
                    endDate.add(const Duration(seconds: 1)),
                  );
            }).toList();
      }

      // Sort by date (newest first)
      filtered.sort((a, b) => b.date.compareTo(a.date));

      setState(() {
        _filteredTransactions = filtered;
      });
    } catch (e) {
      print('Error applying filters: $e');
      _showSnackBar('Error filtering transactions: $e');
    }
  }

  void _showSnackBar(String message) {
    final theme = Theme.of(context);
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: theme.colorScheme.primary,
      ),
    );
  }

  Future<void> _selectBankAccount() async {
    final theme = Theme.of(context);
    final selected = await showDialog<BankAccountModel?>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Select Bank Account'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                ListTile(
                  title: const Text('All Bank Accounts'),
                  leading: Icon(
                    Icons.select_all,
                    color: theme.colorScheme.primary,
                  ),
                  onTap: () => Navigator.of(context).pop(null),
                  selected: _selectedBankAccount == null,
                ),
                const Divider(),
                ..._bankAccounts.map(
                  (account) => ListTile(
                    title: Text(account.bankName),
                    subtitle: Text('A/C: ${account.accountNumber}'),
                    leading: Icon(
                      Icons.account_balance,
                      color: theme.colorScheme.primary,
                    ),
                    onTap: () => Navigator.of(context).pop(account),
                    selected: _selectedBankAccount?.id == account.id,
                  ),
                ),
              ],
            ),
          ),
    );

    if (selected != _selectedBankAccount) {
      setState(() {
        _selectedBankAccount = selected;
      });
      await _applyFilters();
    }
  }

  Future<void> _selectDateRange() async {
    final picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      initialDateRange: _selectedDateRange,
      helpText: 'Select Date Range for Export',
    );

    if (picked != null && picked != _selectedDateRange) {
      setState(() {
        _selectedDateRange = picked;
      });
      await _applyFilters();
    }
  }

  Future<pw.Document> _generatePDF() async {
    final pdf = pw.Document();

    // Separate transactions into income and expenses (exclude self transfers)
    final incomeTransactions =
        _filteredTransactions
            .where(
              (t) =>
                  t.type == TransactionType.credit &&
                  t.category != 'Self Transfer',
            )
            .toList();
    final expenseTransactions =
        _filteredTransactions
            .where(
              (t) =>
                  t.type == TransactionType.debit &&
                  t.category != 'Self Transfer',
            )
            .toList();

    // Calculate totals
    final totalIncome = incomeTransactions.fold(
      0.0,
      (sum, t) => sum + t.amount,
    );
    final totalExpenses = expenseTransactions.fold(
      0.0,
      (sum, t) => sum + t.amount,
    );
    final netAmount = totalIncome - totalExpenses;

    // Limit transactions per page to prevent overflow
    const maxTransactionsPerSection = 15;

    // Calculate bank balance
    final double currentBankBalance;
    final String bankBalanceLabel;
    if (_selectedBankAccount != null) {
      // Single account selected
      currentBankBalance = _selectedBankAccount!.currentAmount;
      bankBalanceLabel = 'Current Balance (${_selectedBankAccount!.bankName})';
    } else {
      // All accounts - sum all balances
      currentBankBalance = _bankAccounts.fold(
        0.0,
        (sum, account) => sum + account.currentAmount,
      );
      bankBalanceLabel = 'Total Balance (All Accounts)';
    }

    final bankName =
        _selectedBankAccount?.bankName ?? 'export_all_accounts'.tr();
    final dateRangeText =
        _selectedDateRange != null
            ? '${DateFormat('MMM dd, yyyy').format(_selectedDateRange!.start)} - ${DateFormat('MMM dd, yyyy').format(_selectedDateRange!.end)}'
            : 'export_all_time'.tr();

    // Add pages to PDF
    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(32),
        build: (pw.Context context) {
          return [
            // Professional Header
            pw.Container(
              padding: const pw.EdgeInsets.all(20),
              decoration: pw.BoxDecoration(
                gradient: const pw.LinearGradient(
                  colors: [PdfColors.blue900, PdfColors.blue700],
                ),
                borderRadius: pw.BorderRadius.circular(12),
              ),
              child: pw.Column(
                children: [
                  pw.Row(
                    mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                    children: [
                      pw.Column(
                        crossAxisAlignment: pw.CrossAxisAlignment.start,
                        children: [
                          pw.Text(
                            'export_personal_finance'.tr().toUpperCase(),
                            style: pw.TextStyle(
                              fontSize: 14,
                              fontWeight: pw.FontWeight.bold,
                              color: PdfColors.white,
                              letterSpacing: 1.2,
                            ),
                          ),
                          pw.Text(
                            'export_financial_system'.tr(),
                            style: const pw.TextStyle(
                              fontSize: 10,
                              color: PdfColors.grey300,
                            ),
                          ),
                        ],
                      ),
                      pw.Container(
                        padding: const pw.EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 6,
                        ),
                        decoration: pw.BoxDecoration(
                          color: PdfColors.grey300,
                          borderRadius: pw.BorderRadius.circular(20),
                        ),
                        child: pw.Text(
                          'export_statement'.tr().toUpperCase(),
                          style: pw.TextStyle(
                            fontSize: 10,
                            fontWeight: pw.FontWeight.bold,
                            color: PdfColors.white,
                            letterSpacing: 1,
                          ),
                        ),
                      ),
                    ],
                  ),
                  pw.SizedBox(height: 16),
                  pw.Container(
                    width: double.infinity,
                    height: 1,
                    color: PdfColors.grey400,
                  ),
                  pw.SizedBox(height: 16),
                  pw.Center(
                    child: pw.Text(
                      'export_transaction_statement'.tr().toUpperCase(),
                      style: pw.TextStyle(
                        fontSize: 22,
                        fontWeight: pw.FontWeight.bold,
                        color: PdfColors.white,
                        letterSpacing: 2,
                      ),
                    ),
                  ),
                  pw.SizedBox(height: 8),
                  pw.Text(
                    'export_generated_on'.tr(
                      namedArgs: {
                        'date': DateFormat(
                          'MMMM dd, yyyy, hh:mm a',
                        ).format(DateTime.now()),
                      },
                    ),
                    style: const pw.TextStyle(
                      fontSize: 11,
                      color: PdfColors.grey300,
                    ),
                  ),
                ],
              ),
            ),

            pw.SizedBox(height: 24),

            // Statement Information
            pw.Container(
              padding: const pw.EdgeInsets.all(20),
              decoration: pw.BoxDecoration(
                color: PdfColors.grey50,
                border: pw.Border.all(color: PdfColors.grey300),
                borderRadius: pw.BorderRadius.circular(8),
              ),
              child: pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  pw.Text(
                    'export_statement_details'.tr().toUpperCase(),
                    style: pw.TextStyle(
                      fontSize: 12,
                      fontWeight: pw.FontWeight.bold,
                      color: PdfColors.grey800,
                      letterSpacing: 1,
                    ),
                  ),
                  pw.SizedBox(height: 12),
                  pw.Row(
                    children: [
                      pw.Expanded(
                        child: pw.Column(
                          crossAxisAlignment: pw.CrossAxisAlignment.start,
                          children: [
                            pw.Text(
                              'export_account'.tr(),
                              style: pw.TextStyle(
                                fontSize: 9,
                                color: PdfColors.grey600,
                                fontWeight: pw.FontWeight.bold,
                              ),
                            ),
                            pw.SizedBox(height: 2),
                            pw.Text(
                              bankName,
                              style: const pw.TextStyle(
                                fontSize: 11,
                                color: PdfColors.black,
                              ),
                            ),
                          ],
                        ),
                      ),
                      pw.Expanded(
                        child: pw.Column(
                          crossAxisAlignment: pw.CrossAxisAlignment.start,
                          children: [
                            pw.Text(
                              'export_statement_period'.tr(),
                              style: pw.TextStyle(
                                fontSize: 9,
                                color: PdfColors.grey600,
                                fontWeight: pw.FontWeight.bold,
                              ),
                            ),
                            pw.SizedBox(height: 2),
                            pw.Text(
                              dateRangeText,
                              style: const pw.TextStyle(
                                fontSize: 11,
                                color: PdfColors.black,
                              ),
                            ),
                          ],
                        ),
                      ),
                      pw.Expanded(
                        child: pw.Column(
                          crossAxisAlignment: pw.CrossAxisAlignment.start,
                          children: [
                            pw.Text(
                              'export_total_transactions'.tr(),
                              style: pw.TextStyle(
                                fontSize: 9,
                                color: PdfColors.grey600,
                                fontWeight: pw.FontWeight.bold,
                              ),
                            ),
                            pw.SizedBox(height: 2),
                            pw.Text(
                              '${_filteredTransactions.length}',
                              style: const pw.TextStyle(
                                fontSize: 11,
                                color: PdfColors.black,
                              ),
                            ),
                          ],
                        ),
                      ),
                      pw.Expanded(
                        child: pw.Column(
                          crossAxisAlignment: pw.CrossAxisAlignment.start,
                          children: [
                            pw.Text(
                              'export_current_balance'.tr(),
                              style: pw.TextStyle(
                                fontSize: 9,
                                color: PdfColors.grey600,
                                fontWeight: pw.FontWeight.bold,
                              ),
                            ),
                            pw.SizedBox(height: 2),
                            pw.Text(
                              '${NumberFormat('#,##,###').format(currentBankBalance)}',
                              style: pw.TextStyle(
                                fontSize: 11,
                                color:
                                    currentBankBalance >= 0
                                        ? PdfColors.green700
                                        : PdfColors.red700,
                                fontWeight: pw.FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            pw.SizedBox(height: 30),

            // Income Section (Vertical Layout for better page fitting)
            pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                // Income Header
                pw.Container(
                  width: double.infinity,
                  padding: const pw.EdgeInsets.all(16),
                  decoration: pw.BoxDecoration(
                    gradient: const pw.LinearGradient(
                      colors: [PdfColors.green700, PdfColors.green600],
                    ),
                    borderRadius: const pw.BorderRadius.only(
                      topLeft: pw.Radius.circular(8),
                      topRight: pw.Radius.circular(8),
                    ),
                  ),
                  child: pw.Center(
                    child: pw.Text(
                      'export_income_transactions'.tr().toUpperCase(),
                      style: pw.TextStyle(
                        fontSize: 14,
                        fontWeight: pw.FontWeight.bold,
                        color: PdfColors.white,
                        letterSpacing: 1,
                      ),
                    ),
                  ),
                ),

                // Income Table Headers
                pw.Container(
                  decoration: const pw.BoxDecoration(color: PdfColors.grey100),
                  child: pw.Row(
                    children: [
                      pw.Expanded(
                        flex: 3,
                        child: pw.Container(
                          padding: const pw.EdgeInsets.all(10),
                          decoration: const pw.BoxDecoration(
                            border: pw.Border(
                              right: pw.BorderSide(color: PdfColors.grey300),
                            ),
                          ),
                          child: pw.Text(
                            'DATE',
                            style: pw.TextStyle(
                              fontWeight: pw.FontWeight.bold,
                              fontSize: 9,
                              letterSpacing: 0.5,
                              color: PdfColors.grey800,
                            ),
                          ),
                        ),
                      ),
                      pw.Expanded(
                        flex: 4,
                        child: pw.Container(
                          padding: const pw.EdgeInsets.all(10),
                          decoration: const pw.BoxDecoration(
                            border: pw.Border(
                              right: pw.BorderSide(color: PdfColors.grey300),
                            ),
                          ),
                          child: pw.Text(
                            'CATEGORY',
                            style: pw.TextStyle(
                              fontWeight: pw.FontWeight.bold,
                              fontSize: 9,
                              letterSpacing: 0.5,
                              color: PdfColors.grey800,
                            ),
                          ),
                        ),
                      ),
                      pw.Expanded(
                        flex: 3,
                        child: pw.Container(
                          padding: const pw.EdgeInsets.all(10),
                          child: pw.Text(
                            'AMOUNT',
                            style: pw.TextStyle(
                              fontWeight: pw.FontWeight.bold,
                              fontSize: 9,
                              letterSpacing: 0.5,
                              color: PdfColors.grey800,
                            ),
                            textAlign: pw.TextAlign.right,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                // Income Transactions (Limited for page fitting)
                ...incomeTransactions
                    .take(maxTransactionsPerSection)
                    .map(
                      (transaction) => pw.Container(
                        decoration: const pw.BoxDecoration(
                          border: pw.Border(
                            bottom: pw.BorderSide(
                              color: PdfColors.grey300,
                              width: 0.5,
                            ),
                          ),
                        ),
                        child: pw.Row(
                          children: [
                            pw.Expanded(
                              flex: 3,
                              child: pw.Padding(
                                padding: const pw.EdgeInsets.all(6),
                                child: pw.Text(
                                  DateFormat(
                                    'dd/MM/yy',
                                  ).format(transaction.date),
                                  style: const pw.TextStyle(fontSize: 9),
                                ),
                              ),
                            ),
                            pw.Expanded(
                              flex: 4,
                              child: pw.Padding(
                                padding: const pw.EdgeInsets.all(6),
                                child: pw.Text(
                                  transaction.category,
                                  style: const pw.TextStyle(
                                    fontSize: 9,
                                    color: PdfColors.grey700,
                                  ),
                                ),
                              ),
                            ),
                            pw.Expanded(
                              flex: 3,
                              child: pw.Padding(
                                padding: const pw.EdgeInsets.all(6),
                                child: pw.Text(
                                  '${NumberFormat('#,##,###').format(transaction.amount)}',
                                  style: const pw.TextStyle(
                                    fontSize: 9,
                                    color: PdfColors.green700,
                                  ),
                                  textAlign: pw.TextAlign.right,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),

                // Income Total
                pw.Container(
                  decoration: const pw.BoxDecoration(
                    color: PdfColors.green50,
                    border: pw.Border.symmetric(
                      horizontal: pw.BorderSide(
                        color: PdfColors.green700,
                        width: 2,
                      ),
                    ),
                  ),
                  child: pw.Row(
                    children: [
                      pw.Expanded(
                        flex: 7,
                        child: pw.Padding(
                          padding: const pw.EdgeInsets.all(8),
                          child: pw.Text(
                            'export_total_income'.tr(),
                            style: pw.TextStyle(
                              fontWeight: pw.FontWeight.bold,
                              fontSize: 12,
                              color: PdfColors.green700,
                            ),
                            textAlign: pw.TextAlign.center,
                          ),
                        ),
                      ),
                      pw.Expanded(
                        flex: 3,
                        child: pw.Padding(
                          padding: const pw.EdgeInsets.all(8),
                          child: pw.Text(
                            '${NumberFormat('#,##,###').format(totalIncome)}',
                            style: pw.TextStyle(
                              fontWeight: pw.FontWeight.bold,
                              fontSize: 12,
                              color: PdfColors.green700,
                            ),
                            textAlign: pw.TextAlign.right,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),

            pw.SizedBox(height: 30),

            // Expense Section
            pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                // Expenses Header
                pw.Container(
                  width: double.infinity,
                  padding: const pw.EdgeInsets.all(16),
                  decoration: pw.BoxDecoration(
                    gradient: const pw.LinearGradient(
                      colors: [PdfColors.red700, PdfColors.red600],
                    ),
                    borderRadius: const pw.BorderRadius.only(
                      topLeft: pw.Radius.circular(8),
                      topRight: pw.Radius.circular(8),
                    ),
                  ),
                  child: pw.Center(
                    child: pw.Text(
                      'export_expense_transactions'.tr().toUpperCase(),
                      style: pw.TextStyle(
                        fontSize: 14,
                        fontWeight: pw.FontWeight.bold,
                        color: PdfColors.white,
                        letterSpacing: 1,
                      ),
                    ),
                  ),
                ),

                // Expenses Table Headers
                pw.Container(
                  decoration: const pw.BoxDecoration(color: PdfColors.grey100),
                  child: pw.Row(
                    children: [
                      pw.Expanded(
                        flex: 3,
                        child: pw.Container(
                          padding: const pw.EdgeInsets.all(10),
                          decoration: const pw.BoxDecoration(
                            border: pw.Border(
                              right: pw.BorderSide(color: PdfColors.grey300),
                            ),
                          ),
                          child: pw.Text(
                            'DATE',
                            style: pw.TextStyle(
                              fontWeight: pw.FontWeight.bold,
                              fontSize: 9,
                              letterSpacing: 0.5,
                              color: PdfColors.grey800,
                            ),
                          ),
                        ),
                      ),
                      pw.Expanded(
                        flex: 4,
                        child: pw.Container(
                          padding: const pw.EdgeInsets.all(10),
                          decoration: const pw.BoxDecoration(
                            border: pw.Border(
                              right: pw.BorderSide(color: PdfColors.grey300),
                            ),
                          ),
                          child: pw.Text(
                            'CATEGORY',
                            style: pw.TextStyle(
                              fontWeight: pw.FontWeight.bold,
                              fontSize: 9,
                              letterSpacing: 0.5,
                              color: PdfColors.grey800,
                            ),
                          ),
                        ),
                      ),
                      pw.Expanded(
                        flex: 3,
                        child: pw.Container(
                          padding: const pw.EdgeInsets.all(10),
                          child: pw.Text(
                            'AMOUNT',
                            style: pw.TextStyle(
                              fontWeight: pw.FontWeight.bold,
                              fontSize: 9,
                              letterSpacing: 0.5,
                              color: PdfColors.grey800,
                            ),
                            textAlign: pw.TextAlign.right,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                // Expense Transactions (Limited for page fitting)
                ...expenseTransactions
                    .take(maxTransactionsPerSection)
                    .map(
                      (transaction) => pw.Container(
                        decoration: const pw.BoxDecoration(
                          border: pw.Border(
                            bottom: pw.BorderSide(
                              color: PdfColors.grey300,
                              width: 0.5,
                            ),
                          ),
                        ),
                        child: pw.Row(
                          children: [
                            pw.Expanded(
                              flex: 3,
                              child: pw.Padding(
                                padding: const pw.EdgeInsets.all(6),
                                child: pw.Text(
                                  DateFormat(
                                    'dd/MM/yy',
                                  ).format(transaction.date),
                                  style: const pw.TextStyle(fontSize: 9),
                                ),
                              ),
                            ),
                            pw.Expanded(
                              flex: 4,
                              child: pw.Padding(
                                padding: const pw.EdgeInsets.all(6),
                                child: pw.Text(
                                  transaction.category,
                                  style: const pw.TextStyle(
                                    fontSize: 9,
                                    color: PdfColors.grey700,
                                  ),
                                ),
                              ),
                            ),
                            pw.Expanded(
                              flex: 3,
                              child: pw.Padding(
                                padding: const pw.EdgeInsets.all(6),
                                child: pw.Text(
                                  '${NumberFormat('#,##,###').format(transaction.amount)}',
                                  style: const pw.TextStyle(
                                    fontSize: 9,
                                    color: PdfColors.red700,
                                  ),
                                  textAlign: pw.TextAlign.right,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),

                // Expenses Total
                pw.Container(
                  decoration: const pw.BoxDecoration(
                    color: PdfColors.red50,
                    border: pw.Border.symmetric(
                      horizontal: pw.BorderSide(
                        color: PdfColors.red700,
                        width: 2,
                      ),
                    ),
                  ),
                  child: pw.Row(
                    children: [
                      pw.Expanded(
                        flex: 7,
                        child: pw.Padding(
                          padding: const pw.EdgeInsets.all(8),
                          child: pw.Text(
                            'export_total_expenses'.tr(),
                            style: pw.TextStyle(
                              fontWeight: pw.FontWeight.bold,
                              fontSize: 12,
                              color: PdfColors.red700,
                            ),
                            textAlign: pw.TextAlign.center,
                          ),
                        ),
                      ),
                      pw.Expanded(
                        flex: 3,
                        child: pw.Padding(
                          padding: const pw.EdgeInsets.all(8),
                          child: pw.Text(
                            '${NumberFormat('#,##,###').format(totalExpenses)}',
                            style: pw.TextStyle(
                              fontWeight: pw.FontWeight.bold,
                              fontSize: 12,
                              color: PdfColors.red700,
                            ),
                            textAlign: pw.TextAlign.right,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),

            pw.SizedBox(height: 30),

            // Net Amount Summary
            pw.Container(
              padding: const pw.EdgeInsets.all(16),
              decoration: pw.BoxDecoration(
                color: netAmount >= 0 ? PdfColors.green50 : PdfColors.red50,
                border: pw.Border.all(
                  color: netAmount >= 0 ? PdfColors.green700 : PdfColors.red700,
                  width: 2,
                ),
                borderRadius: pw.BorderRadius.circular(8),
              ),
              child: pw.Row(
                mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                children: [
                  pw.Text(
                    'export_net_amount'.tr(),
                    style: pw.TextStyle(
                      fontSize: 16,
                      fontWeight: pw.FontWeight.bold,
                      color:
                          netAmount >= 0
                              ? PdfColors.green700
                              : PdfColors.red700,
                    ),
                  ),
                  pw.Text(
                    '${NumberFormat('#,##,###').format(netAmount)}',
                    style: pw.TextStyle(
                      fontSize: 18,
                      fontWeight: pw.FontWeight.bold,
                      color:
                          netAmount >= 0
                              ? PdfColors.green700
                              : PdfColors.red700,
                    ),
                  ),
                ],
              ),
            ),

            pw.SizedBox(height: 16),

            // Bank Balance Summary
            pw.Container(
              padding: const pw.EdgeInsets.all(16),
              decoration: pw.BoxDecoration(
                color: PdfColors.blue50,
                border: pw.Border.all(color: PdfColors.blue700, width: 1),
                borderRadius: pw.BorderRadius.circular(8),
              ),
              child: pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  // Header
                  pw.Container(
                    padding: const pw.EdgeInsets.only(bottom: 8),
                    decoration: const pw.BoxDecoration(
                      border: pw.Border(
                        bottom: pw.BorderSide(
                          color: PdfColors.blue700,
                          width: 1,
                        ),
                      ),
                    ),
                    child: pw.Row(
                      mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                      children: [
                        pw.Text(
                          'export_account_balances'.tr().toUpperCase(),
                          style: pw.TextStyle(
                            fontSize: 12,
                            fontWeight: pw.FontWeight.bold,
                            color: PdfColors.blue700,
                            letterSpacing: 1,
                          ),
                        ),
                        pw.Text(
                          'As of ${DateFormat('MMM dd, yyyy').format(DateTime.now())}',
                          style: pw.TextStyle(
                            fontSize: 8,
                            color: PdfColors.grey600,
                            fontStyle: pw.FontStyle.italic,
                          ),
                        ),
                      ],
                    ),
                  ),
                  pw.SizedBox(height: 12),

                  // Individual Account Balances or Single Account
                  if (_selectedBankAccount != null)
                    // Single account selected
                    pw.Row(
                      mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                      children: [
                        pw.Expanded(
                          child: pw.Column(
                            crossAxisAlignment: pw.CrossAxisAlignment.start,
                            children: [
                              pw.Text(
                                _selectedBankAccount!.bankName,
                                style: pw.TextStyle(
                                  fontSize: 10,
                                  fontWeight: pw.FontWeight.bold,
                                  color: PdfColors.grey800,
                                ),
                              ),
                              pw.Text(
                                'A/C: ${_selectedBankAccount!.accountNumber}',
                                style: const pw.TextStyle(
                                  fontSize: 8,
                                  color: PdfColors.grey600,
                                ),
                              ),
                            ],
                          ),
                        ),
                        pw.Text(
                          '${NumberFormat('#,##,###').format(_selectedBankAccount!.currentAmount)}',
                          style: pw.TextStyle(
                            fontSize: 11,
                            fontWeight: pw.FontWeight.bold,
                            color:
                                _selectedBankAccount!.currentAmount >= 0
                                    ? PdfColors.green700
                                    : PdfColors.red700,
                          ),
                        ),
                      ],
                    )
                  else
                    // All accounts - show individual balances
                    pw.Column(
                      children: [
                        ..._bankAccounts.map(
                          (account) => pw.Container(
                            margin: const pw.EdgeInsets.only(bottom: 6),
                            child: pw.Row(
                              mainAxisAlignment:
                                  pw.MainAxisAlignment.spaceBetween,
                              children: [
                                pw.Expanded(
                                  child: pw.Column(
                                    crossAxisAlignment:
                                        pw.CrossAxisAlignment.start,
                                    children: [
                                      pw.Text(
                                        account.bankName,
                                        style: pw.TextStyle(
                                          fontSize: 9,
                                          fontWeight: pw.FontWeight.bold,
                                          color: PdfColors.grey800,
                                        ),
                                      ),
                                      pw.Text(
                                        'A/C: ${account.accountNumber}',
                                        style: const pw.TextStyle(
                                          fontSize: 7,
                                          color: PdfColors.grey600,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                pw.Text(
                                  '${NumberFormat('#,##,###').format(account.currentAmount)}',
                                  style: pw.TextStyle(
                                    fontSize: 9,
                                    fontWeight: pw.FontWeight.bold,
                                    color:
                                        account.currentAmount >= 0
                                            ? PdfColors.green700
                                            : PdfColors.red700,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),

                        // Total row
                        pw.Container(
                          margin: const pw.EdgeInsets.only(top: 8),
                          padding: const pw.EdgeInsets.only(top: 6),
                          decoration: const pw.BoxDecoration(
                            border: pw.Border(
                              top: pw.BorderSide(
                                color: PdfColors.blue300,
                                width: 1,
                              ),
                            ),
                          ),
                          child: pw.Row(
                            mainAxisAlignment:
                                pw.MainAxisAlignment.spaceBetween,
                            children: [
                              pw.Text(
                                'export_total_balance'.tr().toUpperCase(),
                                style: pw.TextStyle(
                                  fontSize: 10,
                                  fontWeight: pw.FontWeight.bold,
                                  color: PdfColors.blue700,
                                  letterSpacing: 0.5,
                                ),
                              ),
                              pw.Text(
                                '${NumberFormat('#,##,###').format(currentBankBalance)}',
                                style: pw.TextStyle(
                                  fontSize: 12,
                                  fontWeight: pw.FontWeight.bold,
                                  color:
                                      currentBankBalance >= 0
                                          ? PdfColors.blue700
                                          : PdfColors.red700,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                ],
              ),
            ),

            // Start Investment Summary on New Page
            pw.NewPage(),

            // Investment Summary
            pw.Container(
              padding: const pw.EdgeInsets.all(12),
              decoration: pw.BoxDecoration(
                color: PdfColors.orange50,
                border: pw.Border.all(color: PdfColors.orange700, width: 1),
                borderRadius: pw.BorderRadius.circular(8),
              ),
              child: pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  // Header
                  pw.Container(
                    padding: const pw.EdgeInsets.only(bottom: 8),
                    decoration: const pw.BoxDecoration(
                      border: pw.Border(
                        bottom: pw.BorderSide(
                          color: PdfColors.orange700,
                          width: 1,
                        ),
                      ),
                    ),
                    child: pw.Row(
                      mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                      children: [
                        pw.Text(
                          'export_investment_summary'.tr().toUpperCase(),
                          style: pw.TextStyle(
                            fontSize: 12,
                            fontWeight: pw.FontWeight.bold,
                            color: PdfColors.orange700,
                            letterSpacing: 1,
                          ),
                        ),
                        pw.Text(
                          'As of ${DateFormat('MMM dd, yyyy').format(DateTime.now())}',
                          style: pw.TextStyle(
                            fontSize: 8,
                            color: PdfColors.grey600,
                            fontStyle: pw.FontStyle.italic,
                          ),
                        ),
                      ],
                    ),
                  ),
                  pw.SizedBox(height: 8),

                  // Investment Details
                  if (_investments.isEmpty)
                    pw.Container(
                      padding: const pw.EdgeInsets.all(12),
                      decoration: pw.BoxDecoration(
                        color: PdfColors.grey100,
                        borderRadius: pw.BorderRadius.circular(8),
                      ),
                      child: pw.Center(
                        child: pw.Text(
                          'export_no_investment'.tr(),
                          style: pw.TextStyle(
                            fontSize: 11,
                            color: PdfColors.grey600,
                          ),
                        ),
                      ),
                    )
                  else
                    pw.Column(
                      children: [
                        ..._investments.map(
                          (investment) => pw.Container(
                            margin: const pw.EdgeInsets.only(bottom: 4),
                            child: pw.Row(
                              mainAxisAlignment:
                                  pw.MainAxisAlignment.spaceBetween,
                              children: [
                                pw.Expanded(
                                  child: pw.Column(
                                    crossAxisAlignment:
                                        pw.CrossAxisAlignment.start,
                                    children: [
                                      pw.Text(
                                        investment.name,
                                        style: pw.TextStyle(
                                          fontSize: 10,
                                          fontWeight: pw.FontWeight.bold,
                                          color: PdfColors.grey800,
                                        ),
                                      ),
                                      pw.Text(
                                        'export_category_label'.tr(
                                          namedArgs: {
                                            'category': investment.category,
                                          },
                                        ),
                                        style: const pw.TextStyle(
                                          fontSize: 8,
                                          color: PdfColors.grey600,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                pw.Text(
                                  '${NumberFormat('#,##,###').format(investment.currentValue)}',
                                  style: pw.TextStyle(
                                    fontSize: 10,
                                    fontWeight: pw.FontWeight.bold,
                                    color: PdfColors.orange700,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),

                        // Total row
                        pw.Container(
                          margin: const pw.EdgeInsets.only(top: 6),
                          padding: const pw.EdgeInsets.only(top: 4),
                          decoration: const pw.BoxDecoration(
                            border: pw.Border(
                              top: pw.BorderSide(
                                color: PdfColors.orange300,
                                width: 1,
                              ),
                            ),
                          ),
                          child: pw.Row(
                            mainAxisAlignment:
                                pw.MainAxisAlignment.spaceBetween,
                            children: [
                              pw.Text(
                                'export_total_investment_value'
                                    .tr()
                                    .toUpperCase(),
                                style: pw.TextStyle(
                                  fontSize: 10,
                                  fontWeight: pw.FontWeight.bold,
                                  color: PdfColors.orange700,
                                  letterSpacing: 0.5,
                                ),
                              ),
                              pw.Text(
                                '${NumberFormat('#,##,###').format(_investments.fold(0.0, (sum, investment) => sum + investment.currentValue))}',
                                style: pw.TextStyle(
                                  fontSize: 12,
                                  fontWeight: pw.FontWeight.bold,
                                  color: PdfColors.orange700,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                ],
              ),
            ),

            // Professional Footer
            pw.SizedBox(height: 40),
            pw.Container(
              padding: const pw.EdgeInsets.all(20),
              decoration: pw.BoxDecoration(
                gradient: const pw.LinearGradient(
                  colors: [PdfColors.grey100, PdfColors.grey50],
                ),
                borderRadius: pw.BorderRadius.circular(8),
                border: pw.Border.all(color: PdfColors.grey300),
              ),
              child: pw.Column(
                children: [
                  pw.Row(
                    mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                    children: [
                      pw.Column(
                        crossAxisAlignment: pw.CrossAxisAlignment.start,
                        children: [
                          pw.Text(
                            'export_personal_finance'.tr().toUpperCase(),
                            style: pw.TextStyle(
                              fontSize: 12,
                              fontWeight: pw.FontWeight.bold,
                              color: PdfColors.grey800,
                              letterSpacing: 1,
                            ),
                          ),
                          pw.Text(
                            'export_financial_system'.tr(),
                            style: const pw.TextStyle(
                              fontSize: 9,
                              color: PdfColors.grey600,
                            ),
                          ),
                        ],
                      ),
                      pw.Column(
                        crossAxisAlignment: pw.CrossAxisAlignment.end,
                        children: [
                          pw.Text(
                            'export_auto_generated'.tr(),
                            style: const pw.TextStyle(
                              fontSize: 9,
                              color: PdfColors.grey600,
                            ),
                          ),
                          pw.Text(
                            'export_currency_note'.tr(),
                            style: pw.TextStyle(
                              fontSize: 8,
                              color: PdfColors.grey500,
                              fontStyle: pw.FontStyle.italic,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                  pw.SizedBox(height: 12),
                  pw.Container(
                    width: double.infinity,
                    height: 1,
                    color: PdfColors.grey300,
                  ),
                  pw.SizedBox(height: 8),
                  pw.Center(
                    child: pw.Text(
                      'export_signature_note'.tr(),
                      style: pw.TextStyle(
                        fontSize: 8,
                        color: PdfColors.grey500,
                        fontStyle: pw.FontStyle.italic,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ];
        },
      ),
    );

    return pdf;
  }

  Future<void> _exportToPDF() async {
    setState(() {
      _isExporting = true;
    });

    try {
      // Generate PDF
      final pdf = await _generatePDF();

      // Directly navigate to PDF preview
      await _previewPDF(pdf);
    } catch (e) {
      _showSnackBar('Failed to generate PDF: $e');
    } finally {
      setState(() {
        _isExporting = false;
      });
    }
  }

  Future<void> _previewPDF(pw.Document pdf) async {
    final pdfBytes = await pdf.save();

    // Navigate to PDF viewer screen
    Navigator.of(context).push(
      MaterialPageRoute(
        builder:
            (context) => _PDFViewerScreen(
              pdfBytes: pdfBytes,
              filename:
                  'Transaction_Report_${DateFormat('yyyy_MM_dd').format(DateTime.now())}.pdf',
            ),
      ),
    );
  }

  Future<void> _sharePDF(pw.Document pdf) async {
    try {
      await Printing.sharePdf(
        bytes: await pdf.save(),
        filename:
            'Transaction_Report_${DateFormat('yyyy_MM_dd').format(DateTime.now())}.pdf',
      );
      _showSnackBar('PDF shared successfully!');
    } catch (e) {
      _showSnackBar('Failed to share PDF: $e');
    }
  }

  Future<void> _savePDF(pw.Document pdf) async {
    try {
      final bytes = await pdf.save();
      final dir = await getApplicationDocumentsDirectory();
      final file = File(
        '${dir.path}/Transaction_Report_${DateFormat('yyyy_MM_dd_HHmm').format(DateTime.now())}.pdf',
      );

      print(
        '${dir.path}/Transaction_Report_${DateFormat('yyyy_MM_dd_HHmm').format(DateTime.now())}.pdf',
      );
      await file.writeAsBytes(bytes);
      _showSnackBar('PDF saved to: ${file.path}');
    } catch (e) {
      _showSnackBar('Failed to save PDF: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        title: Text(
          'export_transactions'.tr(),
          style: theme.appBarTheme.titleTextStyle?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    // Filter Section
                    Container(
                      padding: const EdgeInsets.all(20),
                      decoration: BoxDecoration(
                        color: theme.cardColor,
                        borderRadius: BorderRadius.circular(16),
                        boxShadow: [
                          BoxShadow(
                            color: theme.shadowColor.withValues(alpha: 0.1),
                            blurRadius: 10,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'export_filter_options'.tr(),
                            style: theme.textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),

                          // Bank Account Filter
                          GestureDetector(
                            onTap: _selectBankAccount,
                            child: Container(
                              padding: const EdgeInsets.all(16),
                              decoration: BoxDecoration(
                                border: Border.all(
                                  color: theme.hintColor.withValues(alpha: 0.3),
                                ),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Row(
                                children: [
                                  Icon(
                                    Icons.account_balance,
                                    color: theme.colorScheme.primary,
                                  ),
                                  const SizedBox(width: 12),
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          'export_bank_account'.tr(),
                                          style: theme.textTheme.bodySmall
                                              ?.copyWith(
                                                color: theme.hintColor,
                                              ),
                                        ),
                                        Text(
                                          _selectedBankAccount?.bankName ??
                                              'export_all_bank_accounts'.tr(),
                                          style: theme.textTheme.bodyMedium
                                              ?.copyWith(
                                                fontWeight: FontWeight.w600,
                                              ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  Icon(
                                    Icons.arrow_drop_down,
                                    color: theme.hintColor,
                                  ),
                                ],
                              ),
                            ),
                          ),
                          const SizedBox(height: 12),

                          // Date Range Filter
                          GestureDetector(
                            onTap: _selectDateRange,
                            child: Container(
                              padding: const EdgeInsets.all(16),
                              decoration: BoxDecoration(
                                border: Border.all(
                                  color: theme.hintColor.withValues(alpha: 0.3),
                                ),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Row(
                                children: [
                                  Icon(
                                    Icons.date_range,
                                    color: theme.colorScheme.primary,
                                  ),
                                  const SizedBox(width: 12),
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          'export_date_range'.tr(),
                                          style: theme.textTheme.bodySmall
                                              ?.copyWith(
                                                color: theme.hintColor,
                                              ),
                                        ),
                                        Text(
                                          _selectedDateRange != null
                                              ? '${DateFormat('MMM dd, yyyy').format(_selectedDateRange!.start)} - ${DateFormat('MMM dd, yyyy').format(_selectedDateRange!.end)}'
                                              : 'export_all_time'.tr(),
                                          style: theme.textTheme.bodyMedium
                                              ?.copyWith(
                                                fontWeight: FontWeight.w600,
                                              ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  Icon(
                                    Icons.arrow_drop_down,
                                    color: theme.hintColor,
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 20),

                    // Results Section
                    Container(
                      padding: const EdgeInsets.all(20),
                      decoration: BoxDecoration(
                        color: theme.cardColor,
                        borderRadius: BorderRadius.circular(16),
                        boxShadow: [
                          BoxShadow(
                            color: theme.shadowColor.withValues(alpha: 0.1),
                            blurRadius: 10,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                'export_preview'.tr(),
                                style: theme.textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 12,
                                  vertical: 6,
                                ),
                                decoration: BoxDecoration(
                                  color: theme.colorScheme.primary.withValues(
                                    alpha: 0.1,
                                  ),
                                  borderRadius: BorderRadius.circular(20),
                                ),
                                child: Text(
                                  'export_transaction_count'.tr(
                                    namedArgs: {
                                      'count':
                                          _filteredTransactions.length
                                              .toString(),
                                    },
                                  ),
                                  style: theme.textTheme.bodySmall?.copyWith(
                                    color: theme.colorScheme.primary,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),

                          if (_filteredTransactions.isEmpty)
                            Container(
                              padding: const EdgeInsets.all(20),
                              decoration: BoxDecoration(
                                color: theme.hintColor.withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Column(
                                children: [
                                  Icon(
                                    Icons.inbox,
                                    size: 48,
                                    color: theme.hintColor.withValues(
                                      alpha: 0.5,
                                    ),
                                  ),
                                  const SizedBox(height: 12),
                                  Text(
                                    'export_no_transactions'.tr(),
                                    style: theme.textTheme.titleSmall?.copyWith(
                                      color: theme.hintColor,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  const SizedBox(height: 4),
                                  Text(
                                    'export_adjust_filters'.tr(),
                                    style: theme.textTheme.bodySmall?.copyWith(
                                      color: theme.hintColor.withValues(
                                        alpha: 0.7,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            )
                          else
                            Column(
                              children: [
                                // Quick stats
                                Row(
                                  children: [
                                    Expanded(
                                      child: Container(
                                        padding: const EdgeInsets.all(12),
                                        decoration: BoxDecoration(
                                          color: AppColorPalette.success
                                              .withValues(alpha: 0.1),
                                          borderRadius: BorderRadius.circular(
                                            8,
                                          ),
                                        ),
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              'export_income'.tr(),
                                              style: theme.textTheme.bodySmall
                                                  ?.copyWith(
                                                    color:
                                                        AppColorPalette.success,
                                                  ),
                                            ),
                                            Text(
                                              '₹${NumberFormat('#,##,###').format(_filteredTransactions.where((t) => t.type == TransactionType.credit && t.category != 'Self Transfer').fold(0.0, (sum, t) => sum + t.amount))}',
                                              style: theme.textTheme.bodyMedium
                                                  ?.copyWith(
                                                    color:
                                                        AppColorPalette.success,
                                                    fontWeight: FontWeight.bold,
                                                  ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                    const SizedBox(width: 8),
                                    Expanded(
                                      child: Container(
                                        padding: const EdgeInsets.all(12),
                                        decoration: BoxDecoration(
                                          color: theme.colorScheme.error
                                              .withValues(alpha: 0.1),
                                          borderRadius: BorderRadius.circular(
                                            8,
                                          ),
                                        ),
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              'export_expenses'.tr(),
                                              style: theme.textTheme.bodySmall
                                                  ?.copyWith(
                                                    color:
                                                        theme.colorScheme.error,
                                                  ),
                                            ),
                                            Text(
                                              '₹${NumberFormat('#,##,###').format(_filteredTransactions.where((t) => t.type == TransactionType.debit && t.category != 'Self Transfer').fold(0.0, (sum, t) => sum + t.amount))}',
                                              style: theme.textTheme.bodyMedium
                                                  ?.copyWith(
                                                    color:
                                                        theme.colorScheme.error,
                                                    fontWeight: FontWeight.bold,
                                                  ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 12),

                                // Investment Summary Card
                                Container(
                                  width: double.infinity,
                                  padding: const EdgeInsets.all(10),
                                  decoration: BoxDecoration(
                                    color: Colors.orange.withValues(
                                      alpha: 0.05,
                                    ),
                                    border: Border.all(
                                      color: Colors.orange.withValues(
                                        alpha: 0.2,
                                      ),
                                    ),
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      // Header
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          Text(
                                            'export_investment_summary'.tr(),
                                            style: theme.textTheme.bodyMedium
                                                ?.copyWith(
                                                  color: Colors.orange.shade700,
                                                  fontWeight: FontWeight.bold,
                                                ),
                                          ),
                                          Text(
                                            'export_investment_count'.tr(
                                              namedArgs: {
                                                'count':
                                                    _investments.length
                                                        .toString(),
                                              },
                                            ),
                                            style: theme.textTheme.bodySmall
                                                ?.copyWith(
                                                  color: theme.hintColor,
                                                  fontSize: 10,
                                                ),
                                          ),
                                        ],
                                      ),
                                      const SizedBox(height: 6),
                                      Divider(
                                        color: Colors.orange.withValues(
                                          alpha: 0.2,
                                        ),
                                        height: 1,
                                      ),
                                      const SizedBox(height: 6),

                                      if (_investments.isEmpty)
                                        Center(
                                          child: Text(
                                            'export_no_investments'.tr(),
                                            style: theme.textTheme.bodySmall
                                                ?.copyWith(
                                                  color: theme.hintColor,
                                                ),
                                          ),
                                        )
                                      else
                                        Column(
                                          children: [
                                            // Show first 3 investments as preview
                                            ..._investments
                                                .take(3)
                                                .map(
                                                  (investment) => Padding(
                                                    padding:
                                                        const EdgeInsets.only(
                                                          bottom: 6,
                                                        ),
                                                    child: Row(
                                                      mainAxisAlignment:
                                                          MainAxisAlignment
                                                              .spaceBetween,
                                                      children: [
                                                        Expanded(
                                                          child: Column(
                                                            crossAxisAlignment:
                                                                CrossAxisAlignment
                                                                    .start,
                                                            children: [
                                                              Text(
                                                                investment.name,
                                                                style: theme
                                                                    .textTheme
                                                                    .bodySmall
                                                                    ?.copyWith(
                                                                      fontWeight:
                                                                          FontWeight
                                                                              .w600,
                                                                      fontSize:
                                                                          11,
                                                                    ),
                                                              ),
                                                              Text(
                                                                investment
                                                                    .category,
                                                                style: theme
                                                                    .textTheme
                                                                    .bodySmall
                                                                    ?.copyWith(
                                                                      color:
                                                                          theme
                                                                              .hintColor,
                                                                      fontSize:
                                                                          9,
                                                                    ),
                                                              ),
                                                            ],
                                                          ),
                                                        ),
                                                        Text(
                                                          '₹${NumberFormat('#,##,###').format(investment.currentValue)}',
                                                          style: theme
                                                              .textTheme
                                                              .bodySmall
                                                              ?.copyWith(
                                                                color:
                                                                    Colors
                                                                        .orange
                                                                        .shade700,
                                                                fontWeight:
                                                                    FontWeight
                                                                        .bold,
                                                                fontSize: 11,
                                                              ),
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                ),

                                            if (_investments.length > 3)
                                              Padding(
                                                padding: const EdgeInsets.only(
                                                  bottom: 6,
                                                ),
                                                child: Text(
                                                  'export_and_more'.tr(
                                                    namedArgs: {
                                                      'count':
                                                          (_investments.length -
                                                                  3)
                                                              .toString(),
                                                    },
                                                  ),
                                                  style: theme
                                                      .textTheme
                                                      .bodySmall
                                                      ?.copyWith(
                                                        color: theme.hintColor,
                                                        fontStyle:
                                                            FontStyle.italic,
                                                        fontSize: 10,
                                                      ),
                                                ),
                                              ),

                                            // Total row
                                            Container(
                                              margin: const EdgeInsets.only(
                                                top: 6,
                                              ),
                                              padding: const EdgeInsets.only(
                                                top: 6,
                                              ),
                                              decoration: BoxDecoration(
                                                border: Border(
                                                  top: BorderSide(
                                                    color: Colors.orange
                                                        .withValues(alpha: 0.3),
                                                    width: 1,
                                                  ),
                                                ),
                                              ),
                                              child: Row(
                                                mainAxisAlignment:
                                                    MainAxisAlignment
                                                        .spaceBetween,
                                                children: [
                                                  Text(
                                                    'export_total_investment_value'
                                                        .tr()
                                                        .toUpperCase(),
                                                    style: theme
                                                        .textTheme
                                                        .bodySmall
                                                        ?.copyWith(
                                                          color:
                                                              Colors
                                                                  .orange
                                                                  .shade700,
                                                          fontWeight:
                                                              FontWeight.bold,
                                                          fontSize: 11,
                                                          letterSpacing: 0.5,
                                                        ),
                                                  ),
                                                  Text(
                                                    '₹${NumberFormat('#,##,###').format(_investments.fold(0.0, (sum, investment) => sum + investment.currentValue))}',
                                                    style: theme
                                                        .textTheme
                                                        .bodyMedium
                                                        ?.copyWith(
                                                          color:
                                                              Colors
                                                                  .orange
                                                                  .shade700,
                                                          fontWeight:
                                                              FontWeight.bold,
                                                        ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ],
                                        ),
                                    ],
                                  ),
                                ),
                                const SizedBox(height: 12),

                                // Account Balances Card
                                Container(
                                  width: double.infinity,
                                  padding: const EdgeInsets.all(12),
                                  decoration: BoxDecoration(
                                    color: theme.colorScheme.primary.withValues(
                                      alpha: 0.05,
                                    ),
                                    border: Border.all(
                                      color: theme.colorScheme.primary
                                          .withValues(alpha: 0.2),
                                    ),
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      // Header
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          Text(
                                            'export_account_balances'.tr(),
                                            style: theme.textTheme.bodyMedium
                                                ?.copyWith(
                                                  color:
                                                      theme.colorScheme.primary,
                                                  fontWeight: FontWeight.bold,
                                                ),
                                          ),
                                          Text(
                                            DateFormat(
                                              'MMM dd, yyyy',
                                            ).format(DateTime.now()),
                                            style: theme.textTheme.bodySmall
                                                ?.copyWith(
                                                  color: theme.hintColor,
                                                  fontSize: 10,
                                                ),
                                          ),
                                        ],
                                      ),
                                      const SizedBox(height: 8),
                                      Divider(
                                        color: theme.colorScheme.primary
                                            .withValues(alpha: 0.2),
                                        height: 1,
                                      ),
                                      const SizedBox(height: 8),

                                      // Individual Account Balances or Single Account
                                      if (_selectedBankAccount != null)
                                        // Single account selected
                                        Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          children: [
                                            Expanded(
                                              child: Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  Text(
                                                    _selectedBankAccount!
                                                        .bankName,
                                                    style: theme
                                                        .textTheme
                                                        .bodySmall
                                                        ?.copyWith(
                                                          fontWeight:
                                                              FontWeight.w600,
                                                          fontSize: 13,
                                                        ),
                                                  ),
                                                  Text(
                                                    'A/C: ${_selectedBankAccount!.accountNumber}',
                                                    style: theme
                                                        .textTheme
                                                        .bodySmall
                                                        ?.copyWith(
                                                          color:
                                                              theme.hintColor,
                                                          fontSize: 10,
                                                        ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                            Text(
                                              '₹${NumberFormat('#,##,###').format(_selectedBankAccount!.currentAmount)}',
                                              style: theme.textTheme.bodyMedium
                                                  ?.copyWith(
                                                    color:
                                                        _selectedBankAccount!
                                                                    .currentAmount >=
                                                                0
                                                            ? AppColorPalette
                                                                .success
                                                            : theme
                                                                .colorScheme
                                                                .error,
                                                    fontWeight: FontWeight.bold,
                                                  ),
                                            ),
                                          ],
                                        )
                                      else
                                        // All accounts - show individual balances
                                        Column(
                                          children: [
                                            ..._bankAccounts.map(
                                              (account) => Padding(
                                                padding: const EdgeInsets.only(
                                                  bottom: 6,
                                                ),
                                                child: Row(
                                                  mainAxisAlignment:
                                                      MainAxisAlignment
                                                          .spaceBetween,
                                                  children: [
                                                    Expanded(
                                                      child: Column(
                                                        crossAxisAlignment:
                                                            CrossAxisAlignment
                                                                .start,
                                                        children: [
                                                          Text(
                                                            account.bankName,
                                                            style: theme
                                                                .textTheme
                                                                .bodySmall
                                                                ?.copyWith(
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .w600,
                                                                  fontSize: 11,
                                                                ),
                                                          ),
                                                          Text(
                                                            'A/C: ${account.accountNumber}',
                                                            style: theme
                                                                .textTheme
                                                                .bodySmall
                                                                ?.copyWith(
                                                                  color:
                                                                      theme
                                                                          .hintColor,
                                                                  fontSize: 9,
                                                                ),
                                                          ),
                                                        ],
                                                      ),
                                                    ),
                                                    Text(
                                                      '₹${NumberFormat('#,##,###').format(account.currentAmount)}',
                                                      style: theme
                                                          .textTheme
                                                          .bodySmall
                                                          ?.copyWith(
                                                            color:
                                                                account.currentAmount >=
                                                                        0
                                                                    ? AppColorPalette
                                                                        .success
                                                                    : theme
                                                                        .colorScheme
                                                                        .error,
                                                            fontWeight:
                                                                FontWeight.bold,
                                                            fontSize: 11,
                                                          ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ),

                                            // Total row
                                            Container(
                                              margin: const EdgeInsets.only(
                                                top: 6,
                                              ),
                                              padding: const EdgeInsets.only(
                                                top: 6,
                                              ),
                                              decoration: BoxDecoration(
                                                border: Border(
                                                  top: BorderSide(
                                                    color: theme
                                                        .colorScheme
                                                        .primary
                                                        .withValues(alpha: 0.3),
                                                    width: 1,
                                                  ),
                                                ),
                                              ),
                                              child: Row(
                                                mainAxisAlignment:
                                                    MainAxisAlignment
                                                        .spaceBetween,
                                                children: [
                                                  Text(
                                                    'export_total_balance'
                                                        .tr()
                                                        .toUpperCase(),
                                                    style: theme
                                                        .textTheme
                                                        .bodySmall
                                                        ?.copyWith(
                                                          color:
                                                              theme
                                                                  .colorScheme
                                                                  .primary,
                                                          fontWeight:
                                                              FontWeight.bold,
                                                          fontSize: 11,
                                                          letterSpacing: 0.5,
                                                        ),
                                                  ),
                                                  Text(
                                                    '₹${NumberFormat('#,##,###').format(_bankAccounts.fold(0.0, (sum, account) => sum + account.currentAmount))}',
                                                    style: theme
                                                        .textTheme
                                                        .bodyMedium
                                                        ?.copyWith(
                                                          color:
                                                              theme
                                                                  .colorScheme
                                                                  .primary,
                                                          fontWeight:
                                                              FontWeight.bold,
                                                        ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ],
                                        ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 20),

                    // Generate PDF Button
                    ElevatedButton.icon(
                      onPressed:
                          _filteredTransactions.isEmpty || _isExporting
                              ? null
                              : _exportToPDF,
                      icon:
                          _isExporting
                              ? SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                    colorScheme.onPrimary,
                                  ),
                                ),
                              )
                              : const Icon(Icons.picture_as_pdf),
                      label: Text(
                        _isExporting
                            ? 'Generating PDF...'
                            : 'Generate PDF Report',
                      ),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: theme.colorScheme.primary,
                        foregroundColor: colorScheme.onPrimary,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        textStyle: theme.textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
    );
  }
}

class _PDFViewerScreen extends StatefulWidget {
  final Uint8List pdfBytes;
  final String filename;

  const _PDFViewerScreen({required this.pdfBytes, required this.filename});

  @override
  State<_PDFViewerScreen> createState() => _PDFViewerScreenState();
}

class _PDFViewerScreenState extends State<_PDFViewerScreen> {
  final TransformationController _transformationController =
      TransformationController();
  double _currentScale = 1.0;

  void _zoomIn() {
    setState(() {
      _currentScale = (_currentScale * 1.2).clamp(0.5, 3.0);
      _transformationController.value =
          Matrix4.identity()..scale(_currentScale);
    });
  }

  void _zoomOut() {
    setState(() {
      _currentScale = (_currentScale / 1.2).clamp(0.5, 3.0);
      _transformationController.value =
          Matrix4.identity()..scale(_currentScale);
    });
  }

  void _resetZoom() {
    setState(() {
      _currentScale = 1.0;
      _transformationController.value = Matrix4.identity();
    });
  }

  @override
  void dispose() {
    _transformationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        title: Text(
          'PDF Preview',
          style: theme.appBarTheme.titleTextStyle?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios),
          onPressed: () => Navigator.of(context).pop(),
        ),
        actions: [
          // Zoom Out button
          IconButton(icon: const Icon(Icons.zoom_out), onPressed: _zoomOut),

          // Reset Zoom button
          IconButton(
            icon: const Icon(Icons.center_focus_strong),
            onPressed: _resetZoom,
          ),

          // Zoom In button
          IconButton(icon: const Icon(Icons.zoom_in), onPressed: _zoomIn),

          // Save button
          IconButton(
            icon: const Icon(Icons.save),
            onPressed: () async {
              try {
                final dir = await getApplicationDocumentsDirectory();
                final file = File(
                  '${dir.path}/${widget.filename.replaceAll('.pdf', '_${DateFormat('HHmm').format(DateTime.now())}.pdf')}',
                );
                await file.writeAsBytes(widget.pdfBytes);

                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('PDF saved to: ${file.path}'),
                    backgroundColor: colorScheme.primary,
                  ),
                );
                print('PDF saved to: ${file.path}');
              } catch (e) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('Failed to save PDF: $e'),
                    backgroundColor: colorScheme.error,
                  ),
                );
              }
            },
          ),

          // Share button
          IconButton(
            icon: Icon(Icons.share, color: colorScheme.onPrimary),
            onPressed: () async {
              try {
                await Printing.sharePdf(
                  bytes: widget.pdfBytes,
                  filename: widget.filename,
                );
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: const Text('PDF shared successfully!'),
                    backgroundColor: colorScheme.primary,
                  ),
                );
              } catch (e) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('Failed to share PDF: $e'),
                    backgroundColor: colorScheme.error,
                  ),
                );
              }
            },
          ),

          // Print button
          IconButton(
            icon: Icon(Icons.print, color: colorScheme.onPrimary),
            onPressed: () async {
              try {
                await Printing.layoutPdf(
                  onLayout: (PdfPageFormat format) async => widget.pdfBytes,
                  name: widget.filename,
                );
              } catch (e) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('Failed to print PDF: $e'),
                    backgroundColor: colorScheme.error,
                  ),
                );
              }
            },
          ),
        ],
      ),
      body: InteractiveViewer(
        transformationController: _transformationController,
        minScale: 0.5,
        maxScale: 3.0,
        child: PdfPreview(
          build: (format) => widget.pdfBytes,
          allowPrinting: false,
          allowSharing: false,
          canChangePageFormat: false,
          canChangeOrientation: false,
          canDebug: false,
          initialPageFormat: PdfPageFormat.a4,
          pdfFileName: widget.filename,
        ),
      ),
    );
  }
}
