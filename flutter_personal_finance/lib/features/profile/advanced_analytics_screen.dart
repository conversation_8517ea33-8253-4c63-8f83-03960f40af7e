import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:easy_localization/easy_localization.dart';

import '../../resources/app_theme.dart';
import '../../resources/app_text_styles.dart';
import '../../services/local_storage_service.dart';
import '../../features/models/transaction_model.dart';

class AdvancedAnalyticsScreen extends StatefulWidget {
  const AdvancedAnalyticsScreen({super.key});

  @override
  State<AdvancedAnalyticsScreen> createState() =>
      _AdvancedAnalyticsScreenState();
}

class _AdvancedAnalyticsScreenState extends State<AdvancedAnalyticsScreen> {
  List<TransactionModel> _allTransactions = [];
  bool _isLoading = true;

  // Chart data
  final List<FlSpot> _incomeSpots = [];
  final List<FlSpot> _expenseSpots = [];
  final List<String> _monthLabels = [];
  final List<double> _monthlyExpenses = [];
  final List<String> _monthlyLabels = [];
  final Map<String, double> _categorySpending = {};
  double _maxIncome = 0;
  double _maxExpense = 0;

  @override
  void initState() {
    super.initState();
    _loadTransactions();
  }

  Future<void> _loadTransactions() async {
    setState(() => _isLoading = true);

    try {
      final transactions = await LocalStorageService.getTransactions();
      setState(() {
        _allTransactions = transactions;
      });
      _generateChartData();
      _generateCategoryInsights();
    } catch (e) {
      debugPrint('Error loading transactions: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _generateChartData() {
    final now = DateTime.now();
    final monthsToShow = 6;

    // Create month labels and data points
    _monthLabels.clear();
    _incomeSpots.clear();
    _expenseSpots.clear();
    _monthlyExpenses.clear();
    _monthlyLabels.clear();

    _maxIncome = 0;
    _maxExpense = 0;

    for (int i = monthsToShow - 1; i >= 0; i--) {
      final targetMonth = DateTime(now.year, now.month - i, 1);
      final monthLabel = DateFormat('MMM').format(targetMonth);
      final monthWithYear = DateFormat('MMM yyyy').format(targetMonth);
      _monthLabels.add(monthLabel);

      // Calculate income and expenses for this month
      double monthlyIncome = 0;
      double monthlyExpense = 0;

      for (final transaction in _allTransactions) {
        final transactionDate = transaction.date;

        if (transactionDate.year == targetMonth.year &&
            transactionDate.month == targetMonth.month) {
          if (transaction.type == TransactionType.credit) {
            monthlyIncome += transaction.amount;
          } else if (transaction.type == TransactionType.debit) {
            monthlyExpense += transaction.amount;
          }
        }
      }

      _incomeSpots.add(
        FlSpot((monthsToShow - 1 - i).toDouble(), monthlyIncome),
      );
      _expenseSpots.add(
        FlSpot((monthsToShow - 1 - i).toDouble(), monthlyExpense),
      );

      // Add to monthly spending trends (always last 6 months)
      if (i < 6) {
        _monthlyExpenses.add(monthlyExpense);
        _monthlyLabels.add(monthWithYear);
      }

      if (monthlyIncome > _maxIncome) _maxIncome = monthlyIncome;
      if (monthlyExpense > _maxExpense) _maxExpense = monthlyExpense;
    }
  }

  void _generateCategoryInsights() {
    final now = DateTime.now();
    final thirtyDaysAgo = now.subtract(const Duration(days: 30));

    // Clear previous data
    _categorySpending.clear();

    // Calculate spending by category for last 30 days
    for (final transaction in _allTransactions) {
      final transactionDate = transaction.date;

      // Only include expense transactions from last 30 days
      if (transaction.type == TransactionType.debit &&
          transactionDate.isAfter(thirtyDaysAgo) &&
          transactionDate.isBefore(now.add(const Duration(days: 1)))) {
        final category =
            transaction.category.isNotEmpty
                ? transaction.category
                : 'uncategorized'.tr();

        _categorySpending[category] =
            (_categorySpending[category] ?? 0) + transaction.amount;
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'advanced_analytics_title'.tr(),
          style: AppTextStyles.titleLarge.copyWith(
            color: AppColorPalette.white,
            fontWeight: FontWeight.w600,
          ),
        ),
        backgroundColor: AppColorPalette.primary,
        foregroundColor: AppColorPalette.white,
        elevation: 0,
      ),
      body:
          _isLoading
              ? const Center(
                child: CircularProgressIndicator(
                  color: AppColorPalette.primary,
                ),
              )
              : RefreshIndicator(
                onRefresh: _loadTransactions,
                color: AppColorPalette.primary,
                child: SingleChildScrollView(
                  physics: const AlwaysScrollableScrollPhysics(),
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildMonthlySpendingTrends(),
                      const SizedBox(height: 24),
                      _buildCategoryInsights(),
                    ],
                  ),
                ),
              ),
    );
  }

  Widget _buildMonthlySpendingTrends() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.trending_down,
                  color: AppColorPalette.primary,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'monthly_spending_trends'.tr(),
                    style: AppTextStyles.titleMedium.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Monthly spending list
            _monthlyExpenses.isEmpty
                ? Center(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 20),
                    child: Column(
                      children: [
                        Icon(
                          Icons.account_balance_wallet_outlined,
                          size: 48,
                          color: AppColorPalette.grey,
                        ),
                        const SizedBox(height: 12),
                        Text(
                          'no_spending_data_available'.tr(),
                          style: AppTextStyles.bodyMedium.copyWith(
                            color: AppColorPalette.grey,
                          ),
                        ),
                      ],
                    ),
                  ),
                )
                : Column(
                  children:
                      _monthlyExpenses.asMap().entries.map((entry) {
                        final index = entry.key;
                        final expense = entry.value;
                        final monthLabel = _monthlyLabels[index];

                        // Calculate relative width for the blue bar (like in screenshot)
                        final maxExpenseInRange =
                            _monthlyExpenses.isEmpty
                                ? 1.0
                                : _monthlyExpenses.reduce(
                                  (a, b) => a > b ? a : b,
                                );
                        final relativeWidth =
                            maxExpenseInRange > 0
                                ? (expense / maxExpenseInRange)
                                : 0.0;

                        return Column(
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                // Month label
                                Text(
                                  monthLabel,
                                  style: const TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                    color: Colors.black,
                                  ),
                                ),

                                // Amount
                                Text(
                                  '₹${NumberFormat('#,##,###').format(expense)}',
                                  style: const TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                    color: Color(0xFF4A90E2),
                                  ),
                                ),
                              ],
                            ),

                            // Blue progress bar (only for non-zero amounts)
                            if (expense > 0)
                              Container(
                                height: 4,
                                width: double.infinity,
                                decoration: BoxDecoration(
                                  color: Colors.grey[200],
                                  borderRadius: BorderRadius.circular(2),
                                ),
                                child: Align(
                                  alignment: Alignment.centerLeft,
                                  child: Container(
                                    height: 4,
                                    width:
                                        MediaQuery.of(context).size.width *
                                        0.8 *
                                        relativeWidth,
                                    decoration: BoxDecoration(
                                      color: const Color(0xFF4A90E2),
                                      borderRadius: BorderRadius.circular(2),
                                    ),
                                  ),
                                ),
                              ),

                            // Divider (except for last item)
                            if (index < _monthlyExpenses.length - 1)
                              Container(
                                margin: const EdgeInsets.only(top: 16),
                                height: 1,
                                color: Colors.grey[300],
                              ),

                            const SizedBox(height: 8),
                          ],
                        );
                      }).toList(),
                ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoryInsights() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  width: 20,
                  height: 20,
                  decoration: BoxDecoration(
                    color: const Color(0xFF4DD0E1),
                    borderRadius: BorderRadius.circular(5),
                  ),
                  child: const Icon(
                    Icons.pie_chart,
                    color: Colors.white,
                    size: 14,
                  ),
                ),
                const SizedBox(width: 10),
                Expanded(
                  child: Text(
                    'category_insights_last_30_days'.tr(),
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.black,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Category spending list
            _categorySpending.isEmpty
                ? Center(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 20),
                    child: Column(
                      children: [
                        Icon(
                          Icons.category_outlined,
                          size: 48,
                          color: AppColorPalette.grey,
                        ),
                        const SizedBox(height: 12),
                        Text(
                          'no_category_data_available'.tr(),
                          style: AppTextStyles.bodyMedium.copyWith(
                            color: AppColorPalette.grey,
                          ),
                        ),
                      ],
                    ),
                  ),
                )
                : Column(children: _buildCategoryItems()),
          ],
        ),
      ),
    );
  }

  List<Widget> _buildCategoryItems() {
    // Sort categories by spending amount (highest first)
    final sortedCategories =
        _categorySpending.entries.toList()
          ..sort((a, b) => b.value.compareTo(a.value));

    // Calculate total spending for percentage calculation
    final totalSpending = _categorySpending.values.fold(
      0.0,
      (sum, amount) => sum + amount,
    );

    return sortedCategories.map((entry) {
      final category = entry.key;
      final amount = entry.value;
      final percentage =
          totalSpending > 0 ? (amount / totalSpending) * 100 : 0.0;
      // Use percentage for progress bar width (convert from 0-100 to 0-1)
      final relativeWidth =
          percentage > 0 ? (percentage / 100).clamp(0.0, 1.0) : 0.0;

      return Padding(
        padding: const EdgeInsets.only(bottom: 8),
        child: Row(
          children: [
            // Category name
            Expanded(
              flex: 2,
              child: Text(
                category,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Colors.black,
                ),
              ),
            ),

            // Progress bar
            Expanded(
              flex: 2,
              child: Container(
                height: 6,
                margin: const EdgeInsets.symmetric(horizontal: 10),
                decoration: BoxDecoration(
                  color: Colors.grey[200],
                  borderRadius: BorderRadius.circular(3),
                ),
                child: LayoutBuilder(
                  builder: (context, constraints) {
                    // Ensure minimum width for visibility, but don't exceed actual relative width
                    final barWidth =
                        relativeWidth > 0
                            ? (constraints.maxWidth * relativeWidth).clamp(
                              4.0,
                              constraints.maxWidth,
                            )
                            : 0.0;

                    return Align(
                      alignment: Alignment.centerLeft,
                      child: Container(
                        height: 6,
                        width: barWidth,
                        decoration: BoxDecoration(
                          color: const Color(0xFF4DD0E1),
                          borderRadius: BorderRadius.circular(3),
                        ),
                      ),
                    );
                  },
                ),
              ),
            ),

            // Percentage
            SizedBox(
              width: 45,
              child: Text(
                '${percentage.toStringAsFixed(1)}%',
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF4DD0E1),
                ),
                textAlign: TextAlign.center,
              ),
            ),

            // Amount
            SizedBox(
              width: 75,
              child: Text(
                '₹${NumberFormat('#,##,###').format(amount)}',
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Colors.black54,
                ),
                textAlign: TextAlign.end,
              ),
            ),
          ],
        ),
      );
    }).toList();
  }
}
