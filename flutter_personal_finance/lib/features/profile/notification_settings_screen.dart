import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../resources/app_theme.dart';
import '../../services/notification_service.dart';
import '../models/upcoming_expense_model.dart';
import '../models/credit_card_model.dart';

class NotificationSettingsScreen extends StatefulWidget {
  const NotificationSettingsScreen({super.key});

  @override
  State<NotificationSettingsScreen> createState() =>
      _NotificationSettingsScreenState();
}

class _NotificationSettingsScreenState
    extends State<NotificationSettingsScreen> {
  NotificationSettings _settings = NotificationSettings();
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    try {
      final settings = await NotificationService.getNotificationSettings();
      setState(() {
        _settings = settings;
        _isLoading = false;
      });
    } catch (e) {
      print('Error loading notification settings: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _saveSettings() async {
    try {
      await NotificationService.saveNotificationSettings(_settings);
      _showSnackBar('Notification settings saved successfully');
    } catch (e) {
      _showSnackBar('Error saving settings');
    }
  }

  Future<void> _selectNotificationTime() async {
    final time = await showTimePicker(
      context: context,
      initialTime: _settings.notificationTime,
    );

    if (time != null) {
      setState(() {
        _settings = _settings.copyWith(notificationTime: time);
      });
      await _saveSettings();
    }
  }

  void _toggleDaysBefore(int days) {
    setState(() {
      final currentDays = List<int>.from(_settings.daysBefore);
      if (currentDays.contains(days)) {
        currentDays.remove(days);
      } else {
        currentDays.add(days);
        currentDays.sort();
      }
      _settings = _settings.copyWith(daysBefore: currentDays);
    });
    _saveSettings();
  }

  void _showSnackBar(String message) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          message,
          style: theme.textTheme.bodyMedium?.copyWith(
            color: colorScheme.onPrimary,
          ),
        ),
        backgroundColor: theme.colorScheme.primary,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        title: Text(
          'Notification Settings',
          style: theme.appBarTheme.titleTextStyle?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios),
          onPressed: () => context.pop(),
        ),
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Enable Notifications Toggle
                    _buildSettingCard(
                      icon: Icons.notifications,
                      title: 'Enable Notifications',
                      subtitle: 'Get notified about upcoming expense payments',
                      trailing: Switch(
                        value: _settings.enabled,
                        onChanged: (value) {
                          setState(() {
                            _settings = _settings.copyWith(enabled: value);
                          });
                          _saveSettings();
                        },
                        activeColor: theme.colorScheme.primary,
                        inactiveTrackColor: theme.hintColor.withValues(
                          alpha: 0.3,
                        ),
                      ),
                    ),
                    const SizedBox(height: 24),

                    if (_settings.enabled) ...[
                      // Notification Time
                      Text(
                        'Notification Time',
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 12),

                      _buildSettingCard(
                        icon: Icons.schedule,
                        title: 'Time of Day',
                        subtitle: 'When to send daily notifications',
                        trailing: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              _settings.notificationTime.format(context),
                              style: theme.textTheme.bodyMedium?.copyWith(
                                fontWeight: FontWeight.w600,
                                color: theme.colorScheme.primary,
                              ),
                            ),
                            const SizedBox(width: 8),
                            Icon(
                              Icons.arrow_forward_ios,
                              size: 16,
                              color: theme.hintColor,
                            ),
                          ],
                        ),
                        onTap: _selectNotificationTime,
                      ),
                      const SizedBox(height: 24),

                      // Days Before Settings
                      Text(
                        'Notify Me',
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 12),

                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: theme.cardColor,
                          borderRadius: BorderRadius.circular(12),
                          boxShadow: [
                            BoxShadow(
                              color: theme.shadowColor.withValues(alpha: 0.1),
                              blurRadius: 4,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Send notifications before payment due date:',
                              style: theme.textTheme.bodyMedium?.copyWith(
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            const SizedBox(height: 16),

                            Wrap(
                              spacing: 8,
                              runSpacing: 8,
                              children: [
                                _buildDayChip(0, 'On due date'),
                                _buildDayChip(1, '1 day before'),
                                _buildDayChip(2, '2 days before'),
                                _buildDayChip(3, '3 days before'),
                                _buildDayChip(7, '1 week before'),
                                _buildDayChip(14, '2 weeks before'),
                              ],
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 24),

                      // Test & Debug Section
                      Text(
                        'Test & Debug',
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 12),

                      // Permission Request Button
                      Container(
                        width: double.infinity,
                        child: ElevatedButton.icon(
                          onPressed: () async {
                            try {
                              _showSnackBar(
                                '🔔 Requesting notification permissions...',
                              );

                              final granted =
                                  await NotificationService.requestPermissionsManually();

                              if (granted) {
                                _showSnackBar(
                                  '✅ Permissions granted! Check iPhone Settings → Notifications for your app.',
                                );
                              } else {
                                _showSnackBar(
                                  '❌ Permissions not granted. You may need to enable them manually in iPhone Settings.',
                                );
                              }
                            } catch (e) {
                              _showSnackBar(
                                '❌ Error requesting permissions: $e',
                              );
                              print('Permission request error: $e');
                            }
                          },
                          icon: Icon(
                            Icons.security,
                            size: 20,
                            color: colorScheme.onPrimary,
                          ),
                          label: Text(
                            'Request Notification Permissions',
                            style: theme.textTheme.bodyMedium?.copyWith(
                              fontWeight: FontWeight.w600,
                              color: colorScheme.onPrimary,
                            ),
                          ),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: theme.colorScheme.primary,
                            foregroundColor: colorScheme.onPrimary,
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                        ),
                      ),

                      const SizedBox(height: 12),

                      // Test Notification Button
                      Container(
                        width: double.infinity,
                        child: ElevatedButton.icon(
                          onPressed: () async {
                            try {
                              await NotificationService.showTestNotification();
                              _showSnackBar(
                                'Test notification sent! Check your notification panel.',
                              );
                            } catch (e) {
                              _showSnackBar(
                                'Error sending test notification: $e',
                              );
                              print('Test notification error: $e');
                            }
                          },
                          icon: Icon(
                            Icons.notification_add,
                            size: 20,
                            color: colorScheme.onPrimary,
                          ),
                          label: Text(
                            'Send Test Notification',
                            style: theme.textTheme.bodyMedium?.copyWith(
                              fontWeight: FontWeight.w600,
                              color: colorScheme.onPrimary,
                            ),
                          ),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColorPalette.success,
                            foregroundColor: colorScheme.onPrimary,
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(height: 12),

                      // Schedule Sample Notification Button
                      Container(
                        width: double.infinity,
                        child: ElevatedButton.icon(
                          onPressed: () async {
                            try {
                              // Create a sample expense due in 1 minute for testing
                              final testExpense = UpcomingExpenseModel(
                                id:
                                    'test_${DateTime.now().millisecondsSinceEpoch}',
                                category: 'Test Payment',
                                amount: 100.0,
                                dueDate: DateTime.now().add(
                                  Duration(minutes: 1),
                                ),
                                isPaid: false,
                                createdAt: DateTime.now(),
                                updatedAt: DateTime.now(),
                              );

                              await NotificationService.scheduleExpenseNotification(
                                testExpense,
                              );
                              _showSnackBar(
                                'Sample notification scheduled for 1 minute from now!',
                              );
                            } catch (e) {
                              _showSnackBar(
                                'Error scheduling sample notification: $e',
                              );
                              print('Sample notification error: $e');
                            }
                          },
                          icon: Icon(
                            Icons.schedule,
                            size: 20,
                            color: colorScheme.onPrimary,
                          ),
                          label: Text(
                            'Schedule Sample Notification (1 min)',
                            style: theme.textTheme.bodyMedium?.copyWith(
                              fontWeight: FontWeight.w600,
                              color: colorScheme.onPrimary,
                            ),
                          ),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColorPalette.warning,
                            foregroundColor: colorScheme.onPrimary,
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                        ),
                      ),

                      const SizedBox(height: 12),

                      // Test Statement Notification Button
                      Container(
                        width: double.infinity,
                        child: ElevatedButton.icon(
                          onPressed: () async {
                            try {
                              // Create a test credit card with closing date in 1 minute
                              final testCard = CreditCardModel(
                                id:
                                    'test_statement_${DateTime.now().millisecondsSinceEpoch}',
                                cardName: 'Test Statement Card',
                                cardNumber: '1234',
                                cardType: CreditCardType.visa,
                                bankName: 'Test Bank',
                                creditLimit: 50000,
                                outstandingBalance: 15000,
                                dueDate: DateTime.now().add(
                                  const Duration(days: 15),
                                ),
                                closingDate: DateTime.now().add(
                                  const Duration(minutes: 1),
                                ),
                                createdAt: DateTime.now(),
                                updatedAt: DateTime.now(),
                              );

                              await NotificationService.scheduleCreditCardStatementNotification(
                                testCard,
                              );
                              _showSnackBar(
                                'Statement notification scheduled for 1 minute from now!',
                              );
                            } catch (e) {
                              _showSnackBar(
                                'Error scheduling statement notification: $e',
                              );
                              print('Statement notification error: $e');
                            }
                          },
                          icon: Icon(
                            Icons.receipt_long,
                            size: 20,
                            color: colorScheme.onPrimary,
                          ),
                          label: Text(
                            'Test Statement Notification (1 min)',
                            style: theme.textTheme.bodyMedium?.copyWith(
                              fontWeight: FontWeight.w600,
                              color: colorScheme.onPrimary,
                            ),
                          ),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColorPalette.info,
                            foregroundColor: colorScheme.onPrimary,
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                        ),
                      ),

                      const SizedBox(height: 12),

                      // Test Payment Due Notification Button
                      Container(
                        width: double.infinity,
                        child: ElevatedButton.icon(
                          onPressed: () async {
                            try {
                              // Create a test credit card with payment due in 1 minute
                              final testCard = CreditCardModel(
                                id:
                                    'test_payment_${DateTime.now().millisecondsSinceEpoch}',
                                cardName: 'Test Payment Card',
                                cardNumber: '5678',
                                cardType: CreditCardType.mastercard,
                                bankName: 'Test Bank',
                                creditLimit: 50000,
                                outstandingBalance: 25000,
                                dueDate: DateTime.now().add(
                                  const Duration(minutes: 1),
                                ),
                                closingDate: DateTime.now().add(
                                  const Duration(days: 10),
                                ),
                                createdAt: DateTime.now(),
                                updatedAt: DateTime.now(),
                              );

                              await NotificationService.scheduleCreditCardPaymentNotification(
                                testCard,
                              );
                              _showSnackBar(
                                'Payment notification scheduled for 1 minute from now!',
                              );
                            } catch (e) {
                              _showSnackBar(
                                'Error scheduling payment notification: $e',
                              );
                              print('Payment notification error: $e');
                            }
                          },
                          icon: Icon(
                            Icons.payment,
                            size: 20,
                            color: colorScheme.onPrimary,
                          ),
                          label: Text(
                            'Test Payment Notification (1 min)',
                            style: theme.textTheme.bodyMedium?.copyWith(
                              fontWeight: FontWeight.w600,
                              color: colorScheme.onPrimary,
                            ),
                          ),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColorPalette.warning,
                            foregroundColor: colorScheme.onPrimary,
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                        ),
                      ),

                      const SizedBox(height: 12),

                      // Schedule All Credit Card Notifications Button
                      Container(
                        width: double.infinity,
                        child: ElevatedButton.icon(
                          onPressed: () async {
                            try {
                              await NotificationService.scheduleAllCreditCardNotifications();
                              _showSnackBar(
                                'All credit card notifications scheduled!',
                              );
                            } catch (e) {
                              _showSnackBar(
                                'Error scheduling credit card notifications: $e',
                              );
                              print('Credit card notification error: $e');
                            }
                          },
                          icon: Icon(
                            Icons.credit_card,
                            size: 20,
                            color: colorScheme.onPrimary,
                          ),
                          label: Text(
                            'Schedule All Credit Card Notifications',
                            style: theme.textTheme.bodyMedium?.copyWith(
                              fontWeight: FontWeight.w600,
                              color: colorScheme.onPrimary,
                            ),
                          ),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColorPalette.success,
                            foregroundColor: colorScheme.onPrimary,
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                        ),
                      ),

                      const SizedBox(height: 24),

                      // Information Card
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: theme.colorScheme.primary.withValues(
                            alpha: 0.05,
                          ),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: theme.colorScheme.primary.withValues(
                              alpha: 0.2,
                            ),
                          ),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(
                                  Icons.info_outline,
                                  color: theme.colorScheme.primary,
                                  size: 20,
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  'How it works',
                                  style: theme.textTheme.titleSmall?.copyWith(
                                    fontWeight: FontWeight.bold,
                                    color: theme.colorScheme.primary,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 12),
                            _buildInfoRow(
                              Icons.notification_important,
                              'Smart Notifications',
                              'Only unpaid expenses will trigger notifications.',
                            ),
                            const SizedBox(height: 8),
                            _buildInfoRow(
                              Icons.schedule,
                              'Automatic Scheduling',
                              'Notifications are automatically scheduled when you add or update expenses.',
                            ),
                            const SizedBox(height: 8),
                            _buildInfoRow(
                              Icons.phone_android,
                              'System Integration',
                              'Uses your device\'s notification system. Make sure app notifications are enabled in your device settings.',
                            ),

                            const SizedBox(height: 8),
                          ],
                        ),
                      ),
                    ],
                  ],
                ),
              ),
    );
  }

  Widget _buildSettingCard({
    required IconData icon,
    required String title,
    required String subtitle,
    Widget? trailing,
    VoidCallback? onTap,
  }) {
    final theme = Theme.of(context);

    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: theme.cardColor,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: theme.shadowColor.withValues(alpha: 0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: theme.colorScheme.primary.withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(icon, color: theme.colorScheme.primary, size: 20),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Text(
                    subtitle,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.hintColor,
                    ),
                  ),
                ],
              ),
            ),
            if (trailing != null) trailing,
          ],
        ),
      ),
    );
  }

  Widget _buildDayChip(int days, String label) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final isSelected = _settings.daysBefore.contains(days);

    return GestureDetector(
      onTap: () => _toggleDaysBefore(days),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color:
              isSelected
                  ? theme.colorScheme.primary
                  : theme.hintColor.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color:
                isSelected
                    ? theme.colorScheme.primary
                    : theme.hintColor.withValues(alpha: 0.3),
          ),
        ),
        child: Text(
          label,
          style: theme.textTheme.bodySmall?.copyWith(
            color: isSelected ? colorScheme.onPrimary : theme.hintColor,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
          ),
        ),
      ),
    );
  }

  Widget _buildInfoRow(IconData icon, String title, String description) {
    final theme = Theme.of(context);

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(icon, size: 16, color: theme.colorScheme.primary),
        const SizedBox(width: 8),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: theme.textTheme.bodySmall?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              Text(
                description,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.hintColor,
                  height: 1.3,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
