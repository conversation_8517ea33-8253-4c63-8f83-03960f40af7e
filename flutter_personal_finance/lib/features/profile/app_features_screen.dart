import 'package:flutter/material.dart';

class AppFeaturesScreen extends StatelessWidget {
  const AppFeaturesScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        title: Text(
          'App Features',
          style: theme.appBarTheme.titleTextStyle?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header Section
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    colorScheme.primary.withValues(alpha: 0.1),
                    colorScheme.tertiary.withValues(alpha: 0.1),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: colorScheme.primary.withValues(alpha: 0.3),
                ),
              ),
              child: Column(
                children: [
                  Icon(
                    Icons.account_balance_wallet,
                    size: 48,
                    color: colorScheme.primary,
                  ),
                  const SizedBox(height: 12),
                  Text(
                    '💰 Personal Finance',
                    style: theme.textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: colorScheme.primary,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Complete Financial Management Solution',
                    style: theme.textTheme.bodyLarge?.copyWith(
                      color: colorScheme.onSurfaceVariant,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'NO SERVER • COMPLETELY LOCAL • 100% PRIVATE',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: colorScheme.primary,
                      fontWeight: FontWeight.bold,
                      letterSpacing: 0.8,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),

            // Core Financial Management
            _buildFeatureSection(
              context,
              '🏦 Core Financial Management',
              const Color(0xFF4A90E2),
              [
                _FeatureItem(
                  '🔐',
                  'Face Authentication',
                  'Secure biometric access to your financial data',
                ),
                _FeatureItem(
                  '🏦',
                  'Bank & Cash Accounts',
                  'Add and manage multiple bank accounts and cash',
                ),
                _FeatureItem(
                  '💸',
                  'Income & Expense Tracking',
                  'Record all your transactions with categories',
                ),
                _FeatureItem(
                  '🏷️',
                  'Custom Categories',
                  'Create personalized income and expense categories',
                ),
                _FeatureItem(
                  '💰',
                  'Real-time Balance',
                  'Auto-calculated account balances with every transaction',
                ),
              ],
            ),

            // Credit Card Management
            _buildFeatureSection(
              context,
              '💳 Credit Card Management',
              const Color(0xFFFF6B35),
              [
                _FeatureItem(
                  '💳',
                  'Multiple Credit Cards',
                  'Add and manage multiple credit cards with complete details',
                ),
                _FeatureItem(
                  '📅',
                  'Statement & Due Dates',
                  'Track statement closing dates and payment due dates',
                ),
                _FeatureItem(
                  '💰',
                  'Outstanding Balance',
                  'Monitor credit card balances and credit utilization',
                ),
                _FeatureItem(
                  '🏦',
                  'Multi-Bank Support',
                  'Support for Visa, Mastercard, RuPay, and American Express',
                ),
                _FeatureItem(
                  '💸',
                  'Purchase & Payment Tracking',
                  'Record credit card purchases and payments with categories',
                ),
                _FeatureItem(
                  '📊',
                  'Transaction History',
                  'Comprehensive list of all credit card transactions with filters',
                ),
                _FeatureItem(
                  '🔔',
                  'Smart Notifications',
                  'Automated alerts for statement generation and payment due dates',
                ),
                _FeatureItem(
                  '📈',
                  'Spending Analytics',
                  'Category-wise spending analysis and transaction summaries',
                ),
                _FeatureItem(
                  '🗓️',
                  'Auto Date Management',
                  'Automatic date progression when statement or due dates pass',
                ),
                _FeatureItem(
                  '🎨',
                  'Realistic Card Design',
                  'Beautiful credit card tiles with gradients and status indicators',
                ),
                _FeatureItem(
                  '🔄',
                  'Real-time Date Synchronization',
                  'Instant date updates across all screens when opening dashboard or credit card sections',
                ),
                _FeatureItem(
                  '🎯',
                  'Smart Date Progression',
                  'Intelligent handling of month-end dates, leap years, and year rollovers',
                ),
                _FeatureItem(
                  '🎨',
                  'Enhanced UI/UX Design',
                  'Consistent AppBar theming with optimized light/dark mode visibility',
                ),
                _FeatureItem(
                  '🧹',
                  'Simplified Status Management',
                  'Streamlined interface focusing on \'Due Soon\' alerts without overdue clutter',
                ),
                _FeatureItem(
                  '⚡',
                  'Background Date Management',
                  'Zero-maintenance automatic date cycling that works behind the scenes',
                ),
                _FeatureItem(
                  '🔔',
                  'Enhanced Notification System',
                  'Automatic notification rescheduling when dates are updated',
                ),
              ],
            ),

            // Investment Portfolio Management
            _buildFeatureSection(
              context,
              '📈 Investment Portfolio Management',
              const Color(0xFF27AE60),
              [
                _FeatureItem(
                  '💼',
                  'Investment Dashboard',
                  'Collapsible portfolio summary with detailed analytics',
                ),
                _FeatureItem(
                  '📊',
                  'Multiple Investment Types',
                  'SIP, Lump Sum, Manual Entry, Exact Amount tracking',
                ),
                _FeatureItem(
                  '🔄',
                  'Growth Tracking',
                  'Simple Interest, Compound Interest, Manual calculations',
                ),
                _FeatureItem(
                  '💸',
                  'Smart Withdrawals',
                  'Withdrawal system with penalty calculations',
                ),
                _FeatureItem(
                  '📈',
                  'Portfolio Analytics',
                  'Total value, returns %, profit/loss tracking',
                ),
                _FeatureItem(
                  '🏷️',
                  'Investment Status',
                  'Active, Matured, Withdrawn, Paused status tracking',
                ),
              ],
            ),

            // Budget & Planning
            _buildFeatureSection(
              context,
              '📊 Budget & Planning',
              const Color(0xFF8E44AD),
              [
                _FeatureItem(
                  '📅',
                  'Upcoming Expenses',
                  'Plan and track future expenses with notifications',
                ),
                _FeatureItem(
                  '🗂️',
                  'Expense Categories',
                  'Manage categories for upcoming expenses',
                ),
                _FeatureItem(
                  '🔔',
                  'Smart Notifications',
                  'Payment reminders, credit card alerts, and notifications with sound',
                ),
                _FeatureItem(
                  '📈',
                  'Budget Planning',
                  'Set spending limits and track progress',
                ),
                _FeatureItem(
                  '💵',
                  'Cash Flow Analysis',
                  'Income vs expenses summary',
                ),
              ],
            ),

            // Analytics & Insights
            _buildFeatureSection(
              context,
              '📊 Analytics & Insights',
              const Color(0xFF17A2B8),
              [
                _FeatureItem(
                  '📊',
                  'Spending Trends',
                  'Weekly and monthly spending analysis',
                ),
                _FeatureItem(
                  '🥇',
                  'Top Categories',
                  'Track your highest spending categories',
                ),
                _FeatureItem(
                  '📈',
                  'Advanced Analytics',
                  'Financial insights and health score',
                ),
                _FeatureItem(
                  '📊',
                  'Data Visualization',
                  'Interactive charts, graphs & visual reports',
                ),
              ],
            ),

            // Recurring & Automation
            _buildFeatureSection(
              context,
              '🔁 Recurring & Automation',
              const Color(0xFFE91E63),
              [
                _FeatureItem(
                  '🔁',
                  'Recurring Transactions',
                  'Automate regular income and expenses',
                ),
                _FeatureItem(
                  '➕',
                  'Quick Entry',
                  'Fast add recurring entries to transactions',
                ),
                _FeatureItem(
                  '✅',
                  'Mark as Paid',
                  'Convert upcoming expenses to actual transactions',
                ),
                _FeatureItem(
                  '⏰',
                  'Frequency Options',
                  'Daily, weekly, monthly, quarterly, yearly',
                ),
                _FeatureItem(
                  '📅',
                  'End Date Control',
                  'Set when recurring transactions should stop',
                ),
              ],
            ),

            // Data Management & Export
            _buildFeatureSection(
              context,
              '📤 Data Management & Export',
              const Color(0xFF3498DB),
              [
                _FeatureItem(
                  '📄',
                  'PDF Export',
                  'Generate detailed transaction reports with investment summary',
                ),
                _FeatureItem(
                  '📤',
                  'Data Export',
                  'Backup all data in JSON format for other devices',
                ),
                _FeatureItem(
                  '📥',
                  'Data Import',
                  'Restore data from exported backups',
                ),
                _FeatureItem(
                  '🗑️',
                  'Bulk Delete',
                  'Delete transactions by date range and account',
                ),
                _FeatureItem(
                  '💾',
                  'Local Storage',
                  'All data stored locally - no server required',
                ),
              ],
            ),

            // Privacy & Security
            _buildFeatureSection(
              context,
              '🔒 Privacy & Security',
              const Color(0xFF16A085),
              [
                _FeatureItem(
                  '🔐',
                  'Biometric Auth',
                  'Face ID, Fingerprint, and PIN protection',
                ),
                _FeatureItem(
                  '👁️',
                  'Privacy Controls',
                  'Toggle to show/hide income amounts',
                ),
                _FeatureItem(
                  '🔒',
                  'Hide Entries',
                  'Option to hide individual income entries',
                ),
                _FeatureItem(
                  '💾',
                  'Offline Operation',
                  'Works completely offline - no internet required',
                ),
                _FeatureItem(
                  '🛡️',
                  'Data Integrity',
                  'Built-in safeguards and validation',
                ),
              ],
            ),

            // Technical Features
            _buildFeatureSection(
              context,
              '⚙️ Technical Features',
              const Color(0xFF9B59B6),
              [
                _FeatureItem(
                  '🌓',
                  'Light/Dark Mode',
                  'Professional themes with vibrant colors',
                ),
                _FeatureItem(
                  '🏗️',
                  'Multi-environment',
                  'Development, Staging, Production, Enterprise builds',
                ),
                _FeatureItem(
                  '📱',
                  'Responsive Design',
                  'Optimized for all screen sizes',
                ),
                _FeatureItem(
                  '⚡',
                  'Performance',
                  'Optimized for large datasets',
                ),
              ],
            ),

            const SizedBox(height: 24),

            // Footer
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: theme.cardColor,
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: colorScheme.primary.withValues(alpha: 0.2),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Icon(Icons.verified, color: colorScheme.primary, size: 32),
                  const SizedBox(height: 12),
                  Text(
                    '55+ Features',
                    style: theme.textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: colorScheme.primary,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8),
                  SizedBox(
                    width: double.infinity,
                    child: Text(
                      'Complete personal finance management with investment & credit card tracking',
                      textAlign: TextAlign.center,
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: theme.hintColor,
                      ),
                    ),
                  ),
                  const SizedBox(height: 12),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                    decoration: BoxDecoration(
                      color: colorScheme.primary.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Text(
                      '🔒 100% Private & Secure',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: colorScheme.primary,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeatureSection(
    BuildContext context,
    String title,
    Color color,
    List<_FeatureItem> features,
  ) {
    final theme = Theme.of(context);

    return Container(
      margin: const EdgeInsets.only(bottom: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: color.withValues(alpha: 0.3)),
            ),
            child: Text(
              title,
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
          ),
          const SizedBox(height: 12),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: theme.cardColor,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: theme.shadowColor.withValues(alpha: 0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              children:
                  features
                      .map(
                        (feature) => _buildFeatureRow(context, feature, color),
                      )
                      .toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFeatureRow(
    BuildContext context,
    _FeatureItem feature,
    Color color,
  ) {
    final theme = Theme.of(context);

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 36,
            height: 36,
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Center(
              child: Text(feature.emoji, style: const TextStyle(fontSize: 18)),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  feature.title,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: color,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  feature.description,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.hintColor,
                    height: 1.3,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class _FeatureItem {
  final String emoji;
  final String title;
  final String description;

  _FeatureItem(this.emoji, this.title, this.description);
}
