import 'package:flutter/material.dart';
import '../../resources/app_theme.dart';
import '../../services/local_storage_service.dart';

class IncomeExpenseCategoryModel {
  final String id;
  final String name;
  final String type; // 'income' or 'expense'
  final bool isInvestment; // New field for investment flag
  final DateTime createdAt;
  final DateTime updatedAt;

  IncomeExpenseCategoryModel({
    required this.id,
    required this.name,
    required this.type,
    bool? isInvestment, // New optional parameter
    required this.createdAt,
    required this.updatedAt,
  }) : isInvestment = isInvestment ?? false; // Default to false

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'type': type,
      'isInvestment': isInvestment, // Include in JSON
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  factory IncomeExpenseCategoryModel.fromJson(Map<String, dynamic> json) {
    try {
      return IncomeExpenseCategoryModel(
        id:
            json['id']?.toString() ??
            DateTime.now().millisecondsSinceEpoch.toString(),
        name: json['name']?.toString() ?? 'Unknown',
        type: json['type']?.toString() ?? 'expense',
        isInvestment: json['isInvestment'] ?? false, // Parse investment flag
        createdAt:
            json['createdAt'] != null
                ? DateTime.parse(json['createdAt'].toString())
                : DateTime.now(),
        updatedAt:
            json['updatedAt'] != null
                ? DateTime.parse(json['updatedAt'].toString())
                : DateTime.now(),
      );
    } catch (e) {
      print('Error parsing category JSON: $e');
      print('JSON data: $json');
      // Return a default category to prevent crashes
      return IncomeExpenseCategoryModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        name: 'Unknown Category',
        type: 'expense',
        isInvestment: false,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
    }
  }

  IncomeExpenseCategoryModel copyWith({
    String? id,
    String? name,
    String? type,
    bool? isInvestment, // Add to copyWith
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return IncomeExpenseCategoryModel(
      id: id ?? this.id,
      name: name ?? this.name,
      type: type ?? this.type,
      isInvestment: isInvestment ?? this.isInvestment, // Include in copyWith
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

class IncomeExpenseCategoriesScreen extends StatefulWidget {
  const IncomeExpenseCategoriesScreen({super.key});

  @override
  State<IncomeExpenseCategoriesScreen> createState() =>
      _IncomeExpenseCategoriesScreenState();
}

class _IncomeExpenseCategoriesScreenState
    extends State<IncomeExpenseCategoriesScreen> {
  List<IncomeExpenseCategoryModel> _categories = [];
  bool _isLoading = true;
  String _selectedFilter = 'all'; // 'all', 'income', 'expense'

  @override
  void initState() {
    super.initState();
    _loadCategories();
  }

  // Test method to load sample data for debugging
  void _loadTestCategories() {
    setState(() {
      _categories = [
        IncomeExpenseCategoryModel(
          id: '1',
          name: 'Salary',
          type: 'income',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        IncomeExpenseCategoryModel(
          id: '2',
          name: 'Food',
          type: 'expense',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        IncomeExpenseCategoryModel(
          id: '3',
          name: 'Investment',
          type: 'income',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        IncomeExpenseCategoryModel(
          id: '4',
          name: 'Transport',
          type: 'expense',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
      ];
      _isLoading = false;
    });
    print('Test categories loaded: ${_categories.length}');
  }

  Future<void> _loadCategories() async {
    try {
      final categoriesData =
          await LocalStorageService.getIncomeExpenseCategories();

      print('Raw categories data: $categoriesData'); // Debug log
      print('Categories count: ${categoriesData.length}'); // Debug log

      final categories = <IncomeExpenseCategoryModel>[];

      for (int i = 0; i < categoriesData.length; i++) {
        try {
          final categoryJson = categoriesData[i];
          print('Processing category $i: $categoryJson'); // Debug log

          // Handle different data formats
          if (categoryJson is Map<String, dynamic>) {
            categories.add(IncomeExpenseCategoryModel.fromJson(categoryJson));
          } else if (categoryJson is Map) {
            // Convert Map to Map<String, dynamic>
            final convertedJson = Map<String, dynamic>.from(categoryJson);
            categories.add(IncomeExpenseCategoryModel.fromJson(convertedJson));
          } else {
            print('Invalid category format at index $i: $categoryJson');
          }
        } catch (e) {
          print('Error parsing category at index $i: $e');
          print('Category data: ${categoriesData[i]}');
        }
      }

      print('Successfully loaded ${categories.length} categories'); // Debug log

      setState(() {
        _categories = categories;
        _isLoading = false;
      });
    } catch (e) {
      print('Error loading categories: $e');
      // Show error to user
      _showSnackBar('Error loading categories: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  List<IncomeExpenseCategoryModel> get _filteredCategories {
    switch (_selectedFilter) {
      case 'income':
        return _categories.where((cat) => cat.type == 'income').toList();
      case 'expense':
        return _categories.where((cat) => cat.type == 'expense').toList();
      default:
        return _categories;
    }
  }

  Future<void> _addCategory() async {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    final TextEditingController nameController = TextEditingController();
    final GlobalKey<FormState> formKey = GlobalKey<FormState>();
    String selectedType = 'expense'; // Default to expense
    bool isInvestment = false; // Investment flag (only for expense categories)

    await showDialog(
      context: context,
      builder:
          (context) => StatefulBuilder(
            builder:
                (context, setState) => AlertDialog(
                  title: Row(
                    children: [
                      Expanded(
                        child: Text(
                          'Add Category',
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      // Category Type Label (moved from center)
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color:
                              selectedType == 'income'
                                  ? theme.colorScheme.primary.withValues(
                                    alpha: 0.1,
                                  )
                                  : theme.colorScheme.error.withValues(
                                    alpha: 0.1,
                                  ),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color:
                                selectedType == 'income'
                                    ? theme.colorScheme.primary
                                    : theme.colorScheme.error,
                          ),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              selectedType == 'income'
                                  ? Icons.add_circle
                                  : Icons.remove_circle,
                              color:
                                  selectedType == 'income'
                                      ? theme.colorScheme.primary
                                      : theme.colorScheme.error,
                              size: 14,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              selectedType == 'income' ? 'Income' : 'Expense',
                              style: theme.textTheme.bodySmall?.copyWith(
                                color:
                                    selectedType == 'income'
                                        ? theme.colorScheme.primary
                                        : theme.colorScheme.error,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  content: SizedBox(
                    width: double.maxFinite,
                    child: Form(
                      key: formKey,
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          // Category Type Switch
                          Container(
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: theme.hintColor.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Row(
                              children: [
                                Icon(
                                  Icons.category,
                                  color: theme.hintColor,
                                  size: 20,
                                ),
                                const SizedBox(width: 12),
                                Text(
                                  'Category Type',
                                  style: theme.textTheme.bodyMedium?.copyWith(
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                                const Spacer(),
                                // Toggle Switch
                                GestureDetector(
                                  onTap: () {
                                    setState(() {
                                      selectedType =
                                          selectedType == 'income'
                                              ? 'expense'
                                              : 'income';
                                      // Reset investment flag when switching to income
                                      if (selectedType == 'income') {
                                        isInvestment = false;
                                      }
                                    });
                                  },
                                  child: Container(
                                    width: 60,
                                    height: 30,
                                    decoration: BoxDecoration(
                                      color:
                                          selectedType == 'income'
                                              ? theme.colorScheme.primary
                                              : theme.colorScheme.error,
                                      borderRadius: BorderRadius.circular(15),
                                    ),
                                    child: Stack(
                                      children: [
                                        Positioned(
                                          left:
                                              selectedType == 'income' ? 32 : 2,
                                          top: 2,
                                          child: Container(
                                            width: 26,
                                            height: 26,
                                            decoration: const BoxDecoration(
                                              color: Colors.white,
                                              shape: BoxShape.circle,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(height: 16),

                          // Investment Flag Checkbox (only for expense categories)
                          if (selectedType == 'expense') ...[
                            Container(
                              padding: const EdgeInsets.all(12),
                              decoration: BoxDecoration(
                                color: Colors.green.withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(
                                  color: Colors.green.withValues(alpha: 0.3),
                                ),
                              ),
                              child: Row(
                                children: [
                                  Icon(
                                    Icons.trending_up,
                                    color: Colors.green,
                                    size: 20,
                                  ),
                                  const SizedBox(width: 12),
                                  Expanded(
                                    child: Text(
                                      'Investment Category',
                                      style: theme.textTheme.bodyMedium
                                          ?.copyWith(
                                            fontWeight: FontWeight.w600,
                                          ),
                                    ),
                                  ),
                                  Checkbox(
                                    value: isInvestment,
                                    onChanged: (value) {
                                      setState(() {
                                        isInvestment = value ?? false;
                                      });
                                    },
                                    activeColor: Colors.green,
                                  ),
                                ],
                              ),
                            ),
                            const SizedBox(height: 16),
                          ],

                          // Category Name Field
                          TextFormField(
                            controller: nameController,
                            decoration: InputDecoration(
                              labelText: 'Category Name',
                              hintText:
                                  selectedType == 'income'
                                      ? 'e.g., Salary, Bonus, Investment'
                                      : 'e.g., Food, Transport, Shopping',
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                              prefixIcon: Icon(
                                selectedType == 'income'
                                    ? Icons.add_circle
                                    : Icons.remove_circle,
                                color:
                                    selectedType == 'income'
                                        ? theme.colorScheme.primary
                                        : theme.colorScheme.error,
                              ),
                            ),
                            validator: (value) {
                              if (value == null || value.trim().isEmpty) {
                                return 'Please enter category name';
                              }
                              return null;
                            },
                          ),
                        ],
                      ),
                    ),
                  ),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.of(context).pop(),
                      child: Text(
                        'Cancel',
                        style: theme.textTheme.labelLarge?.copyWith(
                          color: theme.hintColor,
                        ),
                      ),
                    ),
                    TextButton(
                      onPressed: () async {
                        if (formKey.currentState!.validate()) {
                          final category = IncomeExpenseCategoryModel(
                            id:
                                DateTime.now().millisecondsSinceEpoch
                                    .toString(),
                            name: nameController.text.trim(),
                            type: selectedType,
                            isInvestment:
                                selectedType == 'expense'
                                    ? isInvestment
                                    : false, // Investment flag only for expenses
                            createdAt: DateTime.now(),
                            updatedAt: DateTime.now(),
                          );

                          await LocalStorageService.addIncomeExpenseCategory(
                            category.toJson(),
                          );
                          Navigator.of(context).pop();
                          await _loadCategories();
                          _showSnackBar('Category added successfully');
                        }
                      },
                      child: Text(
                        'Add',
                        style: theme.textTheme.labelLarge?.copyWith(
                          color: theme.colorScheme.primary,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
          ),
    );
  }

  Future<void> _editCategory(IncomeExpenseCategoryModel category) async {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final TextEditingController nameController = TextEditingController(
      text: category.name,
    );
    final GlobalKey<FormState> formKey = GlobalKey<FormState>();
    String selectedType = category.type;
    bool isInvestment = category.isInvestment; // Initialize with existing value

    await showDialog(
      context: context,
      builder:
          (context) => StatefulBuilder(
            builder:
                (context, setState) => AlertDialog(
                  title: Row(
                    children: [
                      Expanded(
                        child: Text(
                          'Edit Category',
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      // Category Type Label (moved from center)
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color:
                              selectedType == 'income'
                                  ? theme.colorScheme.primary.withValues(
                                    alpha: 0.1,
                                  )
                                  : theme.colorScheme.error.withValues(
                                    alpha: 0.1,
                                  ),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color:
                                selectedType == 'income'
                                    ? theme.colorScheme.primary
                                    : theme.colorScheme.error,
                          ),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              selectedType == 'income'
                                  ? Icons.add_circle
                                  : Icons.remove_circle,
                              color:
                                  selectedType == 'income'
                                      ? theme.colorScheme.primary
                                      : theme.colorScheme.error,
                              size: 14,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              selectedType == 'income' ? 'Income' : 'Expense',
                              style: theme.textTheme.bodySmall?.copyWith(
                                color:
                                    selectedType == 'income'
                                        ? theme.colorScheme.primary
                                        : theme.colorScheme.error,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  content: SizedBox(
                    width: double.maxFinite,
                    child: Form(
                      key: formKey,
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          // Category Type Switch
                          Container(
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: theme.hintColor.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Row(
                              children: [
                                Icon(
                                  Icons.category,
                                  color: theme.hintColor,
                                  size: 20,
                                ),
                                const SizedBox(width: 12),
                                Text(
                                  'Category Type',
                                  style: theme.textTheme.bodyMedium?.copyWith(
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                                const Spacer(),
                                // Toggle Switch
                                GestureDetector(
                                  onTap: () {
                                    setState(() {
                                      selectedType =
                                          selectedType == 'income'
                                              ? 'expense'
                                              : 'income';
                                      // Reset investment flag when switching to income
                                      if (selectedType == 'income') {
                                        isInvestment = false;
                                      }
                                    });
                                  },
                                  child: Container(
                                    width: 60,
                                    height: 30,
                                    decoration: BoxDecoration(
                                      color:
                                          selectedType == 'income'
                                              ? theme.colorScheme.primary
                                              : theme.colorScheme.error,
                                      borderRadius: BorderRadius.circular(15),
                                    ),
                                    child: Stack(
                                      children: [
                                        Positioned(
                                          left:
                                              selectedType == 'income' ? 32 : 2,
                                          top: 2,
                                          child: Container(
                                            width: 26,
                                            height: 26,
                                            decoration: const BoxDecoration(
                                              color: Colors.white,
                                              shape: BoxShape.circle,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(height: 16),

                          // Category Name Field
                          TextFormField(
                            controller: nameController,
                            decoration: InputDecoration(
                              labelText: 'Category Name',
                              hintText:
                                  selectedType == 'income'
                                      ? 'e.g., Salary, Bonus, Investment'
                                      : 'e.g., Food, Transport, Shopping',
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                              prefixIcon: Icon(
                                selectedType == 'income'
                                    ? Icons.add_circle
                                    : Icons.remove_circle,
                                color:
                                    selectedType == 'income'
                                        ? theme.colorScheme.primary
                                        : theme.colorScheme.error,
                              ),
                            ),
                            validator: (value) {
                              if (value == null || value.trim().isEmpty) {
                                return 'Please enter category name';
                              }
                              return null;
                            },
                          ),
                        ],
                      ),
                    ),
                  ),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.of(context).pop(),
                      child: Text(
                        'Cancel',
                        style: theme.textTheme.labelLarge?.copyWith(
                          color: theme.hintColor,
                        ),
                      ),
                    ),
                    TextButton(
                      onPressed: () async {
                        if (formKey.currentState!.validate()) {
                          final updatedCategory = category.copyWith(
                            name: nameController.text.trim(),
                            type: selectedType,
                            isInvestment:
                                selectedType == 'expense'
                                    ? isInvestment
                                    : false, // Investment flag only for expenses
                            updatedAt: DateTime.now(),
                          );

                          await LocalStorageService.updateIncomeExpenseCategory(
                            updatedCategory.toJson(),
                          );
                          Navigator.of(context).pop();
                          await _loadCategories();
                          _showSnackBar('Category updated successfully');
                        }
                      },
                      child: Text(
                        'Update',
                        style: theme.textTheme.labelLarge?.copyWith(
                          color: theme.colorScheme.primary,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
          ),
    );
  }

  Future<void> _deleteCategory(IncomeExpenseCategoryModel category) async {
    final theme = Theme.of(context);
    final shouldDelete = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(
              'Delete Category',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            content: Text(
              'Are you sure you want to delete "${category.name}"? This will affect all transactions using this category.',
              style: theme.textTheme.bodyMedium,
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: Text(
                  'Cancel',
                  style: theme.textTheme.labelLarge?.copyWith(
                    color: theme.hintColor,
                  ),
                ),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                child: Text(
                  'Delete',
                  style: theme.textTheme.labelLarge?.copyWith(
                    color: theme.colorScheme.error,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
    );

    if (shouldDelete == true) {
      await LocalStorageService.deleteIncomeExpenseCategory(category.id);
      await _loadCategories();
      _showSnackBar('Category deleted');
    }
  }

  void _showSnackBar(String message) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          message,
          style: theme.textTheme.bodyMedium?.copyWith(
            color: colorScheme.onPrimary,
          ),
        ),
        backgroundColor: theme.colorScheme.primary,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        title: Text(
          'Transaction Categories',
          style: theme.appBarTheme.titleTextStyle?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios),
          onPressed: () => Navigator.of(context).pop(),
        ),
        centerTitle: false,
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: _addCategory,
            tooltip: 'Add Category',
          ),
        ],
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : Column(
                children: [
                  // Filter Tabs
                  Container(
                    padding: const EdgeInsets.all(16),
                    child: Row(
                      children: [
                        Expanded(child: _buildFilterChip('All', 'all')),
                        const SizedBox(width: 8),
                        Expanded(child: _buildFilterChip('Income', 'income')),
                        const SizedBox(width: 8),
                        Expanded(child: _buildFilterChip('Expense', 'expense')),
                      ],
                    ),
                  ),

                  // Categories List
                  Expanded(
                    child:
                        _filteredCategories.isEmpty
                            ? _buildEmptyState()
                            : _buildCategoriesList(),
                  ),
                ],
              ),
      floatingActionButton: FloatingActionButton(
        backgroundColor: theme.colorScheme.primary,
        onPressed: _addCategory,
        child: Icon(Icons.add, color: colorScheme.onPrimary),
      ),
    );
  }

  Widget _buildFilterChip(String label, String value) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final isSelected = _selectedFilter == value;
    final color =
        value == 'income'
            ? theme.colorScheme.primary
            : value == 'expense'
            ? theme.colorScheme.error
            : theme.hintColor;

    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedFilter = value;
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
        decoration: BoxDecoration(
          color: isSelected ? color : color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isSelected ? color : color.withValues(alpha: 0.3),
          ),
        ),
        child: Text(
          label,
          textAlign: TextAlign.center,
          style: theme.textTheme.bodySmall?.copyWith(
            color: isSelected ? colorScheme.onPrimary : color,
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    final emptyMessage =
        _selectedFilter == 'all'
            ? 'No categories yet'
            : _selectedFilter == 'income'
            ? 'No income categories yet'
            : 'No expense categories yet';

    final emptyDescription =
        _selectedFilter == 'all'
            ? 'Create categories to organize your income and expenses like Salary, Food, Transport, etc.'
            : _selectedFilter == 'income'
            ? 'Create income categories like Salary, Bonus, Investment, etc.'
            : 'Create expense categories like Food, Transport, Shopping, etc.';

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: theme.hintColor.withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                _selectedFilter == 'income'
                    ? Icons.add_circle
                    : _selectedFilter == 'expense'
                    ? Icons.remove_circle
                    : Icons.category,
                size: 64,
                color: theme.hintColor.withValues(alpha: 0.5),
              ),
            ),
            const SizedBox(height: 24),
            Text(
              emptyMessage,
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: theme.hintColor,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              emptyDescription,
              textAlign: TextAlign.center,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.hintColor.withValues(alpha: 0.8),
              ),
            ),
            const SizedBox(height: 32),
            ElevatedButton.icon(
              onPressed: _addCategory,
              style: ElevatedButton.styleFrom(
                backgroundColor: theme.colorScheme.primary,
                foregroundColor: colorScheme.onPrimary,
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              icon: Icon(Icons.add, color: colorScheme.onPrimary),
              label: Text(
                _selectedFilter == 'all'
                    ? 'Add First Category'
                    : 'Add ${_selectedFilter == 'income' ? 'Income' : 'Expense'} Category',
                style: theme.textTheme.labelLarge?.copyWith(
                  color: colorScheme.onPrimary,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoriesList() {
    return RefreshIndicator(
      onRefresh: _loadCategories,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _filteredCategories.length,
        itemBuilder: (context, index) {
          final category = _filteredCategories[index];
          return _buildCategoryCard(category);
        },
      ),
    );
  }

  Widget _buildCategoryCard(IncomeExpenseCategoryModel category) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final isIncome = category.type == 'income';
    final color =
        isIncome ? theme.colorScheme.primary : theme.colorScheme.error;
    final icon = isIncome ? Icons.add_circle : Icons.remove_circle;

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: theme.cardColor,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: theme.shadowColor.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 1),
          ),
        ],
        border: Border.all(
          color:
              category.isInvestment
                  ? Colors.green.withValues(alpha: 0.3)
                  : color.withValues(alpha: 0.2),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(icon, color: color, size: 18),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    category.name,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                if (category.isInvestment) ...[
                  const SizedBox(width: 8),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 6,
                      vertical: 2,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.green.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      Icons.trending_up,
                      size: 12,
                      color: Colors.green,
                    ),
                  ),
                ],
                const SizedBox(width: 8),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 6,
                    vertical: 2,
                  ),
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    isIncome ? 'Income' : 'Expense',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: color,
                      fontWeight: FontWeight.w500,
                      fontSize: 10,
                    ),
                  ),
                ),
              ],
            ),
          ),
          PopupMenuButton<String>(
            padding: EdgeInsets.zero,
            iconSize: 18,
            onSelected: (value) {
              if (value == 'edit') {
                _editCategory(category);
              } else if (value == 'delete') {
                _deleteCategory(category);
              }
            },
            itemBuilder:
                (context) => [
                  PopupMenuItem(
                    value: 'edit',
                    child: Row(
                      children: [
                        Icon(
                          Icons.edit,
                          size: 16,
                          color: theme.colorScheme.primary,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'Edit',
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: theme.colorScheme.primary,
                          ),
                        ),
                      ],
                    ),
                  ),
                  PopupMenuItem(
                    value: 'delete',
                    child: Row(
                      children: [
                        Icon(
                          Icons.delete,
                          size: 16,
                          color: theme.colorScheme.error,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'Delete',
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: theme.colorScheme.error,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
          ),
        ],
      ),
    );
  }
}
