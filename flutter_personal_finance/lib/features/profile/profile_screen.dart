import 'dart:convert';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:go_router/go_router.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../resources/app_theme.dart';
import '../../services/local_storage_service.dart';
import '../../utils/helper/route.dart';

import '../../features/models/bank_account_model.dart';
import '../../features/models/transaction_model.dart';
import 'app_features_screen.dart';
import 'advanced_analytics_screen.dart';
import 'income_expense_categories_screen.dart';
import 'transaction_export_screen.dart';

import '../bank/add_bank_screen.dart';
import '../budget/budget_planner_screen.dart';
import '../upcoming_expenses/upcoming_expenses_list_screen.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  List<BankAccountModel> _bankAccounts = [];
  bool _isLoading = true;
  bool _isExporting = false;
  bool _isImporting = false;

  @override
  void initState() {
    super.initState();
    _loadUserData();
  }

  Future<void> _loadUserData() async {
    try {
      // Load bank accounts
      final accounts = await LocalStorageService.getBankAccounts();

      setState(() {
        _bankAccounts = accounts;
        _isLoading = false;
      });
    } catch (e) {
      print('Error loading user data: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _showSnackBar(String message) {
    final theme = Theme.of(context);
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: theme.colorScheme.primary,
      ),
    );
  }

  /// Navigate to a screen while preserving the bottom navigation bar
  void _navigateWithBottomNav(Widget screen) {
    Navigator.of(context).push(MaterialPageRoute(builder: (context) => screen));
  }

  Future<void> _editBankAccount(BankAccountModel account) async {
    // Use GoRouter with extra parameter for BankAccountModel
    final result = await GoRouter.of(context).pushNamed<bool>(
      GoRouterConstants.editBank,
      pathParameters: {'bankId': account.id.toString()},
      extra: account,
    );

    if (result == true) {
      await _loadUserData();
      _showSnackBar('profile_bank_account_updated_successfully'.tr());
    }
  }

  Future<void> _deleteBankAccount(BankAccountModel account) async {
    final theme = Theme.of(context);
    final shouldDelete = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text('profile_delete_bank_account'.tr()),
            content: Text(
              'profile_delete_bank_confirm'.tr(
                namedArgs: {'bankName': account.bankName},
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: Text('profile_cancel'.tr()),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                child: Text(
                  'profile_delete'.tr(),
                  style: TextStyle(color: theme.colorScheme.error),
                ),
              ),
            ],
          ),
    );

    if (shouldDelete == true) {
      await LocalStorageService.deleteBankAccount(account.id);
      await _loadUserData();
      _showSnackBar('profile_bank_account_deleted'.tr());
    }
  }

  Future<void> _showDeleteTransactionsDialog() async {
    final theme = Theme.of(context);
    if (_bankAccounts.isEmpty) {
      _showSnackBar('profile_no_bank_accounts_found'.tr());
      return;
    }

    BankAccountModel? selectedAccount = _bankAccounts.first;
    DateTimeRange? selectedDateRange;

    await showDialog(
      context: context,
      builder:
          (context) => StatefulBuilder(
            builder:
                (context, setState) => AlertDialog(
                  title: Text('profile_delete_transactions'.tr()),
                  content: SizedBox(
                    width: double.maxFinite,
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('profile_select_bank_and_date_range'.tr()),
                        const SizedBox(height: 16),

                        // Bank Account Selection
                        Text(
                          'profile_bank_account'.tr(),
                          style: theme.textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const SizedBox(height: 8),
                        DropdownButton<BankAccountModel>(
                          value: selectedAccount,
                          isExpanded: true,
                          items:
                              _bankAccounts.map((account) {
                                return DropdownMenuItem<BankAccountModel>(
                                  value: account,
                                  child: Text(
                                    '${account.bankName} - ${account.accountNumber}',
                                  ),
                                );
                              }).toList(),
                          onChanged: (value) {
                            setState(() {
                              selectedAccount = value;
                            });
                          },
                        ),
                        const SizedBox(height: 16),

                        // Date Range Selection
                        Text(
                          'profile_date_range'.tr(),
                          style: theme.textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const SizedBox(height: 8),
                        GestureDetector(
                          onTap: () async {
                            final picked = await showDateRangePicker(
                              context: context,
                              firstDate: DateTime(2020),
                              lastDate: DateTime.now().add(
                                const Duration(days: 365),
                              ), // Allow future dates up to 1 year
                              initialDateRange: selectedDateRange,
                            );
                            if (picked != null) {
                              setState(() {
                                selectedDateRange = picked;
                              });
                            }
                          },
                          child: Container(
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              border: Border.all(
                                color: theme.hintColor.withValues(alpha: 0.3),
                              ),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  selectedDateRange == null
                                      ? 'profile_select_date_range'.tr()
                                      : '${DateFormat('MMM dd, yyyy').format(selectedDateRange!.start)} - ${DateFormat('MMM dd, yyyy').format(selectedDateRange!.end)}',
                                  style: theme.textTheme.bodyMedium,
                                ),
                                const Icon(Icons.calendar_today),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.of(context).pop(),
                      child: Text('profile_cancel'.tr()),
                    ),
                    TextButton(
                      onPressed:
                          selectedAccount != null && selectedDateRange != null
                              ? () {
                                Navigator.of(context).pop();
                                _deleteTransactionsInRange(
                                  selectedAccount!,
                                  selectedDateRange!,
                                );
                              }
                              : null,
                      child: Text(
                        'profile_next'.tr(),
                        style: TextStyle(
                          color:
                              selectedAccount != null &&
                                      selectedDateRange != null
                                  ? theme.colorScheme.error
                                  : theme.hintColor,
                        ),
                      ),
                    ),
                  ],
                ),
          ),
    );
  }

  Future<void> _deleteTransactionsInRange(
    BankAccountModel account,
    DateTimeRange dateRange,
  ) async {
    final theme = Theme.of(context);
    try {
      // Show loading dialog
      showDialog(
        context: context,
        barrierDismissible: false,
        builder:
            (context) => AlertDialog(
              content: Row(
                children: [
                  const CircularProgressIndicator(),
                  const SizedBox(width: 16),
                  Text('profile_fetching_transactions'.tr()),
                ],
              ),
            ),
      );

      // Fetch transactions for the selected account and date range
      final transactions = await _fetchTransactionsForRange(
        account.id,
        dateRange,
      );

      // Close loading dialog
      if (mounted) Navigator.of(context).pop();

      if (transactions.isEmpty) {
        _showSnackBar('profile_no_transactions_found_in_range'.tr());
        return;
      }

      // Show confirmation dialog with transaction count
      final shouldDelete = await showDialog<bool>(
        context: context,
        builder:
            (context) => AlertDialog(
              title: Text('profile_confirm_deletion'.tr()),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'profile_found_transactions'.tr(
                      namedArgs: {'count': transactions.length.toString()},
                    ),
                    style: theme.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 12),
                  Text(
                    'profile_bank_label'.tr(
                      namedArgs: {'bankName': account.bankName},
                    ),
                  ),
                  Text(
                    'profile_date_range_label'.tr(
                      namedArgs: {
                        'startDate': DateFormat(
                          'MMM dd, yyyy',
                        ).format(dateRange.start),
                        'endDate': DateFormat(
                          'MMM dd, yyyy',
                        ).format(dateRange.end),
                      },
                    ),
                  ),
                  const SizedBox(height: 12),
                  Text(
                    'profile_delete_warning'.tr(),
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.error,
                    ),
                  ),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(false),
                  child: Text('profile_cancel'.tr()),
                ),
                TextButton(
                  onPressed: () => Navigator.of(context).pop(true),
                  child: Text(
                    'profile_delete_transactions_count'.tr(
                      namedArgs: {'count': transactions.length.toString()},
                    ),
                    style: TextStyle(
                      color: theme.colorScheme.error,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
      );

      if (shouldDelete == true) {
        await _performBulkTransactionDeletion(transactions);
      }
    } catch (e) {
      // Close loading dialog if still open
      if (mounted) Navigator.of(context).pop();
      _showSnackBar(
        'profile_error_fetching_transactions'.tr(
          namedArgs: {'error': e.toString()},
        ),
      );
    }
  }

  Future<List<TransactionModel>> _fetchTransactionsForRange(
    String bankAccountId,
    DateTimeRange dateRange,
  ) async {
    // Get all transactions from local storage
    final allTransactions = await LocalStorageService.getTransactions();

    // Filter transactions by bank account and date range
    return allTransactions.where((transaction) {
      // Check if transaction belongs to the specified bank account
      if (transaction.bankAccountId != bankAccountId) return false;

      // Check if transaction date is within the specified range
      final transactionDate = transaction.date;
      final startDate = DateTime(
        dateRange.start.year,
        dateRange.start.month,
        dateRange.start.day,
      );
      final endDate = DateTime(
        dateRange.end.year,
        dateRange.end.month,
        dateRange.end.day,
        23,
        59,
        59,
      );

      return transactionDate.isAfter(
            startDate.subtract(const Duration(seconds: 1)),
          ) &&
          transactionDate.isBefore(endDate.add(const Duration(seconds: 1)));
    }).toList();
  }

  Future<void> _performBulkTransactionDeletion(
    List<TransactionModel> transactions,
  ) async {
    try {
      // Show loading dialog
      showDialog(
        context: context,
        barrierDismissible: false,
        builder:
            (context) => AlertDialog(
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const CircularProgressIndicator(),
                  const SizedBox(height: 16),
                  Text(
                    'profile_deleting_transactions'.tr(
                      namedArgs: {'count': transactions.length.toString()},
                    ),
                  ),
                ],
              ),
            ),
      );

      int deletedCount = 0;
      int failedCount = 0;

      // Delete transactions one by one from local storage
      for (final transaction in transactions) {
        try {
          await LocalStorageService.deleteTransaction(transaction.id);
          deletedCount++;
        } catch (e) {
          failedCount++;
          print('Failed to delete transaction ${transaction.id}: $e');
        }
      }

      // Close loading dialog
      if (mounted) Navigator.of(context).pop();

      // Show result
      if (failedCount == 0) {
        _showSnackBar(
          'profile_successfully_deleted_transactions'.tr(
            namedArgs: {'count': deletedCount.toString()},
          ),
        );
      } else {
        _showSnackBar(
          'profile_deleted_with_failures'.tr(
            namedArgs: {
              'deletedCount': deletedCount.toString(),
              'failedCount': failedCount.toString(),
            },
          ),
        );
      }

      // Refresh user data to update bank account balances
      await _loadUserData();
    } catch (e) {
      // Close loading dialog if still open
      if (mounted) Navigator.of(context).pop();
      _showSnackBar(
        'profile_error_deleting_transactions'.tr(
          namedArgs: {'error': e.toString()},
        ),
      );
    }
  }

  Future<void> _exportTransactions() async {
    setState(() {
      _isExporting = true;
    });

    try {
      // Get all data for export
      final exportData = await LocalStorageService.exportAllData();

      // Format the data nicely
      final formattedJson = JsonEncoder.withIndent('  ').convert(exportData);

      // Create comprehensive export summary
      final bankAccounts = exportData['bank_accounts'] as List? ?? [];
      final transactions = exportData['transactions'] as List? ?? [];
      final creditCards = exportData['credit_cards'] as List? ?? [];
      final creditCardTransactions =
          exportData['credit_card_transactions'] as List? ?? [];
      final budgets = exportData['budgets'] as List? ?? [];
      final recurringTransactions =
          exportData['recurring_transactions'] as List? ?? [];
      final upcomingExpenses = exportData['upcoming_expenses'] as List? ?? [];
      final categories = exportData['income_expense_categories'] as List? ?? [];

      final summary = '''
Personal Finance Data Export
Generated on: ${DateFormat('MMM dd, yyyy • hh:mm a').format(DateTime.now())}

📊 Complete Export Summary:
• Bank Accounts: ${bankAccounts.length}
• Transactions: ${transactions.length}
• Credit Cards: ${creditCards.length}
• Credit Card Transactions: ${creditCardTransactions.length}
• Budgets: ${budgets.length}
• Recurring Transactions: ${recurringTransactions.length}
• Upcoming Expenses: ${upcomingExpenses.length}
• Income/Expense Categories: ${categories.length}
• App Version: ${exportData['app_version']}

🔐 This complete backup contains ALL your financial data. 
Keep this file secure as it contains your complete financial information.

📱 To restore this data, use the Import feature in your app.

---

Data (JSON Format):
$formattedJson
''';

      // Show export options dialog
      _showExportOptionsDialog(summary, formattedJson);
    } catch (e) {
      _showSnackBar(
        'profile_failed_to_export_data'.tr(namedArgs: {'error': e.toString()}),
      );
    } finally {
      setState(() {
        _isExporting = false;
      });
    }
  }

  void _showExportOptionsDialog(String summary, String jsonData) {
    final theme = Theme.of(context);
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(
              'profile_export_data'.tr(),
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'profile_choose_export_method'.tr(),
                  style: theme.textTheme.bodyMedium,
                ),
                const SizedBox(height: 16),

                // Copy to Clipboard
                ListTile(
                  leading: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: theme.colorScheme.primary.withValues(alpha: 0.1),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      Icons.copy,
                      color: theme.colorScheme.primary,
                      size: 20,
                    ),
                  ),
                  title: Text(
                    'profile_copy_to_clipboard'.tr(),
                    style: theme.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  subtitle: Text(
                    'profile_copy_json_subtitle'.tr(),
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.hintColor,
                    ),
                  ),
                  onTap: () {
                    Navigator.of(context).pop();
                    _copyToClipboard(jsonData);
                  },
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text('profile_cancel'.tr()),
              ),
            ],
          ),
    );
  }

  Future<void> _copyToClipboard(String data) async {
    try {
      // For now, we'll show the data in a dialog since we can't import Clipboard
      _showDataDialog('profile_export_data'.tr(), data);
    } catch (e) {
      _showSnackBar(
        'profile_failed_to_copy_data'.tr(namedArgs: {'error': e.toString()}),
      );
    }
  }

  void _showDataDialog(String title, String data) {
    final theme = Theme.of(context);
    final TextEditingController textController = TextEditingController(
      text: data,
    );

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Row(
              children: [
                Icon(Icons.code, color: theme.colorScheme.primary, size: 24),
                const SizedBox(width: 8),
                Expanded(child: Text(title)),
              ],
            ),
            content: SizedBox(
              width: double.maxFinite,
              height: 450,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Instructions
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: theme.colorScheme.primary.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: theme.colorScheme.primary.withValues(alpha: 0.3),
                      ),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Icons.info_outline,
                          color: theme.colorScheme.primary,
                          size: 16,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            'profile_text_auto_selected'.tr(),
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: theme.colorScheme.primary,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 12),

                  // JSON Data Field (Auto-selected)
                  Expanded(
                    child: Container(
                      decoration: BoxDecoration(
                        border: Border.all(color: theme.dividerColor),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: TextField(
                        controller: textController,
                        maxLines: null,
                        expands: true,
                        readOnly: true,
                        style: theme.textTheme.bodySmall?.copyWith(
                          fontFamily: 'monospace',
                          fontSize: 11,
                        ),
                        decoration: const InputDecoration(
                          border: InputBorder.none,
                          contentPadding: EdgeInsets.all(12),
                        ),
                        onTap: () {
                          // Auto-select all text when tapped
                          textController.selection = TextSelection(
                            baseOffset: 0,
                            extentOffset: textController.text.length,
                          );
                        },
                      ),
                    ),
                  ),
                ],
              ),
            ),
            actions: [
              // Copy Button
              TextButton.icon(
                onPressed: () async {
                  // Copy to clipboard functionality
                  try {
                    // For now, show a helpful message since we can't import Clipboard directly
                    textController.selection = TextSelection(
                      baseOffset: 0,
                      extentOffset: textController.text.length,
                    );

                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('profile_all_text_selected'.tr()),
                        backgroundColor: theme.colorScheme.primary,
                        duration: const Duration(seconds: 3),
                      ),
                    );
                  } catch (e) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(
                          'profile_error_label'.tr(
                            namedArgs: {'error': e.toString()},
                          ),
                        ),
                        backgroundColor: theme.colorScheme.error,
                      ),
                    );
                  }
                },
                icon: const Icon(Icons.copy, size: 16),
                label: Text('profile_select_all'.tr()),
                style: TextButton.styleFrom(
                  foregroundColor: theme.colorScheme.primary,
                ),
              ),

              // Close Button
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text('profile_close'.tr()),
              ),
            ],
          ),
    ).then((_) {
      // Auto-select all text when dialog opens
      WidgetsBinding.instance.addPostFrameCallback((_) {
        textController.selection = TextSelection(
          baseOffset: 0,
          extentOffset: textController.text.length,
        );
      });
    });

    // Auto-select text immediately when dialog opens
    Future.delayed(const Duration(milliseconds: 100), () {
      textController.selection = TextSelection(
        baseOffset: 0,
        extentOffset: textController.text.length,
      );
    });
  }

  Future<void> _importTransactions() async {
    setState(() {
      _isImporting = true;
    });

    try {
      // Show import dialog
      _showImportDialog();
    } catch (e) {
      _showSnackBar(
        'profile_failed_to_import_data'.tr(namedArgs: {'error': e.toString()}),
      );
    } finally {
      setState(() {
        _isImporting = false;
      });
    }
  }

  void _showImportDialog() {
    final theme = Theme.of(context);
    final TextEditingController importController = TextEditingController();

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(
              'profile_import_data'.tr(),
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            content: SizedBox(
              width: double.maxFinite,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'profile_paste_json_data'.tr(),
                    style: theme.textTheme.bodyMedium,
                  ),
                  const SizedBox(height: 16),

                  Container(
                    height: 200,
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      border: Border.all(
                        color: theme.hintColor.withValues(alpha: 0.3),
                      ),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: TextField(
                      controller: importController,
                      maxLines: null,
                      expands: true,
                      style: theme.textTheme.bodySmall?.copyWith(
                        fontFamily: 'monospace',
                      ),
                      decoration: InputDecoration(
                        hintText: 'profile_paste_json_hint'.tr(),
                        border: InputBorder.none,
                      ),
                    ),
                  ),
                  const SizedBox(height: 12),

                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: AppColorPalette.warning.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: AppColorPalette.warning.withValues(alpha: 0.3),
                      ),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Icons.warning_amber,
                          color: AppColorPalette.warning,
                          size: 16,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            'profile_replace_all_data_warning'.tr(),
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: AppColorPalette.warning,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text('profile_cancel'.tr()),
              ),
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  _processImportData(importController.text);
                },
                child: Text(
                  'profile_import'.tr(),
                  style: TextStyle(
                    color: AppColorPalette.warning,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
    );
  }

  Future<void> _processImportData(String jsonData) async {
    if (jsonData.trim().isEmpty) {
      _showSnackBar('profile_please_paste_json'.tr());
      return;
    }

    try {
      // Show loading dialog
      showDialog(
        context: context,
        barrierDismissible: false,
        builder:
            (context) => AlertDialog(
              content: Row(
                children: [
                  const CircularProgressIndicator(),
                  const SizedBox(width: 16),
                  Text('profile_importing_all_data'.tr()),
                ],
              ),
            ),
      );

      // Parse JSON data
      final Map<String, dynamic> importData = json.decode(jsonData);

      // Enhanced validation - check for at least some core data
      if (!importData.containsKey('export_date') &&
          !importData.containsKey('bank_accounts') &&
          !importData.containsKey('transactions')) {
        throw Exception(
          'Invalid data format. This doesn\'t appear to be a valid Personal Finance export file.',
        );
      }

      // Count what will be imported
      final bankAccounts = importData['bank_accounts'] as List? ?? [];
      final transactions = importData['transactions'] as List? ?? [];
      final creditCards = importData['credit_cards'] as List? ?? [];
      final creditCardTransactions =
          importData['credit_card_transactions'] as List? ?? [];
      final budgets = importData['budgets'] as List? ?? [];
      final recurringTransactions =
          importData['recurring_transactions'] as List? ?? [];
      final upcomingExpenses = importData['upcoming_expenses'] as List? ?? [];
      final categories = importData['income_expense_categories'] as List? ?? [];

      // Import data using LocalStorageService
      await LocalStorageService.importAllData(importData);

      // Close loading dialog
      if (mounted) Navigator.of(context).pop();

      // Reload data
      await _loadUserData();

      // Show comprehensive success message
      final importSummary = '''
Successfully imported:
• ${bankAccounts.length} Bank Accounts
• ${transactions.length} Transactions
• ${creditCards.length} Credit Cards
• ${creditCardTransactions.length} Credit Card Transactions
• ${budgets.length} Budgets
• ${recurringTransactions.length} Recurring Transactions
• ${upcomingExpenses.length} Upcoming Expenses
• ${categories.length} Income/Expense Categories

Your complete financial data has been restored! 🎉''';

      _showImportSuccessDialog(importSummary);
    } catch (e) {
      // Close loading dialog if still open
      if (mounted) Navigator.of(context).pop();
      _showSnackBar(
        'profile_failed_to_import_data_process'.tr(
          namedArgs: {'error': e.toString()},
        ),
      );
    }
  }

  void _navigateToAppFeatures() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const AppFeaturesScreen()),
    );
  }

  /// Show language selection dialog
  void _showLanguageSelectionDialog() {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    // Get current locale
    final currentLocale = context.locale;

    // Define available languages - must match supportedLocales in main_dev.dart
    final languages = [
      {
        'code': 'en',
        'name': 'English',
        'nativeName': 'English',
        'flag': '🇺🇸',
        'locale': const Locale('en'),
      },
      {
        'code': 'hi',
        'name': 'Hindi',
        'nativeName': 'हिंदी',
        'flag': '🇮🇳',
        'locale': const Locale('hi'),
      },
    ];

    showDialog(
      context: context,
      builder:
          (BuildContext dialogContext) => AlertDialog(
            title: Row(
              children: [
                Icon(Icons.language, color: colorScheme.primary, size: 24),
                const SizedBox(width: 12),
                Text(
                  'language'.tr(), // This will be localized
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: colorScheme.primary,
                  ),
                ),
              ],
            ),
            content: SizedBox(
              width: double.maxFinite,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    'select_language'.tr(), // This will be localized
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.hintColor,
                    ),
                  ),
                  const SizedBox(height: 20),
                  ...languages.map((language) {
                    final isSelected =
                        currentLocale.languageCode == language['code'];
                    return Container(
                      margin: const EdgeInsets.only(bottom: 8),
                      child: ListTile(
                        leading: Text(
                          language['flag'] as String,
                          style: const TextStyle(fontSize: 24),
                        ),
                        title: Text(
                          language['name'] as String,
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight:
                                isSelected
                                    ? FontWeight.bold
                                    : FontWeight.normal,
                            color: isSelected ? colorScheme.primary : null,
                          ),
                        ),
                        subtitle: Text(
                          language['nativeName'] as String,
                          style: theme.textTheme.bodySmall?.copyWith(
                            color:
                                isSelected
                                    ? colorScheme.primary.withValues(alpha: 0.7)
                                    : theme.hintColor,
                          ),
                        ),
                        trailing:
                            isSelected
                                ? Icon(
                                  Icons.check_circle,
                                  color: colorScheme.primary,
                                  size: 24,
                                )
                                : Icon(
                                  Icons.circle_outlined,
                                  color: theme.hintColor.withValues(alpha: 0.5),
                                  size: 24,
                                ),
                        onTap: () {
                          Navigator.of(dialogContext).pop();
                          _changeLanguage(language['locale'] as Locale);
                        },
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                          side: BorderSide(
                            color:
                                isSelected
                                    ? colorScheme.primary.withValues(alpha: 0.3)
                                    : theme.dividerColor.withValues(alpha: 0.3),
                            width: isSelected ? 2 : 1,
                          ),
                        ),
                        selected: isSelected,
                        selectedTileColor: colorScheme.primary.withValues(
                          alpha: 0.1,
                        ),
                      ),
                    );
                  }).toList(),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(dialogContext).pop(),
                child: Text('cancel'.tr()), // This will be localized
              ),
            ],
          ),
    );
  }

  /// Change app language and save preference
  Future<void> _changeLanguage(Locale locale) async {
    try {
      // Ensure the locale matches the supported locales in main_dev.dart
      // Supported locales are: [Locale('en'), Locale('hi')]
      final supportedLanguages = ['en', 'hi'];
      final languageCode = locale.languageCode;

      // Validate that the language is supported
      if (!supportedLanguages.contains(languageCode)) {
        print('Unsupported language: $languageCode');
        _showSnackBar('Language not supported');
        return;
      }

      // Create locale without country code to match supportedLocales in main_dev.dart
      final supportedLocale = Locale(languageCode);

      // Change language immediately
      await context.setLocale(supportedLocale);

      // Save language preference to local storage
      await _saveLanguagePreference(languageCode);

      // Show success message
      _showSnackBar('language_changed_successfully'.tr());

      // Refresh the current screen to reflect language changes
      setState(() {});
    } catch (e) {
      print('Error changing language: $e');
      _showSnackBar('error_changing_language'.tr());
    }
  }

  /// Save language preference to local storage
  Future<void> _saveLanguagePreference(String languageCode) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('app_language', languageCode);
    } catch (e) {
      print('Error saving language preference: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        title: Text(
          'profile_settings'.tr(),
          style: theme.appBarTheme.titleTextStyle?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        automaticallyImplyLeading: false,
        actions: [
          // Language selection button
          IconButton(
            icon: const Icon(Icons.language),
            onPressed: () => _showLanguageSelectionDialog(),
            tooltip: 'profile_change_language'.tr(),
          ),
          IconButton(
            icon: const Icon(Icons.help_outline),
            onPressed: () => _navigateToAppFeatures(),
            tooltip: 'profile_help_features'.tr(),
          ),
        ],
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    // Quick Actions Section
                    _buildQuickActionsSection(theme, colorScheme),
                    const SizedBox(height: 20),

                    // Account Management Section
                    _buildAccountManagementSection(theme, colorScheme),
                    const SizedBox(height: 20),

                    // Advanced Tools Section
                    _buildAdvancedToolsSection(theme, colorScheme),
                    const SizedBox(height: 20),

                    // Data & Backup Section
                    _buildDataManagementSection(theme, colorScheme),
                    const SizedBox(height: 20),

                    // Profile Header Card (moved to bottom)
                    _buildProfileHeader(theme, colorScheme),
                    const SizedBox(height: 20),

                    // Developer Credit
                    Center(
                      child: RichText(
                        text: TextSpan(
                          children: [
                            TextSpan(
                              text: 'profile_developed_by'.tr(),
                              style: theme.textTheme.bodySmall?.copyWith(
                                color: theme.hintColor.withValues(alpha: 0.6),
                                fontStyle: FontStyle.italic,
                              ),
                            ),
                            WidgetSpan(
                              child: GestureDetector(
                                onTap: () async {
                                  try {
                                    final Uri url = Uri.parse(
                                      'https://www.indianic.com/services/mobile/hire-ios-application-developers/',
                                    );
                                    if (await canLaunchUrl(url)) {
                                      await launchUrl(
                                        url,
                                        mode: LaunchMode.externalApplication,
                                      );
                                    } else {
                                      // Fallback: show snackbar if URL can't be launched
                                      ScaffoldMessenger.of(
                                        context,
                                      ).showSnackBar(
                                        const SnackBar(
                                          content: Text(
                                            'Could not open https://www.indianic.com',
                                          ),
                                        ),
                                      );
                                    }
                                  } catch (e) {
                                    // Error handling: show snackbar
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      SnackBar(
                                        content: Text('Error opening link: $e'),
                                      ),
                                    );
                                  }
                                },
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Container(
                                      width: 16,
                                      height: 16,
                                      margin: const EdgeInsets.only(right: 4),
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(2),
                                        boxShadow: [
                                          BoxShadow(
                                            color: Colors.black.withValues(
                                              alpha: 0.1,
                                            ),
                                            blurRadius: 2,
                                            offset: const Offset(0, 1),
                                          ),
                                        ],
                                      ),
                                      child: ClipRRect(
                                        borderRadius: BorderRadius.circular(2),
                                        child: Image.network(
                                          'https://s3.us-east-1.amazonaws.com:443/typebot-s3/public/typebots/w5n62ms0tzc6hanclws3m24d/hostAvatar?v=1689227378136',
                                          width: 16,
                                          height: 16,
                                          fit: BoxFit.cover,
                                          errorBuilder: (
                                            context,
                                            error,
                                            stackTrace,
                                          ) {
                                            return Container(
                                              width: 16,
                                              height: 16,
                                              decoration: BoxDecoration(
                                                color: theme.colorScheme.primary
                                                    .withValues(alpha: 0.1),
                                                borderRadius:
                                                    BorderRadius.circular(2),
                                              ),
                                              child: Icon(
                                                Icons.business,
                                                size: 10,
                                                color:
                                                    theme.colorScheme.primary,
                                              ),
                                            );
                                          },
                                          loadingBuilder: (
                                            context,
                                            child,
                                            loadingProgress,
                                          ) {
                                            if (loadingProgress == null)
                                              return child;
                                            return Container(
                                              width: 16,
                                              height: 16,
                                              decoration: BoxDecoration(
                                                color: theme.colorScheme.primary
                                                    .withValues(alpha: 0.1),
                                                borderRadius:
                                                    BorderRadius.circular(2),
                                              ),
                                              child: Center(
                                                child: SizedBox(
                                                  width: 8,
                                                  height: 8,
                                                  child: CircularProgressIndicator(
                                                    strokeWidth: 1,
                                                    valueColor:
                                                        AlwaysStoppedAnimation<
                                                          Color
                                                        >(
                                                          theme
                                                              .colorScheme
                                                              .primary,
                                                        ),
                                                  ),
                                                ),
                                              ),
                                            );
                                          },
                                        ),
                                      ),
                                    ),
                                    Text(
                                      'IndiaNIC',
                                      style: theme.textTheme.bodySmall
                                          ?.copyWith(
                                            color: theme.colorScheme.primary
                                                .withValues(alpha: 0.7),
                                            fontStyle: FontStyle.italic,
                                            decoration:
                                                TextDecoration.underline,
                                            decorationColor: theme
                                                .colorScheme
                                                .primary
                                                .withValues(alpha: 0.7),
                                          ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 20),
                  ],
                ),
              ),
    );
  }

  Widget _buildProfileHeader(ThemeData theme, ColorScheme colorScheme) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            colorScheme.primary.withValues(alpha: 0.05),
            colorScheme.tertiary.withValues(alpha: 0.05),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: colorScheme.primary.withValues(alpha: 0.1)),
      ),
      child: Column(
        children: [
          // App Icon
          Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [colorScheme.primary, colorScheme.tertiary],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: colorScheme.primary.withValues(alpha: 0.3),
                  blurRadius: 15,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: Icon(
              Icons.account_balance_wallet,
              size: 26,
              color: colorScheme.onPrimary,
            ),
          ),
          const SizedBox(height: 12),

          Text(
            'profile_personal_finance_title'.tr(),
            style: theme.textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: colorScheme.primary,
            ),
          ),
          const SizedBox(height: 6),

          Text(
            'profile_track_budget_save_grow'.tr(),
            style: theme.textTheme.bodyLarge?.copyWith(
              color: colorScheme.onSurfaceVariant,
              letterSpacing: 1.1,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActionsSection(ThemeData theme, ColorScheme colorScheme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(left: 4, bottom: 12),
          child: Text(
            'quick_actions'.tr(),
            style: theme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: colorScheme.onSurface,
            ),
          ),
        ),
        Row(
          children: [
            Expanded(
              child: _buildQuickActionCard(
                theme: theme,
                colorScheme: colorScheme,
                icon: Icons.add_card,
                title: 'profile_add_bank'.tr(),
                subtitle: 'profile_new_account'.tr(),
                color: const Color(0xFF4CAF50),
                onTap: () async {
                  _navigateWithBottomNav(const AddBankScreen());
                  await _loadUserData();
                },
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildQuickActionCard(
                theme: theme,
                colorScheme: colorScheme,
                icon: Icons.category,
                title: 'profile_categories'.tr(),
                subtitle: 'profile_manage'.tr(),
                color: const Color(0xFF2196F3),
                onTap: () {
                  _navigateWithBottomNav(const IncomeExpenseCategoriesScreen());
                },
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildQuickActionCard(
                theme: theme,
                colorScheme: colorScheme,
                icon: Icons.pie_chart,
                title: 'profile_budget'.tr(),
                subtitle: 'profile_planner'.tr(),
                color: const Color(0xFF9C27B0),
                onTap: () {
                  _navigateWithBottomNav(const BudgetPlannerScreen());
                },
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildQuickActionCard(
                theme: theme,
                colorScheme: colorScheme,
                icon: Icons.analytics,
                title: 'profile_analytics'.tr(),
                subtitle: 'profile_insights'.tr(),
                color: const Color(0xFFFF9800),
                onTap: () {
                  _navigateWithBottomNav(const AdvancedAnalyticsScreen());
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildQuickActionCard({
    required ThemeData theme,
    required ColorScheme colorScheme,
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.08),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: color.withValues(alpha: 0.2)),
        ),
        child: Column(
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.15),
                shape: BoxShape.circle,
              ),
              child: Icon(icon, color: color, size: 20),
            ),
            const SizedBox(height: 10),
            Text(
              title,
              style: theme.textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
              textAlign: TextAlign.center,
            ),
            Text(
              subtitle,
              style: theme.textTheme.bodySmall?.copyWith(
                color: color.withValues(alpha: 0.8),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAccountManagementSection(
    ThemeData theme,
    ColorScheme colorScheme,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(left: 4, bottom: 12),
          child: Text(
            'profile_account_management'.tr(),
            style: theme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: colorScheme.onSurface,
            ),
          ),
        ),

        // Bank Accounts Card
        Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: theme.cardColor,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: theme.shadowColor.withValues(alpha: 0.08),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'profile_bank_cash_accounts'.tr(),
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  TextButton.icon(
                    onPressed: () async {
                      _navigateWithBottomNav(const AddBankScreen());
                      await _loadUserData();
                    },
                    icon: const Icon(Icons.add, size: 16),
                    label: Text('profile_add'.tr()),
                    style: TextButton.styleFrom(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 8,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),

              if (_bankAccounts.isEmpty)
                Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: theme.hintColor.withValues(alpha: 0.05),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: theme.hintColor.withValues(alpha: 0.1),
                    ),
                  ),
                  child: Column(
                    children: [
                      Icon(
                        Icons.account_balance_outlined,
                        color: theme.hintColor,
                        size: 32,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'profile_no_bank_accounts_yet'.tr(),
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: theme.hintColor,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'profile_add_first_bank_account'.tr(),
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.hintColor.withValues(alpha: 0.8),
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                )
              else
                ListView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: _bankAccounts.length,
                  itemBuilder: (context, index) {
                    final account = _bankAccounts[index];
                    return Container(
                      margin: const EdgeInsets.only(bottom: 12),
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: colorScheme.surfaceContainerHighest.withValues(
                          alpha: 0.3,
                        ),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: theme.dividerColor.withValues(alpha: 0.5),
                        ),
                      ),
                      child: Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.all(10),
                            decoration: BoxDecoration(
                              color: colorScheme.primary.withValues(alpha: 0.1),
                              shape: BoxShape.circle,
                            ),
                            child: Icon(
                              Icons.account_balance,
                              color: colorScheme.primary,
                              size: 22,
                            ),
                          ),
                          const SizedBox(width: 14),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  account.bankName,
                                  style: theme.textTheme.titleSmall?.copyWith(
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                const SizedBox(height: 2),
                                Text(
                                  'A/C: ${account.accountNumber}',
                                  style: theme.textTheme.bodySmall?.copyWith(
                                    color: theme.hintColor,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  '₹${NumberFormat('#,##,###').format(account.currentAmount)}',
                                  style: theme.textTheme.bodyLarge?.copyWith(
                                    color: colorScheme.primary,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          PopupMenuButton<String>(
                            onSelected: (value) {
                              if (value == 'edit') {
                                _editBankAccount(account);
                              } else if (value == 'delete') {
                                _deleteBankAccount(account);
                              }
                            },
                            itemBuilder:
                                (context) => [
                                  PopupMenuItem(
                                    value: 'edit',
                                    child: Row(
                                      children: [
                                        Icon(
                                          Icons.edit,
                                          size: 18,
                                          color: colorScheme.primary,
                                        ),
                                        const SizedBox(width: 12),
                                        Text('profile_edit'.tr()),
                                      ],
                                    ),
                                  ),
                                  PopupMenuItem(
                                    value: 'delete',
                                    child: Row(
                                      children: [
                                        Icon(
                                          Icons.delete,
                                          size: 18,
                                          color: colorScheme.error,
                                        ),
                                        const SizedBox(width: 12),
                                        Text('profile_delete_menu'.tr()),
                                      ],
                                    ),
                                  ),
                                ],
                            child: Container(
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                color: theme.hintColor.withValues(alpha: 0.1),
                                shape: BoxShape.circle,
                              ),
                              child: Icon(
                                Icons.more_vert,
                                size: 18,
                                color: theme.hintColor,
                              ),
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ),
            ],
          ),
        ),

        const SizedBox(height: 16),

        // Category Management
        _buildFeatureCard(
          theme: theme,
          colorScheme: colorScheme,
          icon: Icons.category_outlined,
          title: 'profile_upcoming_expense_categories'.tr(),
          subtitle: 'profile_manage_categories_future_expenses'.tr(),
          color: AppColorPalette.warning,
          onTap: () {
            AppRouter.pushNamed(
              context,
              GoRouterConstants.upcomingExpenseCategories,
            );
          },
        ),
      ],
    );
  }

  Widget _buildAdvancedToolsSection(ThemeData theme, ColorScheme colorScheme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(left: 4, bottom: 12),
          child: Text(
            'profile_advanced_tools'.tr(),
            style: theme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: colorScheme.onSurface,
            ),
          ),
        ),

        const SizedBox(height: 12),

        _buildFeatureCard(
          theme: theme,
          colorScheme: colorScheme,
          icon: Icons.schedule,
          title: 'profile_upcoming_expenses'.tr(),
          subtitle: 'profile_view_manage_future_expenses'.tr(),
          color: const Color(0xFFFF9800),
          onTap: () {
            _navigateWithBottomNav(const UpcomingExpensesListScreen());
          },
        ),

        const SizedBox(height: 12),

        _buildFeatureCard(
          theme: theme,
          colorScheme: colorScheme,
          icon: Icons.repeat,
          title: 'profile_recurring_transactions'.tr(),
          subtitle: 'profile_automatic_transaction_management'.tr(),
          color: const Color(0xFF9C27B0),
          onTap: () {
            AppRouter.pushNamed(
              context,
              GoRouterConstants.recurringTransactions,
            );
          },
        ),
      ],
    );
  }

  Widget _buildDataManagementSection(ThemeData theme, ColorScheme colorScheme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(left: 4, bottom: 12),
          child: Text(
            'profile_data_settings'.tr(),
            style: theme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: colorScheme.onSurface,
            ),
          ),
        ),

        Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: theme.cardColor,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: theme.shadowColor.withValues(alpha: 0.08),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            children: [
              // Export & Import Row
              Row(
                children: [
                  Expanded(
                    child: _buildDataActionCard(
                      theme: theme,
                      icon: Icons.file_upload,
                      title: 'profile_export_data_action'.tr(),
                      subtitle: 'profile_backup'.tr(),
                      color: const Color(0xFF4CAF50),
                      isLoading: _isExporting,
                      onTap: _isExporting ? null : _exportTransactions,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildDataActionCard(
                      theme: theme,
                      icon: Icons.file_download,
                      title: 'profile_import_data_action'.tr(),
                      subtitle: 'profile_restore'.tr(),
                      color: const Color(0xFF2196F3),
                      isLoading: _isImporting,
                      onTap: _isImporting ? null : _importTransactions,
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // Other features
              _buildSimpleFeatureRow(
                theme: theme,
                icon: Icons.picture_as_pdf,
                title: 'profile_export_to_pdf'.tr(),
                subtitle: 'profile_generate_reports'.tr(),
                color: const Color(0xFFE91E63),
                onTap: () {
                  _navigateWithBottomNav(const TransactionExportScreen());
                },
              ),

              // const SizedBox(height: 12),

              // _buildSimpleFeatureRow(
              //   theme: theme,
              //   icon: Icons.bar_chart,
              //   title: 'Data Visualization',
              //   subtitle: 'Charts and graphs',
              //   color: const Color(0xFF00BCD4),
              //   onTap: () {
              //     AppRouter.pushNamed(
              //       context,
              //       GoRouterConstants.dataVisualization,
              //     );
              //   },
              // ),
              const SizedBox(height: 12),

              _buildSimpleFeatureRow(
                theme: theme,
                icon: Icons.notifications,
                title: 'profile_notifications'.tr(),
                subtitle: 'profile_alerts_reminders'.tr(),
                color: const Color(0xFF9C27B0),
                onTap: () {
                  AppRouter.pushNamed(
                    context,
                    GoRouterConstants.notificationSettings,
                  );
                },
              ),

              const SizedBox(height: 12),

              _buildSimpleFeatureRow(
                theme: theme,
                icon: Icons.security,
                title: 'profile_security_settings'.tr(),
                subtitle: 'profile_app_protection'.tr(),
                color: const Color(0xFF607D8B),
                onTap: () {
                  AppRouter.pushNamed(
                    context,
                    GoRouterConstants.securitySettings,
                  );
                },
              ),

              const SizedBox(height: 16),

              // Delete Transactions - Warning section
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: const Color(0xFFE74C3C).withValues(alpha: 0.05),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: const Color(0xFFE74C3C).withValues(alpha: 0.2),
                  ),
                ),
                child: Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: const Color(0xFFE74C3C).withValues(alpha: 0.1),
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        Icons.warning,
                        color: const Color(0xFFE74C3C),
                        size: 20,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'profile_delete_transactions_title'.tr(),
                            style: theme.textTheme.titleSmall?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: const Color(0xFFE74C3C),
                            ),
                          ),
                          Text(
                            'profile_remove_transactions_by_date'.tr(),
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: const Color(
                                0xFFE74C3C,
                              ).withValues(alpha: 0.8),
                            ),
                          ),
                        ],
                      ),
                    ),
                    TextButton(
                      onPressed: _showDeleteTransactionsDialog,
                      style: TextButton.styleFrom(
                        foregroundColor: const Color(0xFFE74C3C),
                        padding: const EdgeInsets.symmetric(horizontal: 12),
                      ),
                      child: Text('profile_delete_action'.tr()),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildFeatureCard({
    required ThemeData theme,
    required ColorScheme colorScheme,
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: theme.cardColor,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: color.withValues(alpha: 0.2)),
          boxShadow: [
            BoxShadow(
              color: theme.shadowColor.withValues(alpha: 0.05),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(icon, color: color, size: 24),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: theme.textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: colorScheme.onSurface,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    subtitle,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.hintColor,
                    ),
                  ),
                ],
              ),
            ),
            Icon(Icons.arrow_forward_ios, color: color, size: 16),
          ],
        ),
      ),
    );
  }

  Widget _buildDataActionCard({
    required ThemeData theme,
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required bool isLoading,
    required VoidCallback? onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.08),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: color.withValues(alpha: 0.2)),
        ),
        child: Column(
          children: [
            if (isLoading)
              SizedBox(
                width: 32,
                height: 32,
                child: CircularProgressIndicator(
                  strokeWidth: 3,
                  valueColor: AlwaysStoppedAnimation<Color>(color),
                ),
              )
            else
              Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.15),
                  shape: BoxShape.circle,
                ),
                child: Icon(icon, color: color, size: 18),
              ),
            const SizedBox(height: 12),
            Text(
              title,
              style: theme.textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
              textAlign: TextAlign.center,
            ),
            Text(
              subtitle,
              style: theme.textTheme.bodySmall?.copyWith(
                color: color.withValues(alpha: 0.8),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSimpleFeatureRow({
    required ThemeData theme,
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 4),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(icon, color: color, size: 20),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Text(
                    subtitle,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.hintColor,
                    ),
                  ),
                ],
              ),
            ),
            Icon(Icons.arrow_forward_ios, color: color, size: 14),
          ],
        ),
      ),
    );
  }

  // Add this new method to show detailed import success
  void _showImportSuccessDialog(String summary) {
    final theme = Theme.of(context);
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Row(
              children: [
                Icon(
                  Icons.check_circle,
                  color: AppColorPalette.success,
                  size: 28,
                ),
                const SizedBox(width: 12),
                Text(
                  'Import Complete',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColorPalette.success,
                  ),
                ),
              ],
            ),
            content: Container(
              constraints: const BoxConstraints(maxWidth: 300),
              child: Text(summary, style: theme.textTheme.bodyMedium),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text(
                  'Great!',
                  style: TextStyle(
                    color: AppColorPalette.success,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
    );
  }
}
