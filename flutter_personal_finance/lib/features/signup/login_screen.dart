import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';
import '../../shared/widgtes/common/custom_button.dart';
import '../../services/auth_service.dart';
import '../../utils/helper/route.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  bool _isAuthenticating = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    // Automatically trigger face authentication when screen loads
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _authenticateWithFace();
    });
  }

  Future<void> _authenticateWithFace() async {
    setState(() {
      _isAuthenticating = true;
      _errorMessage = null;
    });

    try {
      // Check if face auth is enabled first
      final isFaceEnabled = await AuthService.isFaceAuthEnabled();

      if (isFaceEnabled) {
        // Use simplified face authentication method
        final isAuthenticated = await AuthService.authenticateForAppAccess();

        if (isAuthenticated) {
          if (!mounted) return;
          // Navigate to dashboard on successful authentication
          AppRouter.goNamed(context, GoRouterConstants.dashboard);
        } else {
          setState(() {
            _errorMessage = 'login_auth_failed'.tr();
            _isAuthenticating = false;
          });
        }
      } else {
        // Face auth not enabled, ask user to set it up
        setState(() {
          _errorMessage = 'login_not_setup'.tr();
          _isAuthenticating = false;
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'login_not_available'.tr();
        _isAuthenticating = false;
      });
    }
  }

  Future<void> _setupFaceAuth() async {
    setState(() {
      _isAuthenticating = true;
      _errorMessage = null;
    });

    try {
      final isEnabled = await AuthService.enableFaceAuth();

      if (isEnabled) {
        if (!mounted) return;
        // After setup, trigger authentication
        await _authenticateWithFace();
      } else {
        setState(() {
          _errorMessage = 'login_setup_failed'.tr();
          _isAuthenticating = false;
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'login_not_supported'.tr();
        _isAuthenticating = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // App Logo/Icon
              Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  color: colorScheme.primary.withValues(alpha: 0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.account_balance_wallet,
                  size: 60,
                  color: colorScheme.primary,
                ),
              ),
              const SizedBox(height: 40),

              // Welcome text
              Text(
                'login_app_title'.tr(),
                style: theme.textTheme.headlineLarge?.copyWith(
                  color: colorScheme.primary,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),

              Text(
                'login_secure_access'.tr(),
                style: theme.textTheme.bodyLarge?.copyWith(
                  color: theme.hintColor,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 60),

              // Face Authentication Icon
              if (_isAuthenticating)
                Column(
                  children: [
                    Container(
                      width: 80,
                      height: 80,
                      decoration: BoxDecoration(
                        color: colorScheme.primary.withValues(alpha: 0.1),
                        shape: BoxShape.circle,
                      ),
                      child: CircularProgressIndicator(
                        strokeWidth: 3,
                        color: colorScheme.primary,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'login_authenticating'.tr(),
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: colorScheme.primary,
                        fontWeight: FontWeight.w600,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                )
              else
                Column(
                  children: [
                    Container(
                      width: 80,
                      height: 80,
                      decoration: BoxDecoration(
                        color: colorScheme.primary.withValues(alpha: 0.1),
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        Icons.face,
                        size: 40,
                        color: colorScheme.primary,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'login_face_instruction'.tr(),
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: theme.hintColor,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),

              const SizedBox(height: 40),

              // Error message
              if (_errorMessage != null)
                Container(
                  padding: const EdgeInsets.all(16),
                  margin: const EdgeInsets.only(bottom: 20),
                  decoration: BoxDecoration(
                    color: colorScheme.error.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: colorScheme.error.withValues(alpha: 0.3),
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.error_outline,
                        color: colorScheme.error,
                        size: 20,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          _errorMessage!,
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: colorScheme.error,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

              // Action buttons
              if (!_isAuthenticating) ...[
                CustomButton.elevated(
                  onPressed: _authenticateWithFace,
                  backgroundColor: colorScheme.primary,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.face, color: colorScheme.onPrimary, size: 20),
                      const SizedBox(width: 8),
                      Text(
                        'login_authenticate_face'.tr(),
                        style: theme.textTheme.titleMedium?.copyWith(
                          color: colorScheme.onPrimary,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),

                // Setup Face Auth button (if authentication failed)
                if (_errorMessage != null)
                  CustomButton.outlined(
                    onPressed: _setupFaceAuth,
                    borderColor: colorScheme.primary,
                    child: Text(
                      'login_setup_face'.tr(),
                      style: theme.textTheme.titleMedium?.copyWith(
                        color: colorScheme.primary,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}
