import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';
import '../../resources/app_text_styles.dart';
import '../../shared/widgtes/common/custom_button.dart';
import '../../services/auth_service.dart';
import '../../services/biometric_auth_service.dart';
import '../../utils/helper/route.dart';
import '../../resources/app_theme.dart';

class SignupScreen extends StatefulWidget {
  const SignupScreen({super.key});

  @override
  State<SignupScreen> createState() => _SignupScreenState();
}

class _SignupScreenState extends State<SignupScreen> {
  bool _isSettingUp = false;
  String? _errorMessage;
  String? _successMessage;
  bool _biometricAvailable = false;
  bool _showPinOption = false;

  Future<void> _setupBiometricAuth() async {
    setState(() {
      _isSettingUp = true;
      _errorMessage = null;
      _successMessage = null;
    });

    try {
      // Check if biometric authentication is available
      final isAvailable = await AuthService.isBiometricAvailable();

      if (!isAvailable) {
        setState(() {
          _errorMessage = 'signup_biometric_unavailable'.tr();
          _showPinOption = true;
          _isSettingUp = false;
        });
        return;
      }

      // Enable face authentication
      final isEnabled = await AuthService.enableFaceAuth();

      if (isEnabled) {
        // Also enable the system authentication
        await BiometricAuthService.setBiometricEnabled(true);
        await BiometricAuthService.setAuthRequired(true);

        setState(() {
          _successMessage = 'signup_biometric_success'.tr();
          _isSettingUp = false;
        });

        // Auto-navigate to dashboard after 2 seconds
        Future.delayed(const Duration(seconds: 2), () {
          if (mounted) {
            AppRouter.goNamed(context, GoRouterConstants.dashboard);
          }
        });
      } else {
        setState(() {
          _errorMessage = 'signup_biometric_failed'.tr();
          _showPinOption = true;
          _isSettingUp = false;
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'signup_setup_error'.tr();
        _showPinOption = true;
        _isSettingUp = false;
      });
    }
  }

  Future<void> _setupPinAuth() async {
    setState(() {
      _isSettingUp = true;
      _errorMessage = null;
      _successMessage = null;
    });

    try {
      // Setup system authentication (PIN/password)
      await BiometricAuthService.setBiometricEnabled(false);
      await BiometricAuthService.setAuthRequired(true);

      // Test the system authentication
      final authResult = await BiometricAuthService.authenticateWithSystem();

      if (authResult) {
        setState(() {
          _successMessage = 'signup_pin_success'.tr();
          _isSettingUp = false;
        });

        // Auto-navigate to dashboard after 2 seconds
        Future.delayed(const Duration(seconds: 2), () {
          if (mounted) {
            AppRouter.goNamed(context, GoRouterConstants.dashboard);
          }
        });
      } else {
        setState(() {
          _errorMessage = 'signup_pin_failed'.tr();
          _isSettingUp = false;
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'signup_pin_error'.tr();
        _isSettingUp = false;
      });
    }
  }

  Future<void> _checkExistingSetup() async {
    final isFaceEnabled = await AuthService.isFaceAuthEnabled();
    final isSystemAuthRequired = await BiometricAuthService.isAuthRequired();

    if (isFaceEnabled && isSystemAuthRequired) {
      // Authentication already enabled, navigate to dashboard
      if (mounted) {
        AppRouter.goNamed(context, GoRouterConstants.dashboard);
      }
    }
  }

  Future<void> _checkAuthAvailability() async {
    final biometricAvailable =
        await BiometricAuthService.isBiometricSupported();
    setState(() {
      _biometricAvailable = biometricAvailable;
    });
  }

  @override
  void initState() {
    super.initState();
    // Check if auth is already set up and check availability
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkExistingSetup();
      _checkAuthAvailability();
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24.0),
          child: ConstrainedBox(
            constraints: BoxConstraints(
              minHeight:
                  MediaQuery.of(context).size.height -
                  MediaQuery.of(context).padding.top -
                  MediaQuery.of(context).padding.bottom -
                  48,
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // App Logo/Icon
                Container(
                  width: 120,
                  height: 120,
                  decoration: BoxDecoration(
                    color: AppColorPalette.primary.withValues(alpha: 0.1),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.account_balance_wallet,
                    size: 60,
                    color: AppColorPalette.primary,
                  ),
                ),
                const SizedBox(height: 40),

                // Welcome text
                Text(
                  'signup_welcome_title'.tr(),
                  style:
                      theme.textTheme.headlineLarge?.copyWith(
                        color: AppColorPalette.primary,
                        fontWeight: FontWeight.bold,
                      ) ??
                      AppTextStyles.headlineLarge.copyWith(
                        color: AppColorPalette.primary,
                        fontWeight: FontWeight.bold,
                      ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),

                Text(
                  _biometricAvailable
                      ? 'signup_secure_biometric'.tr()
                      : 'signup_secure_pin'.tr(),
                  style:
                      theme.textTheme.bodyLarge?.copyWith(
                        color: theme.hintColor,
                      ) ??
                      AppTextStyles.bodyLarge.copyWith(color: theme.hintColor),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 60),

                // Face Authentication Setup Icon
                if (_isSettingUp)
                  Column(
                    children: [
                      Container(
                        width: 80,
                        height: 80,
                        decoration: BoxDecoration(
                          color: AppColorPalette.primary.withValues(alpha: 0.1),
                          shape: BoxShape.circle,
                        ),
                        child: const CircularProgressIndicator(strokeWidth: 3),
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'signup_setting_up'.tr(),
                        style:
                            theme.textTheme.bodyMedium?.copyWith(
                              color: AppColorPalette.primary,
                              fontWeight: FontWeight.w600,
                            ) ??
                            AppTextStyles.bodyMedium.copyWith(
                              color: AppColorPalette.primary,
                              fontWeight: FontWeight.w600,
                            ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  )
                else if (_successMessage != null)
                  Column(
                    children: [
                      Container(
                        width: 80,
                        height: 80,
                        decoration: BoxDecoration(
                          color: AppColorPalette.success.withValues(alpha: 0.1),
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          Icons.check_circle,
                          size: 40,
                          color: AppColorPalette.success,
                        ),
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'signup_setup_complete'.tr(),
                        style:
                            theme.textTheme.bodyMedium?.copyWith(
                              color: AppColorPalette.success,
                              fontWeight: FontWeight.w600,
                            ) ??
                            AppTextStyles.bodyMedium.copyWith(
                              color: AppColorPalette.success,
                              fontWeight: FontWeight.w600,
                            ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  )
                else
                  Column(
                    children: [
                      Container(
                        width: 80,
                        height: 80,
                        decoration: BoxDecoration(
                          color: AppColorPalette.primary.withValues(alpha: 0.1),
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          _biometricAvailable ? Icons.fingerprint : Icons.pin,
                          size: 40,
                          color: AppColorPalette.primary,
                        ),
                      ),
                      const SizedBox(height: 16),
                      Text(
                        _biometricAvailable
                            ? 'signup_protect_biometric'.tr()
                            : 'signup_protect_pin'.tr(),
                        style:
                            theme.textTheme.bodyMedium?.copyWith(
                              color: theme.hintColor,
                            ) ??
                            AppTextStyles.bodyMedium.copyWith(
                              color: theme.hintColor,
                            ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),

                const SizedBox(height: 40),

                // Success message
                if (_successMessage != null)
                  Container(
                    padding: const EdgeInsets.all(16),
                    margin: const EdgeInsets.only(bottom: 20),
                    decoration: BoxDecoration(
                      color: AppColorPalette.success.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: AppColorPalette.success.withValues(alpha: 0.3),
                      ),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Icons.check_circle_outline,
                          color: AppColorPalette.success,
                          size: 20,
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Text(
                            _successMessage!,
                            style:
                                theme.textTheme.bodyMedium?.copyWith(
                                  color: AppColorPalette.success,
                                ) ??
                                AppTextStyles.bodyMedium.copyWith(
                                  color: AppColorPalette.success,
                                ),
                          ),
                        ),
                      ],
                    ),
                  ),

                // Error message
                if (_errorMessage != null)
                  Container(
                    padding: const EdgeInsets.all(16),
                    margin: const EdgeInsets.only(bottom: 20),
                    decoration: BoxDecoration(
                      color: AppColorPalette.error.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: AppColorPalette.error.withValues(alpha: 0.3),
                      ),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Icons.error_outline,
                          color: AppColorPalette.error,
                          size: 20,
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Text(
                            _errorMessage!,
                            style:
                                theme.textTheme.bodyMedium?.copyWith(
                                  color: AppColorPalette.error,
                                ) ??
                                AppTextStyles.bodyMedium.copyWith(
                                  color: AppColorPalette.error,
                                ),
                          ),
                        ),
                      ],
                    ),
                  ),

                // Action buttons
                if (!_isSettingUp && _successMessage == null) ...[
                  // Show biometric button if available
                  if (_biometricAvailable && !_showPinOption) ...[
                    CustomButton.elevated(
                      onPressed: _setupBiometricAuth,
                      backgroundColor: AppColorPalette.primary,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.fingerprint,
                            color: colorScheme.onPrimary,
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'signup_enable_biometric'.tr(),
                            style:
                                theme.textTheme.titleMedium?.copyWith(
                                  color: colorScheme.onPrimary,
                                  fontWeight: FontWeight.bold,
                                ) ??
                                AppTextStyles.titleMedium.copyWith(
                                  color: colorScheme.onPrimary,
                                  fontWeight: FontWeight.bold,
                                ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 16),
                  ],

                  // Show PIN button if biometric not available or after biometric fails
                  if (!_biometricAvailable || _showPinOption) ...[
                    CustomButton.elevated(
                      onPressed: _setupPinAuth,
                      backgroundColor: AppColorPalette.primary,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.pin,
                            color: colorScheme.onPrimary,
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'signup_set_pin'.tr(),
                            style:
                                theme.textTheme.titleMedium?.copyWith(
                                  color: colorScheme.onPrimary,
                                  fontWeight: FontWeight.bold,
                                ) ??
                                AppTextStyles.titleMedium.copyWith(
                                  color: colorScheme.onPrimary,
                                  fontWeight: FontWeight.bold,
                                ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 16),
                  ],

                  // Skip button (navigate to dashboard)
                  CustomButton.outlined(
                    onPressed: () {
                      AppRouter.goNamed(context, GoRouterConstants.dashboard);
                    },
                    borderColor: theme.hintColor,
                    child: Text(
                      'signup_skip'.tr(),
                      style:
                          theme.textTheme.titleMedium?.copyWith(
                            color: theme.hintColor,
                            fontWeight: FontWeight.w600,
                          ) ??
                          AppTextStyles.titleMedium.copyWith(
                            color: theme.hintColor,
                            fontWeight: FontWeight.w600,
                          ),
                    ),
                  ),
                ],

                // Manual navigation to login (if setup failed)
                // if (_errorMessage != null && !_isSettingUp)
                // Padding(
                //   padding: const EdgeInsets.only(top: 20),
                //   child: TextButton(
                //     onPressed: () {
                //       AppRouter.goNamed(context, GoRouterConstants.login);
                //     },
                //     child: Text(
                //       'Already Have Security Setup?',
                //       style:
                //           theme.textTheme.bodyMedium?.copyWith(
                //             color: AppColorPalette.primary,
                //             fontWeight: FontWeight.w600,
                //           ) ??
                //           AppTextStyles.bodyMedium.copyWith(
                //             color: AppColorPalette.primary,
                //             fontWeight: FontWeight.w600,
                //           ),
                //     ),
                //   ),
                // ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
