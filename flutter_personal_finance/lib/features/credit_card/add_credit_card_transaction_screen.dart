import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';

import 'package:easy_localization/easy_localization.dart';

import '../../shared/widgtes/common/custom_button.dart';
import '../../shared/widgtes/common/custom_text_field.dart';
import '../../services/credit_card_service.dart';
import '../../services/local_storage_service.dart';
import '../../features/models/credit_card_model.dart';
import '../../features/models/credit_card_transaction_model.dart';
import '../../features/models/bank_account_model.dart';
import '../../utils/helper/keyboard_dismiss_wrapper.dart';

class AddCreditCardTransactionScreen extends StatefulWidget {
  final String? creditCardId;

  const AddCreditCardTransactionScreen({super.key, this.creditCardId});

  @override
  State<AddCreditCardTransactionScreen> createState() =>
      _AddCreditCardTransactionScreenState();
}

class _AddCreditCardTransactionScreenState
    extends State<AddCreditCardTransactionScreen> {
  // Controllers and state
  final _formKey = GlobalKey<FormState>();
  final _amountController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _categoryController = TextEditingController();
  final _merchantController = TextEditingController();

  bool _isLoading = false;
  bool _isInitialLoading = true;
  String? _errorMessage;
  CreditCardTransactionType _selectedType = CreditCardTransactionType.purchase;
  DateTime _selectedDate = DateTime.now();
  String? _selectedCategory;

  List<CreditCardModel> _creditCards = [];
  List<BankAccountModel> _bankAccounts = [];
  CreditCardModel? _selectedCreditCard;
  BankAccountModel? _selectedBankAccount;

  static const _expenseCategories = [
    "Food & Dining",
    "Shopping",
    "Entertainment",
    "Transportation",
    "Bills & Utilities",
    "Healthcare",
    "Travel",
    "Groceries",
    "Fuel",
    "Online Shopping",
    "Subscriptions",
    "Education",
    "Other",
  ];

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  @override
  void dispose() {
    _amountController.dispose();
    _descriptionController.dispose();
    _categoryController.dispose();
    _merchantController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() {
      _isInitialLoading = true;
      _errorMessage = null;
    });

    try {
      final creditCards = await CreditCardService.getCreditCards();
      final bankAccounts = await LocalStorageService.getBankAccounts();

      setState(() {
        _creditCards = creditCards;
        _bankAccounts = bankAccounts;

        if (widget.creditCardId != null && creditCards.isNotEmpty) {
          _selectedCreditCard = creditCards.firstWhere(
            (card) => card.id == widget.creditCardId,
            orElse: () => creditCards.first,
          );
        } else if (creditCards.isNotEmpty) {
          _selectedCreditCard = creditCards.first;
        }

        if (bankAccounts.isNotEmpty) {
          _selectedBankAccount = bankAccounts.first;
        }

        _isInitialLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'credit_card_transaction_load_error'.tr(
          namedArgs: {'error': e.toString()},
        );
        _isInitialLoading = false;
      });
    }
  }

  Future<void> _handleSubmit() async {
    if (!_formKey.currentState!.validate()) return;

    if (_selectedCreditCard == null) {
      _showError('credit_card_transaction_select_card_error'.tr());
      return;
    }

    if (_selectedType == CreditCardTransactionType.payment &&
        _selectedBankAccount == null) {
      _showError('credit_card_transaction_select_bank_error'.tr());
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final amount = double.parse(_amountController.text);

      if (_selectedType == CreditCardTransactionType.payment) {
        if (amount > _selectedBankAccount!.currentAmount) {
          _showError('credit_card_transaction_bank_balance_error'.tr());
          return;
        }

        if (amount > _selectedCreditCard!.outstandingBalance) {
          final confirmed = await _showOverpaymentDialog(amount);
          if (!confirmed) return;
        }
      }

      final transaction = CreditCardTransactionModel(
        creditCardId: _selectedCreditCard!.id,
        linkedBankAccountId:
            _selectedType == CreditCardTransactionType.payment
                ? _selectedBankAccount?.id
                : null,
        type: _selectedType,
        amount: amount,
        description: _descriptionController.text.trim(),
        category: _categoryController.text.trim(),
        merchantName:
            _merchantController.text.trim().isEmpty
                ? null
                : _merchantController.text.trim(),
        date: _selectedDate,
      );

      await CreditCardService.addCreditCardTransaction(transaction);

      if (mounted) {
        await _showSuccessDialog();
      }
    } catch (e) {
      _showError('credit_card_transaction_add_error'.tr());
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  void _showError(String message) {
    setState(() {
      _errorMessage = message;
      _isLoading = false;
    });
  }

  void _showCreditCardDialog() {
    final theme = Theme.of(context);

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(
              'credit_card_transaction_select_card'.tr(),
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            content: SizedBox(
              width: double.maxFinite,
              child: ListView.builder(
                shrinkWrap: true,
                itemCount: _creditCards.length,
                itemBuilder:
                    (context, index) => ListTile(
                      title: Text(
                        _creditCards[index].cardName,
                        style: theme.textTheme.bodyMedium,
                      ),
                      subtitle: Text(
                        'credit_card_transaction_outstanding'.tr(
                          namedArgs: {
                            'amount': NumberFormat(
                              '#,##,###',
                            ).format(_creditCards[index].outstandingBalance),
                          },
                        ),
                        style: theme.textTheme.bodySmall,
                      ),
                      onTap: () {
                        setState(() {
                          _selectedCreditCard = _creditCards[index];
                        });
                        Navigator.of(context).pop();
                      },
                    ),
              ),
            ),
          ),
    );
  }

  void _showBankAccountDialog() {
    final theme = Theme.of(context);

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(
              'credit_card_transaction_select_bank'.tr(),
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            content: SizedBox(
              width: double.maxFinite,
              child: ListView.builder(
                shrinkWrap: true,
                itemCount: _bankAccounts.length,
                itemBuilder:
                    (context, index) => ListTile(
                      title: Text(
                        _bankAccounts[index].bankName,
                        style: theme.textTheme.bodyMedium,
                      ),
                      subtitle: Text(
                        'credit_card_transaction_account_number'.tr(
                          namedArgs: {
                            'accountNumber': _bankAccounts[index].accountNumber,
                          },
                        ),
                        style: theme.textTheme.bodySmall,
                      ),
                      onTap: () {
                        setState(() {
                          _selectedBankAccount = _bankAccounts[index];
                        });
                        Navigator.of(context).pop();
                      },
                    ),
              ),
            ),
          ),
    );
  }

  void _showCategoryDialog() {
    final theme = Theme.of(context);

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(
              'credit_card_transaction_select_category'.tr(),
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            content: SizedBox(
              width: double.maxFinite,
              child: ListView.builder(
                shrinkWrap: true,
                itemCount: _expenseCategories.length,
                itemBuilder:
                    (context, index) => ListTile(
                      title: Text(
                        _expenseCategories[index],
                        style: theme.textTheme.bodyMedium,
                      ),
                      onTap: () {
                        setState(() {
                          _selectedCategory = _expenseCategories[index];
                          _categoryController.text = _expenseCategories[index];
                        });
                        Navigator.of(context).pop();
                      },
                    ),
              ),
            ),
          ),
    );
  }

  Future<bool> _showOverpaymentDialog(double amount) async {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final overpay = amount - _selectedCreditCard!.outstandingBalance;

    return await showDialog<bool>(
          context: context,
          builder:
              (context) => AlertDialog(
                backgroundColor: theme.dialogBackgroundColor,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
                title: Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.orange.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        Icons.warning_outlined,
                        color: Colors.orange,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        'credit_card_transaction_overpay_title'.tr(),
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: colorScheme.onSurface,
                        ),
                      ),
                    ),
                  ],
                ),
                content: Text(
                  'credit_card_transaction_overpay_desc'.tr(
                    namedArgs: {
                      'amount': NumberFormat('#,##,###').format(overpay),
                    },
                  ),
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: colorScheme.onSurface.withValues(alpha: 0.8),
                  ),
                ),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.pop(context, false),
                    child: Text(
                      'credit_card_cancel'.tr(),
                      style: theme.textTheme.labelLarge?.copyWith(
                        color: theme.hintColor,
                      ),
                    ),
                  ),
                  ElevatedButton(
                    onPressed: () => Navigator.pop(context, true),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: Text(
                      'credit_card_continue'.tr(),
                      style: theme.textTheme.labelLarge?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
        ) ??
        false;
  }

  Future<void> _showSuccessDialog() async {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    await showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (dialogContext) => AlertDialog(
            backgroundColor: theme.dialogBackgroundColor,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            title: Text(
              'credit_card_transaction_success_title'.tr(),
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            content: Text(
              'credit_card_transaction_success_desc'.tr(
                namedArgs: {
                  'transactionType':
                      _selectedType == CreditCardTransactionType.purchase
                          ? 'credit_card_transaction_purchase'.tr()
                          : 'credit_card_transaction_payment'.tr(),
                },
              ),
              style: theme.textTheme.bodyMedium,
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(dialogContext).pop();
                  if (mounted) context.pop();
                },
                child: Text(
                  'credit_card_ok'.tr(),
                  style: theme.textTheme.labelLarge?.copyWith(
                    color: colorScheme.primary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    // Updated to use Transaction terminology
    final typeText =
        _selectedType == CreditCardTransactionType.purchase
            ? 'credit_card_transaction_type_purchase'.tr()
            : 'credit_card_transaction_type_payment'.tr();

    if (_isInitialLoading) {
      return Scaffold(
        backgroundColor: theme.scaffoldBackgroundColor,
        appBar: AppBar(
          title: Text(
            'credit_card_transaction_add_title'.tr(),
            style: theme.appBarTheme.titleTextStyle?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          leading: IconButton(
            icon: const Icon(Icons.arrow_back_ios),
            onPressed: () => Navigator.of(context).pop(),
          ),
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(
                color: colorScheme.primary,
                strokeWidth: 3,
              ),
              const SizedBox(height: 16),
              Text(
                'credit_card_transaction_loading'.tr(),
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.hintColor,
                ),
              ),
            ],
          ),
        ),
      );
    }

    if (_creditCards.isEmpty) {
      return Scaffold(
        backgroundColor: theme.scaffoldBackgroundColor,
        appBar: AppBar(
          title: Text(
            'credit_card_transaction_add_title'.tr(),
            style: theme.appBarTheme.titleTextStyle?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          leading: IconButton(
            icon: const Icon(Icons.arrow_back_ios),
            onPressed: () => Navigator.of(context).pop(),
          ),
        ),
        body: Center(
          child: Container(
            margin: const EdgeInsets.all(32),
            padding: const EdgeInsets.all(32),
            decoration: BoxDecoration(
              color: theme.hintColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(color: theme.hintColor.withValues(alpha: 0.3)),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: Colors.orange.withValues(alpha: 0.1),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.credit_card_off,
                    size: 48,
                    color: Colors.orange,
                  ),
                ),
                const SizedBox(height: 16),
                Text(
                  'credit_card_transaction_no_cards_title'.tr(),
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.orange,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'credit_card_transaction_no_cards_desc'.tr(),
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.hintColor,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
      );
    }

    return KeyboardDismissWrapper(
      child: Scaffold(
        backgroundColor: theme.scaffoldBackgroundColor,
        appBar: AppBar(
          title: Text(
            'Add $typeText',
            style: theme.appBarTheme.titleTextStyle?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          leading: IconButton(
            icon: const Icon(Icons.arrow_back_ios),
            onPressed: () => Navigator.of(context).pop(),
          ),
        ),
        body: Form(
          key: _formKey,
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                _buildTypeSelector(),
                const SizedBox(height: 24),
                _buildCreditCardDropdown(),
                const SizedBox(height: 24),
                _buildBankAccountDropdown(),
                const SizedBox(height: 24),
                _buildAmountField(),
                const SizedBox(height: 24),
                _buildDescriptionField(),
                const SizedBox(height: 24),
                _buildCategoryField(),
                const SizedBox(height: 24),
                _buildMerchantField(),
                const SizedBox(height: 24),
                _buildDatePicker(),
                const SizedBox(height: 24),
                if (_errorMessage != null) _buildErrorMessage(),
                const SizedBox(height: 32),
                _buildSubmitButton(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTypeSelector() {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      padding: const EdgeInsets.all(4),
      decoration: BoxDecoration(
        color: theme.hintColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          Expanded(
            child: GestureDetector(
              onTap: () {
                setState(() {
                  _selectedType = CreditCardTransactionType.purchase;
                  _selectedCategory = null; // Reset category
                });
              },
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 12),
                decoration: BoxDecoration(
                  color:
                      _selectedType == CreditCardTransactionType.purchase
                          ? theme.colorScheme.error
                          : Colors.transparent,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.shopping_cart_outlined,
                      color:
                          _selectedType == CreditCardTransactionType.purchase
                              ? colorScheme.onPrimary
                              : theme.colorScheme.error,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'credit_card_transaction_type_purchase'.tr(),
                      style: theme.textTheme.titleMedium?.copyWith(
                        color:
                            _selectedType == CreditCardTransactionType.purchase
                                ? colorScheme.onPrimary
                                : theme.colorScheme.error,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          const SizedBox(width: 4),
          Expanded(
            child: GestureDetector(
              onTap: () {
                setState(() {
                  _selectedType = CreditCardTransactionType.payment;
                  _selectedCategory = null; // Reset category
                });
              },
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 12),
                decoration: BoxDecoration(
                  color:
                      _selectedType == CreditCardTransactionType.payment
                          ? theme.colorScheme.primary
                          : Colors.transparent,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.payment_outlined,
                      color:
                          _selectedType == CreditCardTransactionType.payment
                              ? colorScheme.onPrimary
                              : theme.colorScheme.primary,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'credit_card_transaction_type_payment'.tr(),
                      style: theme.textTheme.titleMedium?.copyWith(
                        color:
                            _selectedType == CreditCardTransactionType.payment
                                ? colorScheme.onPrimary
                                : theme.colorScheme.primary,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCreditCardDropdown() {
    final theme = Theme.of(context);

    return GestureDetector(
      onTap: _creditCards.isEmpty ? null : _showCreditCardDialog,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: theme.hintColor.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: theme.hintColor.withValues(alpha: 0.3)),
        ),
        child: Row(
          children: [
            Icon(Icons.credit_card, color: theme.hintColor, size: 20),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'credit_card_transaction_credit_card_label'.tr(),
                    style: theme.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: theme.hintColor,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    _selectedCreditCard != null
                        ? '${_selectedCreditCard!.cardName} (₹${NumberFormat('#,##,###').format(_selectedCreditCard!.outstandingBalance)})'
                        : 'credit_card_transaction_select_credit_card'.tr(),
                    style: theme.textTheme.bodyMedium,
                  ),
                ],
              ),
            ),
            if (_creditCards.isNotEmpty)
              Icon(Icons.arrow_drop_down, color: theme.hintColor),
          ],
        ),
      ),
    );
  }

  Widget _buildBankAccountDropdown() {
    if (_selectedType != CreditCardTransactionType.payment) {
      return const SizedBox.shrink();
    }

    final theme = Theme.of(context);

    return GestureDetector(
      onTap: _bankAccounts.isEmpty ? null : _showBankAccountDialog,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: theme.hintColor.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: theme.hintColor.withValues(alpha: 0.3)),
        ),
        child: Row(
          children: [
            Icon(Icons.account_balance, color: theme.hintColor, size: 20),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'credit_card_transaction_bank_account_label'.tr(),
                    style: theme.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: theme.hintColor,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    _selectedBankAccount?.bankName ??
                        'credit_card_transaction_select_bank_account'.tr(),
                    style: theme.textTheme.bodyMedium,
                  ),
                ],
              ),
            ),
            if (_bankAccounts.isNotEmpty)
              Icon(Icons.arrow_drop_down, color: theme.hintColor),
          ],
        ),
      ),
    );
  }

  Widget _buildAmountField() {
    return CustomTextField(
      controller: _amountController,
      labelText: 'credit_card_transaction_amount_label'.tr(),
      hintText: 'credit_card_transaction_amount_hint'.tr(),
      prefixIcon: Icons.currency_rupee,
      keyboardType: TextInputType.number,
      inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'[0-9.]'))],
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'credit_card_transaction_amount_required'.tr();
        }
        final amount = double.tryParse(value);
        if (amount == null || amount <= 0) {
          return 'credit_card_transaction_amount_invalid'.tr();
        }
        return null;
      },
    );
  }

  Widget _buildDescriptionField() {
    return CustomTextField(
      controller: _descriptionController,
      labelText: 'credit_card_transaction_description_label'.tr(),
      hintText: 'credit_card_transaction_description_hint'.tr(),
      prefixIcon: Icons.description,
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'credit_card_transaction_description_required'.tr();
        }
        return null;
      },
    );
  }

  Widget _buildCategoryField() {
    if (_selectedType == CreditCardTransactionType.payment) {
      return CustomTextField(
        controller: _categoryController,
        labelText: 'credit_card_transaction_category_label'.tr(),
        hintText: 'credit_card_transaction_category_hint'.tr(),
        prefixIcon: Icons.category_outlined,
        enabled: false,
      );
    }

    final theme = Theme.of(context);

    return GestureDetector(
      onTap: _showCategoryDialog,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: theme.hintColor.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: theme.hintColor.withValues(alpha: 0.3)),
        ),
        child: Row(
          children: [
            Icon(Icons.category, color: theme.hintColor, size: 20),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'credit_card_transaction_category_label'.tr(),
                    style: theme.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: theme.hintColor,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    _selectedCategory ??
                        'credit_card_transaction_select_category_label'.tr(),
                    style: theme.textTheme.bodyMedium,
                  ),
                ],
              ),
            ),
            Icon(Icons.arrow_drop_down, color: theme.hintColor),
          ],
        ),
      ),
    );
  }

  Widget _buildMerchantField() {
    if (_selectedType != CreditCardTransactionType.purchase) {
      return const SizedBox.shrink();
    }

    return CustomTextField(
      controller: _merchantController,
      labelText: 'credit_card_transaction_merchant_label'.tr(),
      hintText: 'credit_card_transaction_merchant_hint'.tr(),
      prefixIcon: Icons.store,
    );
  }

  Widget _buildDatePicker() {
    final theme = Theme.of(context);

    return GestureDetector(
      onTap: () async {
        final picked = await showDatePicker(
          context: context,
          initialDate: _selectedDate,
          firstDate: DateTime.now().subtract(const Duration(days: 365)),
          lastDate: DateTime.now(),
        );
        if (picked != null) {
          setState(() => _selectedDate = picked);
        }
      },
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: theme.hintColor.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: theme.hintColor.withValues(alpha: 0.3)),
        ),
        child: Row(
          children: [
            Icon(Icons.calendar_today, color: theme.hintColor, size: 20),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Date',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: theme.hintColor,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    DateFormat('MMM dd, yyyy').format(_selectedDate),
                    style: theme.textTheme.bodyMedium,
                  ),
                ],
              ),
            ),
            Icon(Icons.arrow_drop_down, color: theme.hintColor),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorMessage() {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      padding: const EdgeInsets.all(12),
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: colorScheme.error.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: colorScheme.error),
      ),
      child: Text(
        _errorMessage!,
        style: theme.textTheme.bodyMedium?.copyWith(color: colorScheme.error),
      ),
    );
  }

  Widget _buildSubmitButton() {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final typeText =
        _selectedType == CreditCardTransactionType.purchase
            ? 'credit_card_transaction_type_purchase'.tr()
            : 'credit_card_transaction_type_payment'.tr();
    final typeColor =
        _selectedType == CreditCardTransactionType.purchase
            ? theme.colorScheme.error
            : theme.colorScheme.primary;
    final typeIcon =
        _selectedType == CreditCardTransactionType.purchase
            ? Icons.shopping_cart_outlined
            : Icons.payment_outlined;

    return CustomButton.elevated(
      onPressed: _isLoading ? null : _handleSubmit,
      backgroundColor: typeColor,
      child:
          _isLoading
              ? SizedBox(
                height: 20,
                width: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    colorScheme.onPrimary,
                  ),
                ),
              )
              : Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(typeIcon, color: colorScheme.onPrimary),
                  const SizedBox(width: 8),
                  Text(
                    'Add $typeText',
                    style: theme.textTheme.titleMedium?.copyWith(
                      color: colorScheme.onPrimary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
    );
  }
}
