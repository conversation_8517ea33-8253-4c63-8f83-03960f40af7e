import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';

import '../../services/credit_card_service.dart';
import '../../features/models/credit_card_model.dart';
import '../../features/models/credit_card_transaction_model.dart';
import '../../utils/helper/keyboard_dismiss_wrapper.dart';
import '../../resources/app_theme.dart';

class CreditCardTransactionListScreen extends StatefulWidget {
  final String?
  creditCardId; // Optional - if provided, show only that card's transactions

  const CreditCardTransactionListScreen({super.key, this.creditCardId});

  @override
  State<CreditCardTransactionListScreen> createState() =>
      _CreditCardTransactionListScreenState();
}

class _CreditCardTransactionListScreenState
    extends State<CreditCardTransactionListScreen> {
  // State variables
  List<CreditCardTransactionModel> _allTransactions = [];
  List<CreditCardTransactionModel> _filteredTransactions = [];
  Map<String, CreditCardModel> _creditCardsMap = {};

  bool _isLoading = true;
  String? _errorMessage;

  // Filter and sort options
  final TextEditingController _searchController = TextEditingController();
  CreditCardTransactionType? _selectedTypeFilter;
  String? _selectedCreditCardId;
  DateTimeRange? _selectedDateRange;
  String _sortBy = 'date_desc'; // date_desc, date_asc, amount_desc, amount_asc

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Load credit cards to get names for transactions
      final creditCards = await CreditCardService.getCreditCards();
      _creditCardsMap = {for (var card in creditCards) card.id: card};

      // Load transactions
      List<CreditCardTransactionModel> transactions;
      if (widget.creditCardId != null) {
        transactions =
            await CreditCardService.getCreditCardTransactionsByCardId(
              widget.creditCardId!,
            );
      } else {
        transactions = await CreditCardService.getCreditCardTransactions();
      }

      setState(() {
        _allTransactions = transactions;
        _filteredTransactions = transactions;
        _isLoading = false;
      });

      _applySortAndFilter();
    } catch (e) {
      setState(() {
        _errorMessage = 'credit_card_transaction_load_error'.tr(
          namedArgs: {'error': e.toString()},
        );
        _isLoading = false;
      });
    }
  }

  void _applySortAndFilter() {
    List<CreditCardTransactionModel> filtered = List.from(_allTransactions);

    // Apply credit card filter (only if not already filtered by specific card)
    if (_selectedCreditCardId != null && widget.creditCardId == null) {
      filtered =
          filtered
              .where(
                (transaction) =>
                    transaction.creditCardId == _selectedCreditCardId,
              )
              .toList();
    }

    // Apply type filter
    if (_selectedTypeFilter != null) {
      filtered =
          filtered
              .where((transaction) => transaction.type == _selectedTypeFilter)
              .toList();
    }

    // Apply date range filter
    if (_selectedDateRange != null) {
      filtered =
          filtered.where((transaction) {
            return transaction.date.isAfter(
                  _selectedDateRange!.start.subtract(const Duration(days: 1)),
                ) &&
                transaction.date.isBefore(
                  _selectedDateRange!.end.add(const Duration(days: 1)),
                );
          }).toList();
    }

    // Apply search filter
    final searchQuery = _searchController.text.toLowerCase();
    if (searchQuery.isNotEmpty) {
      filtered =
          filtered.where((transaction) {
            return transaction.description.toLowerCase().contains(
                  searchQuery,
                ) ||
                transaction.category.toLowerCase().contains(searchQuery) ||
                (transaction.merchantName?.toLowerCase().contains(
                      searchQuery,
                    ) ??
                    false) ||
                _getCreditCardName(
                  transaction.creditCardId,
                ).toLowerCase().contains(searchQuery);
          }).toList();
    }

    // Apply sorting
    switch (_sortBy) {
      case 'date_desc':
        filtered.sort((a, b) => b.date.compareTo(a.date));
        break;
      case 'date_asc':
        filtered.sort((a, b) => a.date.compareTo(b.date));
        break;
      case 'amount_desc':
        filtered.sort((a, b) => b.amount.compareTo(a.amount));
        break;
      case 'amount_asc':
        filtered.sort((a, b) => a.amount.compareTo(b.amount));
        break;
    }

    setState(() {
      _filteredTransactions = filtered;
    });
  }

  String _getCreditCardName(String creditCardId) {
    return _creditCardsMap[creditCardId]?.cardName ??
        'credit_card_transaction_unknown_card'.tr();
  }

  String _getShortCreditCardName(String creditCardId) {
    final cardName = _getCreditCardName(creditCardId);
    // Truncate long card names for title display
    if (cardName.length > 20) {
      return '${cardName.substring(0, 17)}...';
    }
    return cardName;
  }

  void _clearFilters() {
    setState(() {
      _selectedTypeFilter = null;
      _selectedCreditCardId = null;
      _selectedDateRange = null;
      _sortBy = 'date_desc';
      _searchController.clear();
    });
    _applySortAndFilter();
  }

  bool _hasActiveFilters() {
    return _selectedTypeFilter != null ||
        (_selectedCreditCardId != null && widget.creditCardId == null) ||
        _selectedDateRange != null ||
        _searchController.text.isNotEmpty;
  }

  Future<void> _deleteCreditCardTransaction(
    CreditCardTransactionModel transaction,
  ) async {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    // Show confirmation dialog
    final confirmed = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text('credit_card_transaction_delete_title'.tr()),
            content: Text(
              'credit_card_transaction_delete_confirm'.tr(
                namedArgs: {
                  'category': transaction.category,
                  'amount': NumberFormat('#,##,###').format(transaction.amount),
                  'date': DateFormat('MMM dd, yyyy').format(transaction.date),
                  'cardName': _getCreditCardName(transaction.creditCardId),
                },
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: Text('credit_card_cancel'.tr()),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                style: TextButton.styleFrom(foregroundColor: colorScheme.error),
                child: Text('credit_card_delete'.tr()),
              ),
            ],
          ),
    );

    if (confirmed == true) {
      try {
        // Delete the transaction using CreditCardService
        await CreditCardService.deleteCreditCardTransaction(transaction.id);

        // Reload transactions to reflect the change
        await _loadData();

        // Show success message
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('credit_card_transaction_deleted_success'.tr()),
              backgroundColor: AppColorPalette.success,
              duration: const Duration(seconds: 2),
            ),
          );
        }
      } catch (e) {
        print('Error deleting credit card transaction: $e');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('credit_card_transaction_delete_error'.tr()),
              backgroundColor: colorScheme.error,
              duration: const Duration(seconds: 3),
            ),
          );
        }
      }
    }
  }

  Future<void> _selectDateRange() async {
    final picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      initialDateRange: _selectedDateRange,
    );

    if (picked != null) {
      setState(() {
        _selectedDateRange = picked;
      });
      _applySortAndFilter();
    }
  }

  void _showFilterBottomSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder:
          (context) => DraggableScrollableSheet(
            initialChildSize: 0.7,
            maxChildSize: 0.9,
            minChildSize: 0.5,
            expand: false,
            builder:
                (context, scrollController) =>
                    _buildFilterSheet(scrollController),
          ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return KeyboardDismissWrapper(
      child: Scaffold(
        backgroundColor: theme.scaffoldBackgroundColor,
        appBar: AppBar(
          title: Text(
            widget.creditCardId != null
                ? _getShortCreditCardName(widget.creditCardId!)
                : 'credit_card_transaction_list_title'.tr(),
            style: theme.appBarTheme.titleTextStyle?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          leading: IconButton(
            icon: const Icon(Icons.arrow_back_ios, size: 20),
            onPressed: () => Navigator.of(context).pop(),
          ),
          actions: [
            Container(
              margin: const EdgeInsets.only(right: 8),
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(10),
              ),
              child: IconButton(
                icon: const Icon(Icons.tune, size: 20),
                onPressed: _showFilterBottomSheet,
              ),
            ),
          ],
        ),
        body: Column(
          children: [
            _buildSearchBar(),
            if (_hasActiveFilters()) _buildActiveFiltersBar(),

            // Hint about delete functionality
            if (_filteredTransactions.isNotEmpty && !_hasActiveFilters())
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 8,
                ),
                color: theme.colorScheme.primary.withValues(alpha: 0.05),
                child: Row(
                  children: [
                    Icon(
                      Icons.info_outline,
                      size: 16,
                      color: theme.colorScheme.primary.withValues(alpha: 0.7),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'credit_card_transaction_swipe_hint'.tr(),
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.primary.withValues(
                            alpha: 0.8,
                          ),
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ],
                ),
              ),

            Expanded(
              child: RefreshIndicator(
                onRefresh: _loadData,
                child: CustomScrollView(
                  slivers: [
                    SliverToBoxAdapter(child: _buildSummaryCard()),
                    _buildTransactionsSliverList(),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchBar() {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      padding: const EdgeInsets.all(16),
      child: Container(
        decoration: BoxDecoration(
          color: theme.cardColor,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: colorScheme.outline.withValues(alpha: 0.2),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: theme.shadowColor.withValues(alpha: 0.08),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: TextField(
          controller: _searchController,
          style: theme.textTheme.bodyMedium?.copyWith(
            color: colorScheme.onSurface,
          ),
          decoration: InputDecoration(
            hintText: 'credit_card_transaction_search_hint'.tr(),
            hintStyle: theme.textTheme.bodyMedium?.copyWith(
              color: theme.hintColor,
            ),
            prefixIcon: Icon(
              Icons.search,
              color: colorScheme.primary,
              size: 20,
            ),
            suffixIcon:
                _searchController.text.isNotEmpty
                    ? Container(
                      margin: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: colorScheme.outline.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: IconButton(
                        icon: Icon(
                          Icons.close,
                          color: theme.hintColor,
                          size: 16,
                        ),
                        onPressed: () {
                          _searchController.clear();
                          _applySortAndFilter();
                        },
                      ),
                    )
                    : null,
            border: InputBorder.none,
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 16,
            ),
          ),
          onChanged: (_) => _applySortAndFilter(),
        ),
      ),
    );
  }

  Widget _buildActiveFiltersBar() {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      color: colorScheme.primary.withValues(alpha: 0.1),
      child: Row(
        children: [
          Expanded(
            child: Wrap(
              spacing: 8,
              runSpacing: 4,
              children: [
                if (_selectedTypeFilter != null)
                  _buildFilterChip(
                    _selectedTypeFilter == CreditCardTransactionType.purchase
                        ? 'credit_card_transaction_filter_purchases'.tr()
                        : 'credit_card_transaction_filter_payments'.tr(),
                    () => setState(() => _selectedTypeFilter = null),
                  ),
                if (_selectedCreditCardId != null &&
                    widget.creditCardId == null)
                  _buildFilterChip(
                    _getCreditCardName(_selectedCreditCardId!),
                    () => setState(() => _selectedCreditCardId = null),
                  ),
                if (_selectedDateRange != null)
                  _buildFilterChip(
                    'credit_card_transaction_date_range'.tr(),
                    () => setState(() => _selectedDateRange = null),
                  ),
              ],
            ),
          ),
          TextButton(
            onPressed: _clearFilters,
            child: Text(
              'Clear All',
              style: theme.textTheme.bodySmall?.copyWith(
                color: colorScheme.primary,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String label, VoidCallback onRemove) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: colorScheme.primary,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            label,
            style: theme.textTheme.bodySmall?.copyWith(
              color: colorScheme.onPrimary,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(width: 4),
          GestureDetector(
            onTap: () {
              onRemove();
              _applySortAndFilter();
            },
            child: Icon(Icons.close, size: 16, color: colorScheme.onPrimary),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryCard() {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    if (_filteredTransactions.isEmpty) return const SizedBox.shrink();

    final totalPurchases = _filteredTransactions
        .where((t) => t.type == CreditCardTransactionType.purchase)
        .fold(0.0, (sum, t) => sum + t.amount);

    final totalPayments = _filteredTransactions
        .where((t) => t.type == CreditCardTransactionType.payment)
        .fold(0.0, (sum, t) => sum + t.amount);

    final netBalance = totalPurchases - totalPayments;

    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: theme.cardColor,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: colorScheme.outline.withValues(alpha: 0.1),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: theme.shadowColor.withValues(alpha: 0.08),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: colorScheme.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.dashboard_outlined,
                  color: colorScheme.primary,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                'credit_card_transaction_overview'.tr(),
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: colorScheme.onSurface,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),

          // Purchases and Payments Row
          Row(
            children: [
              Expanded(
                child: _buildSummaryItem(
                  'Purchases',
                  totalPurchases,
                  Colors.red,
                  Icons.shopping_cart_outlined,
                  isNegative: true,
                ),
              ),
              Container(
                width: 1,
                height: 50,
                color: colorScheme.outline.withValues(alpha: 0.2),
                margin: const EdgeInsets.symmetric(horizontal: 16),
              ),
              Expanded(
                child: _buildSummaryItem(
                  'Payments',
                  totalPayments,
                  Colors.green,
                  Icons.payment,
                  isNegative: false,
                ),
              ),
            ],
          ),

          // Net Balance (if any)
          if (netBalance != 0) ...[
            const SizedBox(height: 16),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: (netBalance > 0 ? colorScheme.error : Colors.green)
                    .withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: (netBalance > 0 ? colorScheme.error : Colors.green)
                      .withValues(alpha: 0.2),
                  width: 1,
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    netBalance > 0 ? Icons.trending_up : Icons.trending_down,
                    color: netBalance > 0 ? colorScheme.error : Colors.green,
                    size: 18,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    netBalance > 0
                        ? 'credit_card_transaction_outstanding'.tr(
                          namedArgs: {
                            'amount': NumberFormat(
                              '#,##,###',
                            ).format(netBalance),
                          },
                        )
                        : 'credit_card_transaction_credit_balance'.tr(
                          namedArgs: {
                            'amount': NumberFormat(
                              '#,##,###',
                            ).format(-netBalance),
                          },
                        ),
                    style: theme.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: netBalance > 0 ? colorScheme.error : Colors.green,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildSummaryItem(
    String label,
    double amount,
    Color color,
    IconData icon, {
    required bool isNegative,
    bool isCount = false,
  }) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(10),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(10),
          ),
          child: Icon(icon, color: color, size: 18),
        ),
        const SizedBox(height: 8),
        Text(
          label,
          style: theme.textTheme.bodySmall?.copyWith(
            color: colorScheme.onSurface.withValues(alpha: 0.7),
            fontWeight: FontWeight.w500,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 4),
        Text(
          isCount
              ? '${amount.toInt()}'
              : '${isNegative ? "-" : "+"}₹${NumberFormat('#,##,###').format(amount)}',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
      ],
    );
  }

  Widget _buildTransactionsSliverList() {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    if (_isLoading) {
      return SliverFillRemaining(
        child: Center(
          child: Container(
            padding: const EdgeInsets.all(20),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(
                  color: colorScheme.primary,
                  strokeWidth: 3,
                ),
                const SizedBox(height: 16),
                Text(
                  'credit_card_transaction_loading'.tr(),
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    }

    if (_errorMessage != null) {
      return SliverFillRemaining(
        child: Center(
          child: Container(
            margin: const EdgeInsets.all(24),
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: theme.cardColor,
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: colorScheme.error.withValues(alpha: 0.2),
                width: 1,
              ),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: colorScheme.error.withValues(alpha: 0.1),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.error_outline,
                    size: 32,
                    color: colorScheme.error,
                  ),
                ),
                const SizedBox(height: 16),
                Text(
                  'credit_card_transaction_error_title'.tr(),
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: colorScheme.onSurface,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  _errorMessage!,
                  textAlign: TextAlign.center,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                ),
                const SizedBox(height: 20),
                ElevatedButton.icon(
                  onPressed: _loadData,
                  icon: const Icon(Icons.refresh, size: 18),
                  label: Text('credit_card_transaction_retry'.tr()),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: colorScheme.primary,
                    foregroundColor: colorScheme.onPrimary,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 24,
                      vertical: 12,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    }

    if (_filteredTransactions.isEmpty) {
      return SliverFillRemaining(
        child: Center(
          child: Container(
            margin: const EdgeInsets.all(24),
            padding: const EdgeInsets.all(32),
            decoration: BoxDecoration(
              color: theme.cardColor,
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: colorScheme.outline.withValues(alpha: 0.1),
                width: 1,
              ),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: colorScheme.primary.withValues(alpha: 0.1),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    _searchController.text.isNotEmpty ||
                            _selectedTypeFilter != null
                        ? Icons.search_off
                        : Icons.receipt_long_outlined,
                    size: 32,
                    color: colorScheme.primary.withValues(alpha: 0.7),
                  ),
                ),
                const SizedBox(height: 16),
                Text(
                  _searchController.text.isNotEmpty ||
                          _selectedTypeFilter != null
                      ? 'credit_card_transaction_no_matching'.tr()
                      : 'credit_card_transaction_no_transactions'.tr(),
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: colorScheme.onSurface,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  _searchController.text.isNotEmpty ||
                          _selectedTypeFilter != null
                      ? 'credit_card_transaction_adjust_filters'.tr()
                      : 'credit_card_transaction_add_first'.tr(),
                  textAlign: TextAlign.center,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    }

    return SliverPadding(
      padding: const EdgeInsets.all(16),
      sliver: SliverList(
        delegate: SliverChildBuilderDelegate((context, index) {
          final transaction = _filteredTransactions[index];
          return _TransactionCard(
            transaction: transaction,
            creditCardName: _getCreditCardName(transaction.creditCardId),
            showCreditCard: widget.creditCardId == null,
            onDelete: () => _deleteCreditCardTransaction(transaction),
          );
        }, childCount: _filteredTransactions.length),
      ),
    );
  }

  Widget _buildFilterSheet(ScrollController scrollController) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      decoration: BoxDecoration(
        color: theme.cardColor,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Handle bar
          Center(
            child: Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: colorScheme.outline.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          ),
          const SizedBox(height: 20),

          Row(
            children: [
              Text(
                'credit_card_transaction_filter_title'.tr(),
                style: theme.textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: colorScheme.onSurface,
                ),
              ),
              const Spacer(),
              TextButton.icon(
                onPressed: () {
                  _clearFilters();
                  Navigator.of(context).pop();
                },
                icon: Icon(Icons.clear_all, size: 16, color: colorScheme.error),
                label: Text(
                  'credit_card_transaction_clear_all'.tr(),
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: colorScheme.error,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),

          Expanded(
            child: ListView(
              controller: scrollController,
              children: [
                // Transaction Type Filter
                _buildFilterSection(
                  'credit_card_transaction_type_label'.tr(),
                  DropdownButton<CreditCardTransactionType?>(
                    value: _selectedTypeFilter,
                    isExpanded: true,
                    hint: Text(
                      'All Types',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: colorScheme.onSurface.withValues(alpha: 0.6),
                      ),
                    ),
                    items: [
                      DropdownMenuItem(
                        value: null,
                        child: Text(
                          'credit_card_transaction_all_types'.tr(),
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: colorScheme.onSurface,
                          ),
                        ),
                      ),
                      DropdownMenuItem(
                        value: CreditCardTransactionType.purchase,
                        child: Text(
                          'credit_card_transaction_filter_purchases'.tr(),
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: colorScheme.onSurface,
                          ),
                        ),
                      ),
                      DropdownMenuItem(
                        value: CreditCardTransactionType.payment,
                        child: Text(
                          'credit_card_transaction_filter_payments'.tr(),
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: colorScheme.onSurface,
                          ),
                        ),
                      ),
                    ],
                    onChanged: (value) {
                      setState(() => _selectedTypeFilter = value);
                    },
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: colorScheme.onSurface,
                    ),
                    dropdownColor: theme.cardColor,
                    underline: Container(
                      height: 1,
                      color: colorScheme.outline.withValues(alpha: 0.3),
                    ),
                  ),
                ),
                const SizedBox(height: 16),

                // Credit Card Filter (only show if not already filtered by specific card)
                if (widget.creditCardId == null && _creditCardsMap.isNotEmpty)
                  _buildFilterSection(
                    'Credit Card',
                    DropdownButton<String?>(
                      value: _selectedCreditCardId,
                      isExpanded: true,
                      hint: Text(
                        'All Credit Cards',
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: colorScheme.onSurface.withValues(alpha: 0.6),
                        ),
                      ),
                      items: [
                        DropdownMenuItem(
                          value: null,
                          child: Text(
                            'All Credit Cards',
                            style: theme.textTheme.bodyMedium?.copyWith(
                              color: colorScheme.onSurface,
                            ),
                          ),
                        ),
                        ..._creditCardsMap.values.map(
                          (card) => DropdownMenuItem(
                            value: card.id,
                            child: Text(
                              card.cardName,
                              style: theme.textTheme.bodyMedium?.copyWith(
                                color: colorScheme.onSurface,
                              ),
                            ),
                          ),
                        ),
                      ],
                      onChanged: (value) {
                        setState(() => _selectedCreditCardId = value);
                      },
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: colorScheme.onSurface,
                      ),
                      dropdownColor: theme.cardColor,
                      underline: Container(
                        height: 1,
                        color: colorScheme.outline.withValues(alpha: 0.3),
                      ),
                    ),
                  ),
                if (widget.creditCardId == null && _creditCardsMap.isNotEmpty)
                  const SizedBox(height: 16),

                // Date Range Filter
                _buildFilterSection(
                  'Date Range',
                  GestureDetector(
                    onTap: _selectDateRange,
                    child: Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: theme.scaffoldBackgroundColor,
                        border: Border.all(
                          color: colorScheme.outline.withValues(alpha: 0.3),
                        ),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                            child: Text(
                              _selectedDateRange == null
                                  ? 'Select Date Range'
                                  : '${DateFormat('MMM dd, yyyy').format(_selectedDateRange!.start)} - ${DateFormat('MMM dd, yyyy').format(_selectedDateRange!.end)}',
                              style: theme.textTheme.bodyMedium?.copyWith(
                                color:
                                    _selectedDateRange == null
                                        ? colorScheme.onSurface.withValues(
                                          alpha: 0.6,
                                        )
                                        : colorScheme.onSurface,
                              ),
                            ),
                          ),
                          Icon(
                            Icons.calendar_today,
                            color: colorScheme.primary,
                            size: 20,
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 16),

                // Sort Options
                _buildFilterSection(
                  'Sort By',
                  Container(
                    decoration: BoxDecoration(
                      color: theme.scaffoldBackgroundColor,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: colorScheme.outline.withValues(alpha: 0.1),
                        width: 1,
                      ),
                    ),
                    child: Column(
                      children: [
                        _buildSortOption(
                          'Date (Newest First)',
                          'date_desc',
                          Icons.arrow_downward,
                        ),
                        _buildDivider(),
                        _buildSortOption(
                          'Date (Oldest First)',
                          'date_asc',
                          Icons.arrow_upward,
                        ),
                        _buildDivider(),
                        _buildSortOption(
                          'Amount (Highest First)',
                          'amount_desc',
                          Icons.trending_up,
                        ),
                        _buildDivider(),
                        _buildSortOption(
                          'Amount (Lowest First)',
                          'amount_asc',
                          Icons.trending_down,
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 20),

          // Apply button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: () {
                _applySortAndFilter();
                Navigator.of(context).pop();
              },
              icon: const Icon(Icons.check, size: 18),
              label: const Text('Apply Filters'),
              style: ElevatedButton.styleFrom(
                backgroundColor: colorScheme.primary,
                foregroundColor: colorScheme.onPrimary,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterSection(String title, Widget child) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: theme.textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
            color: colorScheme.onSurface,
          ),
        ),
        const SizedBox(height: 8),
        child,
      ],
    );
  }

  Widget _buildSortOption(String title, String value, IconData icon) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final isSelected = _sortBy == value;
    return InkWell(
      onTap: () => setState(() => _sortBy = value),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(6),
              decoration: BoxDecoration(
                color:
                    isSelected
                        ? colorScheme.primary.withValues(alpha: 0.1)
                        : colorScheme.outline.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(6),
              ),
              child: Icon(
                icon,
                size: 16,
                color:
                    isSelected
                        ? colorScheme.primary
                        : colorScheme.onSurface.withValues(alpha: 0.6),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                title,
                style: theme.textTheme.bodyMedium?.copyWith(
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                  color:
                      isSelected
                          ? colorScheme.onSurface
                          : colorScheme.onSurface.withValues(alpha: 0.8),
                ),
              ),
            ),
            Container(
              width: 20,
              height: 20,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: isSelected ? colorScheme.primary : colorScheme.outline,
                  width: 2,
                ),
                color: isSelected ? colorScheme.primary : Colors.transparent,
              ),
              child:
                  isSelected
                      ? Icon(
                        Icons.check,
                        size: 12,
                        color: colorScheme.onPrimary,
                      )
                      : null,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDivider() {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      height: 1,
      color: colorScheme.outline.withValues(alpha: 0.1),
      margin: const EdgeInsets.symmetric(horizontal: 16),
    );
  }
}

class _TransactionCard extends StatelessWidget {
  final CreditCardTransactionModel transaction;
  final String creditCardName;
  final bool showCreditCard;
  final VoidCallback onDelete;

  const _TransactionCard({
    required this.transaction,
    required this.creditCardName,
    required this.showCreditCard,
    required this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final isPurchase = transaction.type == CreditCardTransactionType.purchase;
    final transactionColor = isPurchase ? Colors.red : Colors.green;
    final transactionIcon =
        isPurchase ? Icons.remove_shopping_cart : Icons.payment;

    // Use merchant name if available, otherwise use description
    final title = transaction.merchantName ?? transaction.description;
    final subtitle = transaction.category;

    return Dismissible(
      key: Key(transaction.id),
      direction: DismissDirection.endToStart,
      background: Container(
        margin: const EdgeInsets.only(bottom: 12),
        padding: const EdgeInsets.symmetric(horizontal: 20),
        decoration: BoxDecoration(
          color: colorScheme.error,
          borderRadius: BorderRadius.circular(12),
        ),
        alignment: Alignment.centerRight,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(Icons.delete, color: colorScheme.onError, size: 24),
            const SizedBox(width: 8),
            Text(
              'Delete',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: colorScheme.onError,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
      confirmDismiss: (direction) async {
        // Don't auto-dismiss, we'll handle deletion manually
        onDelete();
        return false;
      },
      child: Container(
        margin: const EdgeInsets.only(bottom: 12),
        decoration: BoxDecoration(
          color: theme.cardColor,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: colorScheme.outline.withValues(alpha: 0.1),
            width: 1,
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              // Left side: Icon
              Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: transactionColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Icon(transactionIcon, color: transactionColor, size: 20),
              ),

              const SizedBox(width: 12),

              // Middle: Title and Subtitle Column
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: theme.textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: colorScheme.onSurface,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      subtitle,
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: colorScheme.onSurface.withValues(alpha: 0.6),
                        fontWeight: FontWeight.w500,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),

              const SizedBox(width: 12),

              // Right side: Amount and Date Column
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    '${isPurchase ? '-' : '+'}₹${NumberFormat('#,##,###').format(transaction.amount)}',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: transactionColor,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    DateFormat('MMM dd').format(transaction.date),
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: colorScheme.onSurface.withValues(alpha: 0.6),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
