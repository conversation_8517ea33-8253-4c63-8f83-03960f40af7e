import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';

import 'package:easy_localization/easy_localization.dart';

import '../../shared/widgtes/common/custom_button.dart';
import '../../shared/widgtes/common/custom_text_field.dart';
import '../../services/credit_card_service.dart';
import '../../features/models/credit_card_model.dart';
import '../../utils/helper/keyboard_dismiss_wrapper.dart';

class EditCreditCardScreen extends StatefulWidget {
  final CreditCardModel creditCard;

  const EditCreditCardScreen({super.key, required this.creditCard});

  @override
  State<EditCreditCardScreen> createState() => _EditCreditCardScreenState();
}

class _EditCreditCardScreenState extends State<EditCreditCardScreen> {
  final TextEditingController _cardNameController = TextEditingController();
  final TextEditingController _cardNumberController = TextEditingController();
  final TextEditingController _bankNameController = TextEditingController();
  final TextEditingController _creditLimitController = TextEditingController();
  final TextEditingController _outstandingBalanceController =
      TextEditingController();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  bool _isLoading = false;
  String? _errorMessage;
  CreditCardType _selectedCardType = CreditCardType.visa;
  DateTime _selectedDueDate = DateTime.now().add(const Duration(days: 30));
  DateTime _selectedClosingDate = DateTime.now().add(const Duration(days: 25));

  @override
  void initState() {
    super.initState();
    _initializeFields();
  }

  void _initializeFields() {
    _cardNameController.text = widget.creditCard.cardName;
    _cardNumberController.text = widget.creditCard.cardNumber;
    _bankNameController.text = widget.creditCard.bankName;
    _creditLimitController.text = widget.creditCard.creditLimit.toString();
    _outstandingBalanceController.text =
        widget.creditCard.outstandingBalance.toString();
    _selectedCardType = widget.creditCard.cardType;
    _selectedDueDate = widget.creditCard.dueDate;
    _selectedClosingDate = widget.creditCard.closingDate;
  }

  @override
  void dispose() {
    _cardNameController.dispose();
    _cardNumberController.dispose();
    _bankNameController.dispose();
    _creditLimitController.dispose();
    _outstandingBalanceController.dispose();
    super.dispose();
  }

  Future<void> _handleUpdateCreditCard() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final cardName = _cardNameController.text.trim();
      final cardNumber = _cardNumberController.text.trim();
      final bankName = _bankNameController.text.trim();
      final creditLimit = double.parse(_creditLimitController.text.trim());
      final outstandingBalance = double.parse(
        _outstandingBalanceController.text.trim(),
      );

      // Validate outstanding balance doesn't exceed credit limit
      if (outstandingBalance > creditLimit) {
        setState(() {
          _errorMessage = 'credit_card_balance_exceed_error'.tr();
        });
        return;
      }

      // Create updated credit card model
      final updatedCreditCard = widget.creditCard.copyWith(
        cardName: cardName,
        cardNumber: cardNumber,
        cardType: _selectedCardType,
        bankName: bankName,
        creditLimit: creditLimit,
        outstandingBalance: outstandingBalance,
        dueDate: _selectedDueDate,
        closingDate: _selectedClosingDate,
      );

      // Update credit card
      await CreditCardService.updateCreditCard(updatedCreditCard);

      if (!mounted) return;
      _showSuccessDialog();
    } catch (e) {
      setState(() {
        _errorMessage = 'credit_card_update_error'.tr();
      });
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _showSuccessDialog() {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: theme.cardColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.green.withValues(alpha: 0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.check_circle_outline,
                  color: Colors.green,
                  size: 48,
                ),
              ),
              const SizedBox(height: 24),
              Text(
                'credit_card_updated_title'.tr(),
                style: theme.textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: colorScheme.onSurface,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 12),
              Text(
                'credit_card_updated_desc'.tr(),
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: colorScheme.onSurface.withValues(alpha: 0.8),
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () {
                    Navigator.of(context).pop(); // Close dialog
                    context.pop(true); // Return to previous screen with success
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: Text(
                    'credit_card_continue'.tr(),
                    style: TextStyle(fontWeight: FontWeight.w600, fontSize: 16),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  String _getLocalizedCardTypeName(CreditCardType type) {
    switch (type) {
      case CreditCardType.visa:
        return 'credit_card_type_visa'.tr();
      case CreditCardType.mastercard:
        return 'credit_card_type_mastercard'.tr();
      case CreditCardType.amex:
        return 'credit_card_type_amex'.tr();
      case CreditCardType.rupay:
        return 'credit_card_type_rupay'.tr();
      case CreditCardType.discover:
        return 'credit_card_type_discover'.tr();
      case CreditCardType.other:
        return 'credit_card_type_other'.tr();
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return KeyboardDismissWrapper(
      child: Scaffold(
        backgroundColor: theme.scaffoldBackgroundColor,
        appBar: AppBar(
          title: Text(
            'credit_card_edit_title'.tr(),
            style: theme.appBarTheme.titleTextStyle?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          leading: IconButton(
            icon: const Icon(Icons.arrow_back_ios, size: 20),
            onPressed: () => context.pop(),
          ),
        ),
        body: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // Error message
                if (_errorMessage != null)
                  Container(
                    padding: const EdgeInsets.all(12),
                    margin: const EdgeInsets.only(bottom: 16),
                    decoration: BoxDecoration(
                      color: colorScheme.error.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: colorScheme.error),
                    ),
                    child: Text(
                      _errorMessage!,
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: colorScheme.error,
                      ),
                    ),
                  ),

                // Card Name
                CustomTextField(
                  controller: _cardNameController,
                  labelText: 'credit_card_name_label'.tr(),
                  hintText: 'credit_card_edit_name_hint'.tr(),
                  prefixIcon: Icons.credit_card,
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'credit_card_name_required'.tr();
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // Card Number
                CustomTextField(
                  controller: _cardNumberController,
                  labelText: 'credit_card_edit_number_label'.tr(),
                  hintText: 'credit_card_edit_number_hint'.tr(),
                  prefixIcon: Icons.numbers,
                  keyboardType: TextInputType.number,
                  inputFormatters: [
                    FilteringTextInputFormatter.digitsOnly,
                    LengthLimitingTextInputFormatter(4),
                  ],
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'credit_card_digits_required'.tr();
                    }
                    if (value.length != 4) {
                      return 'credit_card_digits_invalid'.tr();
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // Card Type
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'credit_card_card_type'.tr(),
                      style: theme.textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: colorScheme.onSurface,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Container(
                      decoration: BoxDecoration(
                        color: theme.cardColor,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: colorScheme.outline.withValues(alpha: 0.3),
                          width: 1,
                        ),
                      ),
                      child: DropdownButtonFormField<CreditCardType>(
                        value: _selectedCardType,
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: colorScheme.onSurface,
                        ),
                        dropdownColor: theme.cardColor,
                        decoration: const InputDecoration(
                          border: InputBorder.none,
                          contentPadding: EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 12,
                          ),
                        ),
                        items:
                            CreditCardType.values.map((type) {
                              return DropdownMenuItem(
                                value: type,
                                child: Text(
                                  _getLocalizedCardTypeName(type),
                                  style: theme.textTheme.bodyMedium?.copyWith(
                                    color: colorScheme.onSurface,
                                  ),
                                ),
                              );
                            }).toList(),
                        onChanged: (value) {
                          if (value != null) {
                            setState(() {
                              _selectedCardType = value;
                            });
                          }
                        },
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                // Bank Name
                CustomTextField(
                  controller: _bankNameController,
                  labelText: 'credit_card_bank_label'.tr(),
                  hintText: 'credit_card_bank_hint'.tr(),
                  prefixIcon: Icons.account_balance,
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'credit_card_bank_required'.tr();
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // Credit Limit
                CustomTextField(
                  controller: _creditLimitController,
                  labelText: 'credit_card_limit_label'.tr(),
                  hintText: 'credit_card_edit_limit_hint'.tr(),
                  prefixIcon: Icons.account_balance_wallet,
                  keyboardType: TextInputType.number,
                  inputFormatters: [
                    FilteringTextInputFormatter.allow(RegExp(r'[0-9.]')),
                  ],
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'credit_card_limit_required'.tr();
                    }
                    if (double.tryParse(value) == null) {
                      return 'credit_card_amount_invalid'.tr();
                    }
                    if (double.parse(value) <= 0) {
                      return 'credit_card_edit_limit_positive_error'.tr();
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // Outstanding Balance
                CustomTextField(
                  controller: _outstandingBalanceController,
                  labelText: 'credit_card_edit_balance_label'.tr(),
                  hintText: 'credit_card_edit_balance_hint'.tr(),
                  prefixIcon: Icons.account_balance_wallet_outlined,
                  keyboardType: TextInputType.number,
                  inputFormatters: [
                    FilteringTextInputFormatter.allow(RegExp(r'[0-9.]')),
                  ],
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'credit_card_balance_required'.tr();
                    }
                    if (double.tryParse(value) == null) {
                      return 'credit_card_amount_invalid'.tr();
                    }
                    if (double.parse(value) < 0) {
                      return 'credit_card_edit_balance_negative_error'.tr();
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // Due Date
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'credit_card_edit_due_date_label'.tr(),
                      style: theme.textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: colorScheme.onSurface,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Container(
                      decoration: BoxDecoration(
                        color: theme.cardColor,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: colorScheme.outline.withValues(alpha: 0.3),
                          width: 1,
                        ),
                      ),
                      child: ListTile(
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 4,
                        ),
                        leading: Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: colorScheme.primary.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Icon(
                            Icons.calendar_today,
                            color: colorScheme.primary,
                            size: 20,
                          ),
                        ),
                        title: Text(
                          DateFormat('MMM dd, yyyy').format(_selectedDueDate),
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: colorScheme.onSurface,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        trailing: Icon(
                          Icons.keyboard_arrow_down,
                          color: colorScheme.onSurface.withValues(alpha: 0.6),
                          size: 20,
                        ),
                        onTap: () async {
                          final picked = await showDatePicker(
                            context: context,
                            initialDate: _selectedDueDate,
                            firstDate: DateTime.now(),
                            lastDate: DateTime.now().add(
                              const Duration(days: 365),
                            ),
                            builder: (context, child) {
                              return Theme(
                                data: Theme.of(context).copyWith(
                                  colorScheme: Theme.of(
                                    context,
                                  ).colorScheme.copyWith(
                                    primary: colorScheme.primary,
                                    onPrimary: colorScheme.onPrimary,
                                    surface: theme.cardColor,
                                    onSurface: colorScheme.onSurface,
                                  ),
                                ),
                                child: child!,
                              );
                            },
                          );
                          if (picked != null) {
                            setState(() {
                              _selectedDueDate = picked;
                            });
                          }
                        },
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                // Closing Date
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'credit_card_edit_closing_date_label'.tr(),
                      style: theme.textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: colorScheme.onSurface,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Container(
                      decoration: BoxDecoration(
                        color: theme.cardColor,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: colorScheme.outline.withValues(alpha: 0.3),
                          width: 1,
                        ),
                      ),
                      child: ListTile(
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 4,
                        ),
                        leading: Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: colorScheme.primary.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Icon(
                            Icons.calendar_month,
                            color: colorScheme.primary,
                            size: 20,
                          ),
                        ),
                        title: Text(
                          DateFormat(
                            'MMM dd, yyyy',
                          ).format(_selectedClosingDate),
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: colorScheme.onSurface,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        trailing: Icon(
                          Icons.keyboard_arrow_down,
                          color: colorScheme.onSurface.withValues(alpha: 0.6),
                          size: 20,
                        ),
                        onTap: () async {
                          final picked = await showDatePicker(
                            context: context,
                            initialDate: _selectedClosingDate,
                            firstDate: DateTime.now(),
                            lastDate: DateTime.now().add(
                              const Duration(days: 365),
                            ),
                            builder: (context, child) {
                              return Theme(
                                data: Theme.of(context).copyWith(
                                  colorScheme: Theme.of(
                                    context,
                                  ).colorScheme.copyWith(
                                    primary: colorScheme.primary,
                                    onPrimary: colorScheme.onPrimary,
                                    surface: theme.cardColor,
                                    onSurface: colorScheme.onSurface,
                                  ),
                                ),
                                child: child!,
                              );
                            },
                          );
                          if (picked != null) {
                            setState(() {
                              _selectedClosingDate = picked;
                            });
                          }
                        },
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 32),

                // Update Button
                CustomButton(
                  type: ButtonType.elevated,
                  onPressed: _isLoading ? null : _handleUpdateCreditCard,
                  isLoading: _isLoading,
                  backgroundColor: colorScheme.primary,
                  foregroundColor: colorScheme.onPrimary,
                  child: Text(
                    'credit_card_update_button'.tr(),
                    style: TextStyle(
                      color: colorScheme.onPrimary,
                      fontWeight: FontWeight.w600,
                      fontSize: 16,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
