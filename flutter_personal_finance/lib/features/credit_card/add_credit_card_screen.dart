import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';

import 'package:easy_localization/easy_localization.dart';

import '../../shared/widgtes/common/custom_button.dart';
import '../../shared/widgtes/common/custom_text_field.dart';
import '../../services/credit_card_service.dart';
import '../../features/models/credit_card_model.dart';
import '../../utils/helper/keyboard_dismiss_wrapper.dart';

class AddCreditCardScreen extends StatefulWidget {
  const AddCreditCardScreen({super.key});

  @override
  State<AddCreditCardScreen> createState() => _AddCreditCardScreenState();
}

class _AddCreditCardScreenState extends State<AddCreditCardScreen> {
  final TextEditingController _cardNameController = TextEditingController();
  final TextEditingController _cardNumberController = TextEditingController();
  final TextEditingController _bankNameController = TextEditingController();
  final TextEditingController _creditLimitController = TextEditingController();
  final TextEditingController _outstandingBalanceController =
      TextEditingController();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  bool _isLoading = false;
  String? _errorMessage;
  CreditCardType _selectedCardType = CreditCardType.visa;
  DateTime _selectedDueDate = DateTime.now().add(const Duration(days: 30));
  DateTime _selectedClosingDate = DateTime.now().add(const Duration(days: 25));

  @override
  void dispose() {
    _cardNameController.dispose();
    _cardNumberController.dispose();
    _bankNameController.dispose();
    _creditLimitController.dispose();
    _outstandingBalanceController.dispose();
    super.dispose();
  }

  Future<void> _handleAddCreditCard() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final cardName = _cardNameController.text.trim();
      final cardNumber = _cardNumberController.text.trim();
      final bankName = _bankNameController.text.trim();
      final creditLimit = double.parse(_creditLimitController.text.trim());
      final outstandingBalance = double.parse(
        _outstandingBalanceController.text.trim(),
      );

      // Validate outstanding balance doesn't exceed credit limit
      if (outstandingBalance > creditLimit) {
        setState(() {
          _errorMessage = 'credit_card_balance_exceed_error'.tr();
        });
        return;
      }

      // Create credit card model
      final creditCard = CreditCardModel(
        cardName: cardName,
        cardNumber: cardNumber,
        cardType: _selectedCardType,
        bankName: bankName,
        creditLimit: creditLimit,
        outstandingBalance: outstandingBalance,
        dueDate: _selectedDueDate,
        closingDate: _selectedClosingDate,
      );

      // Save credit card
      await CreditCardService.addCreditCard(creditCard);

      if (!mounted) return;
      _showSuccessDialog();
    } catch (e) {
      setState(() {
        _errorMessage = 'credit_card_add_error'.tr();
      });
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _showSuccessDialog() {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (dialogContext) => AlertDialog(
            backgroundColor: theme.cardColor,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            title: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.green.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.check_circle_outline,
                    color: Colors.green,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  'credit_card_success_title'.tr(),
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: colorScheme.onSurface,
                  ),
                ),
              ],
            ),
            content: Text(
              'credit_card_added_success'.tr(),
              style: theme.textTheme.bodyMedium?.copyWith(
                color: colorScheme.onSurface.withValues(alpha: 0.8),
              ),
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(dialogContext).pop();
                  if (mounted) {
                    context.pop();
                  }
                },
                style: TextButton.styleFrom(
                  foregroundColor: colorScheme.primary,
                  backgroundColor: colorScheme.primary.withValues(alpha: 0.1),
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 12,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: Text(
                  'credit_card_ok'.tr(),
                  style: theme.textTheme.labelLarge?.copyWith(
                    color: colorScheme.primary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
    );
  }

  Widget _buildCardTypeDropdown() {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'credit_card_card_type'.tr(),
          style: theme.textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
            color: colorScheme.onSurface,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            color: theme.cardColor,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: colorScheme.outline.withValues(alpha: 0.3),
              width: 1,
            ),
          ),
          child: DropdownButtonFormField<CreditCardType>(
            value: _selectedCardType,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: colorScheme.onSurface,
            ),
            dropdownColor: theme.cardColor,
            decoration: const InputDecoration(
              border: InputBorder.none,
              contentPadding: EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 12,
              ),
            ),
            items:
                CreditCardType.values.map((type) {
                  return DropdownMenuItem(
                    value: type,
                    child: Row(
                      children: [
                        _getCardTypeIcon(type),
                        const SizedBox(width: 12),
                        Text(
                          _getLocalizedCardTypeName(type),
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: colorScheme.onSurface,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  );
                }).toList(),
            onChanged: (value) {
              if (value != null) {
                setState(() {
                  _selectedCardType = value;
                });
              }
            },
          ),
        ),
      ],
    );
  }

  Widget _getCardTypeIcon(CreditCardType type) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    IconData iconData;
    Color iconColor;

    switch (type) {
      case CreditCardType.visa:
        iconData = Icons.credit_card;
        iconColor = colorScheme.primary;
        break;
      case CreditCardType.mastercard:
        iconData = Icons.credit_card;
        iconColor = colorScheme.error;
        break;
      case CreditCardType.amex:
        iconData = Icons.credit_card;
        iconColor = Colors.green;
        break;
      case CreditCardType.rupay:
        iconData = Icons.credit_card;
        iconColor = Colors.orange;
        break;
      case CreditCardType.discover:
        iconData = Icons.credit_card;
        iconColor = colorScheme.tertiary;
        break;
      case CreditCardType.other:
        iconData = Icons.credit_card_outlined;
        iconColor = theme.hintColor;
        break;
    }

    return Icon(iconData, color: iconColor, size: 20);
  }

  String _getLocalizedCardTypeName(CreditCardType type) {
    switch (type) {
      case CreditCardType.visa:
        return 'credit_card_type_visa'.tr();
      case CreditCardType.mastercard:
        return 'credit_card_type_mastercard'.tr();
      case CreditCardType.amex:
        return 'credit_card_type_amex'.tr();
      case CreditCardType.rupay:
        return 'credit_card_type_rupay'.tr();
      case CreditCardType.discover:
        return 'credit_card_type_discover'.tr();
      case CreditCardType.other:
        return 'credit_card_type_other'.tr();
    }
  }

  Widget _buildDatePicker({
    required String label,
    required DateTime selectedDate,
    required Function(DateTime) onDateSelected,
  }) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: theme.textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
            color: colorScheme.onSurface,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            color: theme.cardColor,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: colorScheme.outline.withValues(alpha: 0.3),
              width: 1,
            ),
          ),
          child: ListTile(
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 4,
            ),
            leading: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: colorScheme.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.calendar_month,
                color: colorScheme.primary,
                size: 20,
              ),
            ),
            title: Text(
              DateFormat('MMM dd, yyyy').format(selectedDate),
              style: theme.textTheme.bodyMedium?.copyWith(
                color: colorScheme.onSurface,
                fontWeight: FontWeight.w500,
              ),
            ),
            trailing: Icon(
              Icons.keyboard_arrow_down,
              color: colorScheme.onSurface.withValues(alpha: 0.6),
              size: 20,
            ),
            onTap: () async {
              final pickedDate = await showDatePicker(
                context: context,
                initialDate: selectedDate,
                firstDate: DateTime.now(),
                lastDate: DateTime.now().add(const Duration(days: 365 * 2)),
                builder: (context, child) {
                  return Theme(
                    data: Theme.of(context).copyWith(
                      colorScheme: Theme.of(context).colorScheme.copyWith(
                        primary: colorScheme.primary,
                        onPrimary: colorScheme.onPrimary,
                        surface: theme.cardColor,
                        onSurface: colorScheme.onSurface,
                      ),
                    ),
                    child: child!,
                  );
                },
              );
              if (pickedDate != null) {
                onDateSelected(pickedDate);
              }
            },
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return KeyboardDismissWrapper(
      child: Scaffold(
        backgroundColor: theme.scaffoldBackgroundColor,
        appBar: AppBar(
          title: Text(
            'credit_card_add_title'.tr(),
            style: theme.appBarTheme.titleTextStyle?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          leading: IconButton(
            icon: const Icon(Icons.arrow_back_ios, size: 20),
            onPressed: () => Navigator.of(context).pop(),
          ),
        ),
        body: SingleChildScrollView(
          padding: const EdgeInsets.all(24),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header
                Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: colorScheme.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: colorScheme.primary.withValues(alpha: 0.2),
                      width: 1,
                    ),
                  ),
                  child: Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: colorScheme.primary.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Icon(
                          Icons.credit_card_rounded,
                          color: colorScheme.primary,
                          size: 28,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'credit_card_new_card'.tr(),
                              style: theme.textTheme.titleMedium?.copyWith(
                                fontWeight: FontWeight.w600,
                                color: colorScheme.onSurface,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              'credit_card_add_desc'.tr(),
                              style: theme.textTheme.bodySmall?.copyWith(
                                color: colorScheme.onSurface.withValues(
                                  alpha: 0.7,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 32),

                // Card Information Section
                Text(
                  'credit_card_card_info'.tr(),
                  style: theme.textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: colorScheme.onSurface,
                  ),
                ),
                const SizedBox(height: 16),

                CustomTextField(
                  controller: _cardNameController,
                  labelText: 'credit_card_name_label'.tr(),
                  hintText: 'credit_card_name_hint'.tr(),
                  prefixIcon: Icons.label,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'credit_card_name_required'.tr();
                    }
                    return null;
                  },
                ),

                const SizedBox(height: 16),

                CustomTextField(
                  controller: _cardNumberController,
                  labelText: 'credit_card_digits_label'.tr(),
                  hintText: 'credit_card_digits_hint'.tr(),
                  prefixIcon: Icons.numbers,
                  keyboardType: TextInputType.number,
                  inputFormatters: [
                    FilteringTextInputFormatter.digitsOnly,
                    LengthLimitingTextInputFormatter(4),
                  ],
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'credit_card_digits_required'.tr();
                    }
                    if (value.length != 4) {
                      return 'credit_card_digits_invalid'.tr();
                    }
                    return null;
                  },
                ),

                const SizedBox(height: 16),

                _buildCardTypeDropdown(),

                const SizedBox(height: 16),

                CustomTextField(
                  controller: _bankNameController,
                  labelText: 'credit_card_bank_label'.tr(),
                  hintText: 'credit_card_bank_hint'.tr(),
                  prefixIcon: Icons.account_balance,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'credit_card_bank_required'.tr();
                    }
                    return null;
                  },
                ),

                const SizedBox(height: 32),

                // Financial Information Section
                Text(
                  'credit_card_financial_info'.tr(),
                  style: theme.textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: colorScheme.onSurface,
                  ),
                ),
                const SizedBox(height: 16),

                CustomTextField(
                  controller: _creditLimitController,
                  labelText: 'credit_card_limit_label'.tr(),
                  hintText: 'credit_card_limit_hint'.tr(),
                  prefixIcon: Icons.account_balance_wallet,
                  keyboardType: TextInputType.number,
                  inputFormatters: [
                    FilteringTextInputFormatter.allow(RegExp(r'[0-9.]')),
                  ],
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'credit_card_limit_required'.tr();
                    }
                    final amount = double.tryParse(value);
                    if (amount == null || amount <= 0) {
                      return 'credit_card_amount_invalid'.tr();
                    }
                    return null;
                  },
                ),

                const SizedBox(height: 16),

                CustomTextField(
                  controller: _outstandingBalanceController,
                  labelText: 'credit_card_balance_label'.tr(),
                  hintText: 'credit_card_balance_hint'.tr(),
                  prefixIcon: Icons.money_off,
                  keyboardType: TextInputType.number,
                  inputFormatters: [
                    FilteringTextInputFormatter.allow(RegExp(r'[0-9.]')),
                  ],
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'credit_card_balance_required'.tr();
                    }
                    final amount = double.tryParse(value);
                    if (amount == null || amount < 0) {
                      return 'credit_card_amount_invalid'.tr();
                    }
                    return null;
                  },
                ),

                const SizedBox(height: 32),

                // Date Information Section
                Text(
                  'credit_card_important_dates'.tr(),
                  style: theme.textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: colorScheme.onSurface,
                  ),
                ),
                const SizedBox(height: 16),

                _buildDatePicker(
                  label: 'credit_card_due_date_label'.tr(),
                  selectedDate: _selectedDueDate,
                  onDateSelected: (date) {
                    setState(() {
                      _selectedDueDate = date;
                    });
                  },
                ),

                const SizedBox(height: 16),

                _buildDatePicker(
                  label: 'credit_card_closing_date_label'.tr(),
                  selectedDate: _selectedClosingDate,
                  onDateSelected: (date) {
                    setState(() {
                      _selectedClosingDate = date;
                    });
                  },
                ),

                const SizedBox(height: 32),

                // Error message
                if (_errorMessage != null)
                  Container(
                    padding: const EdgeInsets.all(12),
                    margin: const EdgeInsets.only(bottom: 16),
                    decoration: BoxDecoration(
                      color: colorScheme.error.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: colorScheme.error),
                    ),
                    child: Text(
                      _errorMessage!,
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: colorScheme.error,
                      ),
                    ),
                  ),

                // Add button
                CustomButton.elevated(
                  onPressed: _isLoading ? null : _handleAddCreditCard,
                  backgroundColor: colorScheme.primary,
                  child:
                      _isLoading
                          ? SizedBox(
                            height: 20,
                            width: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                colorScheme.onPrimary,
                              ),
                            ),
                          )
                          : Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.add_card,
                                color: colorScheme.onPrimary,
                              ),
                              const SizedBox(width: 8),
                              Text(
                                'credit_card_add_button'.tr(),
                                style: theme.textTheme.titleMedium?.copyWith(
                                  color: colorScheme.onPrimary,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                ),

                const SizedBox(height: 16),

                // Info section
                Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: Colors.blue.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: Colors.blue.withValues(alpha: 0.2),
                      width: 1,
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color: Colors.blue.withValues(alpha: 0.2),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Icon(
                              Icons.info_outline,
                              color: Colors.blue,
                              size: 18,
                            ),
                          ),
                          const SizedBox(width: 12),
                          Text(
                            'credit_card_important_info'.tr(),
                            style: theme.textTheme.titleSmall?.copyWith(
                              fontWeight: FontWeight.w600,
                              color: colorScheme.onSurface,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      Text(
                        'credit_card_info_notes'.tr(),
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: colorScheme.onSurface.withValues(alpha: 0.8),
                          height: 1.6,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
