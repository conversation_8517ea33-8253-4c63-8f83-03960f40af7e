import 'dart:io';

import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../core/network/api_service.dart';
import '../../utils/helper/route.dart';
import '../../utils/helper/connectivity_helper.dart';
import '../../services/auth_service.dart';
import '../../resources/app_theme.dart';
import '../../resources/app_text_styles.dart';

class SplashVC extends StatefulWidget {
  const SplashVC({super.key});

  @override
  State<SplashVC> createState() => _SplashVCState();
}

class _SplashVCState extends State<SplashVC> {
  String _statusMessage = 'splash_preparing_journey'.tr();
  bool _updateAvailable = false;

  @override
  void initState() {
    super.initState();
    _initialize();
  }

  void _initialize() async {
    await Future.delayed(const Duration(seconds: 1)); // Splash delay

    if (!mounted) return;

    // Check internet connectivity before checking for updates
    setState(() {
      _statusMessage = 'splash_checking_connectivity'.tr();
    });

    final hasInternet = await ConnectivityHelper.hasInternetConnection();

    if (!mounted) return;

    if (hasInternet) {
      // Internet is available, check for app updates
      setState(() {
        _statusMessage = 'splash_checking_updates'.tr();
      });
      await _callWebhookAPI();

      // FOR TESTING: Uncomment the line below to test the update dialog manually
      // _testUpdateDialog();

      if (!mounted) return;

      // If update is available, stop here and don't proceed with navigation
      if (_updateAvailable) {
        setState(() {
          _statusMessage = 'Please update to continue';
        });
        return; // Stop initialization here
      }
    } else {
      // No internet connection, skip update check
      debugPrint('No internet connection, skipping app update check');
      setState(() {
        _statusMessage = 'splash_no_internet_offline'.tr();
      });
      await Future.delayed(const Duration(seconds: 1));
    }

    if (!mounted) return;

    // Check if face authentication is set up
    final isFaceAuthEnabled = await AuthService.isFaceAuthEnabled();

    if (isFaceAuthEnabled) {
      // Face auth is set up, navigate to login screen for authentication
      setState(() {
        _statusMessage = 'splash_secure_access'.tr();
      });
      await Future.delayed(const Duration(seconds: 1));
      if (!mounted) return;
      AppRouter.goNamed(context, GoRouterConstants.login);
    } else {
      // Face auth not set up, navigate to signup screen for setup
      setState(() {
        _statusMessage = 'splash_secure_path'.tr();
      });
      await Future.delayed(const Duration(seconds: 1));
      if (!mounted) return;
      AppRouter.goNamed(context, GoRouterConstants.signup);
    }
  }

  /// Show update popup dialog
  Future<void> _showUpdateDialog(String newVersion, String updateUrl) async {
    if (!mounted) return;

    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    await showDialog(
      context: context,
      barrierDismissible: false, // User must tap button to dismiss
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          title: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      colorScheme.primary,
                      colorScheme.primary.withValues(alpha: 0.8),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  Icons.system_update,
                  color: colorScheme.onPrimary,
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Update Available',
                      style: theme.textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      'Version $newVersion',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: colorScheme.primary,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'A new version of the app is available. Please update to get the latest features and improvements.',
                style: theme.textTheme.bodyMedium,
              ),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: colorScheme.surfaceContainerHighest.withValues(
                    alpha: 0.3,
                  ),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.info_outline,
                      color: colorScheme.primary,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'Tap "Update" to download the latest version.',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          actions: [
            FilledButton.icon(
              onPressed: () async {
                Navigator.of(context).pop();
                await _openUpdateUrl(updateUrl);
              },
              icon: const Icon(Icons.download, size: 18),
              label: const Text('Update'),
              style: FilledButton.styleFrom(
                backgroundColor: colorScheme.primary,
                foregroundColor: colorScheme.onPrimary,
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  /// Open update URL in browser
  Future<void> _openUpdateUrl(String url) async {
    try {
      debugPrint('Opening update URL: $url');
      final uri = Uri.parse(url);

      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
        debugPrint('Update URL opened successfully');
      } else {
        debugPrint('Could not launch update URL: $url');
        // Show error message to user
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Could not open update link'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      debugPrint('Error opening update URL: $e');
      // Show error message to user
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error opening update link: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Compare version strings to determine if update is needed
  bool _isUpdateAvailable(String currentVersion, String remoteVersion) {
    try {
      // Remove any non-numeric characters except dots
      final currentClean = currentVersion.replaceAll(RegExp(r'[^\d\.]'), '');
      final remoteClean = remoteVersion.replaceAll(RegExp(r'[^\d\.]'), '');

      debugPrint('Comparing versions: $currentClean vs $remoteClean');

      // Parse version strings (e.g., "1.0.0" -> [1, 0, 0])
      final currentParts =
          currentClean
              .split('.')
              .map(int.tryParse)
              .where((v) => v != null)
              .cast<int>()
              .toList();
      final remoteParts =
          remoteClean
              .split('.')
              .map(int.tryParse)
              .where((v) => v != null)
              .cast<int>()
              .toList();

      // Compare version parts
      final maxLength =
          currentParts.length > remoteParts.length
              ? currentParts.length
              : remoteParts.length;

      for (int i = 0; i < maxLength; i++) {
        final current = i < currentParts.length ? currentParts[i] : 0;
        final remote = i < remoteParts.length ? remoteParts[i] : 0;

        if (remote > current) {
          debugPrint(
            'Update available: remote version $remote > current version $current',
          );
          return true;
        }
        if (remote < current) {
          debugPrint(
            'Current version is newer: current version $current > remote version $remote',
          );
          return false;
        }
      }

      debugPrint('Versions are equal');
      return false; // Versions are equal
    } catch (e) {
      debugPrint('Error comparing versions: $e');
      return false; // On error, assume no update needed
    }
  }

  /// API call to webhook endpoint using ApiService
  Future<void> _callWebhookAPI() async {
    try {
      debugPrint('Making API call to webhook using ApiService...');

      // Get ApiService instance (singleton)
      final apiService = ApiService();

      // Make POST request with custom headers
      final response = await apiService.post(
        endpoint:
            'https://workflow.indianic.org/webhook/a92cbf72-64e7-40f7-a7b1-397a2a44d720',
        headers: {
          'authorization':
              'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY3NzdiNTZhMGQzOGRjODQxNjcxNDlhZCIsImFsZ29yaXRobSI6IkhTMjU2IiwiaWF0IjoxNzM1ODk4NjcyfQ-3w',
        },
      );

      debugPrint('API Response Success: ${response.success}');
      debugPrint('API Response Status Code: ${response.statusCode}');
      debugPrint('API Response Message: ${response.message}');
      debugPrint('API Response Data: ${response.data}');

      if (response.success && response.hasData) {
        debugPrint('Webhook API call successful');
        debugPrint('Response has data: ${response.data}');

        // Parse the response data
        final responseData = response.data as Map<String, dynamic>;
        final remoteVersion = responseData['version'] as String?;
        final iosUrl = responseData['url_ios'] as String?;
        final androidUrl = responseData['url_android'] as String?;

        if (remoteVersion != null && (iosUrl != null || androidUrl != null)) {
          // Get current app version
          final packageInfo = await PackageInfo.fromPlatform();
          final currentVersion = packageInfo.version;

          debugPrint('Current app version: $currentVersion');
          debugPrint('Remote app version: $remoteVersion');

          // Check if update is available
          if (_isUpdateAvailable(currentVersion, remoteVersion)) {
            debugPrint('Update available! Showing update dialog...');

            // Set the update available flag
            setState(() {
              _updateAvailable = true;
            });

            // Determine which URL to use based on platform
            String updateUrl;
            if (Platform.isIOS && iosUrl != null) {
              updateUrl = iosUrl;
            } else if (Platform.isAndroid && androidUrl != null) {
              updateUrl = androidUrl;
            } else {
              // Fallback to android URL if platform-specific URL not available
              updateUrl = androidUrl ?? iosUrl ?? '';
            }

            if (updateUrl.isNotEmpty) {
              // Show update dialog after a brief delay to ensure UI is ready
              debugPrint('Scheduling update dialog to show in 500ms...');
              Future.delayed(const Duration(milliseconds: 500), () {
                debugPrint('Attempting to show update dialog now...');
                _showUpdateDialog(remoteVersion, updateUrl);
              });
            } else {
              debugPrint('No valid update URL found');
            }
          } else {
            debugPrint('App is up to date');
          }
        } else {
          debugPrint('Invalid response format: missing version or URL');
        }
      } else {
        debugPrint('Webhook API call failed: ${response.message}');
        if (response.hasError) {
          debugPrint('Error details: ${response.error}');
        }
      }
    } catch (e) {
      debugPrint('Error calling webhook API: $e');
      // Handle error gracefully - app should still work even if API fails
    }
  }

  /// Test method to manually show update dialog (for debugging)
  /// Uncomment the call in _initialize() to test manually
  // ignore: unused_element
  void _testUpdateDialog() {
    Future.delayed(const Duration(seconds: 2), () {
      debugPrint('Testing update dialog...');
      // Set the flag to prevent navigation
      setState(() {
        _updateAvailable = true;
        _statusMessage = 'Please update to continue';
      });
      _showUpdateDialog('1.1.0', 'https://overair.app/xzdUYPu7');
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: AppColorPalette.primary,
      body: Center(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // App Logo/Icon
              Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  color: AppColorPalette.white20,
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: 20,
                      offset: const Offset(0, 8),
                    ),
                  ],
                ),
                child: Icon(
                  Icons.account_balance_wallet,
                  size: 60,
                  color: AppColorPalette.white,
                ),
              ),
              const SizedBox(height: 40),

              // App Title
              Text(
                'splash_app_title'.tr(),
                style:
                    theme.textTheme.headlineLarge?.copyWith(
                      color: AppColorPalette.white,
                      fontWeight: FontWeight.bold,
                    ) ??
                    AppTextStyles.headlineLarge.copyWith(
                      color: AppColorPalette.white,
                      fontWeight: FontWeight.bold,
                    ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),

              // Subtitle
              Text(
                'splash_subtitle'.tr(),
                style:
                    theme.textTheme.bodyLarge?.copyWith(
                      color: AppColorPalette.white80,
                    ) ??
                    AppTextStyles.bodyLarge.copyWith(
                      color: AppColorPalette.white80,
                    ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 60),

              // Loading indicator
              CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(
                  AppColorPalette.white,
                ),
                strokeWidth: 3,
              ),
              const SizedBox(height: 20),

              // Status message
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: Text(
                  _statusMessage,
                  style:
                      theme.textTheme.bodyMedium?.copyWith(
                        color: AppColorPalette.white90,
                      ) ??
                      AppTextStyles.bodyMedium.copyWith(
                        color: AppColorPalette.white90,
                      ),
                  textAlign: TextAlign.center,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
