import 'dart:io';

import 'package:device_preview_plus/device_preview_plus.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart' show GetIt;

import 'core/constants/app_constants.dart';
import 'enviroment/config.dart';
import 'resources/app_theme.dart';
import 'utils/helper/route.dart';

GetIt sl = GetIt.instance;

class App extends StatefulWidget {
  final AppConfig appConfig;

  App(this.appConfig, {super.key});
  @override
  MyAppState createState() => MyAppState();
}

class MyAppState extends State<App> {
  @override
  void initState() {
    super.initState();
    setupDataUsingFlavours();
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp.router(
      routerConfig: router,
      debugShowCheckedModeBanner: false,
      title: 'My Finance',
      localizationsDelegates: context.localizationDelegates,
      supportedLocales: context.supportedLocales,
      locale: context.locale,
      // locale: DevicePreview.locale(context), // Device preview
      builder: DevicePreview.appBuilder,
      theme: AppTheme.lightTheme, // Light theme
      darkTheme: AppTheme.darkTheme, // Dark theme
      themeMode: ThemeMode.system, // Follows system theme
    );
  }

  /// Sets up the data using the current flavor.
  ///
  /// This is called in the [main] function before the app is built.
  Future<void> setupDataUsingFlavours() async {
    if (widget.appConfig.flavor == FlavoursName.development.name) {
      debugPrint('Development');
      env = ENV.development;
    } else if (widget.appConfig.flavor == FlavoursName.staging.name) {
      debugPrint('Staging');
      env = ENV.staging;
    } else if (widget.appConfig.flavor == FlavoursName.production.name) {
      debugPrint('Production');
      env = ENV.production;
    } else if (widget.appConfig.flavor == FlavoursName.enterprise.name) {
      debugPrint('Enterprise');
      env = ENV.production;
    }
  }

  /// Returns the device type based on the screen size.
  ///
  /// This method returns an [AppDeviceType] enum value based on the screen size.
  ///
  /// The [AppDeviceType] enum has the following values:
  /// - [AppDeviceType.webPortraitMobile]: Web device in portrait mode
  /// - [AppDeviceType.webTablet]: Web device in tablet mode
  /// - [AppDeviceType.webWideScreen]: Web device in wide screen mode
  /// - [AppDeviceType.mobileIosAndroid]: Mobile device for iOS or Android
  /// - [AppDeviceType.unknown]: Unknown device type
  AppDeviceType getAppDeviceType(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final double width = size.width;
    final double height = size.height;
    final double aspectRatio = width / height;

    debugPrint(
      'Device detection - Width: $width, Height: $height, Aspect Ratio: $aspectRatio',
    );
    debugPrint('Is Web Platform: $kIsWeb');

    if (kIsWeb) {
      // More reliable web detection based on width rather than just aspect ratio
      if (width < 600) {
        debugPrint('Detected as webPortraitMobile');
        return AppDeviceType.webPortraitMobile;
      } else if (width < 1200) {
        debugPrint('Detected as webTablet');
        return AppDeviceType.webTablet;
      } else {
        debugPrint('Detected as webWideScreen');
        return AppDeviceType.webWideScreen;
      }
    } else {
      if (Platform.isAndroid || Platform.isIOS) {
        debugPrint('Detected as mobileIosAndroid');
        return AppDeviceType.mobileIosAndroid;
      }
    }

    debugPrint('Detected as unknown device');
    return AppDeviceType.unknown;
  }
}
