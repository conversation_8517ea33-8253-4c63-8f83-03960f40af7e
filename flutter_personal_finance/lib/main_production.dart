import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:timezone/data/latest.dart' as tz;
import 'app.dart';
import 'core/constants/app_constants.dart';
import 'core/network/api_service.dart';
import 'enviroment/config.dart';
import 'utils/helper/shared_preference.dart';
import 'services/notification_service.dart';

Future<void> main() async {
  // Initialize Flutter bindings for platform channels
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize localization
  await EasyLocalization.ensureInitialized();

  // Initialize timezone data for notifications
  tz.initializeTimeZones();

  // Initialize notification service
  await NotificationService.initialize();

  await setSingleton();

  // SharedPreferences initialization
  await SharedPreferencesHelper.init();

  // Configure app for production environment
  AppConfig productionAppConfig = AppConfig(
    flavor: FlavoursName.production.name,
  );

  // Run the app with EasyLocalization for language support
  // Supports English, Hindi, and Arabic languages
  runApp(
    EasyLocalization(
      // Localization setup
      supportedLocales: const [Locale('en'), Locale('hi')],
      path: 'langs',
      fallbackLocale: const Locale('en'),
      child: App(productionAppConfig),
    ),
  );
}

Future<void> setSingleton() async {
  sl.registerLazySingleton<ApiService>(ApiService.new);
}
